import pytest
from model_bakery import baker

from core.models_commons import Cidade
from core.models_rota import LocalEmbarque, Rota, TrechoVendido
from search_result.service.searchbox_svc import (
    SearchboxItem,
    _list_cidade_items,
    _list_locais_de_embarque_items,
    list_searchbox_items,
)


@pytest.fixture
def trecho_ativo():
    cidade_origem = baker.make(Cidade, id=111, name="São Paulo", uf="SP", slug="sao-paulo-sp", ativo=True)
    cidade_destino = baker.make(Cidade, id=222, name="Curitiba", uf="PR", slug="curitiba-pr", ativo=True)
    local_origem = baker.make(
        LocalEmbarque,
        id=333,
        cidade=cidade_origem,
        nickname="Estacionamento Tietê",
        slug="estacione-tiete-sao-paulo-sp",
        latitude=-23.5172678,
        longitude=-46.6277156,
        ativo=True,
    )
    local_destino = baker.make(
        LocalEmbarque,
        id=444,
        cidade=cidade_destino,
        endereco="Rua Plácido <PERSON>, 1004 - <PERSON><PERSON><PERSON>",
        slug="rua-placido-de-castro-1004-jardim-botanico-curitiba-pr",
        latitude=-25.4536849,
        longitude=-49.2433825,
        ativo=True,
    )
    rota = baker.make(Rota, origem=local_origem, destino=local_destino, ativo=True)
    return baker.make(TrechoVendido, rota=rota, origem=local_origem, destino=local_destino)


@pytest.fixture
def trecho_ativo_com_cidade_inativa():
    cidade_origem = baker.make(Cidade, id=111, name="São Paulo", uf="SP", slug="sao-paulo-sp", ativo=False)
    cidade_destino = baker.make(Cidade, id=222, name="Curitiba", uf="PR", slug="curitiba-pr", ativo=False)
    local_origem = baker.make(
        LocalEmbarque,
        id=333,
        cidade=cidade_origem,
        nickname="Estacionamento Tietê",
        slug="estacione-tiete-sao-paulo-sp",
        latitude=-23.5172678,
        longitude=-46.6277156,
        ativo=True,
    )
    local_destino = baker.make(
        LocalEmbarque,
        id=444,
        cidade=cidade_destino,
        endereco="Rua Plácido de Castro, 1004 - Jardim Botânico",
        slug="rua-placido-de-castro-1004-jardim-botanico-curitiba-pr",
        latitude=-25.4536849,
        longitude=-49.2433825,
        ativo=True,
    )
    rota = baker.make(Rota, origem=local_origem, destino=local_destino, ativo=True)
    return baker.make(TrechoVendido, rota=rota, origem=local_origem, destino=local_destino)


@pytest.fixture
def trecho_inativo():
    cidade_origem = baker.make(Cidade, id=555, name="Aracaju", uf="SE", slug="aracaju-se", ativo=True)
    cidade_destino = baker.make(Cidade, id=666, name="Belém", uf="PA", slug="belem-pa", ativo=True)
    baker.make(Cidade, ativo=False)
    local_origem = baker.make(
        LocalEmbarque,
        id=777,
        cidade=cidade_origem,
        nickname="Posto Rio Poxim (Posto Shell)",
        slug="posto-rio-poxim-posto-shell-aracaju-se",
        latitude=-10.9511642,
        longitude=-37.0649371,
        ativo=False,
    )
    local_destino = baker.make(
        LocalEmbarque,
        id=888,
        cidade=cidade_destino,
        endereco="Av. Pedro Álvares Cabral, 17 - Sacramenta",
        slug="av-pedro-alvares-cabral-17-sacramenta-belem-pa",
        latitude=-1.4106812,
        longitude=-48.4687408,
        ativo=False,
    )
    rota = baker.make(Rota, origem=local_origem, destino=local_destino, ativo=False)
    return baker.make(TrechoVendido, rota=rota, origem=local_origem, destino=local_destino)


def test_list_searchbox_items_empty():
    items = list_searchbox_items()
    assert items == []


def test_list_searchbox_items_cidades(trecho_ativo, trecho_inativo):
    items = _list_cidade_items()
    items.sort(key=lambda x: x.id)
    assert items == [
        SearchboxItem(id="111", city="São Paulo", state="SP", slug="sao-paulo-sp"),
        SearchboxItem(id="222", city="Curitiba", state="PR", slug="curitiba-pr"),
        SearchboxItem(id="555", city="Aracaju", state="SE", slug="aracaju-se"),
        SearchboxItem(id="666", city="Belém", state="PA", slug="belem-pa"),
    ]


def test_list_searchbox_items_locais_de_embarque(trecho_ativo, trecho_inativo):
    items = _list_locais_de_embarque_items()
    items.sort(key=lambda x: x.id)
    assert items == [
        SearchboxItem(
            id="111-333",
            city="São Paulo",
            state="SP",
            addr="Estacionamento Tietê",
            slug="estacione-tiete-sao-paulo-sp",
            lat=-23.5172678,
            lon=-46.6277156,
            city_id=111,
        ),
        SearchboxItem(
            id="222-444",
            city="Curitiba",
            state="PR",
            addr="Rua Plácido de Castro, 1004 - Jardim Botânico",
            slug="rua-placido-de-castro-1004-jardim-botanico-curitiba-pr",
            lat=-25.4536849,
            lon=-49.2433825,
            city_id=222,
        ),
    ]


def test_list_searchbox_items_both(trecho_ativo, trecho_inativo):
    items = list_searchbox_items()
    items.sort(key=lambda x: x.id)
    assert items == [
        SearchboxItem(id="111", city="São Paulo", state="SP", slug="sao-paulo-sp"),
        SearchboxItem(
            id="111-333",
            city="São Paulo",
            state="SP",
            addr="Estacionamento Tietê",
            slug="estacione-tiete-sao-paulo-sp",
            lat=-23.5172678,
            lon=-46.6277156,
            city_id=111,
        ),
        SearchboxItem(id="222", city="Curitiba", state="PR", slug="curitiba-pr"),
        SearchboxItem(
            id="222-444",
            city="Curitiba",
            state="PR",
            addr="Rua Plácido de Castro, 1004 - Jardim Botânico",
            slug="rua-placido-de-castro-1004-jardim-botanico-curitiba-pr",
            lat=-25.4536849,
            lon=-49.2433825,
            city_id=222,
        ),
        SearchboxItem(id="555", city="Aracaju", state="SE", slug="aracaju-se"),
        SearchboxItem(id="666", city="Belém", state="PA", slug="belem-pa"),
    ]


def test_list_searchbox_cidades_inativas_com_rota_ativa(trecho_ativo_com_cidade_inativa):
    items = list_searchbox_items()
    items.sort(key=lambda x: x.id)
    assert items == [
        SearchboxItem(id="111", city="São Paulo", state="SP", slug="sao-paulo-sp"),
        SearchboxItem(
            id="111-333",
            city="São Paulo",
            state="SP",
            addr="Estacionamento Tietê",
            slug="estacione-tiete-sao-paulo-sp",
            lat=-23.5172678,
            lon=-46.6277156,
            city_id=111,
        ),
        SearchboxItem(id="222", city="Curitiba", state="PR", slug="curitiba-pr"),
        SearchboxItem(
            id="222-444",
            city="Curitiba",
            state="PR",
            addr="Rua Plácido de Castro, 1004 - Jardim Botânico",
            slug="rua-placido-de-castro-1004-jardim-botanico-curitiba-pr",
            lat=-25.4536849,
            lon=-49.2433825,
            city_id=222,
        ),
    ]


def test_list_searchbox_alias_places(trecho_ativo):
    alias = baker.make("core.AliasPlace", alias="Teste", cidade=trecho_ativo.origem.cidade)
    items = list_searchbox_items()
    items.sort(key=lambda x: x.id)
    assert items == [
        SearchboxItem(id="111", city="São Paulo", state="SP", slug="sao-paulo-sp"),
        SearchboxItem(
            id="111-333",
            city="São Paulo",
            state="SP",
            addr="Estacionamento Tietê",
            slug="estacione-tiete-sao-paulo-sp",
            lat=-23.5172678,
            lon=-46.6277156,
            city_id=111,
        ),
        SearchboxItem(
            id=f"111_{alias.id}",
            city="São Paulo",
            state="SP",
            addr=None,
            slug="teste-sp",
            lat=None,
            lon=None,
            city_id=111,
            alias="Teste",
        ),
        SearchboxItem(id="222", city="Curitiba", state="PR", slug="curitiba-pr"),
        SearchboxItem(
            id="222-444",
            city="Curitiba",
            state="PR",
            addr="Rua Plácido de Castro, 1004 - Jardim Botânico",
            slug="rua-placido-de-castro-1004-jardim-botanico-curitiba-pr",
            lat=-25.4536849,
            lon=-49.2433825,
            city_id=222,
        ),
    ]
