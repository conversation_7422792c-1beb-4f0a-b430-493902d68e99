import json
from datetime import datetime, timedelta
from decimal import Decimal as D
from http import HTTPStatus
from unittest import mock

import django.contrib.auth.models as django_auth_models
import pytest
from django.test import TestCase, override_settings
from django.test.client import Client
from django.urls import resolve
from django.utils import timezone
from model_bakery import baker
from pydantic.v1 import ValidationError

from commons import dateutils, slug_utils
from commons.tests.test_utils import ANY_DATETIME, DictEqualsKeysAndValues
from commons.utils import hashint
from core.models_commons import Imagem
from core.models_grupo import Grupo, TrechoClasse
from core.models_rota import LocalEmbarque
from core.tests.mock import mock_dateutils
from search_result import views as sr_views
from search_result.adapter.cidade_adapter import CidadeAdapter
from search_result.adapter.local_embarque_adapter import LocalEmbarqueAdapter
from search_result.serializer import serializer_trecho_classe
from search_result.service import grupos_svc, itinerario_svc
from search_result.tests.fixture.grupos_e_trechos import (
    grupo_classe_bhipa,
    grupo_classe_bhipa_rota3,
    grupo_classe_bhmoc,
    grupo_classe_cgbh,
    grupo_classe_spbh,
    grupo_classe_spcwb,
    grupos_e_trechos,
)
from search_result.tests.fixture.locais import locais
from search_result.tests.fixture.mock_rota import (
    _mock_rota2,
    rota3,
    rota_campo_grande_bh,
)
from search_result.tests.fixture.travels import make_dummy_travels
from search_result.tests.fixture.user import user_factory, user_ze


@pytest.fixture(autouse=True)
def set_price_range_endpoint_enabled(globalsettings_mock):
    globalsettings_mock("enable_price_range_endpoint", True)


class TestSearch(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.b = grupos_e_trechos(pessoasnogrupo=1, dtbase=datetime(2018, 1, 19, 19, 0, 0))
        grupo_classe_bhipa(cls.b, pessoasnogrupo=0, dtbase=datetime(2018, 1, 19, 19, 0, 0))
        grupo_classe_spbh(cls.b, pessoasnogrupo=1, dtbase=timezone.now() + timedelta(days=3))

        rota_spcwb = _mock_rota2(cls.b.local_sp_se, cls.b.local_cwb_castelo)
        grupo_classe_spcwb(
            rota_spcwb,
            pessoasnogrupo=1,
            dtbase=timezone.now() + timedelta(days=2),
            capacidade=4,
        )
        grupo_classe_spcwb(
            rota_spcwb,
            pessoasnogrupo=4,
            dtbase=timezone.now() + timedelta(days=4),
            capacidade=4,
        )
        grupo_classe_spcwb(
            rota_spcwb,
            pessoasnogrupo=1,
            dtbase=timezone.now() + timedelta(days=7),
            capacidade=4,
            marketplace=True,
        )
        grupo_classe_spcwb(
            rota_spcwb,
            pessoasnogrupo=4,
            dtbase=timezone.now() + timedelta(days=2),
            capacidade=4,
            marketplace=True,
        )
        rota_campo_grande_bh(cls.b)
        grupo_classe_cgbh(cls.b, dtbase=datetime(2018, 1, 19, 23, 59, 0))
        grupo_classe_bhmoc(cls.b, pessoasnogrupo=1, dtbase=datetime(2018, 1, 19, 19, 0, 0))
        rota3(cls.b)
        locais(cls.b)
        make_dummy_travels()

    def setUp(self):
        self.use_underlined_slug = False

    def test_search(self):
        b = self.b
        self.client.force_login(user_ze())
        self._search_and_assert(b.cidade_bh, b.cidade_sp, "2018-01-19")
        self._search_and_assert(b.cidade_bh, b.cidade_montes, "2018-01-19")
        # log_empty_search
        self._search_and_assert(b.cidade_bh, b.cidade_ipatinga, dateutils.today().strftime("%Y-%m-%d"))
        # horário de verão
        # log_empty_search
        self._search_and_assert(b.cidade_bh, b.cidade_ipatinga, "2018-11-04")
        self._search_and_assert(b.cidade_sp, b.cidade_bh)
        self._search_and_assert(b.cidade_campogrande, b.cidade_bh, "2018-01-19")
        self._search_and_assert(
            b.cidade_sp,
            b.cidade_bh,
            timezone.localtime(timezone.now() + timedelta(days=3)).strftime("%Y-%m-%d"),
        )
        self._search_nearby_and_assert(b.cidade_sp, b.cidade_bh)
        self._search_nearby_and_assert(
            b.cidade_sp,
            b.cidade_bh,
            timezone.localtime(timezone.now() + timedelta(days=5)).strftime("%Y-%m-%d"),
        )

    def test_underline_slug_search(self):
        self.use_underlined_slug = True
        self.test_search()

    def test_search_com_parenteses(self):
        # mantém compatibilidade com slugs com parenteses
        origem = baker.prepare("core.Cidade", slug="belo-(horizonte)-mg")
        destino = baker.prepare("core.Cidade", slug="sao-(paulo)-sp")
        self._search_and_assert(origem, destino, "2018-01-19")

    def test_search_apenas_origem(self):
        origem = "cidade-a-aa"
        with mock.patch("core.service.activitylog_svc.publish") as publish_log_mock:
            self._search_origin_and_assert(origem)
            publish_log_mock.assert_called_with(
                DictEqualsKeysAndValues(
                    **{
                        "created_at": ANY_DATETIME,
                        "origem": "CIDADE A",
                        "destino": None,
                    }
                )
            )

    def test_search_apenas_destino(self):
        destino = "cidade-a-aa"
        with mock.patch("core.service.activitylog_svc.publish") as publish_log_mock:
            self._search_origin_and_assert(destino)
            publish_log_mock.assert_called_with(
                DictEqualsKeysAndValues(
                    **{
                        "created_at": ANY_DATETIME,
                        "origem": None,
                        "destino": "CIDADE A",
                    }
                )
            )

    def test_search_duas_rotas_com_mesmo_trecho(self):
        b = self.b
        grupo_classe_bhipa(b)
        grupo_classe_bhipa_rota3(b)
        self._search_and_assert(b.cidade_bh, b.cidade_ipatinga)

    def test_search_top_rotas(self):
        cidade_slug = "cidade-a-aa"
        self._assert_top_rotas(cidade_slug)

    def test_search_top_rotas_cidade_inexistente(self):
        cidade_slug = "tangamandapio"
        self._assert_top_rotas_inexistentes(cidade_slug)

    def test_search_top_rotas_localembarque(self):
        b = self.b

        local_ativo = b.local_sp_se
        self._assert_top_rotas_localembarque(
            local_ativo.cidade.slug,
            local_ativo.bairro_slug,
            local_ativo.nickname_slug,
            ativo=True,
        )

        local_inativo = baker.make(
            "core.LocalEmbarque",
            ativo=False,
            _fill_optional=["bairro_slug", "nickname_slug"],
        )
        self._assert_top_rotas_localembarque(
            local_inativo.cidade.slug,
            local_inativo.bairro_slug,
            local_inativo.nickname_slug,
            ativo=False,
        )

        self._assert_top_rotas_localembarque_mesmo_bairro_e_cidade(b.local_bh_lagoapatos, b.local_bh_lagoapatos2)

    def test_search_trecho_nao_vendido(self):
        b = self.b
        grupo_classe_bhipa(b)
        grupo_classe_bhipa_rota3(b)
        self._search_and_assert(b.cidade_sp, b.cidade_ipatinga, "2018-01-19", is_trecho_vendido=False)

    def test_search_rotas_com_grupos(self):
        self._search_rotas_com_grupos_and_assert()

    def test_search_default(self):
        params = {
            "origemSlug": self.b.cidade_bh.slug,
            "destinoSlug": self.b.cidade_ipatinga.slug,
            "departureDate": "2018-01-19",
        }
        with mock_dateutils.patch_at(datetime_at=dateutils.to_default_tz_required("2018-01-19")):
            r = self.client.get("/api/search", params)

        self.assertEqual(200, r.status_code)

        res = r.json()
        checkpoint = list(res["itinerarios"].values())[0][0]
        self.assertNotIn("local", checkpoint)
        self.assertIn("destino_picture_url", res)

    def test_search_hidden_group_for_pax(self):
        params = {
            "origemSlug": self.b.cidade_bh.slug,
            "destinoSlug": self.b.cidade_ipatinga.slug,
            "departureDate": "2018-01-19",
        }
        Grupo.objects.filter(id=self.b.grupo_classe_bhipa.grupo.id).update(hidden_for_pax=True)
        with mock_dateutils.patch_at(datetime_at=dateutils.to_default_tz_required("2018-01-19")):
            r = self.client.get("/api/search", params)

        self.assertEqual(200, r.status_code)
        res = r.json()
        self.assertFalse(res["has_grupos"])
        self.assertEqual(0, sum([len(d["grupos"]) for d in res["groups_by_date"]]))

        # Listar grupos para revendedor
        revendedor = user_factory(roles=["Revendedor"])
        client_revendedor = Client()
        client_revendedor.force_login(revendedor)
        with mock_dateutils.patch_at(datetime_at=dateutils.to_default_tz_required("2018-01-19")):
            r = client_revendedor.get("/api/search", params, HTTP_CLIENT="vendas")
        self.assertEqual(200, r.status_code)
        res = r.json()
        self.assertTrue(res["has_grupos"])
        self.assertEqual(1, sum([len(d["grupos"]) for d in res["groups_by_date"]]))
        Grupo.objects.filter(id=self.b.grupo_classe_bhipa.grupo.id).update(hidden_for_pax=False)

    def test_pydantic_validation(self):
        res = self.client.get("/api/search")

        self.assertEqual(res.status_code, HTTPStatus.BAD_REQUEST)
        self.assertIn("error", res.json())

    def test_search_rotas_sem_data_ida(self):
        sp, curitiba = self.b.cidade_sp, self.b.cidade_curitiba

        params = {
            "origemSlug": sp.slug,
            "destinoSlug": curitiba.slug,
        }
        r = self.client.get("/api/search", params)
        self.assertEqual(200, r.status_code)

        res = r.json()
        groups = res["groups_by_date"]
        self.assertEqual(len(groups), 3)
        self.assertEqual(self._count_marketplace_groups(groups), 2)
        self.assertIn("itinerarios", res)
        self.assertIn("locais", res)

    def _search_nearby_and_assert(self, origem, destino, date=None):
        params = {
            "origemSlug": origem.slug,
            "destinoSlug": destino.slug,
        }
        if date:
            params["date"] = date
        r = self.client.get("/api/get_nearby_departures", params)
        self.assertEqual(200, r.status_code)

    def _search_and_assert(self, origem, destino, data_ida=None, is_trecho_vendido=True):
        if self.use_underlined_slug:
            origem_slug = slug_utils.to_underline_slug(origem.slug)
            destino_slug = slug_utils.to_underline_slug(destino.slug)
        else:
            origem_slug = origem.slug
            destino_slug = destino.slug

        params = {
            "origemSlug": origem_slug,
            "destinoSlug": destino_slug,
        }
        if data_ida:
            params["departureDate"] = data_ida

        with mock_dateutils.patch_at(datetime_at=dateutils.to_default_tz_required(data_ida) if data_ida else None):
            r = self.client.get("/api/search", params)

            hit_params = {
                "origem": params["origemSlug"],
                "destino": params["destinoSlug"],
            }
            if data_ida:
                grupos = r.json()["groups_by_date"]

                # One-line pra pegar o primeiro match
                grupos_no_dia = next(x for x in grupos if x["datetime_ida"] == data_ida)

                hit_params.update(
                    {
                        "data_ida": data_ida,
                        "count": len(grupos_no_dia["grupos"]),
                    }
                )

        self.assertEqual(200, r.status_code)
        res = r.json()

        if data_ida:
            grupos = [item for item in res["groups_by_date"] if item["datetime_ida"] == params["departureDate"]][0][
                "grupos"
            ]
            self.assertIsNotNone(res["trecho"])
        else:
            grupos = list(res["groups_by_date"])[0]["grupos"]

        self.assertEqual(is_trecho_vendido, res["is_trecho_vendido"])

    def _search_origin_and_assert(self, origem):
        r = self.client.get("/api/search/origem", {"origem": origem})
        self.assertEqual(200, r.status_code)
        res = r.json()
        self.assertEqual("CIDADE A", res["name"])
        trechos = list(res["saindo_de"])
        self.assertEqual("CIDADE B", trechos[0]["name"])
        for t in trechos:
            with self.subTest(t):
                self.assertIn("itinerario", t)

    def _search_destino_and_assert(self, destino):
        r = self.client.get("/api/search/destino", {"destino": destino})
        self.assertEqual(200, r.status_code)
        res = r.json()
        self.assertEqual("CIDADE A", res["name"])
        trechos = list(res["indo_para"])
        self.assertEqual("CIDADE C", trechos[0]["name"])
        for t in trechos:
            with self.subTest(t):
                self.assertIn("itinerario", t)

    def _assert_top_rotas(self, cidade_slug):
        params = {"limit": 3}
        r = self.client.get("/api/search/top_rotas/%s" % cidade_slug, params)
        self.assertEqual(200, r.status_code)
        res = json.loads(r.content.decode("utf-8"), parse_float=D)
        self.assertIsNotNone(res["info"])
        self.assertIsNotNone(res["wikipedia"])
        self.assertIsNotNone(res["sigla"])
        cidades_indo_para = res["indo_para"]
        cidades_saindo_de = res["saindo_de"]

        self.assertEqual("CIDADE C", cidades_indo_para[0]["name"])
        self.assertEqual(D("75.00"), cidades_indo_para[0]["best_price"])

        self.assertEqual("CIDADE D", cidades_indo_para[1]["name"])
        self.assertEqual(D("50.00"), cidades_indo_para[1]["best_price"])

        self.assertEqual("CIDADE B", cidades_indo_para[2]["name"])
        self.assertEqual(D("100.00"), cidades_indo_para[2]["best_price"])

        self.assertEqual("CIDADE B", cidades_saindo_de[0]["name"])
        self.assertEqual(D("30.00"), cidades_saindo_de[0]["best_price"])

        self.assertEqual("CIDADE D", cidades_saindo_de[1]["name"])
        self.assertEqual(D("75.00"), cidades_saindo_de[1]["best_price"])

        self.assertEqual("CIDADE C", cidades_saindo_de[2]["name"])
        self.assertEqual(D("50.00"), cidades_saindo_de[2]["best_price"])

        params2 = dict(bairroSlug="bairro-da-conchinchina", cidadeSlug="conchinchina")
        r2 = self.client.get("/api/search/top_rotas/%s" % cidade_slug, params2)
        self.assertEqual(200, r2.status_code)
        res = json.loads(r2.content.decode("utf-8"), parse_float=D)
        self.assertEqual(len(res["indo_para"]), 0)
        self.assertEqual(len(res["saindo_de"]), 0)

    def _assert_top_rotas_inexistentes(self, cidade_slug):
        params = {"limit": 3}
        r = self.client.get("/api/search/top_rotas/%s" % cidade_slug, params)
        self.assertEqual(200, r.status_code)
        res = json.loads(r.content.decode("utf-8"), parse_float=D)
        self.assertEqual(res["go"], "destinos")

    def _assert_top_rotas_localembarque(self, cidade_slug, bairro_slug, nickname_slug, ativo=True):
        params = {
            "destinoSlug": cidade_slug,
            "bairroSlug": bairro_slug,
            "nicknameSlug": nickname_slug,
        }
        r = self.client.get("/api/search/top_rotas", params)
        self.assertEqual(200, r.status_code)
        res = r.json()

        if ativo:
            self.assertIsNotNone(res.get("nickname"))
            self.assertIsNotNone(res.get("endereco_bairro"))
            self.assertIsNotNone(res.get("endereco_logradouro"))
        else:
            self.assertIsNone(res.get("nickname"))
            self.assertIsNone(res.get("endereco_bairro"))
            self.assertIsNone(res.get("endereco_logradouro"))

    def _assert_top_rotas_localembarque_mesmo_bairro_e_cidade(self, local1, local2):
        params1 = {
            "destinoSlug": local1.cidade.slug,
            "bairroSlug": local1.bairro_slug,
            "nicknameSlug": local1.nickname_slug,
        }

        params2 = {
            "destinoSlug": local2.cidade.slug,
            "bairroSlug": local2.bairro_slug,
            "nicknameSlug": local2.nickname_slug,
        }

        self.assertEqual(local1.bairro_slug, local2.bairro_slug)
        self.assertEqual(local1.cidade.slug, local2.cidade.slug)

        r1 = self.client.get("/api/search/top_rotas", params1)
        r2 = self.client.get("/api/search/top_rotas", params2)
        self.assertEqual(200, r1.status_code)
        self.assertEqual(200, r2.status_code)
        res1 = r1.json()
        res2 = r2.json()
        self.assertEqual(res1["nickname"], local1.nickname)
        self.assertEqual(res2["nickname"], local2.nickname)

    def _search_rotas_com_grupos_and_assert(self):
        r = self.client.get("/api/search/rotas_com_grupos")
        self.assertEqual(200, r.status_code)
        res = r.json()

        assert res["count_items"] == 10  # criados no make_dummy_travels
        assert res["count_pages"] == 1

    def _count_marketplace_groups(self, groups_by_date):
        count = 0
        for group_by_date in groups_by_date:
            for group in group_by_date["grupos"]:
                count += 1 if group["modelo_venda"] == Grupo.ModeloVenda.MARKETPLACE else 0
        return count


@pytest.fixture
def sao_paulo(db):
    return baker.make(
        "core.Cidade",
        name="sao paulo",
        uf="SP",
        slug="sao-paulo-sp",
        image="trecho_images/sao_paulo-sp.jpg",
    )


@pytest.fixture
def rio_de_janeiro(db):
    return baker.make(
        "core.Cidade",
        name="rio de janeiro",
        uf="RJ",
        slug="rio-de-janeiro-rj",
        image="trecho_images/rio-de-janeiro-rj.jpg",
    )


@pytest.fixture
def rio_de_janeiro_info(db, rio_de_janeiro):
    return baker.make("core.CidadeInfo", cidade=rio_de_janeiro)


@pytest.fixture
def rio_de_janeiro_thumb_image(db, rio_de_janeiro_info):
    return baker.make("core.Imagem", tipo=Imagem.Tipo.THUMB, cidade_info=rio_de_janeiro_info)


@pytest.fixture
def local_sp(db, sao_paulo):
    return baker.make("core.LocalEmbarque", cidade=sao_paulo, slug="local-sp-sao-paulo-sp")


@pytest.fixture
def local_sp_1(db, sao_paulo):
    return baker.make(
        "core.LocalEmbarque",
        cidade=sao_paulo,
        nickname="Local SP 1",
        slug="local-sp-1-sao-paulo-sp",
    )


@pytest.fixture
def local_sp_2(db, sao_paulo):
    return baker.make(
        "core.LocalEmbarque",
        cidade=sao_paulo,
        nickname="Local SP 2",
        slug="local-sp-2-sao-paulo-sp",
        features=[LocalEmbarque.Features.METRO],
    )


@pytest.fixture
def local_rj(db, rio_de_janeiro):
    return baker.make("core.LocalEmbarque", cidade=rio_de_janeiro, slug="local-rj-rio-de-janeiro-rj")


@pytest.fixture
def rota_sp_rj(db, local_sp, local_rj):
    rota = baker.make("core.Rota", origem=local_sp, destino=local_rj)
    itinerario = [
        baker.prepare("core.Checkpoint", local=local_sp, rota=rota, idx=0),
        baker.prepare("core.Checkpoint", local=local_rj, rota=rota, idx=1),
    ]
    rota.itinerario.bulk_create(itinerario)
    return rota


@pytest.fixture
def rota_rj_sp(db, local_rj, local_sp):
    rota = baker.make("core.Rota", origem=local_rj, destino=local_sp)
    itinerario = [
        baker.prepare("core.Checkpoint", local=local_rj, rota=rota, idx=0),
        baker.prepare("core.Checkpoint", local=local_sp, rota=rota, idx=1),
    ]
    rota.itinerario.bulk_create(itinerario)
    return rota


@pytest.fixture
def rota_sp1_rj(db, local_sp_1, local_rj):
    rota = baker.make("core.Rota", origem=local_sp_1, destino=local_rj)
    itinerario = [
        baker.prepare("core.Checkpoint", local=local_sp_1, rota=rota, idx=0),
        baker.prepare("core.Checkpoint", local=local_rj, rota=rota, idx=1),
    ]
    rota.itinerario.bulk_create(itinerario)
    return rota


@pytest.fixture
def rota_sp2_rj(db, local_sp_2, local_rj):
    rota = baker.make("core.Rota", origem=local_sp_2, destino=local_rj)
    itinerario = [
        baker.prepare("core.Checkpoint", local=local_sp_2, rota=rota, idx=0),
        baker.prepare("core.Checkpoint", local=local_rj, rota=rota, idx=1),
    ]
    rota.itinerario.bulk_create(itinerario)
    return rota


@pytest.fixture
def trecho_vendido_sp_rj(db, rota_sp_rj, local_sp, local_rj):
    return baker.make("core.TrechoVendido", rota=rota_sp_rj, origem=local_sp, destino=local_rj)


@pytest.fixture
def trecho_vendido_rj_sp(db, rota_rj_sp, local_rj, local_sp):
    return baker.make("core.TrechoVendido", rota=rota_rj_sp, origem=local_rj, destino=local_sp)


@pytest.fixture
def trecho_vendido_sp1_rj(db, rota_sp1_rj, local_sp_1, local_rj):
    return baker.make("core.TrechoVendido", rota=rota_sp1_rj, origem=local_sp_1, destino=local_rj)


@pytest.fixture
def trecho_vendido_sp2_rj(db, rota_sp2_rj, local_sp_2, local_rj):
    return baker.make("core.TrechoVendido", rota=rota_sp2_rj, origem=local_sp_2, destino=local_rj)


@pytest.fixture
def make_trecho_classe(trecho_vendido_sp_rj):
    def factory(
        datetime_as_str,
        status="travel_confirmed",
        tipo_assento="leito",
        max_split_value=D("25"),
        duracao_ida=timedelta(hours=2),
        modelo_venda=Grupo.ModeloVenda.BUSER,
        include_company=False,
        include_parent_company=False,
        buckets=None,
        vagas=5,
        trecho_vendido=trecho_vendido_sp_rj,
        include_price_manager=False,
        price_manager_value=None,
        modelo_operacao=Grupo.ModeloOperacao.DEFAULT,
    ):
        company = None
        if include_company:
            company = baker.make(
                "core.Company",
                name="Linda compania",
                logo_s3key="https://www.youtube.com/watch?v=nYMDFv5cKaA",
            )
        if include_company and include_parent_company:
            company.parent_company_hibrido = baker.make(
                "core.Company",
                name="Pai da Linda compania",
                logo_s3key="https://www.youtube.com/watch?v=jiyDOoRt2wo",
            )
            company.save()

        price_manager = None
        if include_price_manager and price_manager_value:
            price_manager = baker.make("core.PriceManager", value=price_manager_value)

        if price_manager and buckets:
            for idx, bucket in enumerate(buckets):
                baker.make(
                    "core.PriceBucket",
                    idx=idx,
                    value=bucket["max_split_value"],
                    ref_value=bucket.get("ref_split_value", bucket["max_split_value"]),
                    tamanho=bucket["tamanho"],
                    expiration_days=bucket.get("expiration_days"),
                    price_manager=price_manager,
                )

        datetime_ida = dateutils.to_default_tz_required(datetime_as_str)

        tc = baker.make(
            "core.TrechoClasse",
            datetime_ida=datetime_ida,
            trecho_vendido=trecho_vendido,
            grupo_classe__capacidade=25,
            max_split_value=max_split_value,
            duracao_ida=duracao_ida,
            grupo__status=status,
            grupo__rota_id=trecho_vendido.rota_id,
            grupo__datetime_ida=datetime_ida,
            grupo_classe__tipo_assento=tipo_assento,
            grupo__company=company,
            grupo__modelo_venda=modelo_venda,
            grupo__modelo_operacao=modelo_operacao,
            vagas=vagas,
            price_manager=price_manager,
        )
        return tc

    return factory


def test_itinerario_cache(django_assert_num_queries):
    rota = baker.make("core.Rota")
    baker.make("core.Checkpoint", rota=rota, _quantity=3)

    with django_assert_num_queries(1):
        itinerario = itinerario_svc.get_itinerario_data([rota.id])
        assert len(itinerario[rota.id]) == 3

    with django_assert_num_queries(0):
        itinerario = itinerario_svc.get_itinerario_data([rota.id])


def test_pesquisa_por_origem_deve_nao_deve_retornar_grupos_antigos(
    client,
    rota_sp_rj,
    make_trecho_classe,
    sao_paulo,
):
    # Grupos antigos não devem ser exibidos no resultado.
    make_trecho_classe("2020-10-06 18:00")

    with mock_dateutils.patch_at(datetime_at=dateutils.to_default_tz_required("2021-05-11")):
        resp = client.get("/api/search/origem", {"origem": sao_paulo.slug})

    assert 200 == resp.status_code
    assert 0 == len(resp.json()["saindo_de"])


def test_pesquisa_por_destino_nao_deve_retornar_grupos_antigos(
    client,
    rota_sp_rj,
    make_trecho_classe,
    rio_de_janeiro,
):
    # Grupos antigos não devem ser exibidos no resultado.
    make_trecho_classe("2020-10-06 18:00")

    with mock_dateutils.patch_at(datetime_at=dateutils.to_default_tz_required("2021-05-11")):
        resp = client.get("/api/search/destino", {"destino": rio_de_janeiro.slug})

    assert 200 == resp.status_code
    assert 0 == len(resp.json()["indo_para"])


def test_search_sem_horarios_esgotados_5_abertos_1_fechado(make_trecho_classe, client, sao_paulo, rio_de_janeiro):
    _cria_n_grupos_abertos(make_trecho_classe, 5)
    _cria_n_grupos_fechados(make_trecho_classe, 2)
    _cria_n_grupos_abertos(make_trecho_classe, 4)
    _cria_n_grupos_fechados(make_trecho_classe, 1)
    _cria_n_grupos_abertos(make_trecho_classe, 2)

    _test_horarios_esgotados_n_resultados(client, sao_paulo, rio_de_janeiro, 11)


def test_search_sem_horarios_esgotados_5_abertos(make_trecho_classe, client, sao_paulo, rio_de_janeiro):
    _cria_n_grupos_abertos(make_trecho_classe, 5)
    _test_horarios_esgotados_n_resultados(client, sao_paulo, rio_de_janeiro, 5)


def test_search_sem_horarios_esgotados_6_fechados(make_trecho_classe, client, sao_paulo, rio_de_janeiro):
    _cria_n_grupos_fechados(make_trecho_classe, 6)
    _test_horarios_esgotados_n_resultados(client, sao_paulo, rio_de_janeiro, 0)


def test_search_sem_horarios_esgotados_6_abertos(make_trecho_classe, client, sao_paulo, rio_de_janeiro):
    _cria_n_grupos_abertos(make_trecho_classe, 6)
    _test_horarios_esgotados_n_resultados(client, sao_paulo, rio_de_janeiro, 6)


def test_search_sem_horarios_esgotados_2_abertos_3_fechados(make_trecho_classe, client, sao_paulo, rio_de_janeiro):
    _cria_n_grupos_abertos(make_trecho_classe, 2)
    _cria_n_grupos_fechados(make_trecho_classe, 3)
    _test_horarios_esgotados_n_resultados(client, sao_paulo, rio_de_janeiro, 2)


def test_search_sem_horarios_esgotados_3_abertos_3_fechados(make_trecho_classe, client, sao_paulo, rio_de_janeiro):
    _cria_n_grupos_abertos(make_trecho_classe, 3)
    _cria_n_grupos_fechados(make_trecho_classe, 3)

    _test_horarios_esgotados_n_resultados(client, sao_paulo, rio_de_janeiro, 3)


def _test_horarios_esgotados_n_resultados(client, sao_paulo, rio_de_janeiro, qtde_results=5):
    params = {
        "origemSlug": sao_paulo.slug,
        "destinoSlug": rio_de_janeiro.slug,
        "departureDate": "2021-03-13",
    }
    with mock_dateutils.patch_at(datetime_at=dateutils.to_default_tz_required("2021-03-13")):
        res = client.get("/api/search", params)
    assert len(res.json()["groups_by_date"][0]["grupos"]) == qtde_results


def _cria_n_grupos_abertos(make_trecho_classe, qtde_grupos):
    for i in range(0, qtde_grupos):
        make_trecho_classe(f"2021-03-13 {i + 1}:00")


def _cria_n_grupos_fechados(make_trecho_classe, qtde_grupos):
    for i in range(0, qtde_grupos):
        tc = make_trecho_classe(f"2021-03-13 {i + 1}:00")
        tc.vagas = 0
        tc.save()


def test_search_grupos_dia_seguinte(make_trecho_classe, rf, sao_paulo, rio_de_janeiro, time_machine):
    time_machine.move_to("2021-03-13 00:10:00")
    qtde_grupos_no_dia = 4
    qtde_grupos_pos_meia_noite = 2
    _cria_n_grupos_abertos(make_trecho_classe, qtde_grupos_no_dia)
    departure_date = "2021-03-13"
    next_day = "2021-03-14"
    make_trecho_classe(f"{next_day} 00:20")
    make_trecho_classe(f"{next_day} 01:20")
    params = {
        "origemSlug": sao_paulo.slug,
        "destinoSlug": rio_de_janeiro.slug,
        "departureDate": departure_date,
    }
    request = rf.get("/api/search", params)
    request.user = django_auth_models.AnonymousUser()
    res = sr_views.search(request)
    data = json.loads(res.content)
    assert len(data["groups_by_date"][0]["grupos"]) == qtde_grupos_no_dia
    assert len(data["groups_by_date"][1]["grupos"]) == qtde_grupos_pos_meia_noite


def test_nao_considera_grupos_dps_das_2(make_trecho_classe, rf, sao_paulo, rio_de_janeiro, time_machine):
    time_machine.move_to("2021-03-13 00:10:00")
    make_trecho_classe("2021-03-14 01:59")
    make_trecho_classe("2021-03-14 02:00")
    params = {
        "origemSlug": sao_paulo.slug,
        "destinoSlug": rio_de_janeiro.slug,
        "departureDate": "2021-03-13",
    }
    request = rf.get("/api/search", params)
    request.user = django_auth_models.AnonymousUser()
    res = sr_views.search(request)
    data = json.loads(res.content)
    assert len(data["groups_by_date"][0]["grupos"]) == 1


def test_search_serializer_query_count(make_trecho_classe, django_assert_num_queries):
    serializer = serializer_trecho_classe.SearchSerializer()

    make_trecho_classe("2020-10-06 18:00")
    trecho_classe = TrechoClasse.objects.to_serialize(serializer).first()
    serializer.prepare_objects([trecho_classe])
    with django_assert_num_queries(0):
        trecho_classe.serialize()


def test_search_sem_data_considera_vagas_do_trechoclasse(client, sao_paulo, rio_de_janeiro, make_trecho_classe):
    tomorrow = dateutils.now() + timedelta(days=1)
    tc = make_trecho_classe(tomorrow, tipo_assento="leito cama")
    tc.grupo_classe.pessoas = tc.grupo_classe.capacidade
    tc.grupo_classe.save()
    tc.vagas = 10
    tc.save()

    params = {
        "origemSlug": sao_paulo.slug,
        "destinoSlug": rio_de_janeiro.slug,
    }
    resp = client.get("/api/search", params)
    assert resp.json()["has_grupos"]


def test_search_with_destino_info(client, rio_de_janeiro, rio_de_janeiro_info, sao_paulo, rio_de_janeiro_thumb_image):
    params = {
        "origemSlug": sao_paulo.slug,
        "destinoSlug": rio_de_janeiro.slug,
    }
    resp = client.get("/api/search", params)
    data = resp.json()
    assert "info" in data["trecho"]["destino"]
    assert data["trecho"]["destino"]["info"]["descricao"] == rio_de_janeiro_info.descricao
    assert data["trecho"]["destino"]["info"]["sumario"] == rio_de_janeiro_info.sumario
    assert "trecho_images/rio-de-janeiro-rj.jpg" in data["trecho"]["destino"]["info"]["imagens"][0]["url"]


def test_search_por_data_com_destino_nao_deve_ter_info(
    client, rio_de_janeiro, rio_de_janeiro_info, sao_paulo, rio_de_janeiro_thumb_image
):
    params = {
        "origemSlug": sao_paulo.slug,
        "destinoSlug": rio_de_janeiro.slug,
        "departureDate": "2018-01-19",
    }
    resp = client.get("/api/search", params)
    data = resp.json()
    assert "info" not in data["trecho"]["destino"]


def test_search_destino_with_info(client, rio_de_janeiro, rio_de_janeiro_info):
    response = client.get("/api/search/destino", {"destino": rio_de_janeiro.slug, "with_extra_info": True})
    assert response.status_code == 200
    data = response.json()
    assert "info" in data
    assert data["info"] == {
        "descricao": rio_de_janeiro_info.descricao,
        "sumario": rio_de_janeiro_info.sumario,
        "pontos_turisticos": rio_de_janeiro_info.pontos_turisticos,
        "hospedagens": rio_de_janeiro_info.hospedagens,
        "restaurantes": rio_de_janeiro_info.restaurantes,
        "eventos": rio_de_janeiro_info.eventos,
        "imagens": [],
    }


def test_search_destino_without_info(rf, rio_de_janeiro, rio_de_janeiro_info):
    request = rf.get(
        "/api/search/destino",
        {"destino": rio_de_janeiro.slug, "with_extra_info": False},
    )
    response = sr_views.search_destino(request)
    assert HTTPStatus.OK == response.status_code
    data = json.loads(response.content)
    assert "info" not in data


def test_search_com_parent_company_info(rf, make_trecho_classe, sao_paulo, rio_de_janeiro):
    user = django_auth_models.AnonymousUser()
    params = {
        "origemSlug": sao_paulo.slug,
        "destinoSlug": rio_de_janeiro.slug,
        "departureDate": "2021-03-13",
    }

    make_trecho_classe(
        "2021-03-13 1:00",
        modelo_venda=Grupo.ModeloVenda.HIBRIDO,
        include_company=True,
        include_parent_company=True,
    )
    with mock_dateutils.patch_at(datetime_at=dateutils.to_default_tz_required("2021-03-13")):
        request = rf.get("/api/search", params)
        request.user = user
        response = sr_views.search(request)
        response = json.loads(response.content)

    grupo = response["groups_by_date"][0]["grupos"][0]
    assert grupo["parent_company_name"]
    assert grupo["parent_company_logo_url"]


def test_search_com_modelo_operacao(rf, make_trecho_classe, sao_paulo, rio_de_janeiro):
    user = django_auth_models.AnonymousUser()
    params = {
        "origemSlug": sao_paulo.slug,
        "destinoSlug": rio_de_janeiro.slug,
        "departureDate": "2021-03-13",
    }

    make_trecho_classe(
        "2021-03-13 1:00",
        modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
        modelo_operacao=Grupo.ModeloOperacao.POLTRONAS_ANTECIPADAS,
        include_company=True,
        include_parent_company=True,
    )
    with mock_dateutils.patch_at(datetime_at=dateutils.to_default_tz_required("2021-03-13")):
        request = rf.get("/api/search", params)
        request.user = user
        response = sr_views.search(request)
        response = json.loads(response.content)

    grupo = response["groups_by_date"][0]["grupos"][0]
    assert grupo["modelo_operacao"] == Grupo.ModeloOperacao.POLTRONAS_ANTECIPADAS
    assert grupo["operacao_configs"] == {"modelo_atendimento": "buser"}


def test_search_com_rotina_integrada_no_hibrido_RodeRotas(
    rf, globalsettings_mock, make_trecho_classe, sao_paulo, rio_de_janeiro
):
    user = django_auth_models.AnonymousUser()
    params = {
        "origemSlug": sao_paulo.slug,
        "destinoSlug": rio_de_janeiro.slug,
        "departureDate": "2021-03-13",
    }

    tc = make_trecho_classe(
        "2021-03-13 1:00",
        modelo_venda=Grupo.ModeloVenda.HIBRIDO,
        include_company=True,
        include_parent_company=True,
    )
    empresa_lop = baker.make("core.Company", id=313, name="Empresa dona da LOP", logo_s3key="abc")
    tc.grupo.rotina_onibus = baker.make("core.RotinaOnibus")
    tc.grupo.save()
    globalsettings_mock("rotinas_hibrido_integradas_rode_rotas", [tc.grupo.rotina_onibus_id, 10])

    with mock_dateutils.patch_at(datetime_at=dateutils.to_default_tz_required("2021-03-13")):
        request = rf.get("/api/search", params)
        request.user = user
        response = sr_views.search(request)
        response = json.loads(response.content)

    grupo = response["groups_by_date"][0]["grupos"][0]
    assert grupo["parent_company_name"] is None
    assert grupo["parent_company_logo_url"] is None
    assert grupo["company_name"] == "Empresa dona da LOP"
    assert grupo["company_logo_url"] == empresa_lop.logo_url


@pytest.mark.parametrize(
    "max_split_value, parcelamento",
    [
        (D("100"), {"valor_por_parcela": 37.08, "quantidade_de_parcelas": 3}),
        (D("15"), {"valor_por_parcela": 5.56, "quantidade_de_parcelas": 3}),
        (D("13.99"), None),
    ],
)
def test_search_com_parcelamento_do_max_split_value(
    rf,
    make_trecho_classe,
    sao_paulo,
    rio_de_janeiro,
    max_split_value,
    parcelamento,
    time_machine,
):
    user = django_auth_models.AnonymousUser()
    params = {
        "origemSlug": sao_paulo.slug,
        "destinoSlug": rio_de_janeiro.slug,
        "departureDate": "2021-03-13",
    }

    time_machine.move_to("2021-03-12 1:00", tick=False)
    make_trecho_classe(
        "2021-03-13 1:00",
        modelo_venda=Grupo.ModeloVenda.BUSER,
        max_split_value=max_split_value,
    )
    request = rf.get("/api/search", params)
    request.user = user
    response = sr_views.search(request)
    response = json.loads(response.content)

    parcelamento_response = response["groups_by_date"][0]["grupos"][0]["parcelamento"]
    assert parcelamento_response == parcelamento


@pytest.mark.parametrize(
    "max_split_value, qtd_parcelas, expected",
    [
        (D("100"), 4, {"valor_por_parcela": D("27.84"), "quantidade_de_parcelas": 4}),
        (D("15"), 3, {"valor_por_parcela": D("5.56"), "quantidade_de_parcelas": 3}),
        (D("13.99"), 3, None),
    ],
)
def test_insert_parcelamento_from_value(max_split_value, qtd_parcelas, expected):
    assert serializer_trecho_classe.parcelamento_from_value(max_split_value, qtd_parcelas) == expected


@pytest.mark.parametrize("vagas, expected", [(20, 13), (10, 10)])
def test_limita_retorno_de_quantidade_de_assento_em_13_se_maior_que_13(
    rf, make_trecho_classe, sao_paulo, rio_de_janeiro, vagas, expected, time_machine
):
    user = django_auth_models.AnonymousUser()
    params = {
        "origemSlug": sao_paulo.slug,
        "destinoSlug": rio_de_janeiro.slug,
        "departureDate": "2021-03-13",
    }

    time_machine.move_to("2021-03-12 1:00", tick=False)
    make_trecho_classe("2021-03-13 1:00", modelo_venda=Grupo.ModeloVenda.BUSER, vagas=vagas)
    request = rf.get("/api/search", params)
    request.user = user
    response = sr_views.search(request)
    response = json.loads(response.content)

    vagas_response = response["groups_by_date"][0]["grupos"][0]["vagas"]
    assert vagas_response == expected


def test_search_origem_bucketizado_pega_valor_do_price_manager(rf, make_trecho_classe, sao_paulo, rio_de_janeiro):
    tomorrow = dateutils.now() + timedelta(days=1)
    price_manager = baker.make(
        "core.PriceManager",
        value=D("21.20"),
        min_pessoas=0,
        max_pessoas=5,
        expiration_days=7,
    )
    tc = make_trecho_classe(tomorrow, tipo_assento="leito cama", max_split_value=D("90.00"))
    tc.price_manager = price_manager
    tc.save(update_fields=["price_manager"])

    request = rf.get("/api/search/origem", {"origem": sao_paulo.slug, "with_extra_info": False})
    response = sr_views.search_origem(request)
    assert HTTPStatus.OK == response.status_code
    data = json.loads(response.content)
    assert data["saindo_de"][0]["best_price"] == 21.20


def test_search_origem_pega_o_valor_do_max_split_value(rf, make_trecho_classe, sao_paulo, rio_de_janeiro):
    tomorrow = dateutils.now() + timedelta(days=1)
    buckets = [
        {"max_split_value": D("21.20"), "tamanho": 5},
        {"max_split_value": D("45.90"), "tamanho": 3},
    ]
    make_trecho_classe(tomorrow, tipo_assento="leito cama", buckets=buckets, max_split_value=D("90.00"))

    request = rf.get(
        "/api/search/origem",
        {"origem": sao_paulo.slug, "with_extra_info": False, "promo": True},
    )
    response = sr_views.search_origem(request)
    assert HTTPStatus.OK == response.status_code
    data = json.loads(response.content)
    assert data["saindo_de"][0]["best_price"] == D("90.00")


@pytest.mark.parametrize(
    "max_split_value_1, max_split_value_2, max_split_value_3, valor_esperado",
    [
        (D("3.00"), D("2.00"), D("1.00"), D("1.00")),
        (D("2.00"), D("1.00"), D("3.00"), D("1.00")),
        (D("1.00"), D("3.00"), D("2.00"), D("1.00")),
    ],
)
def test_pesquisa_origem_destino_com_data_deve_retornar_menor_valor_com_varios_trechos(
    client,
    make_trecho_classe,
    rio_de_janeiro,
    sao_paulo,
    trecho_vendido_sp_rj,
    user,
    max_split_value_1,
    max_split_value_2,
    max_split_value_3,
    valor_esperado,
    time_machine,
):
    time_machine.move_to("2022-09-05 12:30:00")
    make_trecho_classe("2022-09-05 14:30:00", max_split_value=max_split_value_1)
    make_trecho_classe("2022-09-05 14:30:00", max_split_value=max_split_value_2)
    make_trecho_classe("2022-09-05 14:30:00", max_split_value=max_split_value_3)
    result_busca_com_datas = grupos_svc.search(
        dateutils.now() + timedelta(hours=1),
        sao_paulo.slug,
        rio_de_janeiro.slug,
        client=client,
        user=user,
    )
    groups_by_date = result_busca_com_datas["groups_by_date"]
    for dates in groups_by_date:
        for g in dates["grupos"]:
            if g["id"] == result_busca_com_datas["grupos_recomendados"]["menor_preco"]:
                assert g["max_split_value"] == valor_esperado


@pytest.mark.parametrize(
    "max_split_value_1, max_split_value_2, max_split_value_3, valor_esperado",
    [
        (D("3.00"), D("2.00"), D("1.00"), D("1.00")),
        (D("2.00"), D("1.00"), D("3.00"), D("1.00")),
        (D("1.00"), D("3.00"), D("2.00"), D("1.00")),
    ],
)
def test_pesquisa_origem_destino_sem_data_deve_retornar_menor_valor(
    client,
    make_trecho_classe,
    rio_de_janeiro,
    sao_paulo,
    trecho_vendido_sp_rj,
    user,
    max_split_value_1,
    max_split_value_2,
    max_split_value_3,
    valor_esperado,
    time_machine,
):
    time_machine.move_to("2022-09-05 10:30:00")
    make_trecho_classe("2022-09-05 14:30:00", max_split_value=max_split_value_1)
    make_trecho_classe("2022-09-06 14:30:00", max_split_value=max_split_value_2)
    make_trecho_classe("2022-09-17 14:30:00", max_split_value=max_split_value_3)
    result_busca_sem_datas = grupos_svc.search(None, sao_paulo.slug, rio_de_janeiro.slug, client=client, user=user)
    groups_by_date = result_busca_sem_datas["groups_by_date"]
    for dates in groups_by_date:
        for g in dates["grupos"]:
            if g["id"] == result_busca_sem_datas["grupos_recomendados"]["menor_preco"]:
                assert g["max_split_value"] == valor_esperado


def test_pesquisa_origem_destino_com_data_sem_grupos_deve_retornar_best_price_vazio(
    client, rio_de_janeiro, sao_paulo, trecho_vendido_sp_rj, user
):
    result_busca_com_datas = grupos_svc.search(
        dateutils.now() + timedelta(hours=1),
        sao_paulo.slug,
        rio_de_janeiro.slug,
        client=client,
        user=user,
    )
    assert not result_busca_com_datas["grupos_recomendados"]


def test_cost_benefit_tem_que_retornar_grupo_com_trecho_pesquisado_e_nao_alternativo(
    client,
    make_trecho_classe,
    rio_de_janeiro,
    local_sp_2,
    trecho_vendido_sp1_rj,
    trecho_vendido_sp2_rj,
    user,
    globalsettings_mock,
    time_machine,
):
    time_machine.move_to("2022-09-05 12:30:00")
    globalsettings_mock("enable_ab_custo_beneficio", True)

    menor_valor_correto = D("2")
    make_trecho_classe(
        "2022-09-05 22:30:00",
        max_split_value=D("1"),
        trecho_vendido=trecho_vendido_sp1_rj,
    )
    make_trecho_classe(
        "2022-09-05 22:30:00",
        max_split_value=menor_valor_correto,
        trecho_vendido=trecho_vendido_sp2_rj,
    )
    make_trecho_classe(
        "2022-09-05 23:30:00",
        max_split_value=D("3"),
        trecho_vendido=trecho_vendido_sp2_rj,
    )

    result_busca_com_datas = grupos_svc.search(
        dateutils.now() + timedelta(hours=1),
        local_sp_2.slug,
        rio_de_janeiro.slug,
        client=client,
        user=user,
    )

    groups_by_date = result_busca_com_datas["groups_by_date"]
    recomendados = result_busca_com_datas["grupos_recomendados"]
    recomendado = next(
        (g for g in groups_by_date[0]["grupos"] if g["id"] == recomendados["custo_beneficio"]),
        None,
    )

    assert recomendado is not None
    assert recomendado["trecho_alternativo"] is False
    assert recomendado["max_split_value"] == menor_valor_correto


def test_best_price_tem_que_retornar_grupo_com_trecho_pesquisado_e_nao_alternativo(
    client,
    make_trecho_classe,
    rio_de_janeiro,
    local_sp_2,
    trecho_vendido_sp1_rj,
    trecho_vendido_sp2_rj,
    user,
    time_machine,
):
    time_machine.move_to("2022-09-05 12:30:00")
    menor_valor_correto = D("2")
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("1"),
        trecho_vendido=trecho_vendido_sp1_rj,
    )
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=menor_valor_correto,
        trecho_vendido=trecho_vendido_sp2_rj,
    )
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("3"),
        trecho_vendido=trecho_vendido_sp2_rj,
    )

    result_busca_com_datas = grupos_svc.search(
        dateutils.now() + timedelta(hours=1),
        local_sp_2.slug,
        rio_de_janeiro.slug,
        client=client,
        user=user,
    )

    groups_by_date = result_busca_com_datas["groups_by_date"]
    recomendados = result_busca_com_datas["grupos_recomendados"]
    recomendado = next(
        (g for g in groups_by_date[0]["grupos"] if g["id"] == recomendados["menor_preco"]),
        None,
    )

    assert recomendado is not None
    assert recomendado["trecho_alternativo"] is False
    assert recomendado["max_split_value"] == menor_valor_correto


def test_search_origem_destino_by_week_day_domingo(
    client,
    make_trecho_classe,
    rio_de_janeiro,
    sao_paulo,
    trecho_vendido_sp_rj,
    user,
    time_machine,
):
    time_machine.move_to("2022-11-23 12:30:00")
    make_trecho_classe("2022-11-26 14:30:00", trecho_vendido=trecho_vendido_sp_rj)
    sundays = ["2022-11-27", "2022-12-04"]
    for date in sundays:
        make_trecho_classe(f"{date} 14:30:00", trecho_vendido=trecho_vendido_sp_rj)

    result_busca_sem_datas = grupos_svc.search(
        None,
        sao_paulo.slug,
        rio_de_janeiro.slug,
        client=client,
        user=user,
        week_day="domingo",
    )
    groups_by_date = result_busca_sem_datas["groups_by_date"]
    assert sundays == [date["datetime_ida"] for date in groups_by_date]


def test_search_origem_destino_by_week_day_hoje(
    client,
    make_trecho_classe,
    rio_de_janeiro,
    sao_paulo,
    trecho_vendido_sp_rj,
    user,
    time_machine,
):
    time_machine.move_to("2022-11-23 12:30:00")
    make_trecho_classe("2022-11-23 14:30:00", trecho_vendido=trecho_vendido_sp_rj)
    make_trecho_classe("2022-11-22 14:30:00", trecho_vendido=trecho_vendido_sp_rj)
    make_trecho_classe("2022-11-24 14:30:00", trecho_vendido=trecho_vendido_sp_rj)

    result_busca_sem_datas = grupos_svc.search(
        None,
        sao_paulo.slug,
        rio_de_janeiro.slug,
        client=client,
        user=user,
        week_day="hoje",
    )
    groups_by_date = result_busca_sem_datas["groups_by_date"]
    dates = [date["datetime_ida"] for date in groups_by_date]
    assert ["2022-11-23"] == dates


def test_search_origem_destino_by_week_day_amanha(
    client,
    make_trecho_classe,
    rio_de_janeiro,
    sao_paulo,
    trecho_vendido_sp_rj,
    user,
    time_machine,
):
    time_machine.move_to("2022-11-23 12:30:00")
    make_trecho_classe("2022-11-23 14:30:00", trecho_vendido=trecho_vendido_sp_rj)
    make_trecho_classe("2022-11-22 14:30:00", trecho_vendido=trecho_vendido_sp_rj)
    make_trecho_classe("2022-11-24 14:30:00", trecho_vendido=trecho_vendido_sp_rj)

    result_busca_sem_datas = grupos_svc.search(
        None,
        sao_paulo.slug,
        rio_de_janeiro.slug,
        client=client,
        user=user,
        week_day="amanha",
    )
    groups_by_date = result_busca_sem_datas["groups_by_date"]
    dates = [date["datetime_ida"] for date in groups_by_date]
    assert ["2022-11-24"] == dates


def test_search_origem_destino_by_invalid_week_day(
    client,
    make_trecho_classe,
    rio_de_janeiro,
    sao_paulo,
    trecho_vendido_sp_rj,
    user,
    time_machine,
):
    time_machine.move_to("2022-11-23 12:30:00")
    make_trecho_classe("2022-11-26 14:30:00", trecho_vendido=trecho_vendido_sp_rj)

    result_busca_sem_datas = grupos_svc.search(
        None,
        sao_paulo.slug,
        rio_de_janeiro.slug,
        client=client,
        user=user,
        week_day="sexta",
    )
    groups_by_date = result_busca_sem_datas["groups_by_date"]
    assert groups_by_date == []


def test_endpoint_price_range_by_date_exists():
    match = resolve("/api/price_range")
    assert match._func_path == "search_result.views.price_range_by_date"


def test_endpoint_price_range_by_date_is_turned_off_by_globalsetting(
    rf, globalsettings_mock, make_trecho_classe, sao_paulo, rio_de_janeiro, time_machine
):
    time_machine.move_to("2022-09-05 10:30:00")
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("50.00"),
        include_price_manager=True,
        price_manager_value=D("10.00"),
    )
    dates = "2022-09-05"
    request = rf.get(
        "/api/price_range",
        {
            "origemSlug": sao_paulo.slug,
            "destinoSlug": rio_de_janeiro.slug,
            "dates": dates,
        },
    )
    request.user = django_auth_models.AnonymousUser()
    globalsettings_mock("enable_price_range_endpoint", False)
    response = sr_views.price_range_by_date(request)
    response = json.loads(response.content)

    assert response == {}


def test_finds_max_min_price_for_a_date(rf, make_trecho_classe, rio_de_janeiro, sao_paulo, time_machine):
    time_machine.move_to("2022-09-05 10:30:00")
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("50.00"),
        include_price_manager=True,
        price_manager_value=D("10.00"),
    )
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("30.00"),
        include_price_manager=True,
        price_manager_value=D("20.00"),
    )
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("15.00"),
        include_price_manager=True,
        price_manager_value=D("15.00"),
    )

    dates = "2022-09-05"
    request = rf.get(
        "/api/price_range",
        {
            "origemSlug": sao_paulo.slug,
            "destinoSlug": rio_de_janeiro.slug,
            "dates": dates,
        },
    )
    request.user = django_auth_models.AnonymousUser()
    response = sr_views.price_range_by_date(request)
    response = json.loads(response.content)

    assert response["2022-09-05"]["min_price"] == D("10.00")
    assert response["2022-09-05"]["max_price"] == D("20.00")


def test_price_range_nao_retorna_datas_nao_solicitadas(rf, make_trecho_classe, rio_de_janeiro, sao_paulo, time_machine):
    time_machine.move_to("2022-09-05 10:30:00")
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("50.00"),
        include_price_manager=True,
        price_manager_value=D("10.00"),
    )
    make_trecho_classe(
        "2022-09-06 14:30:00",
        max_split_value=D("50.00"),
        include_price_manager=True,
        price_manager_value=D("10.00"),
    )

    dates = "2022-09-05"
    request = rf.get(
        "/api/price_range",
        {
            "origemSlug": sao_paulo.slug,
            "destinoSlug": rio_de_janeiro.slug,
            "dates": dates,
        },
    )
    request.user = django_auth_models.AnonymousUser()
    response = sr_views.price_range_by_date(request)
    response = json.loads(response.content)

    with pytest.raises(KeyError):
        response["2022-09-06"]


def test_price_range_com_data_que_nao_tem_viagem(rf, make_trecho_classe, rio_de_janeiro, sao_paulo, time_machine):
    time_machine.move_to("2022-09-05 10:30:00")
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("50.00"),
        include_price_manager=True,
        price_manager_value=D("10.00"),
    )

    dates = "2022-09-05,2022-09-06"
    request = rf.get(
        "/api/price_range",
        {
            "origemSlug": sao_paulo.slug,
            "destinoSlug": rio_de_janeiro.slug,
            "dates": dates,
        },
    )
    request.user = django_auth_models.AnonymousUser()
    response = sr_views.price_range_by_date(request)
    response = json.loads(response.content)

    expected_response = {
        "2022-09-05": {
            "min_price": D("10.00"),
            "max_price": D("10.00"),
        }
    }

    assert response == expected_response


def test_finds_max_min_price_for_a_date_com_tc_sem_price_manager(
    rf, make_trecho_classe, rio_de_janeiro, sao_paulo, time_machine
):
    time_machine.move_to("2022-09-05 10:30:00")
    make_trecho_classe("2022-09-05 14:30:00", max_split_value=D("5.00"))
    make_trecho_classe("2022-09-05 14:30:00", max_split_value=D("50.00"))
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("30.00"),
        include_price_manager=True,
        price_manager_value=D("20.00"),
    )

    dates = "2022-09-05"
    request = rf.get(
        "/api/price_range",
        {
            "origemSlug": sao_paulo.slug,
            "destinoSlug": rio_de_janeiro.slug,
            "dates": dates,
        },
    )
    request.user = django_auth_models.AnonymousUser()
    response = sr_views.price_range_by_date(request)
    response = json.loads(response.content)

    assert response["2022-09-05"]["min_price"] == D("5.00")
    assert response["2022-09-05"]["max_price"] == D("50.00")


def test_finds_max_min_price_com_dependendo_do_use_price_manager(
    rf, globalsettings_mock, make_trecho_classe, rio_de_janeiro, sao_paulo, time_machine
):
    time_machine.move_to("2022-09-05 10:30:00")
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("50.00"),
        include_price_manager=True,
        price_manager_value=D("10.00"),
    )
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("30.00"),
        include_price_manager=True,
        price_manager_value=D("20.00"),
    )
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("15.00"),
        include_price_manager=True,
        price_manager_value=D("15.00"),
    )

    dates = "2022-09-05"
    request = rf.get(
        "/api/price_range",
        {
            "origemSlug": sao_paulo.slug,
            "destinoSlug": rio_de_janeiro.slug,
            "dates": dates,
        },
    )
    request.user = django_auth_models.AnonymousUser()
    globalsettings_mock("enable_price_range_endpoint", True)
    globalsettings_mock("use_price_manager", False)
    response = sr_views.price_range_by_date(request)
    response = json.loads(response.content)

    assert response["2022-09-05"]["min_price"] == D("15.00")
    assert response["2022-09-05"]["max_price"] == D("50.00")


def test_price_range_funciona_com_datas_nao_sequenciais(
    rf, make_trecho_classe, rio_de_janeiro, sao_paulo, time_machine
):
    time_machine.move_to("2022-09-05 10:30:00")
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("50.00"),
        include_price_manager=True,
        price_manager_value=D("10.00"),
    )
    make_trecho_classe(
        "2022-09-08 14:30:00",
        max_split_value=D("20.00"),
        include_price_manager=True,
        price_manager_value=D("20.00"),
    )
    make_trecho_classe(
        "2022-09-13 14:30:00",
        max_split_value=D("15.00"),
        include_price_manager=True,
        price_manager_value=D("15.00"),
    )

    dates = "2022-09-05,2022-09-08,2022-09-13"
    request = rf.get(
        "/api/price_range",
        {
            "origemSlug": sao_paulo.slug,
            "destinoSlug": rio_de_janeiro.slug,
            "dates": dates,
        },
    )
    request.user = django_auth_models.AnonymousUser()
    response = sr_views.price_range_by_date(request)
    response = json.loads(response.content)

    expected_response = {
        "2022-09-05": {
            "min_price": D("10.00"),
            "max_price": D("10.00"),
        },
        "2022-09-08": {
            "min_price": D("20.00"),
            "max_price": D("20.00"),
        },
        "2022-09-13": {
            "min_price": D("15.00"),
            "max_price": D("15.00"),
        },
    }

    assert response == expected_response


def test_price_range_funciona_com_datas_em_meses_diferentes(
    rf, make_trecho_classe, rio_de_janeiro, sao_paulo, time_machine
):
    time_machine.move_to("2022-11-30 10:30:00")
    make_trecho_classe(
        "2022-11-30 14:30:00",
        max_split_value=D("50.00"),
        include_price_manager=True,
        price_manager_value=D("10.00"),
    )
    make_trecho_classe(
        "2022-12-01 14:30:00",
        max_split_value=D("20.00"),
        include_price_manager=True,
        price_manager_value=D("20.00"),
    )

    dates = "2022-11-30,2022-12-01"
    request = rf.get(
        "/api/price_range",
        {
            "origemSlug": sao_paulo.slug,
            "destinoSlug": rio_de_janeiro.slug,
            "dates": dates,
        },
    )
    request.user = django_auth_models.AnonymousUser()
    response = sr_views.price_range_by_date(request)
    response = json.loads(response.content)

    expected_response = {
        "2022-11-30": {
            "min_price": D("10.00"),
            "max_price": D("10.00"),
        },
        "2022-12-01": {
            "min_price": D("20.00"),
            "max_price": D("20.00"),
        },
    }

    assert response == expected_response


def test_price_range_validation_error_para_datas_no_passado(
    rf, make_trecho_classe, rio_de_janeiro, sao_paulo, time_machine
):
    """
    Se o endpoint pesquisa uma data anterior à data atual ele não deve retornar nada
    """
    time_machine.move_to("2022-09-05 10:30:00")
    dates = "2022-09-04"
    request = rf.get(
        "/api/price_range",
        {
            "origemSlug": sao_paulo.slug,
            "destinoSlug": rio_de_janeiro.slug,
            "dates": dates,
        },
    )
    request.user = django_auth_models.AnonymousUser()
    with pytest.raises(ValidationError):
        sr_views.price_range_by_date(request)


def test_price_range_by_date_queries(
    rf,
    make_trecho_classe,
    rio_de_janeiro,
    sao_paulo,
    django_assert_num_queries,
    time_machine,
):
    time_machine.move_to("2022-09-05 10:30:00")
    make_trecho_classe("2022-09-05 14:30:00", max_split_value=D("10.00"))

    dates = "2022-09-05"
    with django_assert_num_queries(6):
        request = rf.get(
            "/api/price_range",
            {
                "origemSlug": sao_paulo.slug,
                "destinoSlug": rio_de_janeiro.slug,
                "dates": dates,
            },
        )
        request.user = django_auth_models.AnonymousUser()
        sr_views.price_range_by_date(request)


def test_price_range_retornar_marketplace_sem_vagas(rf, make_trecho_classe, rio_de_janeiro, sao_paulo, time_machine):
    time_machine.move_to("2022-09-05 10:30:00")
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("5.00"),
        include_price_manager=True,
        price_manager_value=D("5.00"),
        vagas=0,
        modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
    )
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("5.00"),
        include_price_manager=True,
        price_manager_value=D("500.00"),
    )

    dates = "2022-09-05"
    request = rf.get(
        "/api/price_range",
        {
            "origemSlug": sao_paulo.slug,
            "destinoSlug": rio_de_janeiro.slug,
            "dates": dates,
        },
    )
    request.user = django_auth_models.AnonymousUser()
    response = sr_views.price_range_by_date(request)
    response = json.loads(response.content)

    assert response["2022-09-05"]["min_price"] == D("5.00")
    assert response["2022-09-05"]["max_price"] == D("500.00")


def test_price_range_by_date_ignora_grupo_fechado(rf, make_trecho_classe, rio_de_janeiro, sao_paulo, time_machine):
    time_machine.move_to("2022-09-05 10:30:00")
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("5.00"),
        include_price_manager=True,
        price_manager_value=D("5.00"),
        status="closed",
    )
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("5.00"),
        include_price_manager=True,
        price_manager_value=D("500.00"),
    )

    dates = "2022-09-05"
    request = rf.get(
        "/api/price_range",
        {
            "origemSlug": sao_paulo.slug,
            "destinoSlug": rio_de_janeiro.slug,
            "dates": dates,
        },
    )
    request.user = django_auth_models.AnonymousUser()
    response = sr_views.price_range_by_date(request)
    response = json.loads(response.content)

    assert response["2022-09-05"]["min_price"] == D("500.00")
    assert response["2022-09-05"]["max_price"] == D("500.00")


def test_price_range_by_date_ignora_grupo_sem_vagas(rf, make_trecho_classe, rio_de_janeiro, sao_paulo, time_machine):
    time_machine.move_to("2022-09-05 10:30:00")
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("5.00"),
        include_price_manager=True,
        price_manager_value=D("5.00"),
        vagas=0,
    )
    make_trecho_classe(
        "2022-09-05 14:30:00",
        max_split_value=D("5.00"),
        include_price_manager=True,
        price_manager_value=D("500.00"),
    )

    dates = "2022-09-05"
    request = rf.get(
        "/api/price_range",
        {
            "origemSlug": sao_paulo.slug,
            "destinoSlug": rio_de_janeiro.slug,
            "dates": dates,
        },
    )
    request.user = django_auth_models.AnonymousUser()
    response = sr_views.price_range_by_date(request)
    response = json.loads(response.content)

    assert response["2022-09-05"]["min_price"] == D("500.00")
    assert response["2022-09-05"]["max_price"] == D("500.00")


def test_price_range_by_date_data_invalida(rf, rio_de_janeiro, sao_paulo, time_machine):
    time_machine.move_to("2022-09-05 10:30:00")
    invalid_date = "lindadata"
    request = rf.get(
        "/api/price_range",
        {
            "origemSlug": sao_paulo.slug,
            "destinoSlug": rio_de_janeiro.slug,
            "dates": invalid_date,
        },
    )
    request.user = django_auth_models.AnonymousUser()
    with pytest.raises(ValidationError):
        sr_views.price_range_by_date(request)


@pytest.mark.parametrize(
    "adapter,modelo",
    [
        pytest.param(CidadeAdapter, "core.Cidade"),
        pytest.param(LocalEmbarqueAdapter, "core.LocalEmbarque"),
    ],
)
@override_settings(THUMBOR_SERVER="http://localhost:9000", THUMBOR_MEDIA_URL="buser-django:8000/media")
def test_generate_destino_picture_url(globalsettings_mock, adapter, modelo):
    image = "trecho_images/sao_paulo-sp.jpg"
    if modelo == "core.Cidade":
        destino = baker.make(modelo, image=image)
    else:
        destino = baker.make(modelo, cidade__image=image)
    destino_adapter = adapter(destino)
    picture_url = grupos_svc._get_destino_picture_url(destino_adapter)

    assert "http://localhost:9000" in picture_url
    assert "/80x80/" in picture_url
    assert image in picture_url


@override_settings(THUMBOR_SERVER="http://localhost:9000", THUMBOR_MEDIA_URL="buser-django:8000/media")
def test_search_top_rotas_by_origin_with_provided_slug(
    rf, globalsettings_mock, make_trecho_classe, sao_paulo, rio_de_janeiro
):
    make_trecho_classe(dateutils.now() + timedelta(days=3))

    request = rf.get("/api/geo/search_top_rotas_by_origem", {"origem": sao_paulo.slug, "limit": 3})
    response = sr_views.search_top_rotas_by_origin(request)
    result_saindo_de = json.loads(response.content)
    cidade_destino = result_saindo_de["saindo_de"][0]

    assert result_saindo_de["slug"] == sao_paulo.slug
    assert cidade_destino["slug"] == rio_de_janeiro.slug
    assert "/368x184/" in cidade_destino["picture_url"]
    assert rio_de_janeiro.image.name in cidade_destino["picture_url"]


def test_search_top_rotas_by_origin_validation_error(rf, globalsettings_mock):
    request = rf.get("/api/geo/search_top_rotas_by_origem", {"limit": "um"})

    response = sr_views.search_top_rotas_by_origin(request)

    response_dict = json.loads(response.content)

    assert "error" in response_dict
    assert response.status_code == 400


@override_settings(THUMBOR_SERVER="http://localhost:9000", THUMBOR_MEDIA_URL="buser-django:8000/media")
def test_search_top_rotas_by_origin_city_not_exists(
    rf,
    globalsettings_mock,
    make_trecho_classe,
    rio_de_janeiro,
    sao_paulo,
):
    make_trecho_classe(dateutils.now() + timedelta(days=3))

    request = rf.get(
        "/api/geo/search_top_rotas_by_origem",
        {"origem": "tangamandapio-sp", "limit": 3},
    )
    response = sr_views.search_top_rotas_by_origin(request)
    result_saindo_de = json.loads(response.content)
    cidade_destino = result_saindo_de["saindo_de"][0]

    assert result_saindo_de["slug"] == sao_paulo.slug
    assert cidade_destino["slug"] == rio_de_janeiro.slug
    assert "/368x184/" in cidade_destino["picture_url"]
    assert rio_de_janeiro.image.name in cidade_destino["picture_url"]


@override_settings(THUMBOR_SERVER="http://localhost:9000", THUMBOR_MEDIA_URL="buser-django:8000/media")
def test_search_top_rotas_by_origin_without_provided_slug(
    rf,
    globalsettings_mock,
    make_trecho_classe,
    rio_de_janeiro,
    sao_paulo,
    trecho_vendido_rj_sp,
):
    make_trecho_classe(dateutils.now() + timedelta(days=3), trecho_vendido=trecho_vendido_rj_sp)
    CLOUDFRONT_HEADERS = {
        "CloudFront-Viewer-City": "Rio de Janeiro",
        "CloudFront-Viewer-Country-Region-Name": "Rio de Janeiro",
        "CloudFront-Viewer-Country-Region": "RJ",
        "CloudFront-Viewer-Latitude": 26.357896,
        "CloudFront-Viewer-Longitude": 127.783809,
    }
    CLOUDFRONT_HEADERS_REQ = {f"HTTP_{header}": CLOUDFRONT_HEADERS[header] for header in CLOUDFRONT_HEADERS}

    request = rf.get("/api/geo/search_top_rotas_by_origem", **CLOUDFRONT_HEADERS_REQ)
    response = sr_views.search_top_rotas_by_origin(request)
    result_saindo_de = json.loads(response.content)
    cidade_destino = result_saindo_de["saindo_de"][0]

    assert result_saindo_de["slug"] == rio_de_janeiro.slug
    assert cidade_destino["slug"] == sao_paulo.slug
    assert "/368x184/" in cidade_destino["picture_url"]
    assert sao_paulo.image.name in cidade_destino["picture_url"]


def test_search_cidade_with_origin_and_destination(rf, rio_de_janeiro, sao_paulo):
    request = rf.get("api/search/places", {"origem": "rio-de-janeiro-rj", "destino": "sao-paulo-sp"})
    response = sr_views.search_places(request)

    result_cidades = json.loads(response.content)

    assert result_cidades["origem"]["slug"] == "rio-de-janeiro-rj"
    assert result_cidades["destino"]["slug"] == "sao-paulo-sp"


def test_search_cidade_with_origin_and_destination_with_alias(rf, rio_de_janeiro, sao_paulo):
    alias_origem = baker.make("core.AliasPlace", cidade=rio_de_janeiro, alias="buser")
    alias_destino = baker.make("core.AliasPlace", cidade=sao_paulo, alias="buser")
    request = rf.get("api/search/places", {"origem": alias_origem.slug, "destino": alias_destino.slug})
    response = sr_views.search_places(request)

    result_cidades = json.loads(response.content)

    assert result_cidades["origem"]["slug"] == "rio-de-janeiro-rj"
    assert result_cidades["destino"]["slug"] == "sao-paulo-sp"


def test_search_cidade_with_origin_only(rf, rio_de_janeiro):
    request = rf.get("api/search/places", {"origem": "rio-de-janeiro-rj"})
    response = sr_views.search_places(request)

    result_cidades = json.loads(response.content)

    assert result_cidades["origem"]["slug"] == "rio-de-janeiro-rj"
    assert result_cidades["destino"] is None


def test_search_inexistent_cidade(rf):
    request = rf.get("api/search/places", {"origem": "tangamandapio-rj"})
    response = sr_views.search_places(request)

    result_cidades = json.loads(response.content)

    assert result_cidades["origem"] is None


@pytest.mark.parametrize(
    "slug_origem, slug_destino",
    [
        ("sao-paulo-sp", "rio-de-janeiro-rj"),
        ("local-sao-paulo", "local-rio-de-janeiro"),
    ],
)
def test_search_returns_corresponding_locals(
    rf,
    make_trecho_classe,
    sao_paulo,
    rio_de_janeiro,
    time_machine,
    slug_origem,
    slug_destino,
):
    time_machine.move_to("2022-11-23 12:30:00")
    local_sp = baker.make("core.LocalEmbarque", cidade=sao_paulo, slug="local-sao-paulo")
    local_rj = baker.make("core.LocalEmbarque", cidade=rio_de_janeiro, slug="local-rio-de-janeiro")
    rota_sp_rj = baker.make("core.Rota", origem=local_sp, destino=local_rj)
    itinerario = [
        baker.prepare("core.Checkpoint", local=local_sp, rota=rota_sp_rj, idx=0),
        baker.prepare("core.Checkpoint", local=local_rj, rota=rota_sp_rj, idx=1),
    ]
    rota_sp_rj.itinerario.bulk_create(itinerario)
    trecho_vendido_sp_rj = baker.make("core.TrechoVendido", rota=rota_sp_rj, origem=local_sp, destino=local_rj)

    make_trecho_classe("2022-11-26 14:30:00", trecho_vendido=trecho_vendido_sp_rj)
    params = {
        "origemSlug": slug_origem,
        "destinoSlug": slug_destino,
        "departureDate": "2022-11-26",
    }
    request = rf.get("/api/search", params)
    request.user = django_auth_models.AnonymousUser()
    res = sr_views.search(request)
    data = json.loads(res.content)

    assert local_sp.id in data["locais_de_embarque"]
    assert local_rj.id in data["locais_de_desembarque"]


def test_with_groups_ranking_searchrank_api(
    globalsettings_mock,
    rf,
    make_trecho_classe,
    sao_paulo,
    rio_de_janeiro,
    time_machine,
    mocker,
):
    globalsettings_mock("enable_searchrank_api", True)
    time_machine.move_to("2022-11-23 12:30:00")
    local_sp = baker.make("core.LocalEmbarque", cidade=sao_paulo, slug="local-sao-paulo")
    local_rj = baker.make("core.LocalEmbarque", cidade=rio_de_janeiro, slug="local-rio-de-janeiro")
    rota_sp_rj = baker.make("core.Rota", origem=local_sp, destino=local_rj)
    itinerario = [
        baker.prepare("core.Checkpoint", local=local_sp, rota=rota_sp_rj, idx=0),
        baker.prepare("core.Checkpoint", local=local_rj, rota=rota_sp_rj, idx=1),
    ]
    rota_sp_rj.itinerario.bulk_create(itinerario)
    trecho_vendido_sp_rj, trecho_vendido_sp_rj_2 = baker.make(
        "core.TrechoVendido",
        rota=rota_sp_rj,
        origem=local_sp,
        destino=local_rj,
        _quantity=2,
    )
    make_trecho_classe("2022-11-26 14:30:00", trecho_vendido=trecho_vendido_sp_rj)
    make_trecho_classe("2022-11-26 14:30:00", trecho_vendido=trecho_vendido_sp_rj_2)

    mocker.patch(
        "search_result.service.grupos_svc.recommendation_svc.get_recommendations_searchrank_api",
        return_value=[23.123131, 24.41241],
    )
    mocker.patch("core.service.amplitude_svc.get_amplitude_device_id", return_value="12123133")
    params = {
        "origemSlug": local_sp.slug,
        "destinoSlug": local_rj.slug,
        "departureDate": "2022-11-26",
    }
    request = rf.get("/api/search", params)
    request.user = django_auth_models.AnonymousUser()
    res = sr_views.search(request)
    data = json.loads(res.content)
    grupos = data["groups_by_date"][0]["grupos"]
    for group in grupos:
        assert "searchrank_api" in group["ranking"]
        assert group["ranking"]["searchrank_api"] is not None
        assert group["ranking"]["searchrank_api"] > 0


def test_remove_grupos_mkt_duplicados(rf, make_trecho_classe, time_machine, trecho_vendido_sp_rj):
    time_machine.move_to("2022-09-05 11:30:00")
    make_trecho_classe("2022-09-05 14:30:00", modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    igual_com_mais_vagas = make_trecho_classe(
        "2022-09-05 14:30:00", modelo_venda=Grupo.ModeloVenda.MARKETPLACE, vagas=10
    )
    diferente_dos_iguais = make_trecho_classe(
        "2022-09-05 14:30:00", tipo_assento="executivo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE
    )
    params = {
        "origemSlug": trecho_vendido_sp_rj.origem.slug,
        "destinoSlug": trecho_vendido_sp_rj.destino.slug,
        "departureDate": "2022-09-05",
    }
    request = rf.get("/api/search", params)
    request.user = django_auth_models.AnonymousUser()

    res = sr_views.search(request)

    expected_ids = {hashint(igual_com_mais_vagas.id), hashint(diferente_dos_iguais.id)}
    data = json.loads(res.content)
    grupos = data["groups_by_date"][0]["grupos"]
    assert len(grupos) == 2
    assert all(g["id"] in expected_ids for g in grupos)


def test_search_with_alias(rf, make_trecho_classe, time_machine, trecho_vendido_sp_rj):
    alias_origem = baker.make("core.AliasPlace", cidade=trecho_vendido_sp_rj.origem.cidade, alias="buser")
    alias_destino = baker.make("core.AliasPlace", cidade=trecho_vendido_sp_rj.destino.cidade, alias="buser")
    time_machine.move_to("2022-09-05 11:30:00")
    make_trecho_classe(
        "2022-09-05 14:30:00",
    )

    make_trecho_classe("2022-09-05 14:30:00", tipo_assento="executivo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    params = {
        "origemSlug": alias_origem.slug,
        "destinoSlug": alias_destino.slug,
        "departureDate": "2022-09-05",
    }
    request = rf.get("/api/search", params)
    request.user = django_auth_models.AnonymousUser()

    res = sr_views.search(request)

    data = json.loads(res.content)
    grupos = data["groups_by_date"][0]["grupos"]
    assert len(grupos) == 2


def test_search_origem_with_alias(
    client,
    make_trecho_classe,
    sao_paulo,
    time_machine,
):
    alias = baker.make("core.AliasPlace", cidade=sao_paulo, alias="buser")
    time_machine.move_to("2025-04-06 11:00")
    make_trecho_classe("2025-04-06 18:00")

    resp = client.get("/api/search/origem", {"origem": alias.slug})

    assert 200 == resp.status_code
    assert 1 == len(resp.json()["saindo_de"])


def test_search_destino_with_alias(
    client,
    make_trecho_classe,
    time_machine,
    rio_de_janeiro,
):
    alias = baker.make("core.AliasPlace", cidade=rio_de_janeiro, alias="buser")
    time_machine.move_to("2025-04-06 11:00")
    make_trecho_classe("2025-04-06 18:00")
    resp = client.get("/api/search/destino", {"destino": alias.slug})

    assert 200 == resp.status_code
    assert 1 == len(resp.json()["indo_para"])
