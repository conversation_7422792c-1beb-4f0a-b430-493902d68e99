import json
from datetime import timedelta

import pytest
import time_machine
from model_bakery import baker
from pydantic.v1 import ValidationError

from commons.dateutils import now
from core.models_grupo import Grupo
from search_result.service.cidade_proxima_svc import find_trechos_classes_cidade_proxima, get_cidades_proximas
from search_result.service.grupos_svc import search_cidade_proxima_v2
from search_result.views import search_cidade_proxima


@pytest.fixture
def sao_paulo(db):
    return baker.make(
        "core.Cidade",
        name="São Paulo",
        uf="SP",
        slug="sao-paulo-sp",
        centroid="0101000020E610000060E5D022DB5147C0D50968226C8837C0",
    )


@pytest.fixture
def local_embarque_sp(sao_paulo):
    return baker.make("core.LocalEmbarque", cidade=sao_paulo, latitude=-23.53, longitude=-46.67)


@pytest.fixture
def osasco_local_embarque(db):
    city_osasco = baker.make(
        "core.Cidade",
        name="osasco",
        uf="SP",
        slug="osasco-sp",
        centroid="0101000020E6100000BEC11726536547C0F163CC5D4B8837C0",
    )
    return baker.make("core.LocalEmbarque", cidade=city_osasco, latitude=-23.5317, longitude=-46.7899)


@pytest.fixture
def cacapava_local_embarque(db):
    city_cpv = baker.make(
        "core.Cidade",
        name="Caçapava",
        uf="SP",
        slug="cacapava-sp",
        centroid="0101000020E6100000265305A392DA46C061C3D32B651937C0",
    )
    return baker.make("core.LocalEmbarque", cidade=city_cpv, latitude=-23.12, longitude=-45.73)


@pytest.fixture
def sao_jose_local_embarque(db, sao_jose):
    return baker.make("core.LocalEmbarque", cidade=sao_jose, latitude=-23.2, longitude=-45.91)


@pytest.fixture
def taubate_local_embarque(db):
    city_taubate = baker.make(
        "core.Cidade",
        name="Taubaté",
        uf="SP",
        slug="taubate-sp",
        centroid="0101000020E61000008F53742497C746C0DFE00B93A90237C0",
    )
    return baker.make("core.LocalEmbarque", cidade=city_taubate, latitude=-23.02, longitude=-45.55)


@time_machine.travel("2023-06-06 12:30:00", tick=False)
def test_retorna_cidade_proxima_origem_por_esta_melhor_no_ranking(
    osasco_local_embarque, local_embarque_sp, sao_jose_local_embarque, cacapava_local_embarque, rf
):
    ida = now() + timedelta(hours=1)

    tc_sp = _make_trecho_classe(local_embarque_sp, cacapava_local_embarque, ida)
    tc_sj = _make_trecho_classe(sao_jose_local_embarque, osasco_local_embarque, ida)
    _make_trecho_classe(local_embarque_sp, cacapava_local_embarque, ida)

    _make_ranking([(tc_sp, 5), (tc_sj, 2)])

    city_aleatoria = baker.make("core.Cidade", name="Aleatoria", uf="XX", slug="aleatoria-xx")
    le_aleatorio = baker.make("core.LocalEmbarque", cidade=city_aleatoria, latitude=-99.12, longitude=-99.73)

    _make_trecho_classe(cacapava_local_embarque, le_aleatorio, ida)
    _make_trecho_classe(osasco_local_embarque, le_aleatorio, ida)

    # sp vai ser a cidade proxima da origem, sao jose vai ser a cidade proxima do destino
    # sp será a recomendado por está mais alto no ranking

    payload = {"origem_slug": "osasco-sp", "destino_slug": "cacapava-sp", "date_search": ida.strftime("%Y-%m-%d")}
    request = rf.get("/api/search/cidade_proxima", payload)

    response = search_cidade_proxima(request)
    response = json.loads(response.content)

    assert response["origem"]["slug"] == "sao-paulo-sp"
    assert response["destino"]["slug"] == "cacapava-sp"


@time_machine.travel("2023-06-06 12:30:00", tick=False)
def test_retorna_cidade_proxima_mais_bem_ranqueada(
    osasco_local_embarque,
    local_embarque_sp,
    sao_jose_local_embarque,
    cacapava_local_embarque,
    rf,
    taubate_local_embarque,
):
    ida = now() + timedelta(hours=1)

    tc_sp = _make_trecho_classe(local_embarque_sp, cacapava_local_embarque, ida)
    tc_sj = _make_trecho_classe(sao_jose_local_embarque, osasco_local_embarque, ida)
    _make_trecho_classe(taubate_local_embarque, osasco_local_embarque, ida)
    _make_trecho_classe(osasco_local_embarque, sao_jose_local_embarque, ida)

    # sp vai ser a cidade proxima da origem, sao jose e taubate vão ser as cidades de cacapava
    # sao jose será a recomendado por está mais alto no ranking
    _make_ranking([(tc_sp, 2), (tc_sj, 5)])

    city_aleatoria = baker.make("core.Cidade", name="Aleatoria", uf="XX", slug="aleatoria-xx")
    le_aleatorio = baker.make("core.LocalEmbarque", cidade=city_aleatoria, latitude=-99.12, longitude=-99.73)

    _make_trecho_classe(cacapava_local_embarque, le_aleatorio, ida)
    _make_trecho_classe(osasco_local_embarque, le_aleatorio, ida)

    payload = {"origem_slug": "osasco-sp", "destino_slug": "cacapava-sp", "date_search": ida.strftime("%Y-%m-%d")}
    request = rf.get("/api/search/cidade_proxima", payload)

    response = search_cidade_proxima(request)
    response = json.loads(response.content)
    assert response["origem"]["slug"] == "osasco-sp"
    assert response["destino"]["slug"] == "sao-jose-dos-campos-sp"


@time_machine.travel("2023-06-06 12:30:00", tick=False)
def test_retorna_cidade_proxima_destino_por_esta_melhor_no_ranking(
    osasco_local_embarque, local_embarque_sp, sao_jose_local_embarque, cacapava_local_embarque, rf
):
    ida = now() + timedelta(hours=1)

    tc_sp = _make_trecho_classe(local_embarque_sp, cacapava_local_embarque, ida)
    tc_sj = _make_trecho_classe(sao_jose_local_embarque, osasco_local_embarque, ida)
    _make_trecho_classe(osasco_local_embarque, sao_jose_local_embarque, ida)

    # sp vai ser a cidade proxima da origem, sao jose vai ser a cidade proxima do destino
    # sao jose será a recomendado por está mais alto no ranking
    _make_ranking([(tc_sp, 2), (tc_sj, 5)])

    city_aleatoria = baker.make("core.Cidade", name="Aleatoria", uf="XX", slug="aleatoria-xx")
    le_aleatorio = baker.make("core.LocalEmbarque", cidade=city_aleatoria, latitude=-99.12, longitude=-99.73)

    _make_trecho_classe(cacapava_local_embarque, le_aleatorio, ida)
    _make_trecho_classe(osasco_local_embarque, le_aleatorio, ida)

    payload = {"origem_slug": "osasco-sp", "destino_slug": "cacapava-sp", "date_search": ida.strftime("%Y-%m-%d")}
    request = rf.get("/api/search/cidade_proxima", payload)

    response = search_cidade_proxima(request)
    response = json.loads(response.content)
    assert response["origem"]["slug"] == "osasco-sp"
    assert response["destino"]["slug"] == "sao-jose-dos-campos-sp"


@time_machine.travel("2023-06-06 12:30:00", tick=False)
def test_cidade_proxima_origem_sem_opcao_destino(
    rf,
    sao_jose_local_embarque,
    local_embarque_sp,
    cacapava_local_embarque,
    taubate_local_embarque,
):
    ida = now() + timedelta(hours=1)

    _make_trecho_classe(sao_jose_local_embarque, local_embarque_sp, ida)

    # esse aqui não pode ser recomendado
    _make_trecho_classe(taubate_local_embarque, local_embarque_sp, ida)

    city_aleatoria = baker.make("core.Cidade", name="Aleatoria", uf="XX", slug="aleatoria-xx")
    le_aleatorio = baker.make("core.LocalEmbarque", cidade=city_aleatoria, latitude=-99.12, longitude=-99.73)

    _make_trecho_classe(cacapava_local_embarque, le_aleatorio, ida)

    payload = {"origem_slug": "cacapava-sp", "destino_slug": "sao-paulo-sp", "date_search": ida.strftime("%Y-%m-%d")}
    request = rf.get("/api/search/cidade_proxima", payload)

    response = search_cidade_proxima(request)

    response = json.loads(response.content)

    assert response["origem"]["slug"] == "sao-jose-dos-campos-sp"
    assert response["destino"]["slug"] == "sao-paulo-sp"


@time_machine.travel("2023-06-06 12:30:00", tick=False)
def test_cidade_proxima_destino_sem_opcao_origem(
    rf,
    sao_jose_local_embarque,
    local_embarque_sp,
    cacapava_local_embarque,
    taubate_local_embarque,
    osasco_local_embarque,
):
    ida = now() + timedelta(hours=1)
    _make_trecho_classe(local_embarque_sp, cacapava_local_embarque, ida)

    # Taubate, esse aqui não pode ser recomendado por ser longe
    _make_trecho_classe(osasco_local_embarque, taubate_local_embarque, ida)
    _make_trecho_classe(cacapava_local_embarque, local_embarque_sp, ida)

    # Isso aqui serve pra "aparecer na searchbox", regra utilizada no _generate_locais_map
    city_aleatoria = baker.make("core.Cidade", name="Aleatoria", uf="XX", slug="aleatoria-xx")
    le_aleatorio = baker.make("core.LocalEmbarque", cidade=city_aleatoria, latitude=-99.12, longitude=-99.73)

    _make_trecho_classe(le_aleatorio, osasco_local_embarque, ida)

    payload = {"origem_slug": "cacapava-sp", "destino_slug": "osasco-sp", "date_search": ida.strftime("%Y-%m-%d")}
    request = rf.get("/api/search/cidade_proxima", payload)

    response = search_cidade_proxima(request)

    response = json.loads(response.content)

    assert response["origem"]["slug"] == "cacapava-sp"
    assert response["destino"]["slug"] == "sao-paulo-sp"


@pytest.mark.parametrize(
    "payload",
    [
        {"origem_slug": "cacapava-sp", "destino_slu": "osasco-sp"},
        {"orige_slug": "cacapava-sp", "destino_slug": "osasco-sp"},
    ],
)
def test_validation_error(rf, payload):
    request = rf.get("/api/search/cidade_proxima", payload)
    with pytest.raises(ValidationError):
        search_cidade_proxima(request)


# tests v2
@time_machine.travel("2023-06-06 12:30:00", tick=False)
def test_search_cidade_proxima_v2(
    rf,
    user,
    local_embarque_sp,
    cacapava_local_embarque,
    osasco_local_embarque,
    sao_jose_local_embarque,
    taubate_local_embarque,
):
    ida = now() + timedelta(hours=1)

    _make_trecho_classe(osasco_local_embarque, sao_jose_local_embarque, ida)
    _make_trecho_classe(local_embarque_sp, cacapava_local_embarque, ida)
    _make_trecho_classe(local_embarque_sp, taubate_local_embarque, ida)

    map_cidades_proximas = search_cidade_proxima_v2(
        origem_slug="sao-paulo-sp", destino_slug="sao-jose-dos-campos-sp", date_search=ida
    )

    osasco = map_cidades_proximas.get("osasco-sp")
    cacapava = map_cidades_proximas.get("cacapava-sp")
    taubate = map_cidades_proximas.get("taubate-sp")

    assert taubate is None
    assert cacapava is not None
    assert osasco is not None

    assert osasco["distance"] == 16
    assert osasco["cidade_proxima_type"] == "origem"
    assert cacapava["cidade_proxima_type"] == "destino"
    assert osasco["trechos_by_date"][ida.strftime("%Y-%m-%d")]["total_trechos"] == 1
    assert cacapava["trechos_by_date"][ida.strftime("%Y-%m-%d")]["total_trechos"] == 1


@time_machine.travel("2023-06-06 12:30:00", tick=False)
def test_cidade_proxima_proximos_15_dias_v2(
    rf,
    user,
    local_embarque_sp,
    cacapava_local_embarque,
    osasco_local_embarque,
    sao_jose_local_embarque,
    taubate_local_embarque,
):
    ida_1 = now() + timedelta(days=3)
    ida_2 = now() + timedelta(days=4)
    ida_3 = now() + timedelta(days=5)

    _make_trecho_classe(osasco_local_embarque, sao_jose_local_embarque, ida_1)
    _make_trecho_classe(local_embarque_sp, cacapava_local_embarque, ida_2)
    _make_trecho_classe(local_embarque_sp, taubate_local_embarque, ida_3)

    map_cidades_proximas = search_cidade_proxima_v2(
        origem_slug="sao-paulo-sp", destino_slug="sao-jose-dos-campos-sp", date_search=None
    )
    osasco = map_cidades_proximas.get("osasco-sp")
    cacapava = map_cidades_proximas.get("cacapava-sp")
    taubate = map_cidades_proximas.get("taubate-sp")

    assert taubate is None
    assert cacapava is not None
    assert osasco is not None
    assert osasco["distance"] == 16
    assert cacapava["distance"] == 21


@time_machine.travel("2023-06-06 12:30:00", tick=False)
def test_find_trechos_classes_cidade_proxima_v2(
    rf,
    local_embarque_sp,
    cacapava_local_embarque,
    osasco_local_embarque,
    sao_jose_local_embarque,
    taubate_local_embarque,
):
    ida = now() + timedelta(hours=1)
    # origem e destino orginais sao paulo e sao jose
    tc_osasco_sjc = _make_trecho_classe(osasco_local_embarque, sao_jose_local_embarque, ida)
    tc_sp_cacapava = _make_trecho_classe(local_embarque_sp, cacapava_local_embarque, ida)
    tc_sp_taubate = _make_trecho_classe(local_embarque_sp, taubate_local_embarque, ida)

    cidades_proximas_origem, cidades_proximas_destino, trechos_classes = find_trechos_classes_cidade_proxima(
        origem_slug="sao-paulo-sp", destino_slug="sao-jose-dos-campos-sp", date_ida=ida
    )

    assert cidades_proximas_origem[0].slug == "osasco-sp"
    assert cidades_proximas_destino[0].slug == "cacapava-sp"
    assert tc_osasco_sjc.id in trechos_classes
    assert tc_sp_cacapava.id in trechos_classes
    assert tc_sp_taubate.id not in trechos_classes  # muito distante


def test_cidade_proxima(sao_paulo, sao_jose, taubate_local_embarque, osasco_local_embarque, cacapava_local_embarque):
    assert get_cidades_proximas(sao_paulo, distance=40) == [osasco_local_embarque.cidade]

    # caçpava é mais próximo que taubate e a lista está ordenada pela menor distância
    assert get_cidades_proximas(sao_jose, distance=40) == [
        cacapava_local_embarque.cidade,
        taubate_local_embarque.cidade,
    ]


def _make_trecho_classe(origem, destino, ida):
    rota = baker.make("core.Rota", origem=origem, destino=destino, ativo=True)
    grupo = baker.make(
        "core.Grupo",
        status=Grupo.Status.TRAVEL_CONFIRMED,
        rota=rota,
        hidden_for_pax=False,
    )
    tv = baker.make("core.TrechoVendido", rota=rota, origem=origem, destino=destino)
    return baker.make(
        "core.TrechoClasse",
        grupo=grupo,
        trecho_vendido=tv,
        closed=False,
        datetime_ida=ida,
        max_split_value=10,
        vagas=5,
        grupo_classe__closed=False,
    )


def _make_ranking(tcs):
    for trecho_classe, num_travels in tcs:
        baker.make(
            "core.Travel",
            trecho_classe=trecho_classe,
            trecho_vendido=trecho_classe.trecho_vendido,
            grupo=trecho_classe.grupo,
            _quantity=num_travels,
        )


@time_machine.travel("2023-06-06 12:30:00", tick=False)
def test_search_cidade_proxima_v2_with_aliases(
    rf,
    user,
    local_embarque_sp,
    cacapava_local_embarque,
    osasco_local_embarque,
    sao_jose_local_embarque,
    taubate_local_embarque,
):
    ida = now() + timedelta(hours=1)
    alias_origem = baker.make("core.AliasPlace", alias="ceucinza", cidade=local_embarque_sp.cidade)
    alias_destino = baker.make("core.AliasPlace", alias="bairro-sao-jose", cidade=sao_jose_local_embarque.cidade)
    _make_trecho_classe(osasco_local_embarque, sao_jose_local_embarque, ida)
    _make_trecho_classe(local_embarque_sp, cacapava_local_embarque, ida)
    _make_trecho_classe(local_embarque_sp, taubate_local_embarque, ida)

    map_cidades_proximas = search_cidade_proxima_v2(
        origem_slug=alias_origem.slug, destino_slug=alias_destino.slug, date_search=ida
    )

    osasco = map_cidades_proximas.get("osasco-sp")
    cacapava = map_cidades_proximas.get("cacapava-sp")
    taubate = map_cidades_proximas.get("taubate-sp")

    assert taubate is None
    assert cacapava is not None
    assert osasco is not None

    assert osasco["distance"] == 16
    assert osasco["cidade_proxima_type"] == "origem"
    assert cacapava["cidade_proxima_type"] == "destino"
    assert osasco["trechos_by_date"][ida.strftime("%Y-%m-%d")]["total_trechos"] == 1
    assert cacapava["trechos_by_date"][ida.strftime("%Y-%m-%d")]["total_trechos"] == 1
