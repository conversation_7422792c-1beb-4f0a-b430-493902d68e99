from core.models_commons import <PERSON>as<PERSON>lace
from search_result.adapter.cidade_adapter import CidadeAdapter


class AliasPlaceAdapter(CidadeAdapter):
    def __init__(self, alias_place: AliasPlace, local=None, with_city_info=False):
        super().__init__(alias_place.cidade, local, with_city_info)
        self.alias_place = alias_place

    def __repr__(self):
        return f"AliasPlaceAdapter({self.alias_place.pk})"

    @property
    def label(self):
        return f"{self.alias_place.alias}, {self.cidade.name} - {self.cidade.uf}"
