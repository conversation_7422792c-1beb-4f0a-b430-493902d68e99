from collections import defaultdict
from datetime import timed<PERSON>ta
from enum import Enum

import geopy.distance
from django.contrib.gis.db.models.functions import Distance
from django.contrib.gis.measure import D
from django.db.models.query_utils import Q

from commons import dateutils
from commons.cacheback import cacheback
from commons.dateutils import now, to_default_tz, to_tz
from core.models_commons import Cidade
from core.models_grupo import Grupo, TrechoClasse, TrechoVendido
from core.models_rota import LocalEmbarque
from search_result.service.search_svc import (
    get_places_ranking,
    resolve_slugs,
)

SIX_HOURS = 6 * 60 * 60
TWELVE_HOURS = 12 * 60 * 60

DEFAULT_DISTANCE_KM = 25


class TipoSubstituicaoEnum(str, Enum):
    destino = "destino"
    origem = "origem"


def _find_nearby_by_local_embarque(local1, local2, local_map, distance_offset):
    latitudes = local_map.keys()
    lat_offset = 0.5
    data_ida = now()

    #  Considero o primeiro local de embarque pois dentro de uma cidade deveriam ser todos próximos relativamente
    central_point = local1[0]

    # Filtro as latitudes que estejam dentro do range da busca
    lat_search_range = [
        lat for lat in latitudes if central_point.latitude - lat_offset <= lat <= central_point.latitude + lat_offset
    ]

    # Calculo a distância entre local1 e local2, que no caso é a busca que não gerou resultados
    # e busco os locais de embarque que fiquem no maximo à distância de X km do local1
    # sendo X = distance_local1_local2 * distance_offset
    coord_local1 = (local1[0].latitude, local1[0].longitude)
    coord_local2 = (local2[0].latitude, local2[0].longitude)
    distance_local1_local2 = geopy.distance.geodesic(coord_local1, coord_local2).km
    max_distance_offset = distance_local1_local2 * distance_offset

    valid_localembarque = defaultdict(list)
    valid_localembarque_ids = []

    # Calculo a distância entre os locais de embarque dentro do range aceito, em relação ao local1
    for latitude in lat_search_range:
        for local in local_map[latitude]:
            if local.cidade_id == local1[0].cidade_id or local.cidade_id == local2[0].cidade_id:
                continue

            coord_local = (local.latitude, local.longitude)
            distance_local_local1 = geopy.distance.geodesic(coord_local, coord_local1).km

            if distance_local_local1 > max_distance_offset:
                continue

            valid_localembarque[distance_local_local1].append(local)
            valid_localembarque_ids.append(local.id)

    # Preciso ver se existem rotas ativas para esses locais de embarque em relação ao local2
    # O que eu tenho até aqui? Um dict de locais de embarque com a chave sendo a distancia entre eles e o local2
    # Porém, não sei ainda se existe uma rota ativa com grupos, deste local de embarque para o local2
    locais_com_grupo = set(
        TrechoClasse.objects.filter(
            trecho_vendido__destino__cidade_id=local2[0].cidade_id,
            trecho_vendido__origem_id__in=valid_localembarque_ids,
            trecho_vendido__rota__ativo=True,
            grupo__status__in=[Grupo.Status.PENDING, Grupo.Status.TRAVEL_CONFIRMED],
            datetime_ida__range=[
                to_default_tz(data_ida),
                to_default_tz(data_ida + timedelta(days=30)),
            ],
            closed=False,
        ).values_list("trecho_vendido__origem_id", flat=True)
    )

    cities = set()
    ranking = get_places_ranking()
    remaining_cities = 10  # quantidade de cidades que pretendo olhar o ranking

    DEFAULT_STEP = 10000

    # Aqui percorro o dict de locais de embarque pela distancia em ordem crescente
    # procuro ranking de cidades depois escolho a mais bem ranqueada e retorna

    for distance in sorted(valid_localembarque.keys()):
        if remaining_cities <= 0:
            break

        for le in valid_localembarque[distance]:
            if le.id not in locais_com_grupo:
                continue

            cidade_id = str(le.cidade.id)
            city_ranking = ranking.get(cidade_id, DEFAULT_STEP + distance)
            cities.add((le.cidade, city_ranking))
            remaining_cities -= 1

            if remaining_cities <= 0:
                break

    if not cities:
        return None

    cities = sorted(cities, key=lambda city: city[1])
    return cities[0][0]


@cacheback(TWELVE_HOURS)
def _generate_locais_map():
    origens = (
        LocalEmbarque.objects.select_related("cidade")
        .filter(ativo=True, trechos_vendidos_origem__rota__ativo=True)
        .distinct()
        .only(
            "id",
            "cidade_id",
            "cidade__slug",
            "latitude",
            "longitude",
        )
    )
    destinos = (
        LocalEmbarque.objects.select_related("cidade")
        .filter(ativo=True, trechos_vendidos_destino__rota__ativo=True)
        .distinct()
        .only(
            "id",
            "cidade_id",
            "cidade__slug",
            "latitude",
            "longitude",
        )
    )
    locais = list(origens.union(destinos))

    map_slug = defaultdict(list)
    map_coord = defaultdict(list)

    for local in locais:
        map_slug[local.cidade.slug].append(local)
        map_coord[local.latitude].append(local)

    map_coord = dict(map_coord.items())
    return map_slug, map_coord


def get_cidades_proximas(pivo, distance, limit=3):
    try:
        if not pivo.centroid:
            return []
    except AttributeError:
        return []

    return list(
        Cidade.objects.filter(
            centroid__dwithin=(pivo.centroid, D(km=distance)),
            ativo=True,
        )
        .exclude(pk=pivo.pk)
        .annotate(distance=Distance("centroid", pivo.centroid))
        .order_by("distance")[:limit]
    )


@cacheback(60 * 15)
def find_trechos_classes_cidade_proxima(origem_slug, destino_slug, date_ida, distance_offset=0.25):
    origem_slug, destino_slug = resolve_slugs(origem_slug, destino_slug)

    cidades = {
        cidade.slug: cidade
        for cidade in Cidade.objects.filter(slug__in=[origem_slug, destino_slug]).only("centroid", "slug", "pk")
    }

    if not cidades:
        return None, None, None
    _origem = cidades.get(origem_slug)
    _destino = cidades.get(destino_slug)

    if (_origem and _origem.centroid) and (_destino and _destino.centroid):
        distance = _origem.centroid.distance(_destino.centroid) * (100 * distance_offset)
    else:
        distance = DEFAULT_DISTANCE_KM

    cidades_proximas_origem = get_cidades_proximas(_origem, distance)
    cidades_proximas_destino = get_cidades_proximas(_destino, distance)

    try:
        timezone = _origem.timezone
    except Exception:
        timezone = None

    tcs_origem = []
    if _destino:
        tcs_origem = _find_trechos_classes(
            [cidade.pk for cidade in cidades_proximas_origem],
            [_destino.pk],
            date_ida,
            timezone,
        )
    tcs_destino = []
    if _origem:
        tcs_destino = _find_trechos_classes(
            [_origem.pk],
            [cidade.pk for cidade in cidades_proximas_destino],
            date_ida,
            timezone,
        )

    return cidades_proximas_origem, cidades_proximas_destino, tcs_origem + tcs_destino


def _date_lookup_trecho_classe(date_ida, timezone):
    local_date_ida = date_ida or dateutils.now()

    if not timezone:
        local_date_ida = to_default_tz(local_date_ida)
    else:
        local_date_ida = to_tz(local_date_ida, timezone)

    if date_ida:
        return Q(
            datetime_ida__range=(
                local_date_ida,
                local_date_ida.replace(hour=23, minute=59, second=59),
            )
        )

    return Q(datetime_ida__range=[local_date_ida, local_date_ida + timedelta(days=15)])


@cacheback(60 * 60 * 1)
def _find_trechos_classes(cidades_origem, cidades_destino, data_ida, timezone_origem):
    if not (cidades_origem and cidades_destino):
        return []

    date_lookup = _date_lookup_trecho_classe(data_ida, timezone_origem)

    tvs_ids = list(
        TrechoVendido.objects.filter(
            origem__cidade_id__in=cidades_origem,
            destino__cidade_id__in=cidades_destino,
            rota__ativo=True,
        ).values_list("id", flat=True)
    )

    return list(
        TrechoClasse.objects.filter(
            date_lookup,
            trecho_vendido_id__in=tvs_ids,
            grupo__status__in=[Grupo.Status.PENDING, Grupo.Status.TRAVEL_CONFIRMED],
            closed=False,
            vagas__gt=0,
            grupo_classe__closed=False,
            grupo__hidden_for_pax=False,
        ).values_list("pk", flat=True)
    )


@cacheback(SIX_HOURS)
def find_cidade_proxima(origem_slug, destino_slug, distance_offset):
    locais_slug, locais_coord = _generate_locais_map()
    le1 = locais_slug[origem_slug]
    le2 = locais_slug[destino_slug]

    if not le1 or not le2:
        return None, None

    cidade_proxima_para_origem = _find_nearby_by_local_embarque(le1, le2, locais_coord, distance_offset)
    cidade_proxima_para_destino = _find_nearby_by_local_embarque(le2, le1, locais_coord, distance_offset)

    tipo_substituicao = None
    cidade_proxima = None

    if cidade_proxima_para_origem and cidade_proxima_para_destino:
        cidade_proxima = _cidade_melhor_no_ranking(cidade_proxima_para_origem, cidade_proxima_para_destino)
    elif cidade_proxima_para_origem:
        cidade_proxima = cidade_proxima_para_origem
    elif cidade_proxima_para_destino:
        cidade_proxima = cidade_proxima_para_destino

    if cidade_proxima:
        tipo_substituicao = (
            TipoSubstituicaoEnum.origem
            if cidade_proxima_para_origem == cidade_proxima
            else TipoSubstituicaoEnum.destino
        )

    return cidade_proxima, tipo_substituicao


def _cidade_melhor_no_ranking(cidade_origem, cidade_destino):
    ranking = get_places_ranking()

    ranking_cidade_origem = ranking.get(str(cidade_origem.id))
    ranking_cidade_destino = ranking.get(str(cidade_destino.id))

    if ranking_cidade_destino and ranking_cidade_origem:
        if ranking_cidade_origem < ranking_cidade_destino:
            return cidade_origem
        else:
            return cidade_destino

    elif ranking_cidade_origem:
        return cidade_origem

    return cidade_destino
