"""
Search Service
==============

.. autofunction:: search_origem
.. autofunction:: search_destino
.. autofunction:: search_melhores_trechos
.. autofunction:: search_melhores_viagens_no_trecho
.. autofunction:: init
"""

from _operator import itemgetter
from collections import Counter
from datetime import timed<PERSON><PERSON>
from itertools import chain
from typing import cast

from beeline import traced
from django.contrib.postgres.aggregates import ArrayAgg
from django.core.cache import cache
from django.db.models import Avg, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, F, <PERSON>, <PERSON>, Prefetch, Q, QuerySet
from django.db.models import Value as V
from django.db.models.functions import Cast, Coalesce, Concat

from commons import dateutils
from commons.cacheback import cacheback
from commons.memoize import memoize
from commons.utils import binary_search
from core.models_commons import AliasPlace, Cidade, Imagem
from core.models_company import Company
from core.models_grupo import Grupo, TrechoClasse
from core.models_rota import LocalEmbarque
from core.models_travel import Travel, TravelFeedback
from search_result.adapter.alias_place_adapter import AliasPlaceAdapter
from search_result.adapter.cidade_adapter import CidadeAdapter
from search_result.adapter.local_embarque_adapter import LocalEmbarqueAdapter
from search_result.exceptions import LocalDoesNotExist
from search_result.serializer import serializer_cidade
from search_result.serializer.serializer_search_origem_ou_destino import (
    SearchOrigemDestinoCidadeSerializer,
)
from search_result.service import itinerario_svc, log_svc, ofertas_esgotadas_mc_svc
from search_result.service.search_base_svc import (
    SearchDestino,
    SearchMelhoresTrechos,
    SearchOrigem,
    SearchTrechosMes,
    get_n_melhor_trechos_ordered_by_key,
)
from search_result.utils.hashint import hashid_grupos

PLACES_RANKING_CACHE_KEY = "places_ranking_search_result"
DEFAULT_PLACE_RANKING = 10
DAYS_LIMIT = 14
_30_SECS = 30
ONE_MIN = 60 * 1
_15_MINS = 60 * 15
ONE_HOUR = 60 * 60
_12_HOURS = 60 * 60 * 12
ONE_DAY = 60 * 60 * 24
RANK_MAIS_VENDIDO = 1
TC_LIMIT_ENTRE_ORIGEM_E_DESTINO = 2000


@cacheback(_15_MINS)
def search_origem(
    origem_slug: str,
    limit: int | None = None,
    withitinerario: bool = False,
    withextrainfo: bool = False,
    bairro_slug: str | None = None,
    nickname_slug: str | None = None,
):
    """
    Busca trechos de destino a partir de uma origem, devolvendo os trechos ordenados a partir do mais vendido.
    Retorna juntamente com os trechos, o melhor preço para os próximos 14 dias, limitados 50 resultados por destino.


    Args:
        origem_slug (str): Slug de Cidade ou LocalEmbarque de origem.
        limit (int) = None: Quantidade máxima de trechos a serem devolvidos.
        withitinerario (bool) = False: Determina se deve incluir o itinerário no retorno da busca.
        withextrainfo (bool) = False: Determina se inclui os parâmetros
        wikipediainfo, picture_url e info na serialização das Cidades.
        bairro_slug: filtro extra para o local
        nickname_slug: filtro extra para o local

    Returns: (dict)
        origem_serializada: o destino informado, serializado
        saindo_de (list[dict]): lista dos destinos serializadas
        has_more (bool): indica se há mais resultados além do limite imposto, se existir
    """
    _now = dateutils.now()
    custom_filters = {
        "datetime_ida__range": (_now, _now + timedelta(days=DAYS_LIMIT)),
    }

    if bairro_slug:
        custom_filters["trecho_vendido__origem__bairro_slug"] = bairro_slug
    if nickname_slug:
        custom_filters["trecho_vendido__origem__nickname_slug"] = nickname_slug

    custom_annotations = {}
    custom_values = []
    if withitinerario:
        custom_annotations["rota_id"] = F("trecho_vendido__rota_id")
        custom_values.append("rota_id")

    search_unilateral_origem = SearchOrigem(
        origem_slug=origem_slug,
        extra_annotations=custom_annotations,
        extra_values=custom_values,
        extra_filters=custom_filters,
    )

    origem = search_unilateral_origem.local_origem
    destinos_info_map = search_unilateral_origem.get_citymap_from_trecho_classe_queryset()

    if withitinerario:
        destinos_info_map = _attach_trechos_itinerario(destinos_info_map)

    destinos_serializados, has_more = _serialize_cidade_results(
        destinos_info_map, results_limit=limit, serialize_with_extrainfo=withextrainfo
    )

    response = serializer_cidade.serialize(origem.cidade, withextrainfo=withextrainfo)
    response.update(**origem.as_dict())
    response["has_more"] = has_more
    response["saindo_de"] = destinos_serializados

    log_svc.log_search(origem=origem, type="origin_search")

    return response


@cacheback(ONE_HOUR)
def search_destino(
    destino_slug: str,
    limit: int = None,
    withitinerario: bool = False,
    withextrainfo: bool = False,
    bairro_slug: str | None = None,
    nickname_slug: str | None = None,
):
    """
    Busca trechos de origem a partir de um destino, devolvendo os trechos ordenados a partir do mais vendido.
    Retorna juntamente com os trechos, o melhor preço para os próximos 14 dias, limitados 50 resultados por origem.

    Args:
        destino_slug (str): Slug de Cidade ou LocalEmbarque de destino.
        limit (int) = None: Quantidade máxima de trechos a serem devolvidos.
        withitinerario (bool) = False: Determina se deve incluir o itinerário no retorno da busca.
        withextrainfo (bool) = False: Determina se inclui os parâmetros
        wikipediainfo, picture_url e info na serialização das Cidades.
        bairro_slug: filtro extra para o local
        nickname_slug: filtro extra para o local

    Returns: (dict)
        destino_serializado: o destino informado, serializado
        indo_para (list[dict]): lista das origens serializadas
        has_more (bool): indica se há mais resultados além do limite imposto, se existir
    """
    _now = dateutils.now()

    custom_filters = {
        "datetime_ida__range": (_now, _now + timedelta(days=DAYS_LIMIT)),
    }
    if bairro_slug:
        custom_filters["trecho_vendido__destino__bairro_slug"] = bairro_slug
    if nickname_slug:
        custom_filters["trecho_vendido__destino__nickname_slug"] = nickname_slug

    custom_annotations = {}
    custom_values = []
    if withitinerario:
        custom_annotations["rota_id"] = F("trecho_vendido__rota_id")
        custom_values.append("rota_id")

    search_unilateral_destino = SearchDestino(
        destino_slug=destino_slug,
        extra_annotations=custom_annotations,
        extra_values=custom_values,
        extra_filters=custom_filters,
    )

    destino = search_unilateral_destino.local_destino
    origens_info_map = search_unilateral_destino.get_citymap_from_trecho_classe_queryset()

    if withitinerario:
        origens_info_map = _attach_trechos_itinerario(origens_info_map)

    origens_serializadas, has_more = _serialize_cidade_results(
        origens_info_map, results_limit=limit, serialize_with_extrainfo=withextrainfo
    )
    serializer = SearchOrigemDestinoCidadeSerializer.with_picture_url()
    if withextrainfo:
        serializer = SearchOrigemDestinoCidadeSerializer.with_extra_info()

    response = next(serializer.serialize([destino.cidade]))
    response.update(**destino.as_dict())
    response["has_more"] = has_more
    response["indo_para"] = origens_serializadas

    log_svc.log_search(destino=destino, type="destination_search")

    return response


@cacheback(ONE_MIN)
def search_melhores_trechos(origem_slug, limit=4, rank=RANK_MAIS_VENDIDO):
    """
    Busca, a partir de uma origem, o destino mais vendido e os trechos mais baratos para aquele destino.
    Retornando assim a quantidade limit de trechos entre a origem e destino.

    Args:
        origem_slug: slug de uma Cidade ou LocalEmbarque
        limit: limite de trechos devolvidos para dada origem
        rank: inteiro ordinal representando a posição de mais vendido que é pra ser devolvida (ex: primeiro, segundo)

    Returns:
        destino: o melhor destino para a origem serializado, incluindo uma propriedade `melhores_trechos`,
        que contém a lista de de tamanho limit dos melhores trechos encontrados entre a origem e destino
    """
    trechos_list = get_lista_de_trechos_ordenada_por_travel_count_para_destino(origem_slug)

    n_trecho_mais_vendido = get_n_melhor_trechos_ordered_by_key(trechos=trechos_list, rank=rank)

    if not n_trecho_mais_vendido:
        return {}

    destino_id = n_trecho_mais_vendido[0]["destino_id"]
    destino = Cidade.objects.to_serialize(SearchOrigemDestinoCidadeSerializer.with_picture_url()).get(pk=destino_id)
    destino = destino.serialize()
    destino["id"] = str(destino["id"])

    trechos_hidratados = hydrate_trechos_classe(n_trecho_mais_vendido)
    limited_trechos = trechos_hidratados[:limit]
    hashid_grupos(limited_trechos)
    _clean_trechos_extra_params(limited_trechos, ["best_price", "destino_id"])

    trechos = sorted(limited_trechos, key=itemgetter("price", "datetime_ida"))
    origem = get_local_by_slug(origem_slug)
    origem = origem.as_dict()

    return {
        "origem": origem,
        "destino": destino,
        "trechos": trechos,
    }


@cacheback(ONE_MIN)
def search_melhores_viagens_no_trecho(origem_slug, destino_slug, data_ida=None, data_fim=None, limit=3):
    """
    Args:
        origem_slug: slug de uma cidade ou local de embarque de origem
        destino_slug: slug de uma cidade ou local de embarque de destino
        data_ida: datetime da ida
        data_fim: data máxima de fim, quando informada a data ida.
        limit: limite de trechos que serão retornados pela função

    Returns:
        Lista de trechos entre origem_slug e o destino mais vendido a partir dessa origem, ordenada pelo menor preço
    """

    # Limita a data_fim a DAYS_LIMIT da data_ida
    if data_ida and data_fim:
        data_limite = data_ida + timedelta(days=DAYS_LIMIT)
        if data_fim > data_limite:
            data_fim = data_limite

    # Carrega o destino mais vendido caso não seja informado
    if origem_slug and not destino_slug:
        destino_slug = get_destino_mais_vendido_da_origem(origem_slug)

    trechos_list = get_melhores_viagens_no_trecho(origem_slug, destino_slug, data_ida, data_fim)

    origem = get_local_by_slug(origem_slug)
    destino = get_local_by_slug(destino_slug)

    if not origem or not destino:
        return {}

    origem = origem.as_dict()
    destino = destino.as_dict()

    if not trechos_list:
        return {
            "origem": origem,
            "destino": destino,
            "trechos": [],
        }

    trechos_hidratados = hydrate_trechos_classe(trechos_list)
    limited_trechos = trechos_hidratados[:limit]
    hashid_grupos(limited_trechos)
    _clean_trechos_extra_params(limited_trechos, ["best_price", "destino_id"])
    sorted_trechos = sorted(limited_trechos, key=itemgetter("price", "datetime_ida"))

    return {
        "origem": origem,
        "destino": destino,
        "trechos": sorted_trechos,
    }


def search_ofertas(origem_slug, mes, ano, destino_slug=None, limit=6, user_id=None):
    """
    Args:
        origem_slug: slug de uma cidade ou local de embarque de origem
        destino_slug: slug de uma cidade ou local de embarque de destino
        limit: limite de trechos que serão retornados pela função
        mes: mês de busca dos trechos
        ano: ano de busca dos trechos

    Returns:
        Lista de trechos em oferta entre origem_slug e o destino_slug (caso o destino_slug não seja informado
        será retornado o destino mais vendido a partir dessa origem) cujo datetime_ida está no mês e ano
        passados para a função, ordenados pelos maiores descontos
    """
    destino = {}
    if not destino_slug:
        destino_slug = get_destino_mais_vendido_da_origem(origem_slug)

        if not destino_slug:
            # não veio destino na request mas ainda assim não achou nada de destino mais vendido
            origem = get_local_by_slug(origem_slug, raise_exception=True)
            return {"origem": origem.as_dict(), "destino": destino, "trechos": []}

    return _search_ofertas(origem_slug, destino_slug, mes, ano, limit)


@cacheback(_30_SECS)
def _search_ofertas(origem_slug, destino_slug, mes, ano, limit):
    origem = get_local_by_slug(origem_slug, raise_exception=True)
    destino = get_local_by_slug(destino_slug, raise_exception=True)

    trechos_ids = get_trecho_classe_ids_no_mes(origem.slug, destino.slug, mes, ano)

    trechos_hidratados = hydrate_trechos_classe_em_oferta(trechos_ids, origem.timezone)
    trechos_hidratados.sort(key=lambda tc: (-tc["discount"], tc["datetime_ida"]))

    limited_trechos = trechos_hidratados[:limit]
    hashid_grupos(limited_trechos)

    origem = origem.as_dict()
    destino = destino.as_dict()

    return {"origem": origem, "destino": destino, "trechos": limited_trechos}


@cacheback(ONE_HOUR)
def search_ofertas_esgotadas(origem_slug, mes, ano, limit=3):
    """
    Args:
        origem_slug: slug de uma cidade ou local de embarque de origem
        limit: limite de trechos que serão retornados pela função
        mes: mês de busca dos trechos
        ano: ano de busca dos trechos

    Returns:
        Lista de trechos em oferta que já tiveram suas vagas esgotadas.
    """
    origem = get_local_by_slug(origem_slug, raise_exception=True)

    _now = dateutils.to_tz(dateutils.now(), origem.timezone)

    start_year = ano
    start_month = mes

    current_ano_mes = f"{start_year}{str(start_month).zfill(2)}"

    if f"{ano}:{str(mes).zfill(2)}" < current_ano_mes:
        start_year = _now.year
        start_month = _now.month

    ofertas_esgotadas = ofertas_esgotadas_mc_svc.get_ofertas_esgotadas(origem_slug, start_month, start_year)

    return {"trechos": ofertas_esgotadas[:limit]}


@cacheback(_15_MINS)
@traced("search_svc.get_trecho_classe_ids_no_mes")
def get_trecho_classe_ids_no_mes(origem_slug, destino_slug, mes, ano):
    """
    Args:
        origem_slug: slug de uma cidade ou local de embarque de origem
        destino_slug: slug de uma cidade ou local de embarque de destino
        mes: mês de busca dos trechos
        ano: ano de busca dos trechos

    Returns:
        Lista de ids de TrechoClasse saindo entre a origem e o destino em um mês
    """
    search_ofertas_no_mes = SearchTrechosMes(
        origem_slug=origem_slug,
        destino_slug=destino_slug,
        mes=mes,
        ano=ano,
    )

    trechos_classe_list = list(search_ofertas_no_mes.search_trecho_classe_queryset()[:TC_LIMIT_ENTRE_ORIGEM_E_DESTINO])

    trechos = [tc["id"] for tc in trechos_classe_list]
    return trechos


@traced("search_svc.hydrate_trechos_classe_em_oferta")
def hydrate_trechos_classe_em_oferta(list_trecho_classe_ids, origem_timezone):
    """
    Args:
        list_trechos_classe: lista de trechos_classe já em dict

    Returns:
        list_trechos_classe com price, o preço atualizado, old_price, o max_split_value e datetime_chegada
    """
    if not list_trecho_classe_ids:
        return []
    trechos_hidratados = []

    now_in_origem_tz = dateutils.to_tz(dateutils.now(), origem_timezone)

    trechos_classe_map = (
        TrechoClasse.objects.annotate(
            vagas_cleaned=Coalesce("vagas", F("grupo_classe__capacidade") - F("grupo_classe__pessoas")),
            grupo_classe_fechado=F("grupo_classe__closed"),
            modelo_venda=F("grupo__modelo_venda"),
            tipo_assento=F("grupo_classe__tipo_assento"),
            valor_promocional=F("price_manager__ref_value") - F("price_manager__value"),
        )
        .select_related(
            "price_manager",
            "trecho_vendido__destino__cidade",
        )
        .prefetch_related("price_manager__buckets")
        .filter(
            vagas__gt=0,
            vagas_cleaned__gt=0,
            datetime_ida__gt=now_in_origem_tz,
            closed=False,
            grupo_classe__closed=False,
            valor_promocional__gt=0,
            modelo_venda__in=Grupo.ModeloVenda.FRETAMENTO_MODELO_VENDA,
        )
        .only(
            "pk",
            # Bucketização depende de max_split_value, pessoas, price_manager e datetime_ida.
            "max_split_value",
            "pessoas",
            "datetime_ida",
            "duracao_ida",
            "price_manager",
            # As vagas e o ref_split_value podem mudar.
            "vagas",
            "ref_split_value",
            "closed",
            "trecho_vendido__destino__cidade_id",
            "trecho_vendido__destino__cidade__timezone",
        )
        .in_bulk(list_trecho_classe_ids)
    )

    for tc_id in list_trecho_classe_ids:
        try:
            trecho_classe = trechos_classe_map[tc_id]
        except KeyError:
            # TrechoClasse sumiu, melhor remover.
            continue

        ref_value = trecho_classe.ref_split_value_bucket
        value = trecho_classe.max_split_value_bucket

        is_trecho_promocional = value < ref_value

        # remove trechos que não são trechos em promoção
        if not is_trecho_promocional:
            continue

        datetime_chegada = trecho_classe.get_horario_chegada()
        vagas = trecho_classe.vagas_bucket

        trecho_classe_dict = {
            "id": tc_id,
            "price": value,
            "old_price": ref_value,
            "discount": 0,
            "datetime_chegada": datetime_chegada,
            "vagas": vagas,
            "modelo_venda": trecho_classe.modelo_venda,
            "datetime_ida": dateutils.to_tz(trecho_classe.datetime_ida, origem_timezone),
            "tipo_assento": trecho_classe.tipo_assento,
        }

        if ref_value > 0:
            trecho_classe_dict["discount"] = round((ref_value - value) / ref_value, 2)

        trechos_hidratados.append(trecho_classe_dict)

    return trechos_hidratados


@cacheback(ONE_DAY)
@traced("search_svc.get_destino_mais_vendido_da_origem")
def get_destino_mais_vendido_da_origem(origem_slug):
    """
    Args:
        origem_slug: slug de Cidade ou LocalEmbarque de origem

    Returns:
        Slug da cidade destino mais vendida a partir da origem.
    """
    origem = get_local_by_slug(origem_slug, raise_exception=True)
    _now = dateutils.to_tz(dateutils.now(), origem.timezone)
    mais_vendido_slug = (
        TrechoClasse.objects.select_related("trecho_vendido__destino__cidade")
        .prefetch_related("travel")
        .filter(
            **origem.as_queryset_filter("trecho_vendido__origem"),
            datetime_ida__range=(_now, _now + timedelta(days=DAYS_LIMIT)),
        )
        .values_list("trecho_vendido__destino__cidade__slug", flat=True)
        .annotate(travel_count=Count(F("travel")))
        .order_by("-travel_count")
        .first()
    )

    return mais_vendido_slug


@cacheback(ONE_HOUR)
def get_melhores_viagens_no_trecho(origem_slug, destino_slug, data_ida=None, data_fim=None):
    """
    Args:
        origem_slug: slug de uma cidade ou local de embarque de origem
        destino_slug: slug de uma cidade ou local de embarque de destino
        data_ida: datetime da ida
        data_fim: data máxima de fim, quando informada a data ida.

    Returns:
        Lista de trechos entre origem_slug e destino_slug ordenada pelo menor preço.
        Serão retornados os trechos nos próximos DAYS_LIMIT dias caso não haja data_ida.
        Caso contrário apenas os trechos entre max(now, data_ida) e 23:59 da data_ida, caso não seja informada a
        data_fim ou entre max(now, data_ida) e data_fim.
    """
    _now = dateutils.now()

    custom_filters = {}
    if not data_ida:
        custom_filters = {
            "datetime_ida__range": (_now, _now + timedelta(days=DAYS_LIMIT)),
        }

    if data_ida and data_fim:
        data_ida = dateutils.to_default_tz(data_ida)
        data_fim = dateutils.to_default_tz(data_fim.replace(hour=23, minute=59, second=59, microsecond=0))
        custom_filters = {
            "datetime_ida__range": (data_ida, data_fim),
        }

    search_melhores_viagens = SearchMelhoresTrechos(
        origem_slug=origem_slug,
        destino_slug=destino_slug,
        data_ida=data_ida,
        extra_filters=custom_filters,
    )

    trechos = search_melhores_viagens.get_trechos_ordered_by_travel_count()
    trecho_mais_vendido_list = search_melhores_viagens.get_best_trechos_list_from_trecho_classe_queryset(trechos)

    return trecho_mais_vendido_list


@cacheback(ONE_HOUR)
def get_lista_de_trechos_ordenada_por_travel_count_para_destino(origem_slug):
    """
    Args:
        origem_slug: slug de uma cidade ou local de embarque

    Returns:
        Lista de lista de trechos partindo origem_slug ordenada do destino mais vendido ao menos vendido
    """
    _now = dateutils.now()

    custom_filters = {
        "datetime_ida__range": (_now, _now + timedelta(days=DAYS_LIMIT)),
    }
    search_unilateral_melhores_trechos = SearchMelhoresTrechos(
        origem_slug=origem_slug,
        extra_filters=custom_filters,
    )
    trechos = search_unilateral_melhores_trechos.get_trechos_ordered_by_travel_count()
    return trechos


@cacheback(ONE_HOUR)
def search_conexao(
    origem_slug: str,
    destino_slug: str,
    intermediario_ids: list,
    withitinerario: bool = False,
    withextrainfo: bool = False,
):
    _now = dateutils.now()
    base_custom_filters = {
        "datetime_ida__range": (_now, _now + timedelta(days=DAYS_LIMIT)),
    }

    filters_para_search_origem = {
        "trecho_vendido__destino__cidade_id__in": intermediario_ids,
        **base_custom_filters,
    }

    filters_para_search_destino = {
        "trecho_vendido__origem__cidade_id__in": intermediario_ids,
        **base_custom_filters,
    }

    custom_annotations = {}
    custom_values = []
    if withitinerario:
        custom_annotations["rota_id"] = F("trecho_vendido__rota_id")
        custom_values.append("rota_id")

    search_unilateral_origem = SearchOrigem(
        origem_slug=origem_slug,
        extra_annotations=custom_annotations,
        extra_values=custom_values,
        extra_filters=filters_para_search_origem,
    )

    search_unilateral_destino = SearchDestino(
        destino_slug=destino_slug,
        extra_annotations=custom_annotations,
        extra_values=custom_values,
        extra_filters=filters_para_search_destino,
    )

    origem = search_unilateral_origem.local_origem
    destino = search_unilateral_destino.local_destino

    origems_info_map = search_unilateral_origem.get_citymap_from_trecho_classe_queryset()
    destinos_info_map = search_unilateral_destino.get_citymap_from_trecho_classe_queryset()

    if withitinerario:
        destinos_info_map = _attach_trechos_itinerario(destinos_info_map)
        origems_info_map = _attach_trechos_itinerario(origems_info_map)

    trechos_indo_para_serializados, has_more = _serialize_cidade_results(
        destinos_info_map, serialize_with_extrainfo=withextrainfo
    )

    trechos_saindo_de_serializados, has_more = _serialize_cidade_results(
        origems_info_map, serialize_with_extrainfo=withextrainfo
    )

    log_svc.log_search(origem=origem, destino=destino, type="conexao_search")

    return trechos_saindo_de_serializados, trechos_indo_para_serializados


@cacheback(_12_HOURS)
def search_top_rotas(cidade_slug, bairro_slug=None, nickname_slug=None) -> dict:
    """
    Dado um local X retorna top rotas saindo e indo para esse local.
    Args:
        cidade_slug: slug da cidade de origem e destino,
        bairro_slug: slug de um bairro onde está localizado o local exatamente
        nickname_slug: nickname slug do local de embarque


    Returns:
        um dict com as informações da cidade passada, junto de trechos saindo desse local e indo para esse
        local serializados
    """

    viagens_indo_para = search_destino(
        cidade_slug,
        bairro_slug=bairro_slug,
        nickname_slug=nickname_slug,
        withextrainfo=True,
    )
    viagens_saindo_de = search_origem(
        cidade_slug,
        bairro_slug=bairro_slug,
        nickname_slug=nickname_slug,
        limit=3,
        withextrainfo=True,
    )
    local_embarque = informacoes_local_embarque(cidade_slug, bairro_slug, nickname_slug)
    response = {
        "picture_url": viagens_indo_para["picture_url"],
        "cidade": viagens_indo_para["name"],
        "sigla": viagens_indo_para["sigla"],
        "uf": viagens_indo_para["uf"],
        "slug": viagens_indo_para["slug"],
        "info": viagens_indo_para["info"],
        "indo_para": viagens_indo_para["indo_para"],
        "wikipedia": viagens_indo_para["wikipedia"],
        "saindo_de": viagens_saindo_de["saindo_de"],
    }
    if local_embarque:
        response.update(local_embarque)
    return response


def informacoes_local_embarque(cidade_slug, bairro_slug, nickname_slug) -> dict:
    extra_filters = {}

    if bairro_slug is not None:
        extra_filters["bairro_slug"] = bairro_slug
    if nickname_slug is not None:
        extra_filters["nickname_slug"] = nickname_slug

    ponto_qs = LocalEmbarque.objects.filter(cidade__slug=cidade_slug, ativo=True, **extra_filters).values(
        "nickname",
        "endereco_logradouro",
        "slug",
        "endereco_numero",
        "endereco_bairro",
        "endereco_cep",
        "endereco_referencia",
    )

    return ponto_qs.first()


@traced("search_svc.hydrate_trechos_classe")
def hydrate_trechos_classe(list_trechos_classe):
    """
    Args:
        list_trechos_classe: lista de trechos_classe já em dict
        origem: CidadeAdapter ou LocalEmbarqueAdapter

    Returns:
        list_trechos_classe com price, o preço atualizado, old_price, o max_split_value e datetime_chegada
    """
    if not list_trechos_classe:
        return []
    trechos_hidratados = []

    trechos_classe_map = (
        TrechoClasse.objects.annotate(
            vagas_cleaned=Coalesce("vagas", F("grupo_classe__capacidade") - F("grupo_classe__pessoas")),
            grupo_classe_fechado=F("grupo_classe__closed"),
            grupo_classe_fechado_motivo=F("grupo_classe__closed_reason"),
        )
        .select_related(
            "price_manager",
            "trecho_vendido__destino__cidade",
            "trecho_vendido__origem__cidade",
        )
        .prefetch_related("price_manager__buckets")
        .only(
            "pk",
            # Bucketização depende de max_split_value, pessoas, buckets e datetime_ida.
            "max_split_value",
            "pessoas",
            "datetime_ida",
            "duracao_ida",
            "price_manager",
            # As vagas e o ref_split_value podem mudar.
            "vagas",
            "ref_split_value",
            "closed",
            "trecho_vendido__destino__cidade_id",
            "trecho_vendido__origem__cidade__timezone",
        )
        .in_bulk(tc["id"] for tc in list_trechos_classe)
    )

    _now = dateutils.now()

    for trecho_classe_dict in list_trechos_classe:
        try:
            trecho_classe = trechos_classe_map[trecho_classe_dict["id"]]
        except KeyError:
            # TrechoClasse sumiu, melhor remover.
            continue
        origem_timezone = trecho_classe.trecho_vendido.origem.cidade.timezone
        nowstr = dateutils.to_tz(_now, origem_timezone).isoformat()

        # Remove grupos iniciados. Comparação de datas isoformat funciona.
        # 2021-08-30T14:15:00-03:00

        if trecho_classe_dict["datetime_ida"] < nowstr:
            continue

        # Remove grupos fechados ou sem vagas disponíveis
        if trecho_classe.closed or trecho_classe.vagas_cleaned == 0 or trecho_classe.grupo_classe_fechado:
            continue

        price = trecho_classe.max_split_value_bucket
        old_price = trecho_classe.max_split_value
        datetime_chegada = trecho_classe.get_horario_chegada()
        vagas = trecho_classe.vagas_bucket

        trecho_classe_dict["price"] = price
        trecho_classe_dict["old_price"] = old_price
        trecho_classe_dict["datetime_chegada"] = datetime_chegada
        trecho_classe_dict["vagas"] = vagas
        trechos_hidratados.append(trecho_classe_dict)

    return trechos_hidratados


def _prefetch_city_extra_info(qs: QuerySet, lookup: str) -> QuerySet[Cidade | AliasPlace | LocalEmbarque]:
    if lookup:
        lookup = f"{lookup}__"
    return qs.select_related(f"{lookup}cidadeinfo").prefetch_related(
        Prefetch(f"{lookup}cidadeinfo__imagem_set", Imagem.objects.filter(tipo=Imagem.Tipo.THUMB))
    )


def _aliasplace_getter(slug, with_city_info, with_local_info) -> AliasPlaceAdapter | None:
    alias_qs = AliasPlace.objects.select_related("cidade").filter(slug=slug)

    if with_city_info:
        alias_qs = _prefetch_city_extra_info(alias_qs, "cidade")
    alias = alias_qs.first()

    if not alias:
        return None

    alias = cast(AliasPlace, alias)

    local = _get_local_by_city(alias.cidade) if with_local_info else None
    return AliasPlaceAdapter(alias, local, with_city_info)


def _get_local_by_city(cidade: Cidade) -> LocalEmbarque | None:
    return cidade.localembarque_set.filter(ativo=True).first()


def _cidade_getter(slug, with_city_info, with_local_info) -> CidadeAdapter | None:
    cidade_qs = Cidade.objects.filter(slug=slug)

    if with_city_info:
        cidade_qs = _prefetch_city_extra_info(cidade_qs, "")

    cidade = cidade_qs.first()

    if not cidade:
        return None

    cidade = cast(Cidade, cidade)

    local = _get_local_by_city(cidade) if with_local_info else None
    return CidadeAdapter(cidade, local, with_city_info)


def _local_embarque_getter(slug, with_city_info, with_local_info=False) -> LocalEmbarqueAdapter | None:
    local_embarque_qs = LocalEmbarque.objects.select_related("cidade").filter(slug=slug)

    if with_city_info:
        local_embarque_qs = _prefetch_city_extra_info(local_embarque_qs, "cidade")

    local_embarque = local_embarque_qs.first()

    if local_embarque:
        return LocalEmbarqueAdapter(local_embarque, with_city_info)
    return None


def get_local_by_slug(
    slug, raise_exception=False, with_city_info=False, with_local_info=False
) -> CidadeAdapter | LocalEmbarqueAdapter | AliasPlaceAdapter | None:
    if not slug:
        return
    getters = [_cidade_getter, _local_embarque_getter, _aliasplace_getter]

    for getter in getters:
        local = getter(slug, with_city_info, with_local_info)
        if local:
            return local

    if raise_exception:
        raise LocalDoesNotExist(f"o local com slug {slug} não existe.")


@memoize(timeout=10 * 60)
def get_local_by_slug_with_memoize(
    slug, raise_exception=False, with_city_info=False, with_local_info=False
) -> CidadeAdapter | LocalEmbarqueAdapter | None:
    return get_local_by_slug(slug, raise_exception=False, with_city_info=False, with_local_info=False)


@cacheback(24 * 60 * 60)
def get_statistics(origem_slug, destino_slug):
    origem = get_local_by_slug(origem_slug, raise_exception=True)
    destino = get_local_by_slug(destino_slug, raise_exception=True)

    # Precisa exibir estatísticas dos pontos do trecho alternativo.
    if origem.local_alternativo is not None:
        origem = origem.local_alternativo

    travel_feedback_list = _get_travel_feedbacks(origem, destino)

    trechos_vendidos_ids = [feedback.get("trecho_vendido_id") for feedback in travel_feedback_list]
    rating_statistics = _get_rating_statistics(travel_feedback_list)
    trecho_aggregated_data = _get_trecho_aggregated_data(trechos_vendidos_ids, origem, destino)
    reviews = _get_reviews(travel_feedback_list)

    return {
        "rating": rating_statistics,
        "price": trecho_aggregated_data["price"],
        "duration": trecho_aggregated_data["duration"],
        "horario": trecho_aggregated_data["horario"],
        "empresas": trecho_aggregated_data["empresas"],
        "reviews": reviews,
    }


def _get_trecho_aggregated_data(trecho_vendido_ids, origem, destino):
    trecho_vendido_filter = {"trecho_vendido_id__in": trecho_vendido_ids}
    if not trecho_vendido_ids:
        trecho_vendido_filter = {
            **origem.as_queryset_filter("trecho_vendido__origem"),
            **destino.as_queryset_filter("trecho_vendido__destino"),
        }

    today = dateutils.today_midnight()
    agg = (
        TrechoClasse.objects.select_related("grupo__company")
        .filter(
            **trecho_vendido_filter,
            vagas__gt=0,
            datetime_ida__range=(today, today + timedelta(days=15)),
            grupo__status__in=("pending", "travel_confirmed"),
        )
        .aggregate(
            price_min=Min("max_split_value"),
            price_max=Max("max_split_value"),
            price_avg=Avg("max_split_value"),
            duration_min=Min("duracao_ida"),
            duration_avg=Avg("duracao_ida"),
            horarios=ArrayAgg("datetime_ida__time", distinct=True),
            empresas_ids=ArrayAgg(
                "grupo__company_id",
                distinct=True,
                filter=Q(grupo__company_id__isnull=False),
            ),
        )
    )

    empresas = []
    if agg.get("empresas_ids"):
        qs_empresas = Company.objects.filter(pk__in=agg.get("empresas_ids")).values("name", "slug").order_by("name")
        empresas = list(qs_empresas)

    return {
        "price": {
            "min": agg.get("price_min"),
            "max": agg.get("price_max"),
            "avg": agg.get("price_avg") and round(agg.get("price_avg"), 2),
        },
        "duration": {
            "min": agg.get("duration_min") and agg.get("duration_min").total_seconds() // 60,
            "avg": agg.get("duration_avg") and agg.get("duration_avg").total_seconds() // 60,
        },
        "horario": [horario.isoformat(timespec="minutes") for horario in agg.get("horarios")],
        "empresas": empresas,
    }


def _get_travel_feedbacks(origem, destino):
    feedbacks = (
        TravelFeedback.objects.filter(
            **origem.as_queryset_filter("travel__trecho_vendido__origem"),
            **destino.as_queryset_filter("travel__trecho_vendido__destino"),
            created_at__gte=dateutils.today_midnight() - timedelta(days=60),
        )
        .annotate(
            avg_rating=Avg("rating"),
            quantity=Count("id"),
            company_id=F("travel__grupo__company_id"),
            trecho_vendido_id=F("travel__trecho_vendido_id"),
            company_name=F("travel__grupo__company__name"),
        )
        .values(
            "company_id",
            "avg_rating",
            "quantity",
            "trecho_vendido_id",
            "created_at",
            "company_name",
        )
    )

    return list(feedbacks)


def locais_embarque_list_with_ranking_place() -> list:
    cidades = Cidade.objects.values(
        "slug",
        "name",
        "uf",
        "sigla",
        pk=Cast("id", output_field=CharField()),
        label=Concat("name", V(" - "), "uf"),
    )
    locais_embarque = LocalEmbarque.objects.exclude(Q(slug=None) | Q(ativo=False)).values(
        "slug",
        name=F("nickname"),
        uf=F("cidade__uf"),
        sigla=F("cidade__sigla"),
        pk=Concat("cidade_id", V("-"), "id", output_field=CharField()),
        label=Concat(
            "cidade__name",
            V(" - "),
            "cidade__uf",
            V(", "),
            "nickname",
            output_field=CharField(),
        ),
    )
    cidades = cidades.union(locais_embarque).order_by("label")
    return list(_serialize_locais(cidades))


def _serialize_locais(locais):
    ranking = get_places_ranking()

    for local in locais:
        yield {
            "id": local["pk"],
            "name": local["name"],
            "uf": local["uf"],
            "slug": local["slug"],
            "sigla": local["sigla"],
            "label": local["label"],
            "rank": ranking.get(local["pk"], DEFAULT_PLACE_RANKING),
        }


def _get_reviews(travel_feedback_list):
    max_reviews = 5
    feedback_threshold = 4
    reviews_and_created_date = []
    for feedback in travel_feedback_list:
        if feedback.get("avg_rating") >= feedback_threshold:
            reviews_and_created_date.append(
                {
                    "rating": feedback.get("avg_rating"),
                    "created_at": feedback.get("created_at"),
                }
            )
    sorted_reviews = sorted(reviews_and_created_date, key=lambda d: d["created_at"], reverse=True)
    reviews = list(sorted_reviews[:max_reviews])

    return reviews


def _get_rating_statistics(travel_feedback_list):
    best_rated, avg, count, feedbacks_map = None, None, 0, {}
    if travel_feedback_list:
        total_rating, count, best_rated = 0, 0, {}
        company_count_feedbacks_map = {}
        company_name_map = {}
        for feedback in travel_feedback_list:
            company_id = feedback.get("company_id")
            rating = feedback.get("avg_rating")
            count_by_company = feedback.get("quantity")

            total_rating += rating
            count += count_by_company

            feedbacks_map[company_id] = feedbacks_map.get(company_id, 0) + round(rating, 2)
            company_count_feedbacks_map[company_id] = company_count_feedbacks_map.get(company_id, 0) + count_by_company
            company_name_map[company_id] = feedback.get("company_name", "") if feedback.get("company_name") else ""
        avg = round(total_rating / len(travel_feedback_list), 2)

        for company_id in company_count_feedbacks_map:
            if company_count_feedbacks_map[company_id]:
                feedbacks_map[company_id] = round(
                    feedbacks_map[company_id] / company_count_feedbacks_map[company_id],
                    2,
                )

        best_rated_company_id = sorted(feedbacks_map.items(), key=lambda f: f[1], reverse=True)[0][0]

        best_rated = {
            "company_id": best_rated_company_id,
            "rating": feedbacks_map[best_rated_company_id],
            "rating_count": company_count_feedbacks_map[best_rated_company_id],
            "company_name": company_name_map[best_rated_company_id],
        }

    return {
        "avg": avg,
        "count": count,
        "best_rated": best_rated,
        "by_company": feedbacks_map,
    }


def get_places_ranking():
    cached = cache.get(PLACES_RANKING_CACHE_KEY)
    if cached:
        return cached

    travels_by_place, max_travels_by_place = _count_last_days_travels_by_place(days=90)

    s = max_travels_by_place + (10 - max_travels_by_place % 10)
    r = s ** (1 / 9)
    ranking = []
    for x in range(10):  # o ranking é calculado utilizando progressão geométrica
        interval = round(1 * r ** (x - 1)), round(1 * r**x)
        ranking.append((10 - x, interval))

    place_rank_map = {}
    for place, count in travels_by_place.items():
        rank, _ = binary_search(ranking, count, comparator=_compare_rank)
        place_rank_map[place] = rank

    cache.set(PLACES_RANKING_CACHE_KEY, place_rank_map, timeout=12 * 60 * 60)
    return place_rank_map


def _count_last_days_travels_by_place(days):
    min_created_on = dateutils.today_midnight() - timedelta(days=days)
    viagens_indo = (
        Travel.objects.filter(created_on__gte=min_created_on)
        .values("trecho_vendido__destino", "trecho_vendido__destino__cidade")
        .annotate(count=Count("id"))
        .values_list("trecho_vendido__destino", "trecho_vendido__destino__cidade", "count")
        .order_by("trecho_vendido__destino", "trecho_vendido__destino__cidade")
    )
    viagens_saindo = (
        Travel.objects.filter(created_on__gte=min_created_on)
        .values("trecho_vendido__origem", "trecho_vendido__origem__cidade")
        .annotate(count=Count("id"))
        .values_list("trecho_vendido__origem", "trecho_vendido__origem__cidade", "count")
        .order_by("trecho_vendido__origem", "trecho_vendido__origem__cidade")
    )

    result = Counter()
    max_travels = 0
    for ponto_embarque, cidade, count in chain(viagens_indo, viagens_saindo):
        ponto_embarque_id = f"{cidade}-{ponto_embarque}"
        cidade_id = str(cidade)

        result[cidade_id] += count
        result[ponto_embarque_id] += count

        max_travels = max(max_travels, result[cidade_id], result[ponto_embarque_id])

    return dict(result), max_travels


def _compare_rank(position_ranking, count):
    min_count, max_count = position_ranking[1]

    if min_count > count:
        return 1
    elif max_count < count:
        return -1
    else:
        return 0


def _attach_trechos_itinerario(city_map):
    rota_ids = {v["rota_id"] for v in city_map.values()}
    itinerario_data = itinerario_svc.get_itinerario_data(rota_ids)

    for cidade_info in city_map.values():
        cidade_info["itinerario"] = itinerario_data.get(cidade_info["rota_id"])

    return city_map


def _serialize_cidade_results(city_info_map, results_limit=None, serialize_with_extrainfo=False):
    cidades_ids = list(city_info_map.keys())

    serializer = SearchOrigemDestinoCidadeSerializer.with_picture_url()
    if serialize_with_extrainfo:
        serializer = SearchOrigemDestinoCidadeSerializer.with_extra_info()

    cidades_dict = Cidade.objects.to_serialize(serializer).in_bulk(cidades_ids)

    cidades = [cidades_dict[idx] for idx in cidades_ids]

    has_more = False
    if results_limit:
        has_more = len(cidades) > results_limit
        cidades = cidades[:results_limit]

    cidades_serializadas = [cidade.serialize() for cidade in cidades]
    cidades_serializadas = [{**cidade, **city_info_map[cidade["id"]]} for cidade in cidades_serializadas]

    return cidades_serializadas, has_more


def _clean_trechos_extra_params(trechos, params):
    for trecho in trechos:
        for param in params:
            del trecho[param]


@memoize(timeout=24 * 60 * 60)
def resolve_slugs(origem: str, destino: str) -> tuple[str, str]:
    """
    Retorna os slugs de origem e destino, caso existam aliases, para que seja capaz de buscar pelo slug original.
    """
    alias_places = AliasPlace.objects.select_related("cidade").filter(slug__in=[origem, destino])
    if not alias_places:
        return origem, destino

    map_aliases = {alias.slug: alias.cidade.slug for alias in alias_places}
    origem = map_aliases.get(origem) or origem
    destino = map_aliases.get(destino) or destino
    return origem, destino
