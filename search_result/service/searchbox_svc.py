from pydantic.v1 import BaseModel

from core.models_commons import <PERSON><PERSON><PERSON><PERSON>, Cidade
from core.models_rota import LocalEmbarque

SEARCHBOX_FORMAT_VERSION = "7"


class SearchboxItem(BaseModel):
    """
    Esse é o formato de objeto esperado pelo searchbox.
    Se for alterar de forma a quebrar compatibilidade, lembre-se de
    alterar primeiro em: https://gitlab.buser.com.br/buser/searchbox
    """

    id: str
    city: str
    state: str
    addr: str | None = None
    slug: str
    lat: float | None = None
    lon: float | None = None
    city_id: int | None = None
    alias: str | None = None


def _list_alias_places_items() -> list[SearchboxItem]:
    aliases = AliasPlace.objects.select_related("cidade").filter(cidade__ativo=True)

    return [
        SearchboxItem(
            id=f"{alias.cidade.id}_{alias.id}",
            city=alias.cidade.name,
            state=alias.cidade.uf,  # type: ignore
            slug=alias.slug,
            city_id=alias.cidade.id,
            alias=alias.alias,
        )
        for alias in aliases
    ]


def list_searchbox_items() -> list[SearchboxItem]:
    return _list_cidade_items() + _list_locais_de_embarque_items() + _list_alias_places_items()


def _list_cidade_items() -> list[SearchboxItem]:
    # cidades com rota ativa
    cidades_ativas = Cidade.objects.filter(ativo=True).distinct()
    origens_inativas_com_rota_ativa = Cidade.objects.filter(
        localembarque__trechos_vendidos_origem__rota__ativo=True, ativo=False
    ).distinct()
    destinos_inativos_com_rota_ativa = Cidade.objects.filter(
        localembarque__trechos_vendidos_destino__rota__ativo=True, ativo=False
    ).distinct()

    # remove ordenação
    cidades_ativas = cidades_ativas.order_by()
    origens_inativas_com_rota_ativa = origens_inativas_com_rota_ativa.order_by()
    destinos_inativos_com_rota_ativa = destinos_inativos_com_rota_ativa.order_by()

    # cidades sem duplicação
    cidades = list(cidades_ativas.union(origens_inativas_com_rota_ativa.union(destinos_inativos_com_rota_ativa)))

    return [
        SearchboxItem(
            id=str(cidade.id),
            city=cidade.name,
            state=cidade.uf,
            slug=cidade.slug,
        )
        for cidade in cidades
    ]


def _list_locais_de_embarque_items() -> list[SearchboxItem]:
    # locais de embarque ativos
    origens = LocalEmbarque.objects.filter(ativo=True, trechos_vendidos_origem__rota__ativo=True).distinct()
    destinos = LocalEmbarque.objects.filter(ativo=True, trechos_vendidos_destino__rota__ativo=True).distinct()

    # busca cidade junto e remove ordenação
    origens = origens.select_related("cidade").order_by()
    destinos = destinos.select_related("cidade").order_by()

    # locais sem duplicação
    locais = list(origens.union(destinos))

    return [
        SearchboxItem(
            id=f"{local.cidade.id}-{local.id}",
            city_id=local.cidade.id,
            city=local.cidade.name,
            state=local.cidade.uf,
            addr=local.nickname if local.nickname else local.endereco,
            slug=local.slug,
            lat=local.latitude,
            lon=local.longitude,
        )
        for local in locais
    ]
