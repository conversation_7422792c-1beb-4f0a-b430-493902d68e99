upstream django {
    server buser-django:8000;
}

upstream nuxt2 {
    server buser-front:3001;
    keepalive 4;
}

upstream nuxt3 {
    server buser-front:3000;
    keepalive 4;
}

uwsgi_cache_path /tmp/cache-buserdjango levels=1:2 keys_zone=buserdjango_cache:10m max_size=10g inactive=60m use_temp_path=off;
proxy_cache_path /tmp/cache-busernuxt2 levels=1:2 keys_zone=busernuxt2_cache:10m max_size=10g inactive=60m use_temp_path=off;
proxy_cache_path /tmp/cache-busernuxt3 levels=1:2 keys_zone=busernuxt3_cache:10m max_size=10g inactive=60m use_temp_path=off;
uwsgi_cache_key $uri$is_args$args;
proxy_cache_key $uri$is_args$args;

limit_req_zone $limit_key zone=api_search:20m rate=3r/s;
limit_req_zone $limit_key zone=api_search_conexao:20m rate=3r/s;
limit_req_zone $limit_key zone=api_search_marketplace:20m rate=3r/s;
limit_req_zone $limit_key zone=tp_api_search:20m rate=6r/s;
limit_req_zone $limit_key zone=read_bpe:5m rate=1r/s;
limit_req_zone $limit_key zone=safety:5m rate=1r/m;
limit_req_zone $limit_key zone=send_confirmation_link_lead_opt_out:5m rate=1r/s;
limit_req_zone $limit_key zone=delete_account:5m rate=1r/m;
limit_req_zone $limit_key zone=public_search_result:10m rate=3r/s;
limit_req_zone $limit_key zone=bloquear_poltronas:10m rate=1r/s;
limit_req_zone $limit_key zone=itinerario_marketplace:10m rate=1r/s;

### splits

# split_clients "ab_example${remote_addr}${http_user_agent}" $ab_split_example {
#     50%     true;
#     *       false;
# }

split_clients "ab_novo_checkout${remote_addr}${http_user_agent}" $ab_split_novo_checkout {
    10%     "b";
    *       "a";
}

### maps 1

# map $cookie_ab_example $ab_example {
#     default     $ab_split_example;
#     "true"      "true";
#     "false"     "false";
# }

map $cookie_ab_novo_checkout $ab_novo_checkout {
    default     $ab_split_novo_checkout;
    "a"      "a";
    "b"      "b";
}


### maps 2

# map $cookie_ab_example $cookies_ab_example {
#     default     "ab_example=${ab_example};";
#     "true"      "";
#     "false"     "";
# }

map $cookie_ab_novo_checkout $cookies_ab_novo_checkout {
    default     "ab_novo_checkout=${ab_novo_checkout};";
    "a"      "";
    "b"     "";
}

server {
    listen       7000;
    server_name  localhost;

    client_max_body_size 0;

    # redirects iguais em todos os ambientes
    include /etc/nginx/include/redirects.conf;

    # endpoints sem autenticação
    # cache sempre
    location ~ ^/api/(cidades|settings|get_top_destinos|search/init|search/origem|search/destino|search/statistics|empresas|empresas/(.*)|blog/news|blog/articles|cidades_destino|get_helpquestions|promo_destaque_home)$ {
        uwsgi_pass django;
        include     /etc/nginx/include/uwsgi_params;
        uwsgi_read_timeout 3600;
        uwsgi_send_timeout 3600;
        uwsgi_ignore_client_abort on;

        uwsgi_cache buserdjango_cache;
        uwsgi_hide_header Set-Cookie;
        uwsgi_hide_header vary;
        uwsgi_ignore_headers Set-Cookie vary;
        uwsgi_cache_background_update on;
        uwsgi_cache_use_stale error updating;
        uwsgi_cache_lock on;
        add_header X-Cache-Status $upstream_cache_status;
    }

    # endpoints que variam só para usuários logados
    # cache só para usuários não logados
    location ~ ^/api/(whoami|cities|search/locais-favoritos)$ {
        uwsgi_pass django;
        include     /etc/nginx/include/uwsgi_params;
        uwsgi_read_timeout 3600;
        uwsgi_send_timeout 3600;
        uwsgi_ignore_client_abort on;

        uwsgi_cache buserdjango_cache;
        uwsgi_hide_header vary;
        uwsgi_ignore_headers vary;
        uwsgi_cache_background_update on;
        uwsgi_cache_use_stale error updating;
        uwsgi_cache_lock on;
        add_header X-Cache-Status $upstream_cache_status;

        uwsgi_cache_bypass $skip_cache;
        uwsgi_no_cache $skip_cache;
    }

    # endpoints geolocalizados
    # cache na CDN, sem vary cookie
    location ~ ^/api/geo/ {
        uwsgi_pass django;
        include     /etc/nginx/include/uwsgi_params;
        uwsgi_read_timeout 3600;
        uwsgi_send_timeout 3600;
        uwsgi_ignore_client_abort on;

        uwsgi_hide_header vary;
        uwsgi_ignore_headers vary;
    }

    # motivo do '=' https://stackoverflow.com/questions/59846238
    location = /api/search {
        limit_req zone=api_search burst=6 nodelay;
        uwsgi_pass django;
        include     /etc/nginx/include/uwsgi_params;
        uwsgi_read_timeout 1800;
        uwsgi_send_timeout 1800;
        uwsgi_ignore_client_abort on;
    }

    location = /api/search/conexao/v2 {
        limit_req zone=api_search_conexao burst=6 nodelay;
        uwsgi_pass django;
        include     /etc/nginx/include/uwsgi_params;
        uwsgi_read_timeout 1800;
        uwsgi_send_timeout 1800;
        uwsgi_ignore_client_abort on;
    }

    location = /api/search/marketplace {
        limit_req zone=api_search_marketplace burst=6 nodelay;
        uwsgi_pass django;
        include     /etc/nginx/include/uwsgi_params;
        uwsgi_read_timeout 1800;
        uwsgi_send_timeout 1800;
        uwsgi_ignore_client_abort on;
    }

    location = /api/marketplace/itinerario {
        limit_req zone=itinerario_marketplace burst=5 nodelay;
        uwsgi_pass django;
        include     /etc/nginx/include/uwsgi_params;
        uwsgi_read_timeout 1800;
        uwsgi_send_timeout 1800;
        uwsgi_ignore_client_abort on;
    }

    location ^~ /tp-api/search {
        limit_req zone=tp_api_search burst=30 nodelay;
        uwsgi_pass django;
        include     /etc/nginx/include/uwsgi_params;
        uwsgi_read_timeout 3600;
        uwsgi_send_timeout 3600;
        uwsgi_ignore_client_abort on;
    }

    location = /api/v1/bloquear-poltronas {
        limit_req zone=bloquear_poltronas burst=5 nodelay;
        uwsgi_pass django;
        include     /etc/nginx/include/uwsgi_params;
        uwsgi_read_timeout 3600;
        uwsgi_send_timeout 3600;
        uwsgi_ignore_client_abort on;
    }

    location = /api/parceiro/incidents/documents/upload {
        uwsgi_pass django;
        include     /etc/nginx/include/uwsgi_params;
        uwsgi_read_timeout 3600;
        uwsgi_send_timeout 3600;
        uwsgi_ignore_client_abort on;
        client_max_body_size 150m;
    }

    location = /robots.txt {
        try_files $uri @uwsgi;
    }

    # utilizado para llm's consumir conteúdo da buser
    location = /llms.txt {
        try_files $uri @uwsgi;
    }

    location = /api/get_status_bpe {
        uwsgi_pass django;
        include     /etc/nginx/include/uwsgi_params;
        limit_req zone=read_bpe burst=5 nodelay;
        uwsgi_read_timeout 1800;
        uwsgi_send_timeout 1800;
        uwsgi_ignore_client_abort on;
    }

    location = /api/forgot_password {
        uwsgi_pass django;
        include     /etc/nginx/include/uwsgi_params;
        limit_req zone=safety;
        uwsgi_read_timeout 3600;
        uwsgi_send_timeout 3600;
        uwsgi_ignore_client_abort on;
    }

    location = /api/send_confirmation_link_lead_opt_out {
        uwsgi_pass django;
        include     /etc/nginx/include/uwsgi_params;
        limit_req zone=send_confirmation_link_lead_opt_out;
        uwsgi_read_timeout 3600;
        uwsgi_send_timeout 3600;
        uwsgi_ignore_client_abort on;
    }

    location /api/user/delete_account {
        uwsgi_pass django;
        include     /etc/nginx/include/uwsgi_params;
        limit_req zone=delete_account;
        uwsgi_read_timeout 3600;
        uwsgi_send_timeout 3600;
        uwsgi_ignore_client_abort on;
    }

    location ~ ^/(api|tp-api|admin|djamail|login|complete|sitemap|remote-auth) {
        uwsgi_read_timeout 3600;
        uwsgi_send_timeout 3600;
        try_files $uri @uwsgi;
    }

    location /static {
        alias /dkdata/static;
        add_header Cache-Control "public, immutable, max-age=********";  # 1 month
    }

    location = /_nginx_status {
        add_header Content-Type application/json;
        return 200 '{ "status": "ok", "service": "nginx" }';
    }

    location = /sw.js {
        add_header Content-Type text/javascript;
        include /etc/nginx/include/front_nuxt2;
    }

    location /prometheus/metrics {
        uwsgi_pass django;
        include     /etc/nginx/include/uwsgi_params;
    }

    location /reserva {
        proxy_pass  http://nuxt2;
        proxy_cache busernuxt2_cache;

        include     /etc/nginx/include/proxy_params;
        include     /etc/nginx/include/front_params;

        proxy_set_header Host   $host;
        proxy_set_header Cookie $cookies_ab_novo_checkout$http_cookie;
    }

    location ~ /onibus/promo/de { include /etc/nginx/include/front_nuxt3; }

    location ~ /onibus {
        proxy_pass  http://nuxt2;
        proxy_cache busernuxt2_cache;

        include     /etc/nginx/include/proxy_params;
        include     /etc/nginx/include/front_params;

        proxy_set_header Host   $host;

        limit_req zone=public_search_result burst=5 nodelay;
    }

    location ~ /parceria {
        include /etc/nginx/include/front_nuxt2;
        limit_req zone=public_search_result burst=5 nodelay;
    }

    # Páginas migradas para o Nuxt 3
    location /ajuda { include /etc/nginx/include/front_nuxt3; }

    location /fale-conosco { include /etc/nginx/include/front_nuxt3; }

    location /pix { include /etc/nginx/include/front_nuxt3; }

    location /seguro-adicional { include /etc/nginx/include/front_nuxt3; }

    location /empresas { include /etc/nginx/include/front_nuxt3; }

    location /agencias-autorizadas { include /etc/nginx/include/front_nuxt3; }

    location /parceiros { include /etc/nginx/include/front_nuxt3; }

    location /sprites { include /etc/nginx/include/front_nuxt3; }

    location = /sobre { include /etc/nginx/include/front_nuxt3; }

    location /sobre/sustentabilidade { include /etc/nginx/include/front_nuxt3; }

    location /auth/confirmar-telefone { include /etc/nginx/include/front_nuxt3; }

    location /auth/redefinir-senha { include /etc/nginx/include/front_nuxt3; }

    location = /perfil { include /etc/nginx/include/front_nuxt3; }

    location /perfil/notificacoes { include /etc/nginx/include/front_nuxt3; }

    location /perfil/sugerir-viagem { include /etc/nginx/include/front_nuxt3; }

    location /perfil/completar-cadastro { include /etc/nginx/include/front_nuxt3; }

    location /perfil/linked_user { include /etc/nginx/include/front_nuxt3; }

    location /perfil/carteira { include /etc/nginx/include/front_nuxt3; }

    location /perfil/opcoes { include /etc/nginx/include/front_nuxt3; }

    location /perfil/promocoes { include /etc/nginx/include/front_nuxt3; }

    location /perfil/formas-pagamento { include /etc/nginx/include/front_nuxt3; }

    location /perfil/compartilhamento-de-dados { include /etc/nginx/include/front_nuxt3; }

    location /perfil/pesquisa-relacional { include /etc/nginx/include/front_nuxt3; }

    location /perfil/lead-opt-out { include /etc/nginx/include/front_nuxt3; }

    location /perfil/close-friends { include /etc/nginx/include/front_nuxt3; }

    location /perfil/editar { include /etc/nginx/include/front_nuxt3; }

    location /perfil/viajantes { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/perfil/confirm-lead-opt-out { include /etc/nginx/include/front_nuxt3; }

    location /fidelidade { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/fidelidade/\d+ { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/viagem/\d+/pesquisa-pos-embarque { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/viagem/\d+/pesquisa-nps$ { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/viagem/\d+/pesquisa-viagem { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/rodoviaria$ { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/rodoviaria/cancelar-viagem$ { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/rodoviaria/atraso-onibus$ { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/rodoviaria/nao-consegui-viajar$ { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/nao-consegui-viajar$ { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/perdi-um-item$ { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/perfil/viagens/\d+/desvincular-travel$ { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/perfil/viagens/\d+/nao-reconheco-essa-viagem$ { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/perfil/viagens/\d+/alteracao-na-reserva$ { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/rodoviaria/falha-mecanica$ { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/rodoviaria/acidente$ { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/rodoviaria/reembolso$ { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/problemas-com-a-bagagem$ { include /etc/nginx/include/front_nuxt3; }

    location /feriados { include /etc/nginx/include/front_nuxt3; }

    location /convite { include /etc/nginx/include/front_nuxt3; }

    location /sul { include /etc/nginx/include/front_nuxt3; }

    location /chat { include /etc/nginx/include/front_nuxt3; }

    location /chat-renderer-mobile-version { include /etc/nginx/include/front_nuxt3; }

    location /carreiras { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/revendedor/dashboard/[a-z]+$ { include /etc/nginx/include/front_nuxt3; }

    location /revendedor/carteira { include /etc/nginx/include/front_nuxt3; }

    location /revendedor/cupom { include /etc/nginx/include/front_nuxt3; }

    location /reserva/vendas { include /etc/nginx/include/front_nuxt3; }

    location /consultar-pax { include /etc/nginx/include/front_nuxt3; }

    location /erro { include /etc/nginx/include/front_nuxt3; }

    location ~ ^/sobre/(.*) { include /etc/nginx/include/front_nuxt3; }
    
    location /whatsapp { include /etc/nginx/include/front_nuxt3; }

    location /festival { include /etc/nginx/include/front_nuxt3; }

    location = /noticias { include /etc/nginx/include/front_nuxt3; }

    location = /glossario { include /etc/nginx/include/front_nuxt3; }

    location /imprensa { include /etc/nginx/include/front_nuxt3; }
    
    location = /pontos { include /etc/nginx/include/front_nuxt3; }

    location = /motoristas { include /etc/nginx/include/front_nuxt3; }

    location = /destinos { include /etc/nginx/include/front_nuxt3; }

    location /search-redirect { include /etc/nginx/include/front_nuxt3; }

    # Fim páginas migradas para o Nuxt 3

    location / { include /etc/nginx/include/front_nuxt2; }

    location @uwsgi {
        uwsgi_pass django;
        include /etc/nginx/include/uwsgi_params;
        uwsgi_read_timeout 3600;
        uwsgi_send_timeout 3600;
        uwsgi_ignore_client_abort on;
    }
}
