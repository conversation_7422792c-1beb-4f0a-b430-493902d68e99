upstream django {
    server buser-django:8000;
}

upstream motoraback {
    server **********:8001;
}

upstream nuxt2 {
    server buser-front:3001;
    keepalive 4;
}

upstream nuxt3 {
    server buser-front:3000;
    keepalive 4;
}

upstream motorista {
    server **********:3002;
}

upstream staff {
    server **********:3003;
}

upstream vendas {
    server **********:3005;
}

upstream parceiro {
    server **********:3006;
}

upstream bx {
    server **********:3007;
}

server {
    listen       7000;
    server_name  localhost;

    # redirects iguais em todos os ambientes
    include /etc/nginx/include/redirects.conf;

    location ~ ^/(api|admin|login|static|complete|media|sitemap) {
        proxy_pass  http://django;
        include     /etc/nginx/include/proxy_params;
        client_max_body_size 0;
    }

    # https://stackoverflow.com/questions/13672743/eventsource-server-sent-events-through-nginx
    location /_loading/sse {
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        proxy_buffering off;
        proxy_cache off;
        chunked_transfer_encoding off;
        proxy_pass http://nuxt2;
    }

    location /__webpack_hmr/client {
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        proxy_buffering off;
        proxy_cache off;
        chunked_transfer_encoding off;

        proxy_pass http://nuxt2;
    }

    location /_nginx_status {
        add_header Content-Type application/json;
        return 200 '{ "status": "ok", "service": "nginx" }';
    }

    location = /sw.js {
        add_header Content-Type text/javascript;
        proxy_pass http://nuxt2;
    }

    # Assets do Nuxt 3 (websocket)
    location /_nuxt3 {
        proxy_pass  http://nuxt3;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
    }

    # Nuxt 3 devtools
    location /__nuxt_devtools__ {
        proxy_pass  http://nuxt3;
    }

    # Páginas migradas para o Nuxt 3
    location /ajuda { proxy_pass http://nuxt3; }

    location /fale-conosco { proxy_pass http://nuxt3; }

    location /pix { proxy_pass http://nuxt3; }

    location /seguro-adicional { proxy_pass http://nuxt3; }

    location /empresas { proxy_pass http://nuxt3; }

    location /agencias-autorizadas { proxy_pass http://nuxt3; }

    location /parceiros { proxy_pass http://nuxt3; }

    location /sprites { proxy_pass http://nuxt3; }

    location = /sobre { proxy_pass http://nuxt3; }

    location /sobre/sustentabilidade { proxy_pass http://nuxt3; }

    location /auth/confirmar-telefone { proxy_pass http://nuxt3; }

    location /auth/redefinir-senha { proxy_pass http://nuxt3; }

    location = /perfil { proxy_pass http://nuxt3; }

    location /perfil/notificacoes { proxy_pass http://nuxt3; }

    location /perfil/sugerir-viagem { proxy_pass http://nuxt3; }

    location /perfil/completar-cadastro { proxy_pass http://nuxt3; }

    location /perfil/linked_user { proxy_pass http://nuxt3; }

    location /perfil/carteira { proxy_pass http://nuxt3; }

    location /perfil/opcoes { proxy_pass http://nuxt3; }

    location /perfil/promocoes { proxy_pass http://nuxt3; }

    location /perfil/formas-pagamento { proxy_pass http://nuxt3; }

    location /perfil/compartilhamento-de-dados { proxy_pass http://nuxt3; }

    location /perfil/pesquisa-relacional { proxy_pass http://nuxt3; }

    location /perfil/lead-opt-out { proxy_pass http://nuxt3; }

    location /perfil/close-friends { proxy_pass http://nuxt3; }

    location /perfil/editar { proxy_pass http://nuxt3; }

    location /perfil/viajantes { proxy_pass http://nuxt3; }

    location ~ ^/perfil/confirm-lead-opt-out { proxy_pass http://nuxt3; }

    location /fidelidade { proxy_pass http://nuxt3; }

    location ~ ^/fidelidade/\d+ { proxy_pass http://nuxt3; }

    location ~ ^/viagem/\d+/pesquisa-pos-embarque { proxy_pass http://nuxt3; }

    location ~ ^/viagem/\d+/pesquisa-nps$ { proxy_pass http://nuxt3; }

    location ~ ^/viagem/\d+/pesquisa-viagem { proxy_pass http://nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/rodoviaria$ { proxy_pass http://nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/rodoviaria/cancelar-viagem$ { proxy_pass http://nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/rodoviaria/atraso-onibus$ { proxy_pass http://nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/rodoviaria/nao-consegui-viajar$ { proxy_pass http://nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/nao-consegui-viajar { proxy_pass http://nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/perdi-um-item$ { proxy_pass http://nuxt3; }

    location ~ ^/perfil/viagens/\d+/desvincular-travel$ { proxy_pass http://nuxt3; }

    location ~ ^/perfil/viagens/\d+/alteracao-na-reserva$ { proxy_pass http://nuxt3; }

    location ~ ^/perfil/viagens/\d+/nao-reconheco-essa-viagem$ { proxy_pass http://nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/rodoviaria/falha-mecanica$ { proxy_pass http://nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/rodoviaria/acidente$ { proxy_pass http://nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/rodoviaria/reembolso$ { proxy_pass http://nuxt3; }

    location ~ ^/perfil/viagens/\d+/ajuda/problemas-com-a-bagagem$ { proxy_pass http://nuxt3; }

    location /feriados { proxy_pass http://nuxt3; }

    location /convite { proxy_pass http://nuxt3; }

    location /chat { proxy_pass http://nuxt3; }

    location /sul { proxy_pass http://nuxt3; }

    location /chat-renderer-mobile-version { proxy_pass http://nuxt3; }

    location /carreiras { proxy_pass http://nuxt3; }

    location ~ ^/revendedor/dashboard/[a-z]+$ { proxy_pass http://nuxt3; }

    location /revendedor/carteira { proxy_pass http://nuxt3; }

    location /revendedor/cupom { proxy_pass http://nuxt3; }

    location /reserva/vendas { proxy_pass http://nuxt3; }

    location /consultar-pax { proxy_pass http://nuxt3; }

    location /erro { proxy_pass http://nuxt3; }

    location /imprensa { proxy_pass http://nuxt3; }
    
    location ~ ^/sobre/(.*) { proxy_pass http://nuxt3; }

    location /whatsapp { proxy_pass http://nuxt3; }

    location /festival { proxy_pass http://nuxt3; }

    location /noticias { proxy_pass http://nuxt3; }

    location = /glossario { proxy_pass http://nuxt3; }

    location = /pontos { proxy_pass http://nuxt3; }

    location /motoristas { proxy_pass http://nuxt3; }
    
    location /destinos { proxy_pass http://nuxt3; }

    location ~ /onibus/promo/de { proxy_pass http://nuxt3; }

    location /search-redirect { proxy_pass http://nuxt3; }
    # Fim páginas migradas para o Nuxt 3


    location / {
        proxy_pass http://nuxt2;
    }
}

server {
    listen       7000;
    server_name  parceiro parceiro.local;

    location ~ ^/(api|admin|login|static|complete) {
        proxy_pass  http://django;
        include     /etc/nginx/include/proxy_params;
        client_max_body_size 0;
    }

    location / {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;

        proxy_pass http://parceiro;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
    }
}

server {
    listen       7000;
    server_name  motorista;

    location ~ ^(/api/) {
        proxy_pass  http://motoraback;
        include     /etc/nginx/include/proxy_params;
        client_max_body_size 0;
    }

    location ~ ^/(admin|login|static|complete) {
        proxy_pass  http://django;
        include     /etc/nginx/include/proxy_params;
        client_max_body_size 0;
    }

    location / {
        proxy_pass http://motorista/;
    }
}

server {
    listen       7000;
    server_name  staff staff.local;

    location ~ ^/(api|admin|login|static|complete) {
        proxy_pass  http://django;
        include     /etc/nginx/include/proxy_params;
        client_max_body_size 0;
    }

    location / {
        proxy_pass http://staff/;
    }
}

server {
    listen       7000;
    server_name  bx;

    location ~ ^/(api|admin|login|static|complete) {
        proxy_pass  http://django;
        include     /etc/nginx/include/proxy_params;
        client_max_body_size 0;
    }

    location / {
        proxy_pass http://bx/;
    }
}

server {
    listen       7000;
    server_name  vendas;

    location ~ ^/(api|admin|login|static|complete) {
        proxy_pass  http://django;
        include     /etc/nginx/include/proxy_params;
        client_max_body_size 0;
    }

    location / {
        proxy_pass http://vendas/;
    }
}
