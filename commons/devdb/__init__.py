from .accounting_operation import ACCOUNTING_OPERATION_DATA
from .blog import POST_DATA, POST_IMAGEM_DATA
from .buseiro import BUSEIRO_DATA
from .checkpoint import CHECKPOINT_DATA
from .cidades import CIDADES_DATA
from .commons import BANKACCOUNT_DATA, R<PERSON><PERSON>ATION_DATA
from .company import COMPANY_DATA
from .company_accounting_operation import COMPANY_ACCOUNTING_OPERATION_DATA
from .comunicacoes import (
    COMUNICACAO_DATA,
    ENVIO_DATA,
    LOTE_ENVIO_DATA,
    STATUS_ENVIO_DATA,
    TEMPLATE_DATA,
)
from .conexao import CONEXAO_DATA, TRAVEL_CONEXAO, TRECHO_CONEXAO
from .confirmacao_inteligente_logger import SOLVER_DATA
from .evento_extra import EVENTO_EXTRA_DATA
from .feriado import FERIADO_DATA
from .globalsetting import GLOBALSET<PERSON>NG_DATA
from .glossario import CATEGORIA_DATA, PERGUNTA_DATA, TERMO_DATA
from .grupo import GRUPO_DATA
from .grupoclasse import GRUPO_CLASSE_DATA
from .helpquestions import HELPQUESTION_DATA
from .localembarque import LOCALEMBARQUE_DATA
from .onibus import INFO_ONIBUS_DATA, ONIBUS_CLASSE_DATA, ONIBUS_DATA
from .optools import OPTOOLS_EXECUCAO_DATA, OPTOOLS_STRATEGY_DATA
from .pagamento_parceiro import (
    CONFIGURACAO_PAGAMENTO_DATA,
    REPASSE_MARKETPLACE_DATA,
)
from .passageiro import PASSAGEIRO_DATA
from .pontosturisticos import PONTO_TURISTICO_DATA
from .probabilidade_prejuizo_grupo import MODEL_IMPORT_DATA, PROBABILIDADE_DATA
from .remanejamento_automatico import REMANEJAMENTO_AUTOMATICO_DATA
from .reserva import RESERVA_DATA
from .rota import ROTAS_DATA
from .rotaprincipal import ROTAPRINCIPAL_DATA
from .rotina import ROTINA_DATA
from .textos import TEXTOS_DATA
from .travel import TRAVEL_DATA
from .trechoclasse import (
    PRICE_BUCKET_DATA,
    PRICE_MANAGER_DATA,
    TRECHOCLASSE_DATA,
)
from .trechovendido import TRECHOVENDIDO_DATA
from .user import LEAD_DATA, PROFILE_DATA, REVENDEDOR_DATA, USER_DATA

# Altera nome da Classe para melhorar saida padrão
REMANEJAMENTO_AUTOMATICO_DATA[0]._meta.model_name = "cenário de remanejamento automático"

ALL_DATA = [  # This order matters - dont make it alphabetical
    GLOBALSETTING_DATA,
    USER_DATA,
    REVENDEDOR_DATA,
    BUSEIRO_DATA,
    PROFILE_DATA,
    LEAD_DATA,
    CIDADES_DATA,
    BANKACCOUNT_DATA,
    REPUTATION_DATA,
    COMPANY_DATA,
    LOCALEMBARQUE_DATA,
    PONTO_TURISTICO_DATA,
    ROTAPRINCIPAL_DATA,
    ROTAS_DATA,
    TRECHOVENDIDO_DATA,
    CHECKPOINT_DATA,
    GRUPO_DATA,
    GRUPO_CLASSE_DATA,
    PRICE_MANAGER_DATA,
    PRICE_BUCKET_DATA,
    TRECHOCLASSE_DATA,
    POST_DATA,
    POST_IMAGEM_DATA,
    ONIBUS_DATA,
    INFO_ONIBUS_DATA,
    ONIBUS_CLASSE_DATA,
    ROTINA_DATA,
    RESERVA_DATA,
    TRAVEL_DATA,
    PASSAGEIRO_DATA,
    CONFIGURACAO_PAGAMENTO_DATA,
    REPASSE_MARKETPLACE_DATA,
    ACCOUNTING_OPERATION_DATA,
    COMPANY_ACCOUNTING_OPERATION_DATA,
    CONEXAO_DATA,
    TRAVEL_CONEXAO,
    TRECHO_CONEXAO,
    FERIADO_DATA,
    COMUNICACAO_DATA,
    TEMPLATE_DATA,
    LOTE_ENVIO_DATA,
    ENVIO_DATA,
    STATUS_ENVIO_DATA,
    CATEGORIA_DATA,
    TERMO_DATA,
    PERGUNTA_DATA,
    REMANEJAMENTO_AUTOMATICO_DATA,
    SOLVER_DATA,
    HELPQUESTION_DATA,
    OPTOOLS_STRATEGY_DATA,
    OPTOOLS_EXECUCAO_DATA,
    MODEL_IMPORT_DATA,
    PROBABILIDADE_DATA,
    EVENTO_EXTRA_DATA,
    TEXTOS_DATA,
]
