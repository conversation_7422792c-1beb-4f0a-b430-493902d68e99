from itertools import count

from core.models_grupo import Evento<PERSON>xtra, EventoExtraNegociacao, EventoExtraSolicitacao, EventoExtraSolicitacaoPerna

from .onibus import ONIBUS_DATA
from .user import ADMIN_FODAO, STAFF_USER

EVENTOS_EXTRA = [
    EventoExtra(
        id=1,
        nome="São João",
        data_inicial="2025-06-21",
        data_final="2025-06-24",
        status=EventoExtra.EventoStatus.DONE,
        created_at="2025-05-01T00:00:00Z",
        updated_by=ADMIN_FODAO,
    ),
    EventoExtra(
        id=2,
        nome="Corpus Christi",
        data_inicial="2025-06-17",
        data_final="2025-06-22",
        status=EventoExtra.EventoStatus.DOING,
        created_at="2025-05-01T00:00:00Z",
        updated_by=ADMIN_FODAO,
    ),
    EventoExtra(
        id=3,
        nome="Natal",
        data_inicial="2025-12-25",
        data_final="2025-12-31",
        status=EventoExtra.EventoStatus.PENDING,
        created_at="2025-01-05T00:00:00Z",
        updated_by=ADMIN_FODAO,
    ),
]

SOLICITACAO_EXTRA_DATA = [
    ###   EVENTO 1   ###
    EventoExtraSolicitacao(
        id=1,
        evento_extra_id=1,
        rota_principal_id=1,
        rota_prevista="RIO-SAO",
        regional=EventoExtraSolicitacao.Regional.SP,
        prioridade=EventoExtraSolicitacao.Prioridade.ALTA,
        tipos_assento=["semi leito", "leito cama"],
        ticket_medio_estimado="150.90",
        breakeven_esperado="84.5",
        distancia_por_perna=330,
        cash_in_gmv=90,
        is_fechado_rotas=True,
        has_precificacao_inicial=True,
        has_precificacao_final=True,
        is_criado_staff=True,
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    EventoExtraSolicitacao(
        id=2,
        evento_extra_id=1,
        rota_principal_id=1,
        rota_prevista="RIO-SJK-SAO",
        regional=EventoExtraSolicitacao.Regional.SP,
        prioridade=EventoExtraSolicitacao.Prioridade.ALTA,
        tipos_assento=["executivo", "leito cama"],
        ticket_medio_estimado="120.90",
        breakeven_esperado="80.5",
        distancia_por_perna=330,
        cash_in_gmv=90,
        is_fechado_rotas=True,
        has_precificacao_inicial=True,
        has_precificacao_final=True,
        is_criado_staff=True,
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    EventoExtraSolicitacao(
        id=3,
        evento_extra_id=1,
        rota_principal_id=1,
        rota_prevista="NTR-RIO-SAO",
        regional=EventoExtraSolicitacao.Regional.SP,
        prioridade=EventoExtraSolicitacao.Prioridade.ALTA,
        tipos_assento=["leito cama individual", "leito cama"],
        ticket_medio_estimado="170.90",
        breakeven_esperado="70.00",
        distancia_por_perna=330,
        cash_in_gmv=90,
        is_fechado_rotas=True,
        has_precificacao_inicial=True,
        has_precificacao_final=True,
        is_criado_staff=True,
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    ###   EVENTO 2   ###
    EventoExtraSolicitacao(
        id=4,
        evento_extra_id=2,
        rota_principal_id=3,
        rota_prevista="SAO-CGR-CBA",
        regional=EventoExtraSolicitacao.Regional.SP,
        prioridade=EventoExtraSolicitacao.Prioridade.BAIXA,
        tipos_assento=["semi leito", "leito cama"],
        ticket_medio_estimado="150.90",
        breakeven_esperado="84.5",
        distancia_por_perna=2230,
        cash_in_gmv=90,
        is_fechado_rotas=True,
        has_precificacao_inicial=True,
        has_precificacao_final=True,
        is_criado_staff=True,
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    EventoExtraSolicitacao(
        id=5,
        evento_extra_id=2,
        rota_principal_id=3,
        rota_prevista="CBA-CGR",
        regional=EventoExtraSolicitacao.Regional.SP,
        prioridade=EventoExtraSolicitacao.Prioridade.ALTA,
        tipos_assento=["executivo", "leito cama"],
        ticket_medio_estimado="120.90",
        breakeven_esperado="60.0",
        distancia_por_perna=725,
        cash_in_gmv=90,
        is_fechado_rotas=False,
        has_precificacao_inicial=False,
        has_precificacao_final=False,
        is_criado_staff=False,
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    ###   EVENTO 3   ###
    EventoExtraSolicitacao(
        id=6,
        evento_extra_id=3,
        rota_principal_id=5,
        rota_prevista="RIO-BHZ-CNF",
        regional=EventoExtraSolicitacao.Regional.MG,
        prioridade=EventoExtraSolicitacao.Prioridade.BAIXA,
        tipos_assento=["semi leito", "leito cama"],
        ticket_medio_estimado="150.90",
        breakeven_esperado="84.5",
        distancia_por_perna=650,
        cash_in_gmv=90,
        is_fechado_rotas=False,
        has_precificacao_inicial=True,
        has_precificacao_final=True,
        is_criado_staff=True,
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
]
negociacao_id_seq = count(10)
evento_solicitacao_map = {1: [1, 2, 3], 2: [4, 5], 3: [6]}
NEGOCIACAO_EVENTO_1 = [
    ###   EVENTO 1   SOLICITACAO 1  ###
    EventoExtraNegociacao(
        id=next(negociacao_id_seq),
        evento_extra_id=1,
        solicitacao_extra_id=_id,
        gerente_comercial=STAFF_USER,
        distancia_total=900,
        deslocamento=0,
        frete_total="12500",
        frete_km="13.88",
        cask="8.50",
        ticket_medio="150.90",
        breakeven="84.5",
        resultado_max="12500",
        tipos_assento=["semi leito", "leito cama"],
        capacidade=42,
        company_id=2143,  # "Granero"
        onibus=ONIBUS_DATA[0],
        is_fechado_comercial=True,
        has_empresa_escalada=True,
        has_contrato_assinado=False,
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    )
    for _id in evento_solicitacao_map[1]
]

NEGOCIACAO_EVENTO_2 = [
    ###   EVENTO 2   ###
    EventoExtraNegociacao(
        id=next(negociacao_id_seq),
        evento_extra_id=2,
        solicitacao_extra_id=_id,
        gerente_comercial=STAFF_USER,
        distancia_total=900,
        deslocamento=0,
        frete_total="12500",
        frete_km="13.88",
        cask="8.50",
        ticket_medio="150.90",
        breakeven="84.5",
        resultado_max="12500",
        tipos_assento=["semi leito", "leito cama"],
        capacidade=42,
        company_id=2143,  # "Granero"
        onibus=ONIBUS_DATA[0],
        is_fechado_comercial=True,
        has_empresa_escalada=False,
        has_contrato_assinado=True,
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    )
    for _id in evento_solicitacao_map[2]
]
NEGOCIACAO_EXTRA_DATA = NEGOCIACAO_EVENTO_1 + NEGOCIACAO_EVENTO_2

perna_id_seq = count(10)
PERNA_DATA = [
    #   SOLICITACAO 1   ###
    EventoExtraSolicitacaoPerna(
        id=next(perna_id_seq),
        solicitacao_extra_id=1,
        sentido="RIO-SAO",
        turno=EventoExtraSolicitacaoPerna.Turno.MANHA,
        rota_id=15282,
        data="2025-06-21",
        hora="08:00",
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    EventoExtraSolicitacaoPerna(
        id=next(perna_id_seq),
        solicitacao_extra_id=1,
        sentido="SAO-RIO",
        turno=EventoExtraSolicitacaoPerna.Turno.NOITE,
        rota_id=14592,
        data="2025-06-21",
        hora="22:00",
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    EventoExtraSolicitacaoPerna(
        id=next(perna_id_seq),
        solicitacao_extra_id=1,
        sentido="RIO-SAO",
        turno=EventoExtraSolicitacaoPerna.Turno.MANHA,
        rota_id=15282,
        data="2025-06-22",
        hora="08:00",
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    EventoExtraSolicitacaoPerna(
        id=next(perna_id_seq),
        solicitacao_extra_id=1,
        sentido="SAO-RIO",
        turno=EventoExtraSolicitacaoPerna.Turno.NOITE,
        rota_id=14592,
        data="2025-06-22",
        hora="22:00",
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    EventoExtraSolicitacaoPerna(
        id=next(perna_id_seq),
        solicitacao_extra_id=1,
        sentido="RIO-SAO",
        turno=EventoExtraSolicitacaoPerna.Turno.MANHA,
        rota_id=15282,
        data="2025-06-23",
        hora="08:00",
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    EventoExtraSolicitacaoPerna(
        id=next(perna_id_seq),
        solicitacao_extra_id=1,
        sentido="SAO-RIO",
        turno=EventoExtraSolicitacaoPerna.Turno.NOITE,
        rota_id=14592,
        data="2025-06-23",
        hora="22:00",
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    #   SOLICITACAO 4   TZ DIFERENTE###
    # DIA 17 | Ambos com Rota
    EventoExtraSolicitacaoPerna(
        id=next(perna_id_seq),
        solicitacao_extra_id=4,
        sentido="SAO-CBA",
        turno=EventoExtraSolicitacaoPerna.Turno.MANHA,
        rota_id=118012,
        data="2025-06-17",
        hora="06:00",
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    EventoExtraSolicitacaoPerna(
        id=next(perna_id_seq),
        solicitacao_extra_id=4,
        sentido="CBA-SAO",
        turno=EventoExtraSolicitacaoPerna.Turno.NOITE,
        rota_id=118203,
        data="2025-06-17",
        hora="22:00",
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    # DIA 18 | Ida sem Rota Tz -03:00
    EventoExtraSolicitacaoPerna(
        id=next(perna_id_seq),
        solicitacao_extra_id=4,
        sentido="SAO-CBA",
        turno=EventoExtraSolicitacaoPerna.Turno.MANHA,
        data="2025-06-18",
        hora="06:00",
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    EventoExtraSolicitacaoPerna(
        id=next(perna_id_seq),
        solicitacao_extra_id=4,
        sentido="CBA-SAO",
        turno=EventoExtraSolicitacaoPerna.Turno.NOITE,
        rota_id=118203,
        data="2025-06-18",
        hora="22:00",
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    # DIA 19 | Volta sem Rota Tz -04:00
    EventoExtraSolicitacaoPerna(
        id=next(perna_id_seq),
        solicitacao_extra_id=4,
        sentido="SAO-CBA",
        turno=EventoExtraSolicitacaoPerna.Turno.MANHA,
        rota_id=118012,
        data="2025-06-19",
        hora="06:00",
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    EventoExtraSolicitacaoPerna(
        id=next(perna_id_seq),
        solicitacao_extra_id=4,
        sentido="CBA-SAO",
        turno=EventoExtraSolicitacaoPerna.Turno.NOITE,
        data="2025-06-19",
        hora="22:00",
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    # DIA 20 | Ambos SEM Rota
    EventoExtraSolicitacaoPerna(
        id=next(perna_id_seq),
        solicitacao_extra_id=4,
        sentido="SAO-CBA",
        turno=EventoExtraSolicitacaoPerna.Turno.MANHA,
        data="2025-06-20",
        hora="06:00",
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
    EventoExtraSolicitacaoPerna(
        id=next(perna_id_seq),
        solicitacao_extra_id=4,
        sentido="CBA-SAO",
        turno=EventoExtraSolicitacaoPerna.Turno.NOITE,
        data="2025-06-20",
        hora="22:00",
        updated_by=ADMIN_FODAO,
        created_at="2025-05-01T00:00:00Z",
    ),
]

EVENTO_EXTRA_DATA = EVENTOS_EXTRA + SOLICITACAO_EXTRA_DATA + PERNA_DATA + NEGOCIACAO_EXTRA_DATA
