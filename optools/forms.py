from collections import OrderedDict, defaultdict
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from typing import Any

from ortools.sat.python import cp_model
from pydantic import BaseModel, ConfigDict, Field, field_validator, model_validator

from core.service.remanejamento.commons.cost_calculator import CustosRemanejamento
from optools.errors import DataInconsistency


class SolverBooking(BaseModel):
    """Booking é travel do Buser Django com esteroides

    Args:
        id (int): id do booking. Relação de booking (solver) e travel (django) é 1 para 1
        count_seats (int): quantidade de assentos reservados
        trip_id_inicial (int): id do trecho classe escolhido pela travel ou seja id da trip correspondente do booking
        remanejada (int): 0 = não remanejada, 1 = remanejada
        remanejamento (dict): dicionario com o mapeamento entre o id do trecho classe final do remanejamento
        e o custo de cada remanejamento. O número de elementos nesse dicionário deve ser igual ao número de
        trechos classes disponíveis no cenário mesmo que seja um trecho classe proibido para a travel.
        receita (Decimal): receita trazida pelas reservas da travel
        receita_agregada (Decimal): receita total trazida pelas reservas das travels de ida e volta e considerando conexões
        dummy (1): 0 = não dummy, 1 = dummy
    """

    model_config = ConfigDict(populate_by_name=True, arbitrary_types_allowed=True)

    id: int = Field(alias="travel_id")
    count_seats: int
    trecho_classe_id: int = Field(alias="trecho_classe_id_inicial")
    remanejada: int | cp_model.IntVar | None = 0
    remanejamento: dict[int, Decimal] = {}
    max_split_value: Decimal | None = None
    gmv: Decimal | None = None
    receita: Decimal
    receita_agregada: Decimal
    cancelada: int | cp_model.IntVar | None = 0
    dummy: bool

    @model_validator(mode="before")
    @classmethod
    def calcula_receita(cls, values: Any):
        if isinstance(values, dict):
            receita = values.get("receita")
            gmv = values.get("gmv")
            count_seats = values.get("count_seats")
            max_split_value = values.get("max_split_value")
            if receita is None and max_split_value is not None and count_seats is not None:
                values["receita"] = Decimal(max_split_value * count_seats)
            if gmv is None:
                values["gmv"] = values["receita"]
        return values

    @field_validator("remanejamento", mode="after")
    @classmethod
    def ordena_e_filtra_remanejamentos_possiveis(cls, value: dict[int, Decimal]) -> dict[int, Decimal]:
        """Ordena os remanejamentos possíveis e retorna apenas
        a quantidade de remanejamentos possíveis definidos no threshold"""
        threshold = 10
        ordered = OrderedDict(sorted(value.items(), key=lambda item: item[1])[:threshold])
        return ordered


class SolverTrip(BaseModel):
    """Trip é trecho classe com esteroides

    Args:
        id (int): id da trip. Relação de trip (solver) e trecho classe (django) é 1 para 1
        confirmed (int): status da trip. 0 = não confirmada, 1 = confirmada
        chartered_id (int): id do chartered. Chartered ao qual a trip pertence.
        preco_atual (Decimal): preço atual do trecho classe
        tipo_assento (str): classe do grupo classe correspondente ao trecho classe em questão
        capacidade (int): capacidade do trecho classe
        forecast (float): previsão de pax futuros para a trip
        vagas (int): vagas atuais do trecho classe. Na prática as vagas do trecho classe na Buser pode ser
        alterada por um assento ocupado em outro trecho classe que pertença ao mesmo grupo classe.
        max_split_value (Decimal): max_split_value do trecho classe
    """

    model_config = ConfigDict(populate_by_name=True, arbitrary_types_allowed=True)

    id: int = Field(alias="trecho_classe_id")
    confirmed: int | cp_model.IntVar | None = 1
    chartered_id: int = Field(alias="grupo_id")
    preco_atual: Decimal
    max_split_value: Decimal
    capacidade: int
    forecast: int | None
    tipo_assento: str
    sobrepostos: list[int]
    vagas: int

    def __hash__(self) -> int:
        return self.id.__hash__()


## Chartered é grupo com esteroides
class SolverChartered(BaseModel):
    """Chartered é grupo com esteroides

    Args:
        id (int): id do chartered. Relação de chartered (solver) e grupo (django) é 1 para 1
        eixo (str): eixo da rota principal do grupo
        custo_assento (int): custo de cada assento do grupo
        confirmed (int): status do grupo. 0 = não confirmado, 1 = confirmado
        chartered_volta_id (int): id do chartered de volta
        solver_threshold (int | Decimal): critério de confirmação necessária do grupo. Quanto menor, mais fácil de confirmar
        capacity (int): capacidade do grupo. Soma das capacidades dos grupos classe. O número de passageiros
            em cada trecho do grupo deve ser menor ou igual que essa capacidade. Mas o número de passageiros que
            viajaram nesse grupo pode ser maior que essa capacidade uma vez que um assento pode ser ocupado por
            diferentes passageiros em diferentes trechos.
        status (str): status do grupo no momento da simulação usando o solver.
            ["travel_confirmed", "pending", "done", "canceled"]
        closed_by_solver (bool): flag que indica se o grupo foi fechado pelo solver
        forecast (int): forecast do grupo. Indica quantas pessoas vão entrar nesse grupo, considerando que uma pessoa
            ocupa somente um assento disponível.
        company_id (int): id da empresa associada ao grupo
        rotina_onibus_id (int): id da rotina do grupo
        rotina_par_id (int): id da rotina par (no sentido contrário)
        gmv_real (decimal): gmv do grupo calculado a partir dos pax que já compraram trechos deste grupo
        gmv_previsto (decimal): gmv do grupo calculado considerando os passageiros que ainda vão entrar
        fora_da_janela (bool): flag que indica se o grupo está fora da janela original de grupos, não aplicando a solução
        receita_agregada (decimal): receita agregada do grupo a partir de suas travels considerando idas, voltas, conexões e addons
        cancelado_fora_da_base (bool): indica se o chartered foi cancelado fora da base.
    """

    model_config = ConfigDict(populate_by_name=True, arbitrary_types_allowed=True)

    id: int = Field(alias="grupo_id")
    eixo: str
    custo_assento: Decimal | None = Decimal("0")
    frete: Decimal | None = None
    confirmed: int | cp_model.IntVar = 1
    chartered_volta_id: int | None = Field(default=None, alias="grupo_volta_id")
    rotina_par_id: int | None = Field(default=None, alias="grupo_ida_rotina_par_id")
    solver_threshold: float | Decimal | None = None
    capacity: int
    status: str | None = None
    closed_by_solver: bool = False
    company_id: int | None = None
    rotina_onibus_id: int
    receita_real: Decimal | None = None
    receita_prevista: Decimal | None = None
    receita_agregada: Decimal | None = None
    gmv_real: Decimal | None = None
    gmv_previsto: Decimal | None = None
    fora_da_janela: bool = False
    cancelado_fora_da_base: int | cp_model.IntVar = 0
    cancelou_cruzado: bool | cp_model.IntVar = False
    pessoas: int | None = None
    pessoas_adicionadas: int | None = None
    pessoas_remanejadas: int | None = None
    pessoas_canceladas: int | None = None
    dummies: int | None = None
    dummies_adicionados: int | None = None
    dummies_remanejados: int | None = None
    dummies_cancelados: int | None = None

    @model_validator(mode="after")
    def calculate_frete_if_none(self) -> "SolverChartered":
        if self.frete is None and self.custo_assento is not None and self.capacity is not None:
            self.frete = self.custo_assento * Decimal(self.capacity)
        return self


class SolverInputForm(BaseModel):
    grupos: list[SolverChartered]
    travels: list[SolverBooking]
    trechos: list[SolverTrip]
    custo_remanejar: dict[int, list[tuple[int, CustosRemanejamento]]] | None = None


@dataclass
class CancelarGruposForm:
    """
    Args:
        grupo_id: id do grupo
        company_id: id da empresa no grupo
        is_ida: se é grupo ida
        ida: id do grupo ida
        volta: id do grupo volta
        capacidade: capacidade do grupo
        pessoas: quantidade de pessoas no grupo
        pessoas_canceladas: quantidade pessoas canceladas
        pessoas_remanejadas: quantidade pessoas remanejadas
        pessoas_adicionadas: quantidade pessoas adicionadas
        dummies: quantidade de dummies
        dummies_cancelados: quantidade de dummies cancelados
        dummies_remanejados: quantidade de dummies remanejados
        dummies_adicionados: quantidade de dummies adicionados
        cancelar: decisão de cancelar grupo
        cancelar_ida: decisão cancelar grupo de ida
        cancelar_volta: decisão cancelar grupo de volta
    """

    grupo_id: int
    company_id: int | None
    is_ida: bool | None
    ida: int | None
    volta: int | None
    company_id: int | None
    is_ida: bool | None
    ida: int | None
    volta: int | None
    capacidade: int
    pessoas: int | None
    pessoas_canceladas: int | None
    pessoas_remanejadas: int | None
    pessoas_adicionadas: int | None
    dummies: int | None
    dummies_cancelados: int | None
    dummies_remanejados: int | None
    dummies_adicionados: int | None
    pessoas: int | None
    pessoas_canceladas: int | None
    pessoas_remanejadas: int | None
    pessoas_adicionadas: int | None
    dummies: int | None
    dummies_cancelados: int | None
    dummies_remanejados: int | None
    dummies_adicionados: int | None
    cancelar: bool
    cancelar_ida: bool | None
    cancelar_volta: bool | None
    gmv_previsto: Decimal | None
    gmv_previsto_otimizado: Decimal | None
    receita_agregada_otimizada: Decimal | None
    frete: Decimal | None
    cancelar_ida: bool | None
    cancelar_volta: bool | None
    gmv_previsto: Decimal | None
    frete: Decimal | None
    fora_da_janela: bool | None


class RemanejamentoSolverModel:
    """Modelo de remanejamento

    Args:
        solver_chartereds: lista dos grupos que serão avaliados para remanejamento
        solver_bookings: lista das travels que serão avaliados para remanejamento
        solver_trips: lista dos trechos que serão avaliados para remanejamento
    """

    def __init__(self):
        self.model = cp_model.CpModel()
        self.all_bookings: dict[int, SolverBooking] = {}
        self.all_trips: dict[int, SolverTrip] = {}
        self.all_chartereds: dict[int, SolverChartered] = {}
        self.map_trip_chartered: dict[int, int] = {}
        self._min_max_split: float = -1.0

    def validate_input(self, solver_input_form: SolverInputForm):
        self._check_input_grupos_vazio(solver_input_form.grupos)
        self._check_input_travels_vazio(solver_input_form.travels)
        self._check_input_trechos_vazio(solver_input_form.trechos)

    def _check_input_grupos_vazio(self, solver_chartereds: list[SolverChartered]):
        if len(solver_chartereds) == 0:
            raise DataInconsistency("DATA_ERROR", "Lista de grupos vazia")

    def _check_input_travels_vazio(self, solver_bookings: list[SolverBooking]):
        if len(solver_bookings) == 0:
            raise DataInconsistency("DATA_ERROR", "Lista de travels vazia")

    def _check_input_trechos_vazio(self, solver_trips: list[SolverTrip]):
        if len(solver_trips) == 0:
            raise DataInconsistency("DATA_ERROR", "Lista de trechos vazia")

    def init_map_trip_chartered(self, solver_trips: list[SolverTrip]):
        """Cria um mapa entre trecho_classe_id e grupo_id map_trip_chartered[trecho_classe_id] = grupo_id"""
        self.map_trip_chartered = {trip.id: trip.chartered_id for trip in solver_trips}

    def adds_boolean_variables(
        self,
        solver_chartereds: list[SolverChartered] | None,
        solver_bookings: list[SolverBooking] | None,
        solver_trips: list[SolverTrip] | None,
    ) -> None:
        if solver_chartereds is not None:
            for chartered in solver_chartereds:
                chartered.confirmed = self.model.new_bool_var(f"status_chartered_{chartered.id}")
                self.all_chartereds[chartered.id] = chartered

        if solver_bookings is not None:
            for booking in solver_bookings:
                booking.remanejada = self.model.new_bool_var(f"remanejada_travel_{booking.id}")
                booking.cancelada = self.model.new_bool_var(f"cancelada_travel_{booking.id}")
                self.all_bookings[booking.id] = booking

        if solver_trips is not None:
            for trip in solver_trips:
                trip.confirmed = self.model.new_bool_var(f"status_trip_{trip.id}")
                self.all_trips[trip.id] = trip

    def init_remanejamento_matrix(self):
        """
        Inicializa a matriz de remanejamento.
        Indica trip final de cada booking.
        Caso o booking não possua nenhuma trip equivalente como True então o booking é considerado cancelado.
        Emula-se uma matriz a partir de um dicionário com íncides (booking.id, trip.id).
        Os valores da matriz são variáveis de decisão booleanas.
        """
        self.remanejamento_matrix = {}
        self.trip_remanejamento_matrix = defaultdict(int)
        booking: SolverBooking
        for booking in self.all_bookings.values():
            for trip_id in booking.remanejamento.keys():
                self.remanejamento_matrix[(booking.id, trip_id)] = self.model.new_bool_var(f"{booking.id}_{trip_id}")
                self.trip_remanejamento_matrix[trip_id] += (
                    booking.count_seats * self.remanejamento_matrix[(booking.id, trip_id)]
                )

    @classmethod
    def from_solver_input_form(cls, solver_input_form: SolverInputForm):
        solver_model = cls()
        solver_model.validate_input(solver_input_form)
        solver_model.init_map_trip_chartered(solver_input_form.trechos)
        solver_model.adds_boolean_variables(
            solver_input_form.grupos, solver_input_form.travels, solver_input_form.trechos
        )
        solver_model.init_remanejamento_matrix()
        return solver_model


@dataclass(frozen=True)
class RemanejamentoSolverSolutionForm:
    """
    Formulário com o schema dos dados de saída do solver.
    Args:
        objective: retorno financeiro do grupo dado pela função objetivo
        solver_time: tempo que o solver precisou para encontrar a solução
        solver_status: tipo da solução encontrada (OPTIMAL, FEASIBLE)
        receita_travels_mantidas: total de receita gerada pelas travels reais. Valor positivo
        receita_dummies_cancelados: total de receita perdida pelos dummies. Valor positivo
        custo_cancelamento: custo total com cancelamento de travels
        custo_assento_vazio: custo total com assentos vazios
        custo_frete: custo total com fretes
        custo_remanejar: custo total com os remanejamentos realizados
        total_pessoas: quantidade de pessoas confirmadas
        total_dummies: quantidade de pessoas estimadas (usando forecast) em grupos confirmados
        total_grupos: quantidade de grupos confirmados
        grupos_cancelados: quantidade de grupos cancelados
        occ_media_antes: ocupação média dos grupos antes do Solver
        occ_media_depois: ocupação média dos grupos depois do Solver
        pessoas_remanejadas: quantidade de pessoas remanejadas
        pessoas_canceladas: quantidade de pessoas canceladas
        cancelar_grupos: lista de grupos a serem cancelados
        remanejar_travels: mapa entre travels e o destino de remanejamento
        total_dummies_cancelados: quantidade de dummies cancelados
        total_dummies_remanejados: quantidade de dummies remanejados
    """

    objective: float
    solver_time: float
    solver_status: str
    receita_forecast: Decimal
    receita_travels_mantidas: Decimal
    receita_pessoas_canceladas: Decimal
    receita_dummies_cancelados: Decimal
    custo_cancelamento: Decimal
    custo_assento_vazio: Decimal
    custo_remanejar: Decimal
    custo_frete: Decimal
    economia_frete: Decimal
    total_pessoas: int
    total_dummies: int
    total_grupos: int
    grupos_cancelados: int
    grupos_cancelados_com_empresa: int
    grupos_cancelados_sem_empresa: int
    occ_media_antes: float
    occ_media_depois: float
    pessoas_remanejadas: int
    pessoas_canceladas: int
    total_dummies_cancelados: int
    total_dummies_remanejados: int
    cancelar_grupos: list[CancelarGruposForm]
    remanejar_travels: list[dict[str, Any]]


@dataclass
class SolverGrupoLog:
    eixo: str
    grupo_id: int
    rotina_onibus_id: int
    grupo_confirmado: bool
    pessoas_trechoclasse: dict
    forecast_trechoclasse: dict
    pessoas_total: int
    pessoas_remanejadas: int
    pessoas_adicionadas: int
    pessoas_canceladas: int
    dummies_total: int
    dummies_remanejados: int
    dummies_adicionados: int
    dummies_cancelados: int
    gmv_real: Decimal
    gmv_previsto: Decimal
    solver_threshold: Decimal
    valor_frete: Decimal
    additional_data: dict
    solver_strategy: str | None = ""


@dataclass(frozen=True)
class SolverParams:
    cancelamento_fator: float
    dummie_fator: float
    remanejamento_fator: float
    remanejamento_dummy_fator: float
    burn_pax_fator: float = 18
    burn_dummy_fator: float = 18
    start_datetime: datetime | None = None
    end_datetime: datetime | None = None
    dry_run: bool = True
