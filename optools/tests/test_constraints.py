from ortools.sat.python import cp_model
from ortools.sat.python.cp_model import CpSolver

from optools.constraints import (
    adds_constraint_cancelamento_cruzado,
    adds_constraint_grupo_trips_status,
    adds_constraint_not_all_canceled_groups,
    adds_constraint_remanejamentos,
    adds_constraint_travel_trecho_classe,
    adds_constraint_trechoclasse_pax,
)
from optools.forms import RemanejamentoSolverModel
from optools.utils.solution_collector import SolutionCollector


def test_solutions_travel_trecho_classe(cenario_0_sem_dummies):
    """
    Cada travel pertence a, no máximo, um trecho classe final possível.
    Caso não pertença a nenhum trecho classe considera-se travel cancelada
    """
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_0_sem_dummies)

    adds_constraint_travel_trecho_classe(solver_model)

    solver = CpSolver()
    solver.parameters.enumerate_all_solutions = True
    solution_collector = SolutionCollector(
        remanejamento_form=solver_model,
        solution_limit=1000,  # TODO: É possível checar todas as soluções ?
    )
    status = solver.Solve(solver_model.model, solution_collector)
    assert status in [cp_model.OPTIMAL, cp_model.FEASIBLE]


def test_status_constraint_travel_trecho_classe(cenario_0_sem_dummies):
    """
    Cada travel pertence a, no máximo, um trecho classe final possível.
    Caso não pertença a nenhum trecho classe considera-se travel cancelada
    """
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_0_sem_dummies)
    adds_constraint_travel_trecho_classe(solver_model)

    ## Força travel pertencendo a mais de um trecho classe final
    booking = solver_model.all_bookings[0]
    trechos_classes_finais_possiveis = [trip_id for trip_id, custo_remanejar in booking.remanejamento.items()]
    solver_model.model.add(solver_model.remanejamento_matrix[(booking.id, trechos_classes_finais_possiveis[1])] == 1)
    solver_model.model.add(
        solver_model.remanejamento_matrix[(booking.id, trechos_classes_finais_possiveis[1] + 1)] == 1
    )

    solver = CpSolver()
    solver.parameters.enumerate_all_solutions = True
    solution_collector = SolutionCollector(remanejamento_form=solver_model, check_feasible=True)
    status = solver.Solve(solver_model.model, solution_collector)
    assert status == cp_model.INFEASIBLE


def test_solutions_constraint_trechoclasse_capacidade(cenario_0_sem_dummies):
    """
    A soma de pax no trecho classe é menor ou igual a sua capacidade
    """
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_0_sem_dummies)

    adds_constraint_trechoclasse_pax(solver_model)

    solver = CpSolver()
    solver.parameters.enumerate_all_solutions = True
    solution_collector = SolutionCollector(
        remanejamento_form=solver_model,
        solution_limit=1000,  # TODO: É possível checar todas as soluções ?
    )
    status = solver.Solve(solver_model.model, solution_collector)
    assert status in [cp_model.OPTIMAL, cp_model.FEASIBLE]


def test_status_constraint_trechoclasse_capacidade(cenario_0_sem_dummies):
    """
    A soma de pax no trecho classe é menor ou igual a sua capacidade
    """
    # Altera cenário 0 lotando trecho classe 0
    cenario_0_sem_dummies.travels[0].count_seats = 40

    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_0_sem_dummies)

    adds_constraint_trechoclasse_pax(solver_model)

    ## Força confirmação de travel para lotar o trecho classe 0
    solver_model.model.add(solver_model.remanejamento_matrix[(solver_model.all_bookings[0].id, 0)] == 1)
    ## Força remanejamento de travel para o trecho classe lotado
    solver_model.model.add(solver_model.remanejamento_matrix[(solver_model.all_bookings[2].id, 0)] == 1)

    solver = CpSolver()
    solver.parameters.enumerate_all_solutions = True
    solution_collector = SolutionCollector(remanejamento_form=solver_model, check_feasible=True)
    status = solver.Solve(solver_model.model, solution_collector)
    assert status == cp_model.INFEASIBLE


def test_solutions_constraint_fretado_trips_status(cenario_0_sem_dummies):
    """
    Se o status do fretado é confirmado => Todas as trips do fretado são confirmadas
    Se o status do fretado é cancelado => Todas as trips do fretado são canceladas
    """
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_0_sem_dummies)

    adds_constraint_grupo_trips_status(solver_model)

    solver = CpSolver()
    solver.parameters.enumerate_all_solutions = True
    solution_collector = SolutionCollector(
        remanejamento_form=solver_model,
        solution_limit=1000,  # TODO: É possível checar todas as soluções ?
    )
    status = solver.Solve(solver_model.model, solution_collector)
    assert status in [cp_model.OPTIMAL, cp_model.FEASIBLE]


def test_solutions_adds_constraint_grupo_trips_status(cenario_0_sem_dummies):
    """
    Valida todos as soluções possíveis considerando apenas essa constraint
    """
    # dado um cenario onde exista uma trip para cada chartered
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_0_sem_dummies)

    # ao adicionar a contraint de que a trip precisa ter o mesmo status do chartered
    adds_constraint_grupo_trips_status(solver_model)

    solver = CpSolver()
    solver.parameters.enumerate_all_solutions = True
    solution_collector = SolutionCollector(solver_model, get_chartereds=True, get_trips=True)

    # após resolver o modelo
    status = solver.Solve(solver_model.model, solution_collector)
    assert status == cp_model.OPTIMAL

    solution_trips = solution_collector.solution_trips
    solution_chartereds = solution_collector.solution_chartereds

    # trips devem ter o mesmo status do chartered.
    assert (
        solution_trips
        == solution_chartereds
        == {
            ("0", "0", "1", "0"),
            ("0", "0", "0", "1"),
            ("1", "0", "0", "1"),
            ("0", "0", "0", "0"),
            ("1", "0", "0", "0"),
            ("1", "1", "0", "1"),
            ("1", "0", "1", "1"),
            ("1", "1", "0", "0"),
            ("1", "1", "1", "1"),
            ("1", "1", "1", "0"),
            ("0", "1", "1", "1"),
            ("0", "1", "1", "0"),
            ("0", "1", "0", "1"),
            ("1", "0", "1", "0"),
            ("0", "1", "0", "0"),
            ("0", "0", "1", "1"),
        }
    )


def test_status_constraint_fretado_trips_status(cenario_0_sem_dummies):
    """
    Se o status do fretado é confirmado => Todas as trips do fretado são confirmadas
    Se o status do fretado é cancelado => Todas as trips do fretado são canceladas
    """
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_0_sem_dummies)

    adds_constraint_grupo_trips_status(solver_model)

    ## Força confirmação de fretado
    solver_model.model.add(solver_model.all_chartereds[0].confirmed == 1)
    ## Força não confirmação de trip
    solver_model.model.add(solver_model.all_trips[0].confirmed == 0)

    solver = CpSolver()
    solver.parameters.enumerate_all_solutions = True
    solution_collector = SolutionCollector(remanejamento_form=solver_model, check_feasible=True)
    status = solver.Solve(solver_model.model, solution_collector)
    assert status == cp_model.INFEASIBLE

    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_0_sem_dummies)

    adds_constraint_grupo_trips_status(solver_model)

    ## Força não confirmação de fretado
    solver_model.model.add(solver_model.all_chartereds[0].confirmed == 0)
    ## Força confirmação de trip
    solver_model.model.add(solver_model.all_trips[0].confirmed == 1)

    solver = CpSolver()
    solver.parameters.enumerate_all_solutions = True
    solution_collector = SolutionCollector(remanejamento_form=solver_model, check_feasible=True)
    status = solver.Solve(solver_model.model, solution_collector)
    assert status == cp_model.INFEASIBLE


def test_solutions_constraint_not_all_canceled_groups(cenario_0_sem_dummies):  # TODO: testar com mais de um cenário
    """
    Não é permitido cancelar todos os grupos
    """
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_0_sem_dummies)

    adds_constraint_not_all_canceled_groups(solver_model)

    solver = CpSolver()
    solver.parameters.enumerate_all_solutions = True
    solution_collector = SolutionCollector(
        remanejamento_form=solver_model,
        solution_limit=1000,  # TODO: É possível checar todas as soluções ?
    )
    status = solver.Solve(solver_model.model, solution_collector)
    assert status in [cp_model.OPTIMAL, cp_model.FEASIBLE]


def test_status_constraint_not_all_canceled_groups(cenario_0_sem_dummies):
    """
    Não é permitido cancelar todos os grupos
    """
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_0_sem_dummies)
    adds_constraint_not_all_canceled_groups(solver_model)

    ## Força cancelamento de todos os grupos
    for _, chartered in solver_model.all_chartereds.items():
        solver_model.model.add(chartered.confirmed == 0)

    solver = CpSolver()
    solver.parameters.enumerate_all_solutions = True
    solution_collector = SolutionCollector(remanejamento_form=solver_model, check_feasible=True)
    status = solver.Solve(solver_model.model, solution_collector)
    assert status == cp_model.INFEASIBLE


def test_constraint_cancelamento_cruzado(
    cenario_9_rotina_frequencia_diaria_sem_dummies_grupos_ida,
):
    # dado um cenário onde há duas rotinas, sendo rotinas pares. Com grupos em sentidos
    # opostos nas mesmas datas
    cenario = cenario_9_rotina_frequencia_diaria_sem_dummies_grupos_ida
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario)

    # considerando as restrições para cancelamento cruzado
    adds_constraint_cancelamento_cruzado(solver_model)

    solver = CpSolver()
    solver.parameters.enumerate_all_solutions = True
    solution_collector = SolutionCollector(
        remanejamento_form=solver_model,
        solution_limit=1000,  # TODO: É possível checar todas as soluções ?
    )
    # espero que o solver consiga chegar a uma solução viável.
    status = solver.Solve(solver_model.model, solution_collector)
    assert status in [cp_model.OPTIMAL, cp_model.FEASIBLE]


def test_constraint_remanejamentos(cenario_constraint_remanejamentos):
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_constraint_remanejamentos)

    chartered = cenario_constraint_remanejamentos.grupos[0]
    solver_model.model.add(chartered.confirmed == 0)

    adds_constraint_remanejamentos(solver_model)

    solver = CpSolver()
    solver.parameters.enumerate_all_solutions = True
    solution_collector = SolutionCollector(solver_model, get_chartereds=True, get_bookings=True)

    status = solver.Solve(solver_model.model, solution_collector)
    solution_bookings = solution_collector.solution_bookings_map.values()

    # como o grupo é cancelado, é esperado que a travel seja cancelada OU remanejada, nunca os dois
    assert status == cp_model.OPTIMAL
    assert {"remanejada": 0, "cancelada": 0} not in solution_bookings
    assert {"remanejada": 1, "cancelada": 1} not in solution_bookings


def test_constraint_remanejamentos_fora_da_janela(cenario_remanejamento):
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_remanejamento)

    chartered_ida_pendente, chartered_volta_pendente = cenario_remanejamento.grupos[2], cenario_remanejamento.grupos[3]
    chartered_ida_pendente.fora_da_janela = True
    chartered_volta_pendente.fora_da_janela = True

    adds_constraint_remanejamentos(solver_model)

    solver = CpSolver()
    solver.parameters.enumerate_all_solutions = True
    solution_collector = SolutionCollector(solver_model, get_chartereds=True, get_bookings=True)

    status = solver.Solve(solver_model.model, solution_collector)

    # como o par de grupos pendentes está fora da janela, suas travels não são remanejadas nem canceladas
    assert status == cp_model.OPTIMAL
    assert solution_collector.solution_bookings_remanejadas == solution_collector.solution_bookings_canceladas
    assert solution_collector.solution_bookings_remanejadas == {
        # travels do par confirmado e travels do par pendente fora da janela com um dummy cada
        ("1", "1", "0", "0", "0", "0"),
        ("1", "0", "0", "0", "0", "0"),
        ("0", "1", "0", "0", "0", "0"),
        ("0", "0", "0", "0", "0", "0"),
    }
