import logging
from collections import defaultdict
from typing import cast

from ortools.sat.python.cp_model import IntVar

from optools.forms import RemanejamentoSolverModel, SolverBooking, SolverChartered, SolverTrip

buserlogger = logging.getLogger("buserlogger")


def adds_all_constraints(solver_model: RemanejamentoSolverModel, allow_experimental: bool) -> None:
    adds_constraint_status_grupos(solver_model)

    if allow_experimental:
        adds_constraint_cancelamento_cruzado(solver_model)
        adds_constraint_prioriza_travels_reais(solver_model)
    else:
        adds_constraint_grupo_ida_volta(solver_model)

    adds_constraint_remanejamentos(solver_model)
    adds_constraint_travel_trecho_classe(solver_model)
    adds_constraint_trechoclasse_pax(solver_model)
    adds_constraint_grupo_trips_status(solver_model)


def adds_constraint_grupo_ida_volta(solver_model: RemanejamentoSolverModel):
    """
    Grupos ida e volta tem que ter o mesmo status de confirmado em pares.
    Grupo de ida só pode ser confirmado se o grupo de volta for confirmado.
    Grupo de volta só pode ser confirmado se o grupo de ida for confirmado.
    """
    chartered_ida: SolverChartered
    chartered_volta: SolverChartered
    for chartered_ida in solver_model.all_chartereds.values():
        if chartered_ida.chartered_volta_id is None:
            continue
        chartered_volta = solver_model.all_chartereds[chartered_ida.chartered_volta_id]
        solver_model.model.add(chartered_ida.confirmed == chartered_volta.confirmed)


def adds_constraint_cancelamento_cruzado(solver_model: RemanejamentoSolverModel):
    """
    Adiciona restrições para chartereds de ida e volta e cenários de cancelamento cruzado.

        A função trata dois cenários principais:
        1. Chartereds de ida e volta simples: Chartereds de ida e volta devem ter o mesmo status quando
           não há possibilidade de cancelamento cruzado
        2. Cancelamento cruzado entre duas rotinas que operam em direções opostas

        Args:
            solver_model: Objeto RemanejamentoSolverModel contendo o modelo e dados dos chartereds

        Exemplo de cenário com cancelamento cruzado entre duas rotinas:
            Rotina 1:
                G1: A->B dia 1 (ida)
                G2: B->A dia 2 (volta)
            Rotina 2:
                G3: B->A dia 1 (ida)
                G4: A->B dia 2 (volta)

        Cenários permitidos (G1 G2 G3 G4):
            0 0 1 1: Rotina 1 cancelada, Rotina 2 confirmada
            1 1 0 0: Rotina 1 confirmada, Rotina 2 cancelada
            1 1 1 1: Todos chartereds confirmados
            0 0 0 0: Todos chartereds cancelados
            0 1 0 1: Cancelamento cruzado - cancela idas, confirma voltas

        Notas de implementação:
            - Para chartereds sem rotina par, força mesmo status entre ida e volta
            - Para chartereds com rotina par, permite cancelamento cruzado
            - Cancelamento cruzado só é permitido entre idas (na base)

        TODOs:
            - Adicionar restrições para troca de empresa/ônibus nos chartereds futuros
              após cancelamento cruzado
            - Implementar tratamento de upgrade/downgrade quando rotinas têm classes diferentes
            - Adicionar validações para garantir que chartereds pareados sejam da mesma classe
    """
    rot_1_chart_ida: SolverChartered
    rot_1_chart_volta: SolverChartered

    chartereds_rotina_par = set()

    for rot_1_chart_ida in solver_model.all_chartereds.values():
        rot_1_chart_volta_id = rot_1_chart_ida.chartered_volta_id

        # não faz nada se nao houver chartered da volta ou se é o chartered volta
        if rot_1_chart_volta_id is None or rot_1_chart_volta_id == rot_1_chart_ida.id:
            continue

        rot_1_chart_volta = solver_model.all_chartereds[rot_1_chart_volta_id]
        # restrição presente apenas em chartereds que não podem ter cancelamento cruzado
        solver_model.model.add(rot_1_chart_ida.confirmed == rot_1_chart_volta.confirmed).only_enforce_if(
            bool(rot_1_chart_ida.rotina_par_id is None)
        )

        # se não existe chartered_volta da rotina par, não existe cancelamento cruzado
        if rot_1_chart_ida.rotina_par_id is None:
            continue

        # se for chartered ida da rotina_par, já passou por aqui
        if rot_1_chart_ida.id in chartereds_rotina_par:
            continue

        # chartered no sentido oposto da ida da rotina 1
        rot_2_chart_ida = solver_model.all_chartereds[rot_1_chart_ida.rotina_par_id]
        # chartered no sentido oposto da volta da rotina 1
        if rot_2_chart_ida.chartered_volta_id is None or rot_2_chart_ida.chartered_volta_id == rot_2_chart_ida.id:
            # chartered_volta não pode ser nulo ou igual ao chartered de ida da rotina 2
            extra = {
                "error_msg": "falha mapeamento grupos cancelamento cruzado",
                "grupos_ida_rotina_1": rot_1_chart_ida.id,
                "grupos_volta_rotina_1": rot_1_chart_volta_id,
                "grupos_ida_rotina_2": rot_2_chart_ida.id,
                "grupos_volta_rotina_2": rot_2_chart_ida.chartered_volta_id,
            }
            buserlogger.info("confirmacao_inteligente.cancelamento_cruzado", extra=extra)
            # remove grupos dos logs de cancelamento cruzado
            rot_1_chart_ida.rotina_par_id = None
            rot_1_chart_volta.rotina_par_id = None
            # Se não incluir restrição pra cancelamento cruzado, então garante a consistência do status do par de grupos da rotina 1
            solver_model.model.add(rot_1_chart_ida.confirmed == rot_1_chart_volta.confirmed)
            continue

        rot_2_chart_volta = solver_model.all_chartereds[rot_2_chart_ida.chartered_volta_id]

        # OU confirma e cancela os chartereds pares da rotina ou faz o cancelamento cruzado.

        # Pra analisar essa lógica isoladamente, veja o teste
        # test_simulacao.py::test_solver_cancelamento_cruzado_logical_constraints
        g1 = rot_1_chart_ida
        g2 = rot_1_chart_volta
        g3 = rot_2_chart_ida
        g4 = rot_2_chart_volta

        g1.cancelado_fora_da_base = solver_model.model.new_bool_var(f"chartered_fora_base_{g1.id}")
        g2.cancelado_fora_da_base = solver_model.model.new_bool_var(f"chartered_fora_base_{g2.id}")
        g3.cancelado_fora_da_base = solver_model.model.new_bool_var(f"chartered_fora_base_{g3.id}")
        g4.cancelado_fora_da_base = solver_model.model.new_bool_var(f"chartered_fora_base_{g4.id}")

        g1.confirmed = solver_model.model.new_bool_var(f"chartered_confirmed_{g1.id}")
        g2.confirmed = solver_model.model.new_bool_var(f"chartered_confirmed_{g2.id}")
        g3.confirmed = solver_model.model.new_bool_var(f"chartered_confirmed_{g3.id}")
        g4.confirmed = solver_model.model.new_bool_var(f"chartered_confirmed_{g4.id}")

        g1.cancelou_cruzado = solver_model.model.new_bool_var(f"chartered_confirmed_{g1.id}")
        g2.cancelou_cruzado = solver_model.model.new_bool_var(f"chartered_confirmed_{g2.id}")
        g3.cancelou_cruzado = solver_model.model.new_bool_var(f"chartered_confirmed_{g3.id}")
        g4.cancelou_cruzado = solver_model.model.new_bool_var(f"chartered_confirmed_{g4.id}")

        # primeira condição, cancelamento de grupos ida e volta
        pares_grupo_rotina_1 = solver_model.model.new_bool_var("pares_grupo_rotina_1")
        solver_model.model.add(g1.confirmed == g2.confirmed).only_enforce_if(pares_grupo_rotina_1)
        pares_grupos_rotina_2 = solver_model.model.new_bool_var("pares_grupos_rotina_2")
        solver_model.model.add(g3.confirmed == g4.confirmed).only_enforce_if(pares_grupos_rotina_2)
        cancelamento_ida_e_volta = solver_model.model.new_bool_var("cancelamento_ida_e_volta")
        solver_model.model.add_bool_and([pares_grupo_rotina_1, pares_grupos_rotina_2]).only_enforce_if(
            cancelamento_ida_e_volta
        )

        # segunda condição, cancelamento cruzado
        cruzado_pares_ida_confirmed = solver_model.model.new_bool_var("cruzado_pares_ida_confirmed")
        solver_model.model.add(g1.confirmed == g3.confirmed).only_enforce_if(cruzado_pares_ida_confirmed)
        cruzado_pares_volta_confirmed = solver_model.model.new_bool_var("cruzado_pares_volta_confirmed")
        solver_model.model.add(g2.confirmed == g4.confirmed).only_enforce_if(cruzado_pares_volta_confirmed)
        cancelamento_cruzado = solver_model.model.new_bool_var("cancelamento_cruzado")
        solver_model.model.add_bool_and([cruzado_pares_ida_confirmed, cruzado_pares_volta_confirmed]).only_enforce_if(
            cancelamento_cruzado
        )

        # ou cancela ida e volta ou cancelamento cruzado
        solver_model.model.add_bool_or([cancelamento_ida_e_volta, cancelamento_cruzado])

        # log cancelamento cruzado
        idas_canceladas = solver_model.model.new_bool_var("idas_canceladas")
        # Se g1 e g3 estão ambos cancelados, então idas_canceladas = true
        solver_model.model.add_bool_and([g1.confirmed.Not(), g3.confirmed.Not()]).only_enforce_if(idas_canceladas)
        # Se idas_canceladas = false, então pelo menos um dos g1 ou g3 deve estar confirmado
        # solver_model.model.add_bool_or([g1_confirmed, g3_confirmed]).only_enforce_if(idas_canceladas.Not())

        voltas_canceladas = solver_model.model.new_bool_var("voltas_canceladas")
        # Se g2 e g4 estão ambos cancelados, então voltas_canceladas = true
        solver_model.model.add_bool_and([g2.confirmed.Not(), g4.confirmed.Not()]).only_enforce_if(voltas_canceladas)
        # Se voltas_canceladas = false, então pelo menos um dos g2 ou g4 deve estar confirmado
        # solver_model.model.add_bool_or([g2_confirmed, g4_confirmed]).only_enforce_if(voltas_canceladas.Not())

        # Define quando ocorre o cancelamento cruzado (ambos os grupos ida ou ambos os grupos volta)
        cancelamento_cruzado_detectado = solver_model.model.new_bool_var("cancelamento_cruzado_detectado")
        solver_model.model.add_bool_or([idas_canceladas, voltas_canceladas]).only_enforce_if(
            cancelamento_cruzado_detectado
        )
        # # Se não houver cancelamento cruzado, então nem idas nem voltas estão canceladas
        solver_model.model.add_bool_and([idas_canceladas.Not(), voltas_canceladas.Not()]).only_enforce_if(
            cancelamento_cruzado_detectado.Not()
        )

        # # No cancelamento cruzado, os status de ida e volta devem ser diferentes em ambas rotinas
        solver_model.model.add(g1.confirmed != g2.confirmed).only_enforce_if(cancelamento_cruzado_detectado)
        solver_model.model.add(g3.confirmed != g4.confirmed).only_enforce_if(cancelamento_cruzado_detectado)

        # Define os indicadores de cancelamento para cada grupo
        solver_model.model.add(g1.cancelou_cruzado == idas_canceladas)
        solver_model.model.add(g3.cancelou_cruzado == idas_canceladas)
        solver_model.model.add(g2.cancelou_cruzado == voltas_canceladas)
        solver_model.model.add(g4.cancelou_cruzado == voltas_canceladas)

        ###    CANCELAMENTO FORA DA BASE    ###
        cancelou_fora_base = solver_model.model.new_bool_var("cancelou_fora_base")
        # se confirmou grupos de ida e cancelou grupos volta, então há cancelamento fora da base
        solver_model.model.add_bool_and(
            [g1.confirmed, g3.confirmed, g2.confirmed.Not(), g4.confirmed.Not()]
        ).only_enforce_if(cancelou_fora_base)
        # se houve cancelamento fora da base, marca ambos grupos volta como True

        # Se houver cancelamento fora da base, g2 e g4 possuem custo.
        solver_model.model.add(g2.cancelado_fora_da_base == cancelou_fora_base)
        solver_model.model.add(g4.cancelado_fora_da_base == cancelou_fora_base)
        # proibe o caso que restricoes anteriores não tratam.
        solver_model.model.add_forbidden_assignments(
            [
                g1.confirmed,
                g2.confirmed,
                g3.confirmed,
                g4.confirmed,
                g1.cancelado_fora_da_base,
                g2.cancelado_fora_da_base,
                g3.cancelado_fora_da_base,
                g4.cancelado_fora_da_base,
            ],
            [(1, 0, 1, 0, 0, 0, 0, 0)],
        )
        solver_model.model.add_forbidden_assignments(
            [
                g1.confirmed,
                g2.confirmed,
                g3.confirmed,
                g4.confirmed,
                g1.cancelado_fora_da_base,
                g2.cancelado_fora_da_base,
                g3.cancelado_fora_da_base,
                g4.cancelado_fora_da_base,
                g1.cancelou_cruzado,
                g2.cancelou_cruzado,
                g3.cancelou_cruzado,
                g4.cancelou_cruzado,
            ],
            [(1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0), (0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0)],
        )
        # grupos ida nunca contabilizam custo fora da base
        solver_model.model.add(g1.cancelado_fora_da_base == 0)
        solver_model.model.add(g3.cancelado_fora_da_base == 0)

        ###    REGISTRA DADOS NOS MODELS    ###
        # registra que os chartereds já possuem a restrição
        chartereds_rotina_par.add(rot_1_chart_ida.id)
        chartereds_rotina_par.add(rot_1_chart_ida.rotina_par_id)

        # Os chartereds que possuem rotina_par_id são chartereds com cancelamento cruzado.
        # os grupos de ida já possuem essa info, mas os de volta nao.
        g2.rotina_par_id = g4.id
        g4.rotina_par_id = g2.id


def adds_constraint_travel_trecho_classe(solver_model: RemanejamentoSolverModel):
    """
    Cada travel pertence a, no máximo, um trecho classe final possível.
    Caso não pertença a nenhum trecho classe travel.cancelada == 1
    caso contrário travel.cancelada == 0.
    Caso a travel seja remanejada para um trecho classe permitido então
    travel.remanejada == 1 caso contrário travel.remanejada == 0
    """
    booking: SolverBooking
    for booking in solver_model.all_bookings.values():
        booking.remanejada = cast(IntVar, booking.remanejada)
        booking.cancelada = cast(IntVar, booking.cancelada)
        remanejamentos_acceptables = []
        for trip_id in booking.remanejamento.keys():
            remanejamentos_acceptables.append(solver_model.remanejamento_matrix[(booking.id, trip_id)])

        solver_model.model.add(sum(remanejamentos_acceptables) == 1).only_enforce_if(booking.remanejada)
        solver_model.model.add(sum(remanejamentos_acceptables) == 0).only_enforce_if(booking.remanejada.Not())


def adds_constraint_trechoclasse_pax(solver_model: RemanejamentoSolverModel):
    """
    A soma de pax no trecho classe é menor ou igual a sua capacidade
    A capacidade do trecho classe é tida como a capacidade do grupo classe correspondente.
    Evita-se overbooking mas gera perda de oportunidade de receita.
    Para agilizar a busca, somente força a constraint se o trecho classe for confirmado
    Caso o trecho classe não seja confirmado então não pode haver pax no trecho classe.
    """

    trip: SolverTrip
    for trip in solver_model.all_trips.values():
        pax: int = solver_model.trip_remanejamento_matrix[trip.id]
        for overlap_trip_id in trip.sobrepostos:
            if trip.tipo_assento == solver_model.all_trips[overlap_trip_id].tipo_assento:
                pax += solver_model.trip_remanejamento_matrix[overlap_trip_id]

        trip.confirmed = cast(IntVar, trip.confirmed)
        solver_model.model.add(pax <= max(trip.vagas, 0)).only_enforce_if(trip.confirmed)
        solver_model.model.add(pax == 0).only_enforce_if(trip.confirmed.Not())


def adds_constraint_remanejamentos(solver_model: RemanejamentoSolverModel):
    """
    Nenhum pax de grupo confirmado ou fora da janela pode ser cancelado ou remanejado.
    """
    booking: SolverBooking
    for booking in solver_model.all_bookings.values():
        chartered_id: int = solver_model.map_trip_chartered[booking.trecho_classe_id]
        chartered = solver_model.all_chartereds[chartered_id]

        if chartered.fora_da_janela:
            solver_model.model.add(booking.remanejada == 0)
            solver_model.model.add(booking.cancelada == 0)
        else:
            solver_model.model.add(booking.remanejada == 0).only_enforce_if(chartered.confirmed)
            solver_model.model.add(booking.cancelada == 0).only_enforce_if(chartered.confirmed)

        chartered.confirmed = cast(IntVar, chartered.confirmed)
        booking.cancelada = cast(IntVar, booking.cancelada)
        booking.remanejada = cast(IntVar, booking.remanejada)
        solver_model.model.add(sum([booking.remanejada, booking.cancelada]) == 1).only_enforce_if(
            chartered.confirmed.Not()
        )


def adds_constraint_status_grupos(solver_model: RemanejamentoSolverModel):
    """
    Se o grupo é confirmado então o chartered correspondente é confirmado.
    Se o grupo é cancelado, foi fechado pelo solver ou não tem empresa então o chartered correspondente não é confirmado.
    """
    chartered: SolverChartered
    for chartered in solver_model.all_chartereds.values():
        if chartered.status == "travel_confirmed" and chartered.company_id is not None:
            solver_model.model.add(chartered.confirmed == 1)
        elif chartered.status == "canceled" or chartered.closed_by_solver is True or chartered.company_id is None:
            solver_model.model.add(chartered.confirmed == 0)


def adds_constraint_grupo_trips_status(solver_model: RemanejamentoSolverModel):
    """
    Se o status do grupo é confirmado => Todas as trips do grupo são confirmadas.
    Se o status do grupo é cancelado => Todas as trips do grupo são canceladas.
    """
    chartered: SolverChartered
    trip: SolverTrip
    for trip in solver_model.all_trips.values():
        chartered = solver_model.all_chartereds[trip.chartered_id]
        solver_model.model.add(chartered.confirmed == trip.confirmed)


# OBS: Não utilizada atualmente porque alguns subgrafos são apenas um par de grupos
def adds_constraint_not_all_canceled_groups(solver_model: RemanejamentoSolverModel):
    """
    Não é permitido cancelar todos os grupos.
    """
    groups_confirmed = []
    chartered: SolverChartered
    for chartered in solver_model.all_chartereds.values():
        groups_confirmed.append(chartered.confirmed)
    solver_model.model.add(sum(groups_confirmed) > 0)


def adds_constraint_prioriza_travels_reais(solver_model: RemanejamentoSolverModel):
    """
    Prioriza o remanejamento de travels reais sobre travels dummies.
    Esta restrição garante que, para cada trecho classe que pode ser destino de remanejamento:
    1. Se vagas ≤ pax reais disponíveis: Apenas travels reais são remanejadas
    2. Se vagas > pax reais disponíveis: Travels reais são priorizadas, e apenas depois as travels dummies podem ocupar as vagas restantes
    """
    # para cada trecho classe destino, agrupa as travels reais e dummies que podem ser remanejadas para ele
    tc_destino_travels_map = defaultdict(lambda: {"reais": [], "dummies": []})

    for travel_id, travel in solver_model.all_bookings.items():
        for tc_destino_id in travel.remanejamento.keys():
            categoria = "dummies" if travel.dummy else "reais"
            tc_destino_travels_map[tc_destino_id][categoria].append(travel_id)

    for tc_destino_id, travels in tc_destino_travels_map.items():
        travels_reais_ids = travels["reais"]
        travels_dummies_ids = travels["dummies"]

        # pula se não tiver tanto travels reais quanto dummies porque não precisa priorizar
        if not travels_reais_ids or not travels_dummies_ids:
            continue

        tc_destino = solver_model.all_trips[tc_destino_id]

        # quantidade total de pax reais que podem ser remanejados para este trecho
        total_pax_reais = sum(
            solver_model.all_bookings[travel_real_id].count_seats for travel_real_id in travels_reais_ids
        )

        # para cada travel dummy que pode ser remanejada para este trecho
        for travel_dummy_id in travels_dummies_ids:
            pax_dummies = solver_model.all_bookings[travel_dummy_id].count_seats
            travel_dummy_remanejada = solver_model.remanejamento_matrix[(travel_dummy_id, tc_destino_id)]

            # contagem de pax reais que estão sendo remanejados para este trecho
            pax_reais_remanejados = sum(
                solver_model.all_bookings[travel_real_id].count_seats
                * solver_model.remanejamento_matrix[(travel_real_id, tc_destino_id)]
                for travel_real_id in travels_reais_ids
            )

            # se remanejar este dummy impediria que todos os pax reais fossem remanejados
            if tc_destino.vagas - pax_dummies < total_pax_reais:
                # cria variável auxiliar que indica se todos os pax reais foram remanejados
                todos_pax_reais_remanejados = solver_model.model.new_bool_var(
                    f"todos_reais_{tc_destino_id}_{travel_dummy_id}"
                )
                # define quando a variável auxiliar é verdadeira ou falsa
                solver_model.model.add(pax_reais_remanejados == total_pax_reais).only_enforce_if(
                    todos_pax_reais_remanejados
                )
                solver_model.model.add(pax_reais_remanejados < total_pax_reais).only_enforce_if(
                    todos_pax_reais_remanejados.Not()
                )
                # os dummies só podem ser remanejados se todos os pax reais já estiverem remanejados
                solver_model.model.add(travel_dummy_remanejada <= todos_pax_reais_remanejados)
