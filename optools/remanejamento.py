import logging
from collections import defaultdict
from decimal import Decimal

from ortools.sat.cp_model_pb2 import CpSolverStatus
from ortools.sat.python import cp_model

from core.service import globalsettings_svc
from optools.constraints import adds_all_constraints
from optools.errors import SolverFail
from optools.forms import (
    CancelarGruposForm,
    RemanejamentoSolverModel,
    RemanejamentoSolverSolutionForm,
    SolverBooking,
    SolverChartered,
    SolverGrupoLog,
    SolverInputForm,
    SolverParams,
    SolverTrip,
)

logger = logging.getLogger(__name__)

CUSTO_FORA_DA_BASE = 350


def solve_cenario_remanejamento(
    solver_input_form: SolverInputForm, solver_params: SolverParams, strategy: str
) -> tuple[RemanejamentoSolverSolutionForm, list[SolverGrupoLog]]:
    """Solves a scenario optimization problem for booking rearrangements.

    This function takes a dictionary of booking data and optimizes the arrangement of bookings
    across chartered trips by:

    1. Applying various business constraints:
        - Company grouping constraints
        - Round-trip grouping constraints
        - Closed group constraints
        - Travel segment/class constraints
        - Passenger capacity constraints
        - Chartered trip status constraints
        - Non-cancellation group constraints

    2. Maximizing an objective function that considers:
        - Total revenue from all bookings
        - Total charter costs (frete_total)
        - Savings from canceled charters (frete_cancelado)
        - Revenue loss from canceled passengers (with 1.2x penalty)
        - Revenue loss from canceled dummy bookings
        - Cost of rearranging bookings

    Args:
        solver_model (RemanejamentoSolverModel): Form containing the solver model and data
        solver_params (SolverParams): Factors to applied to solver equation.

    Returns:
        RemanejamentoSolverSolutionForm: Processed solution containing optimized arrangement
    """

    solver_model = RemanejamentoSolverModel.from_solver_input_form(solver_input_form)

    # Constraints
    allow_experimental = True if strategy == "SIMULATION" else False
    adds_all_constraints(solver_model, allow_experimental)

    receita_total = 0
    for booking in solver_model.all_bookings.values():
        receita_total += booking.receita_agregada

    frete_total = 0
    frete_cancelado = 0
    custo_cancelamento_fora_da_base = 0
    for chartered in solver_model.all_chartereds.values():
        chartered: SolverChartered
        frete_total += chartered.frete
        frete_cancelado += chartered.confirmed.Not() * float(chartered.frete)
        if chartered.cancelado_fora_da_base is not None:
            custo_cancelamento_fora_da_base += chartered.cancelado_fora_da_base * CUSTO_FORA_DA_BASE

    receita_pax_cancelados = 0
    receita_cancelamento = 0
    receita_dummies_cancelados = 0
    for booking in solver_model.all_bookings.values():
        cancelada = booking.cancelada
        if not booking.dummy:
            receita_pax_cancelados += cancelada * float(booking.receita_agregada)
            receita_cancelamento += cancelada * max(
                solver_params.cancelamento_fator * float(booking.receita_agregada),
                solver_params.burn_pax_fator + float(booking.receita_agregada),
            )
        else:
            receita_dummies_cancelados += cancelada * max(
                solver_params.dummie_fator * float(booking.receita_agregada),
                solver_params.burn_dummy_fator + float(booking.receita_agregada),
            )

    custo_remanejar = 0
    for booking in solver_model.all_bookings.values():
        for trip_id, custo in booking.remanejamento.items():
            custo_remanejar += float(custo) * solver_model.remanejamento_matrix[(booking.id, trip_id)]

    solver_model.model.maximize(
        float(receita_total)
        - float(frete_total)
        + frete_cancelado
        - custo_cancelamento_fora_da_base
        - receita_cancelamento
        - receita_dummies_cancelados
        - solver_params.remanejamento_fator * custo_remanejar
    )

    # Solve model
    solver = cp_model.CpSolver()
    solver.parameters.log_search_progress = False
    solver.parameters.num_workers = globalsettings_svc.get("solver_num_workers", 8)
    solver.parameters.max_time_in_seconds = globalsettings_svc.get("solver_max_time_in_seconds", 60)

    status = solver.Solve(solver_model.model)

    return post_process_data_solution(solver_model, solver, status, solver_params)


def post_process_data_solution(
    solver_model: RemanejamentoSolverModel,
    solver: cp_model.CpSolver,
    status: CpSolverStatus,
    solver_params: SolverParams,
) -> tuple[RemanejamentoSolverSolutionForm, list[SolverGrupoLog]]:
    """Processa os dados retornados após a solução de remanejamento encontrada pelo solver.

    Args:
        solver_model (RemanejamentoSolverModel): O formulário de solução do solver de remanejamento.
        solver (cp_model.CpSolver): O solver de programação linear.
        status (CpSolverStatus): O status da solução do solver.
        solver_params (SolverParams): Parâmetros do solver.

    Raises:
        SolverFail: Exceção lançada quando o solver falha.

    Returns:
        tuple[RemanejamentoSolverSolutionForm, list[SolverGrupoLog]]: O formulário de solução do solver
        de remanejamento e os logs dos grupos.
    """
    if not is_possible_solution(status):
        status_name = solver.status_name(status)
        raise SolverFail(status, f"Status: {status_name}")

    remanejamento_booking_to_trip_map, booking_solution, chartered_solution = get_variable_values_from_solution(
        solver_model, solver
    )

    metrics = _process_metrics(solver_model, chartered_solution, booking_solution, remanejamento_booking_to_trip_map)
    solver_grupo_logs = _create_solver_grupo_logs(solver_model, solver, chartered_solution, metrics["frete_grupo_map"])

    solution = _create_solution_form(
        solver,
        status,
        solver_params,
        metrics,
        solver_model,
        chartered_solution,
        booking_solution,
        remanejamento_booking_to_trip_map,
    )

    return solution, solver_grupo_logs


def _process_metrics(
    solver_model: RemanejamentoSolverModel,
    chartered_solution: list,
    booking_solution: list,
    remanejamento_booking_to_trip_map: dict,
) -> dict:
    metrics = {
        "receita_atual_with_dummies": Decimal(0),
        "receita_atual_without_dummies": Decimal(0),
        "receita_pessoas_canceladas": Decimal(0),
        "receita_dummies_cancelados": Decimal(0),
        "custo_remanejar": Decimal(0),
        "total_pessoas_reais_depois": 0,
        "total_dummies": 0,
        "total_dummies_cancelados": 0,
        "pessoas_reais_remanejadas": 0,
        "pessoas_reais_canceladas": 0,
        "receita_forecast": Decimal(0),
        "total_dummies_remanejados": 0,
        "gmv_previsto_otimizado_por_chartered": defaultdict(Decimal),
        "receita_agregada_otimizada_por_chartered": defaultdict(Decimal),
        "total_capacidade_fisica_antes": 0,
        "total_capacidade_fisica_depois": 0,
        "frete_grupo_map": defaultdict(Decimal),
        "total_grupos_confirmados_depois": 0,
        "total_grupos_cancelados_depois": 0,
        "total_grupos_cancelados_com_empresa_depois": 0,
        "total_grupos_cancelados_sem_empresa_depois": 0,
        "economia_frete": Decimal("0"),
    }

    for booking in booking_solution:
        chartered_id = solver_model.all_trips[booking.trecho_classe_id].chartered_id
        chartered = solver_model.all_chartereds[chartered_id]

        # apenas gmv previsto otimizado por chartered e receita agregada otimizada por chartered consideram os grupos fora da janela
        if not booking.remanejada:
            metrics["gmv_previsto_otimizado_por_chartered"][chartered_id] += booking.gmv
            metrics["receita_agregada_otimizada_por_chartered"][chartered_id] += booking.receita_agregada

        trecho_classe_id_final = remanejamento_booking_to_trip_map.get(booking.id)
        if trecho_classe_id_final is not None and booking.remanejamento.get(trecho_classe_id_final) is not None:
            metrics["custo_remanejar"] += (
                booking.remanejamento[trecho_classe_id_final] if not chartered.fora_da_janela else Decimal(0)
            )
            # travels remanejadas são contabilizadas no grupo de destino
            chartered_id = solver_model.all_trips[trecho_classe_id_final].chartered_id
            metrics["gmv_previsto_otimizado_por_chartered"][chartered_id] += booking.gmv
            metrics["receita_agregada_otimizada_por_chartered"][chartered_id] += booking.receita_agregada

        if chartered.fora_da_janela:
            continue

        metrics["receita_forecast"] += booking.gmv
        metrics["receita_atual_with_dummies"] += (not booking.cancelada) * booking.gmv
        metrics["receita_atual_without_dummies"] += (not booking.cancelada) * booking.gmv * (not booking.dummy)
        metrics["receita_dummies_cancelados"] += booking.cancelada * booking.gmv * booking.dummy
        metrics["receita_pessoas_canceladas"] += booking.cancelada * booking.gmv * (not booking.dummy)

        metrics["pessoas_reais_canceladas"] += booking.cancelada * booking.count_seats * (not booking.dummy)
        metrics["total_pessoas_reais_depois"] += (not booking.cancelada) * booking.count_seats * (not booking.dummy)
        metrics["pessoas_reais_remanejadas"] += booking.remanejada * booking.count_seats * (not booking.dummy)

        metrics["total_dummies"] += booking.dummy * booking.count_seats
        metrics["total_dummies_cancelados"] += booking.cancelada * booking.dummy * booking.count_seats
        metrics["total_dummies_remanejados"] += booking.remanejada * booking.dummy * booking.count_seats

    metrics["total_pessoas_reais_antes"] = metrics["total_pessoas_reais_depois"] + metrics["pessoas_reais_canceladas"]

    for chartered in chartered_solution:
        if chartered.fora_da_janela:
            continue

        metrics["total_capacidade_fisica_antes"] += chartered.capacity
        metrics["total_capacidade_fisica_depois"] += chartered.capacity * chartered.confirmed
        metrics["frete_grupo_map"][chartered.id] += chartered.confirmed * chartered.frete
        metrics["economia_frete"] += (not chartered.confirmed) * chartered.frete
        metrics["total_grupos_confirmados_depois"] += chartered.confirmed

        if chartered.confirmed == 0:
            metrics["total_grupos_cancelados_depois"] += 1
            if chartered.company_id is not None:
                metrics["total_grupos_cancelados_com_empresa_depois"] += 1
            else:
                metrics["total_grupos_cancelados_sem_empresa_depois"] += 1

    metrics["occ_media_antes"] = _calculate_occ_media(
        metrics["total_pessoas_reais_antes"], metrics["total_capacidade_fisica_antes"]
    )
    metrics["occ_media_depois"] = _calculate_occ_media(
        metrics["total_pessoas_reais_depois"], metrics["total_capacidade_fisica_depois"]
    )

    metrics["custo_frete"] = sum(metrics["frete_grupo_map"].values())

    return metrics


def _create_solver_grupo_logs(
    solver_model: RemanejamentoSolverModel,
    solver: cp_model.CpSolver,
    chartered_solution: list,
    frete_grupo_map: dict[int, Decimal],
):
    forecast_trechoclasse_grupo_map = _build_forecast_map(solver_model)
    cancelamento_sem_par = _check_cancelamento_cruzado(solver_model, chartered_solution)

    solver_grupo_logs = []
    for chartered in chartered_solution:
        solver_grupo_logs.append(
            _create_solver_grupo_log(
                chartered,
                solver_model,
                solver,
                cancelamento_sem_par,
                frete_grupo_map,
                forecast_trechoclasse_grupo_map,
            )
        )

    return solver_grupo_logs


def _build_forecast_map(solver_model: RemanejamentoSolverModel) -> dict:
    forecast_map = defaultdict(dict)
    for trip in solver_model.all_trips.values():
        forecast_map[trip.chartered_id][trip.id] = trip.forecast
    return forecast_map


def _calculate_occ_media(total_pessoas: int, total_capacidade: int) -> int | float:
    if isinstance(total_capacidade, (int, float, Decimal)) and total_capacidade != 0:
        return total_pessoas / total_capacidade
    return 0


def _create_solver_grupo_log(
    chartered,
    solver_model: RemanejamentoSolverModel,
    solver: cp_model.CpSolver,
    cancelamento_sem_par: set,
    frete_grupo_map: dict[int, Decimal],
    forecast_trechoclasse_grupo_map: dict,
) -> SolverGrupoLog:
    count_seats_map = _prepare_count_seats_map(chartered, solver_model)
    cancelamento_cruzado_grupo_id = chartered.rotina_par_id
    custo_fora_da_base = (
        solver.value(chartered.cancelado_fora_da_base) if chartered.cancelado_fora_da_base is not None else 0
    )

    additional_data = {
        "grupo_cancelamento_cruzado": cancelamento_cruzado_grupo_id,
        "cancelamento_cruzado": solver.value(chartered.cancelou_cruzado),
        "grupo_sem_par_cancelado": chartered.id in cancelamento_sem_par,
        "custo_fora_da_base": custo_fora_da_base,
    }

    return SolverGrupoLog(
        eixo=chartered.eixo,
        grupo_id=chartered.id,
        rotina_onibus_id=chartered.rotina_onibus_id,
        grupo_confirmado=bool(chartered.confirmed),
        pessoas_trechoclasse=dict(count_seats_map),
        forecast_trechoclasse=forecast_trechoclasse_grupo_map[chartered.id],
        pessoas_total=chartered.pessoas,
        pessoas_remanejadas=chartered.pessoas_remanejadas,
        pessoas_adicionadas=chartered.pessoas_adicionadas,
        pessoas_canceladas=chartered.pessoas_canceladas,
        dummies_total=chartered.dummies,
        dummies_remanejados=chartered.dummies_remanejados,
        dummies_adicionados=chartered.dummies_adicionados,
        dummies_cancelados=chartered.dummies_cancelados,
        gmv_real=chartered.gmv_real,
        gmv_previsto=chartered.gmv_previsto,
        solver_threshold=chartered.solver_threshold,
        valor_frete=frete_grupo_map[chartered.id],
        additional_data=additional_data,
    )


def _create_solution_form(
    solver: cp_model.CpSolver,
    status: CpSolverStatus,
    solver_params: SolverParams,
    metrics: dict,
    solver_model: RemanejamentoSolverModel,
    chartered_solution: list,
    booking_solution: list,
    remanejamento_booking_to_trip_map: dict,
) -> RemanejamentoSolverSolutionForm:
    custo_cancelar = (Decimal(str(solver_params.cancelamento_fator)) - Decimal("1")) * metrics[
        "receita_pessoas_canceladas"
    ]

    return RemanejamentoSolverSolutionForm(
        objective=solver.ObjectiveValue(),
        solver_time=solver.WallTime(),
        solver_status=solver.status_name(status),
        receita_forecast=metrics["receita_forecast"],
        receita_travels_mantidas=metrics["receita_atual_without_dummies"],
        receita_pessoas_canceladas=metrics["receita_pessoas_canceladas"],
        receita_dummies_cancelados=metrics["receita_dummies_cancelados"],
        custo_cancelamento=custo_cancelar,
        custo_assento_vazio=Decimal(0),
        custo_remanejar=metrics["custo_remanejar"],
        custo_frete=metrics["custo_frete"],
        economia_frete=metrics["economia_frete"],
        total_pessoas=metrics["total_pessoas_reais_depois"],
        total_dummies=metrics["total_dummies"],
        total_grupos=metrics["total_grupos_confirmados_depois"],
        grupos_cancelados=metrics["total_grupos_cancelados_depois"],
        grupos_cancelados_com_empresa=metrics["total_grupos_cancelados_com_empresa_depois"],
        grupos_cancelados_sem_empresa=metrics["total_grupos_cancelados_sem_empresa_depois"],
        occ_media_antes=metrics["occ_media_antes"],
        occ_media_depois=metrics["occ_media_depois"],
        pessoas_remanejadas=metrics["pessoas_reais_remanejadas"],
        pessoas_canceladas=metrics["pessoas_reais_canceladas"],
        cancelar_grupos=get_list_cancelar_grupos(
            chartered_solution,
            metrics["gmv_previsto_otimizado_por_chartered"],
            metrics["receita_agregada_otimizada_por_chartered"],
        ),
        remanejar_travels=get_list_remanejar_travels(
            solver_model, booking_solution, chartered_solution, remanejamento_booking_to_trip_map
        ),
        total_dummies_cancelados=metrics["total_dummies_cancelados"],
        total_dummies_remanejados=metrics["total_dummies_remanejados"],
    )


def is_possible_solution(status):
    if status == cp_model.OPTIMAL or status == cp_model.FEASIBLE:
        return True
    return False


def _check_cancelamento_cruzado(solver_model: RemanejamentoSolverModel, chartereds: list[SolverChartered]):
    """Identifica grupos que foram cancelados de forma cruzada e grupos com inconsistência de pares."""
    verificados = set()
    cancelamento_sem_par = set()

    for chartered in chartereds:
        if chartered.id in verificados:
            continue
        verificados.add(chartered.id)
        # continua se nao for grupo de ida
        if chartered.chartered_volta_id is None or chartered.chartered_volta_id == chartered.id:
            continue

        chartered_volta = solver_model.all_chartereds[chartered.chartered_volta_id]
        # continua se não possui rotina_par
        if chartered.rotina_par_id is None:
            # se houve inconsistência, registra.
            if chartered.confirmed != chartered_volta.confirmed:
                cancelamento_sem_par.add(chartered.id)
            continue

        chartered_ida_rotina_par = solver_model.all_chartereds[chartered.rotina_par_id]
        if chartered_ida_rotina_par.rotina_par_id is None or chartered_ida_rotina_par.chartered_volta_id is None:
            # se houve inconsistência, registra.
            cancelamento_sem_par.add(chartered_ida_rotina_par.id)
            continue

        verificados.add(chartered_ida_rotina_par.id)

    return cancelamento_sem_par


def _calculate_passenger_counts(
    booking: SolverBooking,
    chartered_id: int,
    solver_model: RemanejamentoSolverModel,
    remanejamento_booking_to_trip_map: dict[int, int],
    counts: dict,
):
    """Calculates and updates passenger counts based on booking status."""
    if booking.remanejada == 0:
        counts["pessoas"][chartered_id] += booking.count_seats * (not booking.dummy)
        counts["dummies"][chartered_id] += booking.count_seats * booking.dummy

    if booking.remanejada == 1:
        target_trecho_classe_id = remanejamento_booking_to_trip_map[booking.id]
        target_chartered_id = solver_model.all_trips[target_trecho_classe_id].chartered_id

        counts["pessoas_remanejadas"][chartered_id] += booking.count_seats * (not booking.dummy)
        counts["dummies_remanejados"][chartered_id] += booking.count_seats * booking.dummy
        counts["pessoas_adicionadas"][target_chartered_id] += booking.count_seats * (not booking.dummy)
        counts["dummies_adicionados"][target_chartered_id] += booking.count_seats * booking.dummy
        counts["pessoas"][target_chartered_id] += booking.count_seats * (not booking.dummy)
        counts["dummies"][target_chartered_id] += booking.count_seats * booking.dummy

    if booking.cancelada == 1:
        counts["pessoas_canceladas"][chartered_id] += booking.count_seats * (not booking.dummy)
        counts["dummies_cancelados"][chartered_id] += booking.count_seats * booking.dummy


def get_variable_values_from_solution(solver_model: RemanejamentoSolverModel, solver: cp_model.CpSolver):
    """Substitui as variáveis booleans do solver por valores inteiros"""
    remanejamento_booking_to_trip_map = {}
    booking_solution = []
    chartered_solution = []

    trip: SolverTrip
    for trip in solver_model.all_trips.values():
        trip.confirmed = solver.value(trip.confirmed)

    for booking_id, trip_id in solver_model.remanejamento_matrix.keys():
        remanejar = solver.value(solver_model.remanejamento_matrix[booking_id, trip_id])
        if remanejar == 1:
            remanejamento_booking_to_trip_map[booking_id] = trip_id

    booking: SolverBooking
    counts = {
        "pessoas": defaultdict(int),
        "pessoas_remanejadas": defaultdict(int),
        "pessoas_adicionadas": defaultdict(int),
        "pessoas_canceladas": defaultdict(int),
        "dummies": defaultdict(int),
        "dummies_remanejados": defaultdict(int),
        "dummies_adicionados": defaultdict(int),
        "dummies_cancelados": defaultdict(int),
    }

    for booking in solver_model.all_bookings.values():
        current_booking = booking
        current_booking.remanejada = solver.value(booking.remanejada)
        current_booking.cancelada = solver.value(booking.cancelada)
        booking_solution.append(current_booking)

        original_chartered_id = solver_model.map_trip_chartered[current_booking.trecho_classe_id]
        _calculate_passenger_counts(
            current_booking, original_chartered_id, solver_model, remanejamento_booking_to_trip_map, counts
        )

    chartered: SolverChartered
    for chartered in solver_model.all_chartereds.values():
        current_chartered = chartered
        current_chartered.confirmed = solver.value(chartered.confirmed)
        current_chartered.cancelado_fora_da_base = solver.value(chartered.cancelado_fora_da_base)
        current_chartered.pessoas = counts["pessoas"][chartered.id]
        current_chartered.dummies = counts["dummies"][chartered.id]
        current_chartered.pessoas_remanejadas = counts["pessoas_remanejadas"][chartered.id]
        current_chartered.dummies_remanejados = counts["dummies_remanejados"][chartered.id]
        current_chartered.pessoas_adicionadas = counts["pessoas_adicionadas"][chartered.id]
        current_chartered.dummies_adicionados = counts["dummies_adicionados"][chartered.id]
        current_chartered.pessoas_canceladas = counts["pessoas_canceladas"][chartered.id]
        current_chartered.dummies_cancelados = counts["dummies_cancelados"][chartered.id]
        chartered_solution.append(current_chartered)

    return remanejamento_booking_to_trip_map, booking_solution, chartered_solution


def get_list_cancelar_grupos(
    chartered_solution: list[SolverChartered],
    gmv_previsto_otimizado_por_chartered: dict[int, Decimal],
    receita_agregada_otimizada_por_chartered: dict[int, Decimal],
):
    cancelar_grupos = []
    for chartered in chartered_solution:
        cancelar_grupos.append(
            CancelarGruposForm(
                grupo_id=chartered.id,
                company_id=chartered.company_id,
                is_ida=None,
                ida=None,
                volta=None,
                capacidade=chartered.capacity,
                pessoas=chartered.pessoas,
                pessoas_canceladas=chartered.pessoas_canceladas,
                pessoas_remanejadas=chartered.pessoas_remanejadas,
                pessoas_adicionadas=chartered.pessoas_adicionadas,
                dummies=chartered.dummies,
                dummies_cancelados=chartered.dummies_cancelados,
                dummies_remanejados=chartered.dummies_remanejados,
                dummies_adicionados=chartered.dummies_adicionados,
                cancelar=chartered.confirmed == 0,
                cancelar_ida=None,
                cancelar_volta=None,
                gmv_previsto=chartered.gmv_previsto,
                gmv_previsto_otimizado=gmv_previsto_otimizado_por_chartered[chartered.id],
                receita_agregada_otimizada=receita_agregada_otimizada_por_chartered[chartered.id],
                frete=chartered.custo_assento * chartered.capacity,
                fora_da_janela=chartered.fora_da_janela,
            )
        )
    return cancelar_grupos


def get_list_remanejar_travels(
    solver_model: RemanejamentoSolverModel,
    booking_solution: list[SolverBooking],
    chartered_solution: list[SolverChartered],
    remanejamento_booking_to_trip_map: dict[int, int],
) -> list[dict]:
    remanejar_travels = []
    for booking in booking_solution:
        grupo_id_original = solver_model.map_trip_chartered[booking.trecho_classe_id]
        chartered_original = None
        for chartered in chartered_solution:
            if chartered.id == grupo_id_original:
                chartered_original = chartered
                break

        trecho_classe_id_final = remanejamento_booking_to_trip_map.get(booking.id, booking.trecho_classe_id)

        grupo_id_final = solver_model.map_trip_chartered[trecho_classe_id_final]
        custo_remanejamento = 0

        if booking.remanejamento.get(trecho_classe_id_final) is not None:
            custo_remanejamento = booking.remanejamento[trecho_classe_id_final]

        if booking.cancelada:
            custo_remanejamento = None

        grupos_destino_set = set()

        for trecho_classe_id in booking.remanejamento.keys():
            if trecho_classe_id in solver_model.map_trip_chartered:
                grupo_id = solver_model.map_trip_chartered[trecho_classe_id]
                grupos_destino_set.add(grupo_id)

        grupos_destino = list(grupos_destino_set)

        remanejar_travels.append(
            {
                "travel_id": booking.id,
                "is_dummy": booking.dummy,
                "original_grupo_id": grupo_id_original,
                "original_trecho_classe_id": booking.trecho_classe_id,
                "original_cancelar_grupo": chartered_original.confirmed == 0 if chartered_original else None,
                "destino_grupo_id": grupo_id_final,
                "destino_trecho_classe_id": trecho_classe_id_final,
                "remanejar": booking.remanejada,
                "count_seats": booking.count_seats,
                "cancelar": booking.cancelada,
                "receita": booking.receita,
                "receita_agregada": booking.receita_agregada,
                "custo_remanejar": custo_remanejamento,
                "trechos_destino": booking.remanejamento,
                "grupos_destino": grupos_destino,
                "grupo_fora_da_janela": chartered_original.fora_da_janela if chartered_original else False,
            }
        )
    return remanejar_travels


def _prepare_count_seats_map(chartered: SolverChartered, solver_model: RemanejamentoSolverModel) -> dict[int, int]:
    count_seats_map = defaultdict(int)
    for booking in solver_model.all_bookings.values():
        trip = booking.trecho_classe_id
        if solver_model.map_trip_chartered[trip] == chartered.id:
            count_seats_map[trip] += booking.count_seats

    return count_seats_map
