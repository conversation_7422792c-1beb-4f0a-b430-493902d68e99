from django_qserializer.serialization import BaseSerializer

from core.models_rota import LocalEmbarque, Weekdays


class DetailsExtra(BaseSerializer):
    select_related = ["cidade"]

    def serialize_object(self, obj: LocalEmbarque) -> dict:
        local_dict = {
            "cidade_id": obj.cidade.id,
            "name": obj.cidade.name,
            "uf": obj.cidade.uf,
            "slug": obj.cidade.slug,
            "sigla": obj.cidade.sigla,
            "timezone": obj.cidade.timezone,
            "nickname_slug": obj.nickname_slug,
            "endereco_slug": obj.endereco_slug,
            "endereco_logradouro": obj.endereco_logradouro,
            "endereco_numero": obj.endereco_numero,
            "endereco_bairro": obj.endereco_bairro,
            "bairro_slug": obj.bairro_slug,
            "endereco_cep": obj.endereco_cep,
            "driver_endereco_logradouro": obj.driver_endereco_logradouro,
            "driver_endereco_numero": obj.driver_endereco_numero,
            "description": obj.description,
            "description_ao": obj.description_ao,
            "driver_description": obj.driver_description,
            "classification": obj.classification,
            "mapurl": obj.mapurl,
            "street_view_mapurl": obj.street_view_mapurl,
            "features": obj.features,
            "coords": {"latitude": obj.latitude, "longitude": obj.longitude},
            "modelos_venda": obj.modelos_venda,
            "uso_do_local": obj.uso_do_local,
            "categoria_do_ponto": obj.categoria_do_ponto,
            "tipo_de_acesso": obj.tipo_de_acesso,
            "zona_maxima_de_restricao": obj.zona_maxima_de_restricao,
            "impede_bypass": obj.impede_bypass,
            "desembarque_rapido": obj.desembarque_rapido,
        }

        return local_dict


class GruposExtra(BaseSerializer):
    select_related = ["grupos_futuros"]

    def serialize_object(self, obj: LocalEmbarque) -> dict:
        try:
            return {"count_groups": obj.grupos_futuros.count}
        except LocalEmbarque.grupos_futuros.RelatedObjectDoesNotExist:
            return {"count_groups": 0}


class RestrictionsExtra(BaseSerializer):
    select_related = ["detalhes_internos"]
    prefetch_related = ["weekdays", "onibus_suportados", "plataformas"]

    def serialize_object(self, obj: LocalEmbarque) -> dict:
        weekday_serializer = WeekdaysExtra()
        wh_list = [weekday_serializer.serialize_object(wd) for wd in obj.weekdays.all()]

        local_dict = {}
        local_dict["max_embarque_simultaneo"] = obj.max_embarque_simultaneo
        local_dict["max_minutos_permanencia"] = obj.max_minutos_permanencia
        local_dict["working_hour_list"] = wh_list
        local_dict["dias_proibidos"] = (
            ",".join(d.strftime("%d/%m/%Y") for d in obj.dias_proibidos) if obj.dias_proibidos else None
        )

        local_dict["onibus_suportados"] = [suporte.tipo for suporte in obj.onibus_suportados.all()]
        local_dict["aceita_embarque_ao"] = obj.aceita_embarque_ao
        local_dict["platforms"] = [
            {
                "id": plat.id,
                "numero": plat.numero,
                "desembarque": plat.desembarque,
                "local_id": plat.local_id,
            }
            for plat in obj.plataformas.all()
        ]

        if hasattr(obj, "detalhes_internos"):
            local_dict["contato_nome"] = obj.detalhes_internos.contato_nome
            local_dict["contato_email"] = obj.detalhes_internos.contato_email
            local_dict["contato_telefone"] = obj.detalhes_internos.contato_telefone
            local_dict["reports"] = obj.detalhes_internos.reports
            local_dict["observations"] = obj.detalhes_internos.observations
            local_dict["status_flag"] = obj.detalhes_internos.status_flag

        return local_dict


class WeekdaysExtra(BaseSerializer):
    def serialize_object(self, obj: Weekdays) -> dict:
        return {
            "dia": obj.dia,
            "start_time": obj.start_time.strftime("%H:%M") if obj.start_time else None,
            "end_time": obj.end_time.strftime("%H:%M") if obj.end_time else None,
            "max_minutos_permanencia": obj.max_minutos_permanencia,
            "max_embarque_simultaneo": obj.max_embarque_simultaneo,
        }


class LocalEmbarqueSerializer(BaseSerializer):
    extra = {
        "groups": GruposExtra,
        "detailed": DetailsExtra,
        "restrictions": RestrictionsExtra,
    }

    @classmethod
    def with_details(cls):
        return cls(extra=("detailed"))

    @classmethod
    def with_groups(cls):
        return cls(extra=("detailed", "restrictions", "groups"))

    @classmethod
    def with_restrictions(cls):
        return cls(extra=("detailed", "restrictions"))

    def serialize_object(self, obj: LocalEmbarque) -> dict:
        return {
            "id": obj.id,
            "nickname": obj.nickname,
            "endereco": obj.endereco_completo,
            "ativo": obj.ativo,
        }


def serialize_object(local: LocalEmbarque) -> dict:
    extras = ["restrictions", "detailed"]
    serialized = LocalEmbarqueSerializer(extra=extras).serialize([local])
    return next(serialized)
