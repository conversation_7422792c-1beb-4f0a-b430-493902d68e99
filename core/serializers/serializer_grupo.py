from collections import Counter
from dataclasses import asdict
from datetime import timedelta
from statistics import mean

from django.db.models import Count, Prefetch, Q, prefetch_related_objects
from django_qserializer.serialization import BaseSerializer

from adapters import new_incidents_adapter
from adapters.der_mg_adapter.der_mg_adapter import DerMgException
from commons import dateutils, storage
from commons.dateutils import timedelta_to_milliseconds, to_default_tz, to_tz, toprettybrdate
from core.constants import TIPOS_ASSENTO_PESO
from core.models_commons import Cidade
from core.models_company import NotaFiscal
from core.models_grupo import ConfirmacaoInteligenteLogger, Grupo, GrupoClasse, PedidoAlteracaoPreco, TrechoClasse
from core.models_parada import CheckpointParada
from core.models_rota import Checkpoint, HistoricoAlteracaoEmbarque, TrechoVendido
from core.models_travel import Passageiro, Travel
from core.serializers import serializer_cidade
from core.serializers.serializer_pedagio import PedagioExtra
from core.service import grupos_svc, itinerario_svc
from core.service.financial_svc import InfoFinanceiraGrupo
from core.service.helpers.der_mg_cities import get_der_mg_city_code
from core.service.itinerario_dinamico_svc import ItinerarioDinamico
from core.service.itinerario_svc import get_cidades
from core.service.locais_embarque_svc import calculate_arrival_for_first_pde
from core.service.preco_svc import get_active_buckets
from core.service.timezone_svc import to_tz_destino, to_tz_origem
from pagamento_parceiro.service.atribui_valor_repasse_svc import InfoGrupoMarketplace, InfoTrechoMarketplace

DEFAULT_TEMPO_EMBARQUE_MS = 20 * 60 * 1_000  # 20 minutos


class StaffGrupoSerializer(BaseSerializer):
    select_related = [
        "company",
        "rotina_onibus",
        "rotina_onibus__rota_principal",
        "onibus",
        "rota",
    ]

    def serialize(self, objs):
        prefetch_related_objects(
            objs,
            Prefetch("grupoclasse_set", queryset=GrupoClasse.objects.select_related("closed_by")),
            Prefetch("rota__itinerario", queryset=Checkpoint.objects.select_related("local__cidade")),
        )
        self._attach_checkin_count(objs)
        return super().serialize(objs)

    def _attach_checkin_count(self, grupos):
        count_map = dict(
            Passageiro.objects.filter(
                travel__grupo__in={g.pk for g in grupos if g}, travel__status="pending", removed=False, checkin=True
            )
            .values("travel__grupo")
            .annotate(count=Count("id"))
            .values_list("travel__grupo", "count")
        )
        for g in grupos:
            if g:
                setattr(g, "checkin_count", count_map.get(g.id, 0))

    def serialize_object(self, grupo: Grupo) -> dict:
        if grupo is None:
            return

        capacidade, pessoas = self._counts(grupo)
        checkpoints = list(grupo.rota.itinerario.all())
        check_first = checkpoints[0]
        return {
            "id": grupo.id,
            "datetime_ida": to_tz(grupo.datetime_ida, check_first.local.cidade.timezone).isoformat(),
            "rota": {"itinerario": [c.to_dict_json() for c in checkpoints]},
            "rota_eixo": (
                grupo.rotina_onibus.rota_principal.eixo
                if grupo.rotina_onibus_id and grupo.rotina_onibus.rota_principal
                else None
            ),
            "rotina_nome": grupo.rotina_onibus.nome if grupo.rotina_onibus_id else None,
            "empresa": {"name": grupo.company.name if grupo.company else None},
            "onibus_placa": grupo.onibus.placa if grupo.onibus_id else None,
            "count_checkin": getattr(grupo, "checkin_count", None) or grupo.count_checkin(),
            "max_capacity": capacidade,
            "pessoas": pessoas,
            "classes": sorted(
                [gc.to_dict_json(staff=True) for gc in grupo.grupoclasse_set.all()],
                key=lambda gc: gc["tipo_assento"],
            ),
        }

    def _counts(self, grupo):
        capacidade, pessoas = 0, 0
        for gc in grupo.grupoclasse_set.all():
            capacidade += gc.capacidade
            pessoas += gc.pessoas

        return capacidade, pessoas


class SAPGrupoSerializer(BaseSerializer):
    select_related = ["company", "onibus", "driver_one", "notafiscal"]
    prefetch_related = [Prefetch("grupoclasse_set", queryset=GrupoClasse.objects.all())]

    def serialize_object(self, grupo: Grupo) -> dict:
        capacidade, pessoas = self._counts(grupo)
        company = grupo.company
        onibus = grupo.onibus
        motorista = grupo.driver_one
        return {
            "id": grupo.id,
            "datetime_ida": grupo.datetime_ida.isoformat(),
            "company_id": grupo.company_id,
            "company_cnpj": company.cnpj if company else None,
            "company_name": company.name if company else None,
            "company_regional": company.regional if company else None,
            "capacidade": capacidade,
            "pessoas": pessoas,
            "valor_frete": str(round(grupo.valor_frete, 2)) if grupo.valor_frete else "0.00",
            "placa_onibus": onibus.placa if onibus else None,
            "email_motorista": motorista.email if motorista else None,
            "nf_number": grupo.notafiscal.numero if grupo.notafiscal else None,
        }

    def _counts(self, grupo):
        capacidade, pessoas = 0, 0
        for gc in grupo.grupoclasse_set.all():
            capacidade += gc.capacidade
            pessoas += gc.pessoas

        return capacidade, pessoas


class TravelFeedbacks(BaseSerializer):
    select_related = ["reputation"]
    prefetch_related = ["travel_set__surveyresponse_set__surveyanswer_set"]

    # Não sei a lógica, mas são criados diversos registros com esses valores no value, nada disso é um comentário válido
    invalid_surveyanswer_values = {"Sim", "Não", "like", "dislike"}

    def serialize_object(self, grupo: Grupo) -> dict:
        responses = [resp for travel in grupo.travel_set.all() for resp in travel.surveyresponse_set.all()]
        responses.sort(key=lambda x: x.created_at, reverse=True)

        notas = [resp.avg_rating for resp in responses if resp.avg_rating]
        return {
            "nota": mean(notas) if notas else 5,
            "feedbacks": [self._serialize_response(resp) for resp in responses],
        }

    def _serialize_response(self, response):
        answers = (
            response.surveyanswer_set.exclude(value__value__in=self.invalid_surveyanswer_values)
            .exclude(value__value="")
            .exclude(value__value=None)
        )
        return {
            "id": response.id,
            "nota": round(response.avg_rating or 5.0, 2),
            "created_at": response.created_at,
            "comentario": next(
                (answer.value["value"] for answer in answers if isinstance(answer.value["value"], str)), ""
            ),
        }


class GrupoSerializer(BaseSerializer):
    extra = {"travelfeedback": TravelFeedbacks}
    select_related = ["onibus", "company"]
    prefetch_related = [Prefetch("rota__itinerario", queryset=Checkpoint.objects.select_related("local__cidade"))]

    def serialize_object(self, grupo: Grupo) -> dict:
        checkpoints = list(grupo.rota.itinerario.all())

        return {
            "id": grupo.id,
            "datetime_ida": grupo.datetime_ida,
            "driver_one": grupo.driver_one.get_full_name() if grupo.driver_one else None,
            "driver_two": grupo.driver_two.get_full_name() if grupo.driver_two else None,
            "status": grupo.status,
            "rota": {
                "id": grupo.rota.id,
                "itinerario": [c.to_dict_json() for c in checkpoints],
                "ufs_intermediarios": grupo.rota.ufs_intermediarios or "",
                "distancia_total": grupo.rota.distancia_total,
            },
            "onibus": (
                {
                    "id": grupo.onibus.id,
                    "name": grupo.onibus.name,
                    "placa": grupo.onibus.placa,
                    "renavam": grupo.onibus.renavam,
                    "uf": grupo.onibus.uf,
                    "classe": grupo.onibus.classe,
                    "tipo": grupo.onibus.tipo,
                }
                if grupo.onibus_id
                else None
            ),
            "empresa": {"id": grupo.company_id, "name": grupo.company.name if grupo.company else None},
        }

    @classmethod
    def with_feedbacks(cls):
        return cls(extra=("travelfeedback",))


class GroupIncidentsSerializer(BaseSerializer):
    select_related = ["notafiscal", "onibus", "company"]
    prefetch_related = [Prefetch("rota__itinerario", queryset=Checkpoint.objects.select_related("local__cidade"))]

    def serialize_object(self, group):
        itinerario = [c.to_dict_json() for c in group.rota.itinerario.all()]
        destination_local = itinerario[-1]["local"]

        seat_type = None
        if group.onibus:
            for assento in group.onibus.classes.values_list("tipo", flat=True):
                if seat_type:
                    if TIPOS_ASSENTO_PESO.get(assento) > TIPOS_ASSENTO_PESO.get(seat_type):
                        seat_type = assento
                else:
                    seat_type = assento

        shipping_price = None
        if group.rotina_onibus and group.modelo_venda != "marketplace":
            if group.rotina_onibus.frete_atual:
                shipping_price = group.rotina_onibus.frete_atual / 2
            else:
                shipping_price = group.valor_frete

        return {
            "area_pays_group": group.area_pays_group,
            "company_id": group.company_id if group.company else None,
            "company_name": group.company.name if group.company else None,
            "datetime_departure": group.datetime_ida,
            "destination_city": destination_local["name"],
            "destination_state": destination_local["uf"],
            "driver_name": group.driver_one.get_full_name() if group.driver_one else None,
            "group_id": group.id,
            "license_plate": group.onibus.placa if group.onibus else None,
            "receipt_value": group.notafiscal.valor if group.notafiscal else None,
            "route_id": group.rota_id,
            "route_name": " - ".join(ck["local"]["sigla"] for ck in itinerario),
            "sale_pattern": group.modelo_venda,
            "rotina_onibus_id": group.rotina_onibus_id,
            "seat_type": seat_type,
            "shipping_price": shipping_price,
        }


def to_ops(grupo, with_incidents=False):
    grupo_dict = to_dict_json(grupo)
    grupo_dict.update(_to_dict_json_extra(grupo))
    descontos = [
        {"value": d.value, "reason_description": d.reason, "reason_key": d.reason_key, "id": d.id}
        for d in grupo.get_descontos()
    ]
    grupo_dict.update(
        {
            "notafiscal": grupo.notafiscal.to_dict_json() if grupo.notafiscal else None,
            "fix_instructions": grupo.notafiscal.fix_instructions if grupo.billing_status == "disapproved" else None,
            "descontos": descontos,
            "problem_description": grupo.problem_description,
            "observation": grupo.observation,
            "is_extra": grupo.is_extra,
            "driver_one_name": grupo.driver_one.get_full_name() if grupo.driver_one else None,
            "driver_two_name": grupo.driver_two.get_full_name() if grupo.driver_two else None,
            "onibus_placa": grupo.onibus.placa if grupo.onibus else None,
        }
    )

    if grupo.modelo_venda == Grupo.ModeloVenda.MARKETPLACE:
        grupo_dict["info_marketplace"] = asdict(InfoGrupoMarketplace(grupo))

    if hasattr(grupo, "autorizacao_grupo"):
        grupo_dict["autorizacao_grupo"] = grupo.autorizacao_grupo.to_dict_json()

    if with_incidents:
        grupo_dict["pay_group_area"] = grupo.area_pays_group or "Comercial"

    return grupo_dict


def to_company(grupo):
    grupo_dict = to_dict_json(grupo)
    grupo_dict.update(_to_dict_json_extra(grupo))
    grupo_dict.update(grupo.feedback_info())

    if grupo.antt_preview_s3key:
        antt_preview = storage.read_text(grupo.antt_preview_s3key)
    else:
        antt_preview = None

    grupo_dict.update(
        {
            "fix_instructions": grupo.notafiscal.fix_instructions if grupo.billing_status == "disapproved" else None,
            "nf_url": grupo.nf_url(),
            "notafiscal": grupo.notafiscal.to_dict_json(grupo_id=grupo.id) if grupo.notafiscal else None,
            "pessoas": grupo.count_pessoas(),
            "max_capacity": grupo.count_capacidade_onibus(),
            "driver_one": grupo.driver_one.profile.to_dict_json() if grupo.driver_one else None,
            "driver_two": grupo.driver_two.profile.to_dict_json() if grupo.driver_two else None,
            "antt_preview": antt_preview,
            "antt_number": grupo.antt_number,
            "antt_placa": grupo.antt_placa,
            "artesp_number": grupo.artesp_number,
            "onibus": grupo.onibus.to_dict_json() if grupo.onibus else None,
            "count_feedbacks": None,
            "count_problems": None,
            "rotina_onibus": grupo.rotina_onibus.nome if grupo.rotina_onibus else None,
            "rotina_onibus_frete_atual": grupo.rotina_onibus.frete_atual if grupo.rotina_onibus else None,
            "is_marketplace": grupo.is_marketplace,
            "feedbacks": grupo.feedbacks(),
            "is_extra": grupo.is_extra,
            "percentual_repasse": grupo.percentual_repasse,
            "one_driver_exception": grupo.one_driver_exception,
            "need_2_drivers": grupo.need_2_drivers(),
        }
    )
    return grupo_dict


def to_partial_group_company(grupo):
    d = {
        "id": grupo.id,
        "datetime_ida": to_tz_origem(grupo.datetime_ida, grupo.rota_id).isoformat(),
        "duracao_ida": timedelta_to_milliseconds(grupo.duracao_total),
        "chegada_ida": to_tz_destino(grupo.datetime_ida + grupo.duracao_total, grupo.rota_id).isoformat(),
        "rotina_onibus": grupo.rotina_onibus.nome if grupo.rotina_onibus else None,
        "rotina_onibus_frete_atual": grupo.rotina_onibus.frete_atual if grupo.rotina_onibus else None,
        "need_2_drivers": grupo.need_2_drivers(),
    }

    d["empresa"] = (
        {
            "id": grupo.company.id,
            "name": grupo.company.name,
            "vinculo": grupo.company.vinculo,
            "gestor": (
                {
                    "id": grupo.company.gestor.id,
                    "name": grupo.company.gestor.get_full_name(),
                }
                if grupo.company and grupo.company.gestor
                else None
            ),
        }
        if grupo.company
        else None
    )

    d["rota"] = siglas_itinerario_grupo_dict(grupo)

    return d


def to_staff(grupo, with_paradas=False):
    grupo_dict = to_dict_json(
        grupo,
        with_rateio=True,
        with_paradas=with_paradas,
        with_buckets_availability=True,
    )
    grupo_dict.update(_to_dict_json_extra(grupo))
    grupo_dict.update(grupo.feedback_info())
    grupo_dict.update(
        {
            "rotina_id": grupo.rotina_onibus.id if grupo.rotina_onibus else None,
            "rotina_nome": grupo.rotina_onibus.nome if grupo.rotina_onibus else None,
            "rota_eixo": (
                grupo.rotina_onibus.rota_principal.eixo
                if grupo.rotina_onibus and grupo.rotina_onibus.rota_principal
                else None
            ),
            "nf_url": grupo.nf_url(),
            "nf_valor": grupo.nf_valor(),
            "pessoas": grupo.count_pessoas(),
            "max_capacity": grupo.count_capacidade_onibus(),
            "active_driver": grupo.active_driver if grupo.active_driver else None,
            "driver_one": grupo.driver_one.profile.to_dict_json() if grupo.driver_one else None,
            "driver_two": grupo.driver_two.profile.to_dict_json() if grupo.driver_two else None,
            "driver_updated_at": grupo.driver_updated_at,
            "onibus": grupo.onibus.to_dict_json() if grupo.onibus else None,
            "onibus_name": grupo.onibus.name if grupo.onibus else None,
            "onibus_placa": grupo.onibus.placa if grupo.onibus else None,
            "onibus_locadora": grupo.onibus.locadora if grupo.onibus else None,
            "rating": None,
            "antt_number": grupo.antt_number,
            "antt_placa": grupo.antt_placa,
            "artesp_number": grupo.artesp_number,
            "datetime_checkin_open": grupo.datetime_checkin_open.isoformat() if grupo.datetime_checkin_open else None,
            "datetime_checkin_close": (
                grupo.datetime_checkin_close.isoformat() if grupo.datetime_checkin_close else None
            ),
            "datetime_travel_finished": (
                grupo.datetime_travel_finished.isoformat() if grupo.datetime_travel_finished else None
            ),
            "count_checkin": grupo.count_checkin(),
            "ressarcido": grupo.ressarcido,
            "ressarcimento_factor": grupo.ressarcimento_factor,
            "ressarcimento_reason": grupo.ressarcimento_reason,
            "checkpoint_idx": grupo.checkpoint_idx,
            "checkpoints_datetime": grupo.checkpoints_datetime,
            "problem_description": grupo.problem_description,
            "observation": grupo.observation,
            "valor_encomenda": grupo.valor_encomenda,
            "percentual_repasse": grupo.percentual_repasse,
            "percentual_taxa_servico": grupo.percentual_taxa_servico,
            "one_driver_exception": grupo.one_driver_exception,
            "need_2_drivers": grupo.need_2_drivers(),
            "autorizacao_hibrido": grupo.autorizacao_hibrido.to_dict_json() if grupo.autorizacao_hibrido else None,
        }
    )
    return grupo_dict


def serialize_rotas_com_grupo(routes):
    cidade_id_set = {route_tuple[0] for route_tuple in routes} | {route_tuple[1] for route_tuple in routes}
    qs_cidades = Cidade.objects.filter(pk__in=cidade_id_set)
    cidades_dict = serializer_cidade.serialize(qs_cidades)
    cidades_map = {c["id"]: c for c in cidades_dict}

    routes_list = []
    for route in routes:
        # mostra apenas uma perna de cada rota
        routes_copy = list(routes)
        for i, item in enumerate(routes_copy):
            if route[0] == item[1] and route[1] == item[0]:
                routes.remove(routes_copy[i])

        routes_list.append(
            {
                "origem": cidades_map[route[0]],
                "destino": cidades_map[route[1]],
                "next_group_date": to_default_tz(route[2]).date(),
                # 'best_price': route[2]
            }
        )

    return routes_list


def to_dict_json(
    grupo,
    with_rateio=False,
    with_paradas=False,
    with_buckets_availability=False,
):
    d = {
        "id": grupo.id,
        "datetime_ida": to_tz_origem(grupo.datetime_ida, grupo.rota_id).isoformat(),
        "duracao_ida": timedelta_to_milliseconds(grupo.duracao_total),
        "chegada_ida": to_tz_destino(grupo.datetime_ida + grupo.duracao_total, grupo.rota_id).isoformat(),
        "status": grupo.status,
        "canceled_at": grupo.canceled_at.isoformat() if grupo.canceled_at else None,
        "canceled_reason": grupo.canceled_reason,
        "confirmed_at": grupo.confirmed_at.isoformat() if grupo.confirmed_at else None,
        "confirming_probability": grupo.confirming_probability,
        "checkin_status": grupo.checkin_status,
        "vagas": grupo.vagas,
        "cancellation_deadline_hours": grupo.cancellation_deadline_hours,
        "trechos_classes": grupo.get_trechos_classes_map(with_buckets_availability),
        "area_pays_group": grupo.area_pays_group,
        "gmv_atual": grupo.get_gmv_atual(),
    }
    d["rota"] = grupo_rota_dict(grupo, with_rateio=with_rateio, with_paradas=with_paradas)
    grupo.add_pessoas_to_trechos_vendidos(d["trechos_classes"], d["rota"]["trechos_vendidos"])
    return d


def _to_dict_json_extra(grupo):
    return {
        "rota_id": grupo.rota.id,
        "billing_status": grupo.billing_status,
        "empresa": grupo.company.to_dict_json() if grupo.company else None,
        "valor_frete": grupo.valor_frete,
        "nao_teve_frete": grupo.nao_teve_frete,
        "motorista_descansado": grupo.motorista_descansado,
        "contar_dia_parado": grupo.contar_dia_parado,
        "observacao_dia_parado": grupo.observacao_dia_parado,
        "modelo_venda": grupo.modelo_venda,
        "modelo_operacao": grupo.modelo_operacao,
        "autorizacao_url": grupo.autorizacao_url(),
    }


def grupo_rota_dict(grupo: Grupo, with_rateio: bool = False, with_paradas: bool = False) -> dict:
    """
    Caso with_paradas = True as durações e kilometragens de paradas de lanche
    são somente para referência em relação ao checkpoint de embarque anterior,
    não fazem parte da composição incremental do trajeto.
    """
    d = grupo.rota.to_dict_json(with_rateio=with_rateio)
    itinerario = itinerario_svc.get_itinerario(grupo.rota.id, local=True, with_paradas=with_paradas)
    itinerario_dinamico = ItinerarioDinamico(grupo, itinerario).to_dict_json()
    checkpoints = d["itinerario"] = itinerario_dinamico

    duracao = 0

    historico_alteracoes_embarque = HistoricoAlteracaoEmbarque.objects.filter(
        Q(grupo_antigo__id=grupo.id) | Q(grupo_novo__id=grupo.id)
    ).values("grupo_antigo__id", "local_antigo__id", "grupo_novo__id", "local_novo__id")

    alteracoes_set = set()
    for alteracao in historico_alteracoes_embarque:
        alteracoes_set.add((alteracao["grupo_antigo__id"], alteracao["local_antigo__id"]))
        alteracoes_set.add((alteracao["grupo_novo__id"], alteracao["local_novo__id"]))

    for i, checkpoint in enumerate(checkpoints):
        checkpoint["sofreu_alteracao"] = (grupo.id, checkpoint["local_id"]) in alteracoes_set
        if i == 0:
            checkpoint["arrival"] = None
            checkpoint["departure"] = grupo.datetime_ida
            continue

        previous_id = 1
        while checkpoints[i - previous_id]["apenas_parada"]:
            previous_id += 1

        previous_checkpoint = checkpoints[i - previous_id]
        checkpoint["arrival"] = previous_checkpoint["departure"] + timedelta(milliseconds=checkpoint["duracao"])

        if not checkpoint["apenas_parada"]:
            duracao += checkpoint["duracao"]

        tempo_embarque = checkpoint["tempo_embarque"] or DEFAULT_TEMPO_EMBARQUE_MS
        checkpoint["departure"] = checkpoint["arrival"] + timedelta(milliseconds=tempo_embarque)

        if not checkpoint["apenas_parada"] and checkpoint["idx"] != checkpoints[-1]["idx"]:
            duracao += tempo_embarque

    d["duracao_total"] = duracao
    checkpoints[-1]["departure"] = None

    for checkpoint in checkpoints:
        if checkpoint["arrival"]:
            checkpoint["arrival"] = to_tz(checkpoint["arrival"], checkpoint["local"]["timezone"]).isoformat()
        if checkpoint["departure"]:
            checkpoint["departure"] = to_tz(checkpoint["departure"], checkpoint["local"]["timezone"]).isoformat()

    return d


def siglas_itinerario_grupo_dict(grupo):
    itinerario = itinerario_svc.get_itinerario(grupo.rota.id, local=True, with_paradas=False)
    siglas = [{"local": {"sigla": checkpoint["local"]["sigla"]}} for checkpoint in itinerario]

    return {"itinerario": siglas}


def rota_dict_new(grupo, trechos_classes):
    trechos_vendidos = grupo.rota.get_trechos_vendidos_ids_ori_dest()
    grupo.add_pessoas_to_trechos_vendidos(trechos_classes, trechos_vendidos)
    siglas = []
    itinerario = []
    for iti in grupo.rota.itinerario.all():
        siglas.append(iti.local.cidade.sigla)
        itinerario.append(
            {
                "local": {
                    "id": iti.local.id,
                    "name": iti.local.cidade.name,
                    "uf": iti.local.cidade.uf,
                    "sigla": iti.local.cidade.sigla,
                    "nickname": iti.local.nickname,
                    "endereco": iti.local.endereco,
                    "modelos_venda": iti.local.modelos_venda,
                }
            }
        )
    return {
        "id": grupo.rota_id,
        "trechos_vendidos": trechos_vendidos,
        "itinerario": itinerario,
        "distancia_total": grupo.rota.distancia_total,
        "sigla": " - ".join(siglas),
    }


def serialize_replica(grupos):
    return StaffListGrupoSerializer().serialize(grupos, with_additional_properties=True)


class SolverSimulationSerializer(BaseSerializer):
    def prepare_objects(self, objs):
        prefetch_related_objects(
            objs,
            Prefetch(
                "confirmacaointeligentelogger_set",
                queryset=(
                    ConfirmacaoInteligenteLogger.objects.filter(solver_strategy="SIMULATION")
                    .only("grupo_id", "grupo_confirmado", "additional_data")
                    .order_by("-created_at")
                ),
            ),
        )

    def serialize_object(self, obj: Grupo) -> dict[str, bool]:
        log_grupo = obj.confirmacaointeligentelogger_set.first()
        sugestao_solver = False
        cancelamento_cruzado = False
        if log_grupo is not None:
            sugestao_solver = not log_grupo.grupo_confirmado
            cancelamento_cruzado = (
                log_grupo.additional_data.get("cancelamento_cruzado", False) if log_grupo.additional_data else False
            ) and not log_grupo.grupo_confirmado
        return {"sugestao_solver_cancelar": sugestao_solver, "cancelamento_cruzado": cancelamento_cruzado}


class StaffListGrupoSerializer(BaseSerializer):
    extra = {
        "solver_suggestions": SolverSimulationSerializer,
    }

    select_related = [
        "driver_one",
        "driver_two",
        "company",
        "onibus",
        "reputation",
        "rotina_onibus__base_operacional",
        "rotina_onibus__rota_principal",
    ]
    prefetch_related = [
        Prefetch(
            "rota__trechos_vendidos",
            queryset=TrechoVendido.objects.select_related("origem__cidade", "destino__cidade").only(
                "rota_id",
                "origem__cidade__sigla",
                "destino__cidade__sigla",
                "origem__nickname",
                "destino__nickname",
                "secundario",
            ),
        ),
        Prefetch(
            "rota__itinerario",
            queryset=Checkpoint.objects.select_related("local__cidade").only(
                "rota_id",
                "local__cidade__name",
                "local__cidade__uf",
                "local__cidade__sigla",
                "local__modelos_venda",
                "local__nickname",
                "local__endereco",
                "local__cidade__timezone",
            ),
        ),
        Prefetch(
            "trechoclasse_set",
            queryset=TrechoClasse.objects.select_related("grupo_classe", "trecho_vendido", "price_manager")
            .prefetch_related("price_manager__buckets")
            .only(
                "pk",
                # Bucketização depende de max_split_value, pessoas, price_manager e datetime_ida.
                "max_split_value",
                "pessoas",
                "datetime_ida",
                "price_manager",
                # As vagas e o ref_split_value podem mudar.
                "vagas",
                "ref_split_value",
                "grupo_id",
                "trecho_vendido_id",
                "grupo_classe__tipo_assento",
                "closed",
                "closed_reason",
                "closed_by_id",
            ),
        ),
        Prefetch(
            "grupoclasse_set",
            queryset=GrupoClasse.objects.only(
                "grupo_id", "capacidade", "closed", "closed_reason", "closed_by_id", "tipo_assento", "pessoas"
            ),
        ),
    ]

    def prepare_queryset(self, qs):
        return qs.only(
            # campos grupo
            "datetime_ida",
            "status",
            "modelo_venda",
            "modelo_operacao",
            "checkin_status",
            "contar_dia_parado",
            "valor_frete",
            "nao_teve_frete",
            "is_extra",
            "problem_description",
            "observation",
            "valor_encomenda",
            "confirming_probability",
            "rota_id",
            "checkpoint_idx",
            "autorizacao_hibrido_id",
            "percentual_repasse",
            # campos de outras tabelas - select_related
            "rotina_onibus__frete_atual",
            "rotina_onibus__minimo_reservas",
            "rotina_onibus__minimo_reservas_prev",
            "rotina_onibus__solver_threshold",
            "rotina_onibus__regional",
            "rotina_onibus__frequencia",
            "rotina_onibus__nome",
            "rotina_onibus__base_operacional__name",
            "rotina_onibus__rota_principal__eixo",
            "onibus_id",
            "onibus__placa",
            "onibus__locadora",
            "onibus__available",
            "onibus__jsondata",
            "company__name",
            "driver_one__first_name",
            "driver_one__last_name",
            "driver_two__first_name",
            "driver_two__last_name",
            "reputation__count_feedbacks",
            "reputation__count_comments",
            "reputation__rating",
        )

    def groups_with_conexao(self, groups_ids) -> list[int]:
        return list(
            Travel.objects.filter(grupo__in=groups_ids, travel_conexao__isnull=False)
            .values_list("grupo_id", flat=True)
            .order_by()
        )

    def _process_prob_cancelar(self, grupo_id: int, prob_prejuizo_map: dict) -> tuple[str, str] | tuple[None, None]:
        prob_prejuizo = prob_prejuizo_map.get(grupo_id)
        if prob_prejuizo is not None:
            prob_raw = prob_prejuizo["probabilidade_percentual"]
            percentual_str = "{:.2%}".format(prob_raw)
            categorica = "baixa" if prob_raw < 0.4 else "média" if prob_raw <= 0.65 else "alta"
            return percentual_str, categorica
        return None, None

    def prepare_objects(self, grupos):
        gids = [g.id for g in grupos]
        count_checkin_map = grupos_svc.count_checkin(gids)
        prob_prejuizo_map = grupos_svc.get_probabilidade_prejuizo(gids)
        grupos_com_incidentes = new_incidents_adapter.groups_have_incidents(gids)
        groups_with_vip_pax = grupos_svc.groups_with_vip_pax(gids)
        groups_with_conexao = self.groups_with_conexao(gids)
        conexoes_count_by_group = Counter(groups_with_conexao)
        for grupo in grupos:
            grupo.count_checkin = count_checkin_map.get(grupo.id, 0)
            prob_percent, prob_categ = self._process_prob_cancelar(grupo.id, prob_prejuizo_map)
            grupo.probabilidade_prejuizo_percentual = prob_percent
            grupo.probabilidade_prejuizo_categorica = prob_categ
            grupo.has_incident = grupos_com_incidentes.get(grupo.id, False)
            grupo.has_vip = grupo.id in groups_with_vip_pax
            grupo.has_conexao = grupo.id in groups_with_conexao
            grupo.conexao_qtd = conexoes_count_by_group[grupo.id]

    # ATENÇÃO
    # Se adicionar algum dado no serialize_object
    # Garantir que está sendo ***explicitamente*** carregado nos
    # prepare_queryset, prefetch_related ou select_related

    def serialize(self, objs, with_additional_properties=False):
        yield from map(self.serialize_object, objs, [with_additional_properties] * len(objs))

    def serialize_object(self, grupo, with_additional_properties=False):
        # TODO: Tentar melhorar essa parte de rota e checkpoint
        trechos_classes = {}
        for tc in grupo.trechoclasse_set.all():
            cmap = trechos_classes.setdefault(tc.trecho_vendido_id, {})
            dtrechoclasse = {
                "grupo_classe_id": tc.grupo_classe_id,
                "max_split_value": tc.max_split_value,
                "ref_split_value": tc.ref_split_value,
                "preco_atual": tc.get_price_manager().value,
                "buckets": get_active_buckets(tc),
                "tipo_assento": tc.grupo_classe.tipo_assento,
                "pessoas": tc.pessoas,
                "promo_value": tc.max_promo_value_bucket,
            }
            if with_additional_properties:
                dtrechoclasse.update(
                    {
                        "additional_properties": {
                            "closed": tc.closed,
                            "closed_reason": tc.closed_reason,
                            "closed_by_id": tc.closed_by_id,
                        }
                    }
                )
            cmap[tc.grupo_classe_id] = dtrechoclasse

        itinerario_qs = grupo.rota.get_itinerario()
        if grupo.checkpoint_idx > 0:
            itinerario = list(itinerario_qs)
            checkpoint_sigla = itinerario[grupo.checkpoint_idx].local.cidade.sigla
        else:
            checkpoint_sigla = None

        # tz_origem = grupo.rota.origem.cidade.timezone
        tz_origem = itinerario_qs[0].local.cidade.timezone if itinerario_qs else None

        classes = sorted(
            [
                {
                    "id": classe.id,
                    "closed": classe.closed,
                    "closed_reason": classe.closed_reason,
                    "max_capacity": classe.capacidade,
                    "pessoas": classe.pessoas,
                    "tipo_assento": classe.tipo_assento,
                    **(
                        {
                            "additional_properties": {
                                "closed": classe.closed,
                                "closed_reason": classe.closed_reason,
                                "closed_by_id": classe.closed_by_id,
                            }
                        }
                        if with_additional_properties
                        else {}
                    ),
                }
                for classe in grupo.grupoclasse_set.all()
            ],
            key=lambda gc: gc["tipo_assento"],
        )

        serialized = {
            "id": grupo.id,
            "datetime_ida": to_tz(grupo.datetime_ida, tz_origem) if tz_origem else grupo.datetime_ida,
            "status": grupo.status,
            "probabilidade_prejuizo_percentual": grupo.probabilidade_prejuizo_percentual,
            "probabilidade_prejuizo_categorica": grupo.probabilidade_prejuizo_categorica,
            "modelo_venda": grupo.modelo_venda,
            "modelo_operacao": grupo.modelo_operacao,
            "checkin_status": grupo.checkin_status,
            "contar_dia_parado": grupo.contar_dia_parado,
            "valor_frete": grupo.valor_frete,
            "nao_teve_frete": grupo.nao_teve_frete,
            "is_extra": grupo.is_extra,
            "problem_description": grupo.problem_description,
            "observation": grupo.observation,
            "valor_encomenda": grupo.valor_encomenda,
            "confirming_probability": grupo.confirming_probability,
            "count_checkin": grupo.count_checkin,
            "has_incident": grupo.has_incident,
            # prefetch_related joins
            "pessoas": grupo.count_pessoas(),
            "max_capacity": grupo.count_capacidade_onibus(),
            "rota": rota_dict_new(grupo, trechos_classes),
            "checkpoint_sigla": checkpoint_sigla,
            "trechos_classes": trechos_classes,
            "classes": classes,
            "has_vip_users": grupo.has_vip,
            "has_conexao": grupo.has_conexao,
            "conexao_qtd": grupo.conexao_qtd,
        }
        #  select_related_joins
        if grupo.driver_one:
            serialized["driver_one_name"] = grupo.driver_one.get_full_name()
        if grupo.driver_two:
            serialized["driver_two_name"] = grupo.driver_two.get_full_name()
        if grupo.company:
            serialized["empresa_name"] = grupo.company.name
            serialized["empresa_id"] = grupo.company_id
        if grupo.onibus:
            serialized["onibus_id"] = grupo.onibus_id
            serialized["onibus_placa"] = grupo.onibus.placa
            serialized["is_plotado"] = grupo.onibus.is_plotado
            serialized["onibus_available"] = grupo.onibus.available
            serialized["onibus_locadora"] = grupo.onibus.locadora
        if grupo.reputation:
            rep = grupo.reputation
            serialized["count_feedbacks"] = rep.count_feedbacks
            serialized["count_comments"] = rep.count_comments
            serialized["rating"] = rep.rating
        if grupo.rotina_onibus:
            rotina = grupo.rotina_onibus
            serialized["rotina_onibus"] = rotina.nome
            serialized["rotina_onibus_regional"] = rotina.regional
            serialized["rotina_onibus_id"] = rotina.id
            serialized["rotina_onibus_frete_atual"] = rotina.frete_atual
            serialized["rotina_onibus_minimo_reservas"] = rotina.minimo_reservas
            serialized["rotina_onibus_minimo_reservas_prev"] = rotina.minimo_reservas_prev
            serialized["rotina_onibus_solver_threshold"] = rotina.solver_threshold
            serialized["rotina_onibus_frequencia"] = rotina.frequencia
            if rotina.base_operacional:
                serialized["rotina_onibus_base"] = rotina.base_operacional.name
            if rotina.rota_principal:
                serialized["rotina_onibus_rota_principal"] = rotina.rota_principal.eixo
        if grupo.autorizacao_hibrido_id:
            serialized["autorizacao_hibrido_id"] = grupo.autorizacao_hibrido_id

        return serialized


class RotaExtra(BaseSerializer):
    def serialize_object(self, grupo: Grupo) -> dict:
        paradas = sorted(
            [check.to_dict_json() for check in grupo.rota.checkpointparada_set.all()],
            key=lambda x: x["idx"],
        )

        for parada in paradas:
            origem_id = parada["origem_id"]

            indice_origem = next((i for i, check in enumerate(grupo.itinerario) if check["id"] == origem_id), None)

            paradas_ids = [check["id"] for check in grupo.itinerario]

            if indice_origem is not None and parada["id"] not in paradas_ids:
                # verifica se o proximo checkpoint apos o ponto de origem ja é uma parada de lanche
                # se for joga a proxima parada pra proxima posicao

                while grupo.itinerario[indice_origem + 1]["apenas_parada"]:
                    indice_origem += 1

                grupo.itinerario.insert(indice_origem + 1, parada)

        itinerario_dinamico = ItinerarioDinamico(grupo, grupo.itinerario).to_dict_json()

        grupo_arrival_departure = itinerario_svc.append_arrival_and_departure_times(
            itinerario_dinamico, grupo.datetime_ida
        )

        return {
            "rota": {
                "itinerario": [
                    {
                        "id": ck["id"],
                        "checkin_status": grupo.checkin_status,
                        "arrival": grupo_arrival_departure[ck["id"]]["arrival"],
                        "departure": grupo_arrival_departure[ck["id"]]["departure"],
                        "datetime_checkin_open": grupo.datetime_checkin_open,
                        "datetime_checkin_close": grupo.datetime_checkin_close,
                        "datetime_travel_finished": grupo.datetime_travel_finished,
                        "local": {
                            "sigla": ck["local"]["sigla"],
                            "name": ck["local"]["name"],
                            "uf": ck["local"]["uf"],
                            "endereco": ck["local"]["endereco"],
                            "nickname": ck["local"]["nickname"],
                            "mapurl": ck["local"]["mapurl"],
                            "description": ck["local"]["description"],
                            "timezone": ck["local"]["timezone"],
                            "lunch_stop": ck["apenas_parada"],
                            "max_minutes_of_stay": ck["local"]["max_minutos_permanencia"],
                        },
                    }
                    for ck in itinerario_dinamico
                ],
                "distancia_total": grupo.rota.distancia_total,
                "duracao_total": timedelta_to_milliseconds(grupo.duracao_total),
            }
        }

    def prepare_objects(self, grupos):
        (
            prefetch_related_objects(
                grupos,
                Prefetch(
                    "rota__trechos_vendidos",
                    queryset=TrechoVendido.objects.select_related("origem__cidade", "destino__cidade"),
                ),
                Prefetch(
                    "rota__checkpointparada_set",
                    queryset=CheckpointParada.objects.select_related("checkpoint_origem", "checkpoint_destino").filter(
                        ativo=True
                    ),
                ),
            ),
        )

        itinerarios = itinerario_svc.get_itinerario_data(grupos, local=True)

        for grupo in grupos:
            setattr(grupo, "itinerario", itinerarios[grupo.rota_id])


class NotaFiscalExtra(BaseSerializer):
    def prepare_objects(self, grupos):
        prefetch_related_objects(grupos, Prefetch("notafiscal", queryset=NotaFiscal.objects.select_related("company")))

    def serialize_object(self, grupo: Grupo) -> dict:
        return {
            "notafiscal": (
                {
                    "id": grupo.notafiscal.id,
                    "url": grupo.notafiscal.get_url(),
                    "xml_url": storage.storage_url(grupo.notafiscal, "xml_s3key"),
                    "payment": None,  # compatibilidade com o frontend do parceiro
                    "status": grupo.notafiscal.status,
                    "numero": grupo.notafiscal.numero,
                    "serie": grupo.notafiscal.cteos_serie,
                    "valor": grupo.notafiscal.valor,
                    "company_name": grupo.notafiscal.company.razao_social if grupo.notafiscal.company else None,
                    "cnpj": grupo.notafiscal.get_formatted_cnpj(),
                    "emissao_auto": grupo.notafiscal.emissao_auto,
                    "status_parceiro": grupo.notafiscal.status_parceiro,
                    "fix_instructions": grupo.notafiscal.fix_instructions,
                    "status_sefaz": grupo.notafiscal.status_sefaz,
                    "data_emissao": grupo.notafiscal.data_emissao,
                }
                if grupo.notafiscal
                else None
            )
        }


class ClasseExtra(BaseSerializer):
    def prepare_objects(self, grupos):
        prefetch_related_objects(grupos, Prefetch("grupoclasse_set"))

    def serialize_object(self, grupo: Grupo) -> dict:
        return {
            "classes": sorted(
                [
                    {
                        "id": classe.id,
                        "max_capacity": classe.capacidade,
                        "pessoas": classe.pessoas,
                        "tipo_assento": classe.tipo_assento,
                        "closed": classe.closed,
                        "closed_reason": classe.closed_reason,
                        "closed_by": classe.closed_by.get_full_name() if classe.closed_by else None,
                    }
                    for classe in grupo.grupoclasse_set.all()
                ],
                key=lambda gc: gc["tipo_assento"],
            )
        }


class AutorizacaoGrupoExtra(BaseSerializer):
    def serialize_object(self, grupo: Grupo) -> dict:
        if not hasattr(grupo, "autorizacao_grupo"):
            return {"autorizacao_grupo": None}
        autorizacao_grupo = grupo.autorizacao_grupo
        s3_key = autorizacao_grupo.s3_key or grupo.autorizacao_s3key
        autorizacao_url = storage._storage_url("autorizacao_s3key", s3_key, grupo) if s3_key else None
        return {
            "autorizacao_grupo": {
                "reprovacoes": autorizacao_grupo.reprovacoes,
                "id": autorizacao_grupo.id,
                "comentario": autorizacao_grupo.comentario,
                "status": autorizacao_grupo.status,
                "situacao_licenca": autorizacao_grupo.situacao_licenca,
                "authorization_url": autorizacao_url,
            }
        }


class DriverExtra(BaseSerializer):
    def prepare_objects(self, grupos):
        prefetch_related_objects(grupos, Prefetch("driver_one"), Prefetch("driver_two"))

    def serialize_object(self, grupo: Grupo) -> dict:
        return {
            "driver_one": (
                {
                    "id": grupo.driver_one.id,
                    "name": grupo.driver_one.get_full_name(),
                    "first_name": grupo.driver_one.first_name,
                    "last_name": grupo.driver_one.last_name,
                }
                if grupo.driver_one
                else None
            ),
            "driver_two": (
                {
                    "id": grupo.driver_two.id,
                    "name": grupo.driver_two.get_full_name(),
                    "first_name": grupo.driver_two.first_name,
                    "last_name": grupo.driver_two.last_name,
                }
                if grupo.driver_two
                else None
            ),
        }


class MarketplaceExtra(BaseSerializer):
    def prepare_objects(self, grupos):
        prefetch_related_objects(grupos, Prefetch("travel_set__pagamento__accountingoperation_set"))

    def serialize_object(self, grupo: Grupo) -> dict:
        info_financeira_grupo = InfoFinanceiraGrupo(grupo)

        info_marketplace = {
            "info_trechos": [asdict(InfoTrechoMarketplace(tc)) for tc in grupo.trechoclasse_set.all()],
            # "info_financeira": info_financeira_grupo,
            "repasse": info_financeira_grupo.repasse_marketplace,
            "ida_ultimo_trecho": None,
        }

        datetimes_idas_trechos = sorted(
            [
                to_tz(tc.datetime_ida, tc.trecho_vendido.origem.cidade.timezone)
                for tc in grupo.trechoclasse_set.all()
                if tc.datetime_ida is not None
            ]
        )

        if datetimes_idas_trechos:
            info_marketplace["ida_ultimo_trecho"] = datetimes_idas_trechos[-1]

        return {"info_marketplace": info_marketplace}


class GrupoTrechoClasseSerializer(BaseSerializer):
    def prepare_objects(self, grupos):
        prefetch_related_objects(
            grupos,
            Prefetch(
                "trechoclasse_set",
                queryset=TrechoClasse.objects.select_related(
                    "grupo_classe",
                    "trecho_vendido",
                    "price_manager",
                    "trecho_vendido__origem__cidade",
                    "trecho_vendido__destino__cidade",
                ),
            ),
            Prefetch(
                "trechoclasse_set__pedidoalteracaopreco_set",
                queryset=PedidoAlteracaoPreco.objects.select_related(
                    "trecho_classe__trecho_vendido__origem__cidade",
                    "trecho_classe__trecho_vendido__destino__cidade",
                    "trecho_classe__grupo_classe__closed_by",
                ),
            ),
            Prefetch("trechoclasse_set__price_manager"),
            Prefetch("trechoclasse_set__price_manager__buckets"),
        )

    def serialize_object(self, grupo: Grupo) -> dict:
        tcmap = {}

        for tc in grupo.trechoclasse_set.all():
            cmap = tcmap.setdefault(tc.trecho_vendido_id, {})
            dtrechoclasse = tc.to_dict_json_base()
            dtrechoclasse.update(
                {"max_split_value": tc.max_split_value_bucket, "ref_split_value": tc.ref_split_value_bucket}
            )
            cmap[tc.grupo_classe_id] = dtrechoclasse
            cmap[tc.grupo_classe_id]["origem"] = {
                "name": tc.trecho_vendido.origem.cidade.name,
                "uf": tc.trecho_vendido.origem.cidade.uf,
            }
            cmap[tc.grupo_classe_id]["destino"] = {
                "name": tc.trecho_vendido.destino.cidade.name,
                "uf": tc.trecho_vendido.destino.cidade.uf,
            }

            status_base = PedidoAlteracaoPreco.Status.APROVADO
            if tc.pedidoalteracaopreco_set.exists():
                pedido = tc.pedidoalteracaopreco_set.latest("id")
                cmap[tc.grupo_classe_id]["pedidos_alteracao_preco"] = pedido.to_dict_json()

                if pedido.status == PedidoAlteracaoPreco.Status.REJEITADO and pedido.updated_at >= to_default_tz(
                    dateutils.now() - timedelta(days=4)
                ):
                    status_base = PedidoAlteracaoPreco.Status.REJEITADO

                if pedido.status == PedidoAlteracaoPreco.Status.PENDENTE:
                    status_base = PedidoAlteracaoPreco.Status.PENDENTE
            cmap[tc.grupo_classe_id]["status"] = status_base
        return {"id": grupo.id, "trechos_classes": tcmap}


class StatusPedidoAlteracaoPreco(BaseSerializer):
    def prepare_objects(self, grupos):
        prefetch_related_objects(
            grupos,
            Prefetch(
                "trechoclasse_set", queryset=TrechoClasse.objects.select_related("grupo_classe", "trecho_vendido")
            ),
            Prefetch(
                "trechoclasse_set__pedidoalteracaopreco_set",
                queryset=PedidoAlteracaoPreco.objects.select_related(
                    "trecho_classe__trecho_vendido__origem__cidade",
                    "trecho_classe__trecho_vendido__destino__cidade",
                    "trecho_classe__grupo_classe__closed_by",
                ),
            ),
        )

    def serialize_object(self, grupo: Grupo) -> dict:
        if not grupo.is_marketplace:
            return {"status_pedido_alteracao_preco": None}

        status_base = PedidoAlteracaoPreco.Status.APROVADO

        pedidos_mais_recente = {}
        for trecho_classe in grupo.trechoclasse_set.all():
            for p in trecho_classe.pedidoalteracaopreco_set.all():
                if (
                    trecho_classe.id not in pedidos_mais_recente
                    or p.created_at > pedidos_mais_recente[trecho_classe.id].created_at
                ):
                    pedidos_mais_recente[trecho_classe.id] = p

        if any(p.status == PedidoAlteracaoPreco.Status.REJEITADO for p in pedidos_mais_recente.values()):
            status_base = PedidoAlteracaoPreco.Status.REJEITADO

        elif any(p.status == PedidoAlteracaoPreco.Status.PENDENTE for p in pedidos_mais_recente.values()):
            status_base = PedidoAlteracaoPreco.Status.PENDENTE

        return {"status_pedido_alteracao_preco": status_base}


class InicioPrimeiroEmbarqueExtra(BaseSerializer):
    def serialize_object(self, obj: Grupo):
        primeiro_itinerario = obj.rota.itinerario.first()
        datetime_ida = to_tz_origem(obj.datetime_ida, primeiro_itinerario)
        local_embarque = primeiro_itinerario.local if primeiro_itinerario else None
        inicio_primeiro_embarque = calculate_arrival_for_first_pde(local_embarque, datetime_ida)
        return {"inicio_primeiro_embarque": inicio_primeiro_embarque}

    def prepare_objects(self, objs):
        prefetch_related_objects(
            objs,
            Prefetch(
                "rota__itinerario",
                queryset=Checkpoint.objects.select_related("local__cidade").prefetch_related("local__weekdays"),
            ),
        )


class ParceiroFretamentoGrupoSerializer(BaseSerializer):
    select_related = ["autorizacao_grupo", "pedagio"]

    extra = {
        "rota": RotaExtra,
        "notafiscal": NotaFiscalExtra,
        "classe": ClasseExtra,
        "driver": DriverExtra,
        "status_pedido_alteracao_preco": StatusPedidoAlteracaoPreco,
        "marketplace": MarketplaceExtra,
        "autorizacao_grupo": AutorizacaoGrupoExtra,
        "pedagio": PedagioExtra,
        "inicio_primeiro_embarque": InicioPrimeiroEmbarqueExtra,
    }

    @classmethod
    def detailed(cls, modelos_venda=None):
        base_extra = ("rota", "notafiscal", "classe", "driver", "pedagio", "inicio_primeiro_embarque")
        marketplace_extra = ("marketplace", "status_pedido_alteracao_preco")
        fretamento_extra = ("autorizacao_grupo",)

        extra = base_extra

        if modelos_venda is not None:
            if "marketplace" in modelos_venda:
                extra += marketplace_extra
            if "buser" in modelos_venda:
                extra += fretamento_extra

        else:
            extra += marketplace_extra + fretamento_extra

        return cls(extra=extra)

    def prepare_objects(self, grupos):
        prefetch_related_objects(
            grupos,
            Prefetch("onibus"),
            Prefetch(
                "trechoclasse_set",
                queryset=TrechoClasse.objects.select_related("grupo_classe", "trecho_vendido").prefetch_related(
                    "pedidoalteracaopreco_set"
                ),
            ),
        )

    def serialize_object(self, grupo: Grupo) -> dict:
        tz_origem = grupo.rota.origem.cidade.timezone
        grupo_serialized = {
            "id": grupo.id,
            "billing_status": grupo.billing_status,
            "datetime_ida": to_tz(grupo.datetime_ida, tz_origem),
            "timezone_ida": tz_origem,
            "is_marketplace": grupo.is_marketplace,
            "status": grupo.status,
            "nao_teve_frete": grupo.nao_teve_frete,
            "autorizacao_url": grupo.autorizacao_url(),
            "artesp_number": grupo.artesp_number,
            "modelo_venda": grupo.modelo_venda,
            "contar_dia_parado": grupo.contar_dia_parado,
            "valor_frete": grupo.valor_frete,
            "observacao_dia_parado": grupo.observacao_dia_parado,
            "rota_id": grupo.rota_id,
            "rotina_id": grupo.rotina_onibus_id,
            "empresa": {"id": grupo.company_id},
            "onibus": {
                "id": grupo.onibus.id if grupo.onibus else None,
                "placa": grupo.onibus.placa if grupo.onibus else None,
                "modelo_venda": grupo.onibus.modelo_venda if grupo.onibus else None,
                "tipo": grupo.onibus.tipo if grupo.onibus else None,
            },
            "motorista_descansado": grupo.motorista_descansado,
            "confirming_probability": grupo.confirming_probability,
            "one_driver_exception": grupo.one_driver_exception,
        }

        return grupo_serialized


class DerMgGroupSerializer(BaseSerializer):
    select_related = ["driver_one", "driver_two", "onibus", "notafiscal"]

    prefetch_related = [
        Prefetch(
            "rota__trechos_vendidos", queryset=TrechoVendido.objects.select_related("origem__cidade", "destino__cidade")
        ),
        Prefetch("travel_set__passageiro_set", queryset=Passageiro.objects.select_related("buseiro")),
    ]

    def __init__(self, group_back_id, invoice_number, invoice_serie):
        super().__init__()
        self.group_back_id = group_back_id
        self.invoice_number = invoice_number
        self.invoice_serie = invoice_serie

    def serialize_object(self, group):
        cities_names = " - ".join([cidade["name"] for cidade in get_cidades(group.rota.id)])

        group_back = Grupo.objects.get(id=self.group_back_id)
        bus_capacity = group.onibus.capacidade_total

        return {
            "license_plate": group.onibus.placa,
            "driver_one": (
                {"name": group.driver_one.get_full_name(), "identifier": group.driver_one.profile.cpf}
                if group.driver_one
                else None
            ),
            "driver_two": (
                {"name": group.driver_two.get_full_name(), "identifier": group.driver_two.profile.cpf}
                if group.driver_two
                else None
            ),
            "origin_id": get_der_mg_city_code(group.rota.origem.cidade.name),
            "destiny_id": get_der_mg_city_code(group.rota.destino.cidade.name),
            "group_going_date": toprettybrdate(to_tz_origem(group.datetime_ida, group.rota)),
            "group_going_time": to_tz_origem(group.datetime_ida, group.rota).strftime("%H:%M"),
            "group_back_date": toprettybrdate(to_tz_origem(group_back.datetime_ida, group_back.rota)),
            "group_back_time": to_tz_origem(group_back.datetime_ida, group_back.rota).strftime("%H:%M"),
            "route_distance": group.rota.distancia_total,
            "invoice_number": self.invoice_number if not group.notafiscal else group.notafiscal.numero,
            "invoice_serie": self.invoice_serie if not group.notafiscal else group.notafiscal.cteos_serie,
            "route_details": cities_names,
            "passengers": self.get_passengers_documentation([group.id, group_back.id])[:bus_capacity],
        }

    def get_passengers_documentation(self, groups_ids: list) -> list:
        passengers = []

        travels = (
            Travel.objects.prefetch_related(
                Prefetch(
                    "passageiro_set",
                    queryset=Passageiro.objects.filter(removed=False)
                    .select_related("buseiro")
                    .order_by("buseiro__name")
                    .distinct("buseiro__name", "buseiro__rg_number", "buseiro__rg_orgao"),
                )
            )
            .filter(grupo_id__in=groups_ids, status="pending")
            .order_by("grupo__datetime_ida")
        )

        for travel in travels:
            for pax in travel.passageiro_set.all():
                rg = pax.buseiro.rg_number
                rg_org = pax.buseiro.rg_orgao or "SSPMG"

                emissor = rg_org if rg else "CPF"
                identifier = rg if rg else pax.buseiro.cpf

                if any(p.get("identifier") == identifier and p.get("emissor") == emissor for p in passengers):
                    raise DerMgException(
                        error=f"O documento {identifier} está duplicado. Gentileza gerar no portal da DER-MG"
                    )

                passenger_data = {"name": pax.buseiro.name, "identifier": identifier, "emissor": emissor}

                passengers.append(passenger_data)
        return passengers


class GroupsOfTheSameSlotSerializer(BaseSerializer):
    select_related = ["company", "onibus"]

    def serialize_object(self, group):
        return {
            "group_id": group.id,
            "company_name": getattr(group.company, "name", None),
            "license_plate": getattr(group.onibus, "placa", None),
        }
