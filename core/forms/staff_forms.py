import copy
import json
from datetime import date, datetime, time, timedelta
from decimal import Decimal
from enum import Enum
from typing import Any

from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import InMemoryUploadedFile, TemporaryUploadedFile
from django.db.models import F, Prefetch, Q
from django.utils import timezone
from pydantic.v1 import (
    BaseModel,
    Field,
    Json,
    StrictBool,
    condecimal,
    conint,
    constr,
    root_validator,
    validator,
)
from pydantic.v1.dataclasses import dataclass

from commons import dateutils
from commons.dateutils import to_default_tz, today
from commons.pydantic import (
    Cep,
    Cnpj,
    Cpf,
    Email,
    ModelForm,
    Phone,
    model_choice,
    model_choice_list,
)
from commons.utils import only_numbers
from core.constants import MOTIVOS_REMANEJAMENTO, TIPOS_ASSENTO
from core.forms.base import RequestForm
from core.forms.buckets_forms import Trecho<PERSON>lasseForm
from core.forms.core_forms import Slug
from core.forms.paginator_forms import PaginatorConfigForm
from core.models_commons import Cidade, Imagem, LandingPage, PontoDeVenda
from core.models_company import Company
from core.models_driving import AlertaSeguranca
from core.models_grupo import GROUP_CONFIRMING_STATUSES, Grupo, RestrictionTypes
from core.models_parada import CheckpointParada
from core.models_plataforma import Plataforma
from core.models_rota import (
    Checkpoint,
    DuracaoDinamica,
    LocalEmbarque,
    LocalRetiradaMarketplace,
    Rota,
)
from core.models_travel import Pagamento, Travel
from core.serializers.serializer_locais import LocalEmbarqueSerializer
from core.service.editar_porcentagem_repasse_taxa_servico_svc import PercentualTypes
from core.service.image_validator_svc import image_is_valid

GROUP_STATUSES = Grupo.Status.choices


def validate_tipo_assento(cls, tipo_assento):
    if tipo_assento not in TIPOS_ASSENTO:
        raise ValidationError(f"Tipo de assento {tipo_assento} inválido.")
    return tipo_assento


class PaginableModel(BaseModel):
    LIST_ALL_ITEMS = -1
    paginator: PaginatorConfigForm | None = None

    @property
    def has_pagination(self):
        return self.paginator and self.paginator.rows_per_page != self.LIST_ALL_ITEMS

    @property
    def order_by(self):
        if not self.paginator:
            return None

        return self.paginator.order_by


class ListRotasForm(PaginableModel):
    class Config:
        anystr_strip_whitespace = True

    status_filter: bool | None = None
    only_fully_active: bool | None = False
    with_trechos: bool | None = False
    with_paradas: bool | None = False
    search: str = ""
    city_ids: list[int] | None = None
    local_embarque_ids: list[int] | None = None

    @property
    def search_by_cities_ids(self):
        return bool(self.city_ids)

    @property
    def search_by_local_embarque_ids(self):
        return bool(self.local_embarque_ids)

    @property
    def search_term_list(self):
        if self.search:
            return list({term.upper().strip() for term in self.search.split(",")})
        return []

    @property
    def is_search_by_trigrams(self):
        if self.search_term_list:
            return all(len(term) == 3 and term.isalpha() for term in self.search_term_list)

    @property
    def is_search_by_id(self):
        if self.search_term_list:
            return all(term.isdigit() for term in self.search_term_list)


class ListOnibusPlacaForm(BaseModel):
    placa: str | None = None
    onibus_ids: list[int] | None = Field(alias="onibusIds", default=None)
    company_id: int | None = Field(alias="companyId", default=None)
    removed: bool | None = False

    @validator("onibus_ids", pre=True)
    def parse_onibus_ids(cls, value):
        if value is None:
            return None
        if isinstance(value, list):
            return value
        if isinstance(value, str):
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                raise ValueError("Invalid JSON for onibusIds")
        raise ValueError("onibusIds must be a list or a JSON string")


class KeysetPaginatorForm(BaseModel):
    last_id: str | None = Field(alias="lastId")
    rows_per_page: int | None = Field(alias="rowsPerPage", default=10)
    prev_page: bool = Field(alias="prevPage", default=False)


class PaginatorForm(BaseModel):
    rows_per_page: int = Field(alias="rowsPerPage", default=10)
    page: int = Field(alias="page", default=1)
    descending: bool = False
    sort_by: str = Field(default="id", alias="sortBy")


class ListCompaniesForm(PaginableModel):
    all: bool = False
    detailed: bool = False

    company_id: int | None = Field(alias="id", default=None)
    name: str | None = None
    is_enabled: bool | None = Field(alias="isEnabled", default=None)
    vinculo: str | None = None
    modelo_venda: str | None = Field(alias="modeloVenda", default=None)
    doc_estadual_status: str | None = Field(alias="docEstadualStatus", default=None)
    gestor_id: int | None = Field(alias="gestorId", default=None)
    uf: str | None = None
    cidade_id: int | None = Field(alias="cidadeId", default=None)
    cobra_taxa_servico_checkout: bool | None = Field(alias="cobraTaxaServico", default=None)


class ListSimpleCompaniesForm(BaseModel):
    exclude_company: int | None


class BaseReplaceLocal(BaseModel):
    reason_change: str | None
    should_send_mail_alert: bool
    should_send_zap_alert: bool
    inactivate_existing_local: bool
    replacing_local: int | None
    affect_group_from_date: datetime | None
    group_id_list: list[int] | None

    @validator("affect_group_from_date")
    def validate_affect_group_from_data(cls, affect_group_from_date):
        return to_default_tz(affect_group_from_date)


class CheckpointForm(BaseModel):
    id: int
    local_id: int | str
    distancia_km: float | None = 0
    duracao: float | None = 0
    tempo_embarque: float | None = 0
    apenas_parada: bool
    local: dict | None
    additional_properties: dict[str, Any] = {}
    is_conexao: bool | None = False

    class Config:
        validate_assignment = True

    @validator("distancia_km")
    def set_distancia(cls, distancia_km):
        return distancia_km or 0


class ImpactoForm(BaseModel):
    old_rota_id: int
    new_rota_itinerario: list[CheckpointForm]
    old_rota_itinerario: list[CheckpointForm]


class ReplaceLocalForm(BaseReplaceLocal):
    local: dict | int
    route_id_list: list[int] | None
    is_new_local: bool | None = True
    impactos: list[ImpactoForm] | None = []

    @property
    def new_rota_itinerario_map(self):
        impactos = self.impactos or []
        return {i.old_rota_id: [ckpt.dict() for ckpt in i.new_rota_itinerario] for i in impactos}

    @validator("route_id_list", always=True, allow_reuse=True)
    def validate_route_id_list(cls, route_id_list, values):
        if route_id_list:
            return route_id_list
        if values.get("group_id_list"):
            group_id_list = values["group_id_list"]
            rota_id_qs = Grupo.objects.filter(id__in=group_id_list).values("rota_id").distinct()
            return [r["rota_id"] for r in rota_id_qs]
        raise ValueError("route_id_list ou group_id_list devem ser informados")

    @validator("is_new_local", allow_reuse=True)
    def validate_is_new_local(cls, is_new_local, values):
        local = values["local"]
        replacing_local = values.get("replacing_local")
        is_valid_new_local = isinstance(local, dict) and is_new_local
        is_valid_existing_local = isinstance(local, int) and replacing_local and not is_new_local
        if is_valid_new_local or is_valid_existing_local:
            return is_new_local
        raise ValueError("local deve ser um objeto quando is_new_local é true e um inteiro quando is_new_local é false")


class ReplaceLocalMapRow(BaseModel):
    route_id: int
    new_local_id: int


class ReplaceLocalBatchForm(BaseReplaceLocal):
    is_new_local: bool = False
    local_replacement_map: list[ReplaceLocalMapRow]


class HistoricoAlteracaoEmbarqueForm(BaseModel):
    local_id: int
    grupo_id: int


class ListDriversForm(PaginableModel):
    company: int | None
    driver: str | None
    company: str | None
    gestor_qualidade: int | None = Field(alias="gestorQualidade")
    gestor_comercial: int | None = Field(alias="gestorComercial")
    docs: list[str] | None

    class Config:
        anystr_strip_whitespace = True

    @validator("driver", "company")
    def empty_as_none(cls, v):
        return v or None


class ListPontosDeVendaForm(PaginableModel):
    class Config:
        anystr_strip_whitespace = True

    search: str | None
    cidade: Slug = None
    status_filter: bool | None

    @validator("status_filter")
    def se_tem_cidade_status_filter_eh_verdadeiro(cls, v, values):
        if values["cidade"]:
            return True
        return v


class ListLocaisForm(PaginableModel):
    class Config:
        anystr_strip_whitespace = True

    search: str | None = None
    status_filter: bool | None = None
    include_group_count: bool | None = False
    filter_city: list[int] | None = []
    filter_classification: list[str] | None = []
    filter_state: list[str] | str | None = []
    local_id: int | None = None
    modelos_venda: list[str] | None = []
    uso_do_local: list[str] | None = []
    gestor: dict | None = None
    status_flag: str | None = None
    onibus_suportados: list[str] | None = None
    categoria_do_ponto: str | None = None
    tipo_de_acesso: str | None = None
    zona_maxima_de_restricao: bool | None


class ListLociasSimpleForm(PaginableModel):
    exclude_self_id: int | None


class BasePontoDeVendaForm(BaseModel):
    nickname: str
    cnpj: Cnpj
    email: str
    phone: Phone
    map_url: str | None
    latitude: float | None
    longitude: float | None
    cidade: model_choice(Cidade) | None = Field(alias="cidade_id")
    endereco_logradouro: str | None
    endereco_bairro: str | None
    endereco_cep: str | None
    endereco_numero: str | None
    endereco_referencia: str | None
    ativo: bool | None = True
    public: bool
    tem_ponto_fisico: bool = True
    maximo_divida_pdv_para_bloqueio: float = 1000
    maximo_atraso_dias_para_bloqueio: int = 10
    vende_em_dinheiro: bool = False


class UpdatePontoDeVendaForm(BasePontoDeVendaForm):
    ponto_de_venda: model_choice(PontoDeVenda) = Field(alias="id")
    nickname: str | None
    cnpj: Cnpj | None
    phone: Phone | None
    email: Email | None
    map_url: str | None
    latitude: float | None
    longitude: float | None
    cidade: model_choice(Cidade) | None = Field(alias="cidade_id")
    endereco_logradouro: str | None
    endereco_bairro: str | None
    endereco_cep: str | None


class UpdateGestorPontoDeVendaForm(BaseModel):
    ponto_de_venda: model_choice(queryset=PontoDeVenda.objects.all()) = Field(alias="pdv_id")
    gestor_id: int | None


class SelectOptionsForm(BaseModel):
    search: str | None = None


class ListLocaisSelectOptionsForm(SelectOptionsForm): ...


class ListRotasSelectOptionsForm(SelectOptionsForm): ...


class ListRotasPrincipaisSelectOptionsForm(SelectOptionsForm):
    with_cidades_trajeto: bool = Field(default=False, alias="withCidadesTrajeto")
    paginator: PaginatorConfigForm | None


class ListCidadesForm(PaginableModel):
    active: bool | None = True
    with_viagens_or_pontos_turisticos_only: bool | None = False
    with_status: bool | None = False
    search_text: str | None = None
    uf: str | None

    def _set_order_desc_nulls_last(self, sort):
        if sort.startswith("-"):
            return F(sort[1:]).desc(nulls_last=True)
        return sort

    @property
    def order_by(self):
        if not self.paginator or not self.paginator.order_by:
            return None

        return [self._set_order_desc_nulls_last(field) for field in self.paginator.order_by]


class ListRestrictionsForm(BaseModel):
    rota_id: int
    date_ida_list: list[date]
    time_ida: str
    bus_id: int | None
    modelo_venda: str | None = None


class ListRestrictionsAlternativeForm(ListRestrictionsForm):
    avoid_local: list[model_choice(LocalEmbarque)]


class GetCidadeForm(PaginableModel):
    cidade: model_choice(queryset=Cidade.objects.all().prefetch_related("cidadeinfo__imagem_set"), field="slug") = (
        Field(alias="slug")
    )


class FinishGroupForm(RequestForm):
    group_id: int
    valor_frete: condecimal(ge=0, max_digits=12, decimal_places=2) | None
    nao_teve_frete: bool | None = False


class CanalNotificacao(str, Enum):
    SMS = "sms"
    EMAIL = "email"
    INBOX = "inbox"
    PUSH = "push"
    ZAP = "zap"


class AcaoContabil(str, Enum):
    RETER_PAGAMENTO = "RETER_PAGAMENTO"
    CREDITAR_APENAS = "CREDITAR_APENAS"
    CREDITAR_E_ESTORNAR = "CREDITAR_E_ESTORNAR"


CANCEL_GRUPO_QS = Grupo.objects.only(
    "status",
    "confirming_probability",
    "reputation_id",
    "notafiscal",
    "datetime_ida",
    "rota",
    "company__name",
    "company__gestor__first_name",
    "rotina_onibus_id",
    "rotina_onibus__rota_id",
    "onibus__placa",
).prefetch_related(
    "onibus",
    "company",
    "rotina_onibus",
    "rota__itinerario__local__cidade",
    Prefetch(
        "travel_set",
        queryset=Travel.objects.only(
            "status",
            "grupo",
            "grupo_classe",
            "trecho_classe",
            "trecho_vendido",
            "user",
            "pagamento",
            "travel_conexao_id",
        ),
    ),
)


class CancelarGrupoForm(BaseModel):
    notificacao: list[CanalNotificacao] = []
    acao_contabil: AcaoContabil = AcaoContabil.CREDITAR_APENAS
    canceled_reason: str | None
    reason_description: str | None
    auto: bool = False
    dias_parados: list[dict] = []
    groups: model_choice_list(
        Grupo,
        queryset=CANCEL_GRUPO_QS,
    )

    @root_validator
    def check_reter_pagamento_de_grupo_pending(cls, values):
        acao_contabil = values.get("acao_contabil")
        if acao_contabil == AcaoContabil.RETER_PAGAMENTO:
            groups = values.get("groups")
            if any(group.status == "pending" for group in groups):
                raise ValueError("ERRO: tentando RETER_PAGAMENTO de grupo pending")

        return values

    @property
    def tracking_keys(self):
        if self.tracking is None:
            return None
        return [t.value for t in self.tracking]


class BaseSendWhatsappTemplateForm(RequestForm):
    msgid: str
    group_id: int | None
    user_ids: list[int] | None = []
    contact_reason: str

    @validator("user_ids", pre=True)
    def validate_user_ids(cls, user_ids):
        if len(user_ids) > 0:
            return json.loads(user_ids)


class BaseSendWhatsappTemplateFormV2(RequestForm):
    msgid: str
    travel_ids: list[int] | None = None
    user_ids: list[int] | None = []
    contact_reason: str

    @validator("user_ids", pre=True)
    def validate_user_ids(cls, user_ids):
        if len(user_ids) > 0:
            return json.loads(user_ids)

    @validator("travel_ids", pre=True)
    def validate_travel_ids(cls, travel_ids):
        if travel_ids:
            return json.loads(travel_ids)


class SendWhatsappLancheTemplateForm(BaseSendWhatsappTemplateForm):
    valor_ressarcimento: Decimal

    @validator("valor_ressarcimento")
    def validate_valor_ressarcimento(cls, valor_ressarcimento):
        if valor_ressarcimento > 0:
            return valor_ressarcimento
        else:
            raise ValidationError("Valor de ressarcimento deve ser especificado")


class Weekday(conint(gt=0, lt=8)):
    pass


class TipoOnibus(str, Enum):
    TOCO = "toco"
    TRUCADO = "trucado"
    LD = "ld"
    DD = "dd"
    MICRO_ONIBUS = "micro-onibus"
    VAN = "van"


class WorkingHour(BaseModel):
    start_time: time
    end_time: time
    dia: Weekday
    max_embarque_simultaneo: int = 0
    max_minutos_permanencia: int = 30

    @validator("end_time")
    def validate_end_time(cls, end_time, values):
        start_time = values.get("start_time")
        if not start_time:
            return
        if end_time <= start_time:
            raise ValueError(f"Periodo de funcionamento invalido: das {start_time:%H:%M} às {end_time:'%H:%M'}")
        return end_time


class BaseLocalEmbarqueForm(BaseModel):
    working_hour_list: list[WorkingHour] | None
    always_working: bool | None
    endereco_numero: str | None
    endereco_cep: Cep | None
    max_embarque_simultaneo: conint(ge=0) | None
    onibus_suportados: list[TipoOnibus] | None
    classification: LocalEmbarque.Classification | None
    max_minutos_permanencia: int = 0

    @validator("always_working", always=True)
    def validate_always_working(cls, always_working, values):
        working_hour_list = values.get("working_hour_list")
        if always_working is True and working_hour_list:
            raise ValueError("Horario de funcionamento não deve ser especificado qnd always_working é true")
        if always_working is False and not working_hour_list:
            raise ValueError("Horario de funcionamento deve ser especificado qnd always_working é false")

        return always_working

    @validator("max_embarque_simultaneo")
    def validate_max_embarque_simultaneo(cls, max_embarque_simultaneo):
        if max_embarque_simultaneo == 0:
            return None
        return max_embarque_simultaneo


class LocalEmbarqueForm(BaseLocalEmbarqueForm):
    cidade: model_choice(Cidade) = Field(alias="cidade_id")
    map_url: str = Field(alias="mapurl")
    street_view_mapurl: str | None
    nickname: str
    description: str | None
    description_ao: str | None
    endereco_logradouro: str
    endereco_bairro: str
    latitude: float
    longitude: float
    always_working: bool = True
    reports: str | None
    observations: str | None
    status_flag: str | None
    features: list[str] | None
    contato_nome: str | None
    contato_email: str | None
    contato_telefone: str | None
    driver_description: str | None
    driver_endereco_numero: str | None
    driver_endereco_logradouro: str | None
    modelos_venda: list[str] | None
    uso_do_local: list[str] | None
    categoria_do_ponto: str | None
    tipo_de_acesso: str | None
    zona_maxima_de_restricao: bool = False
    aceita_embarque_ao: bool = False
    desembarque_rapido: bool = False

    @property
    def endereco(self):
        numero = f", {self.endereco_numero}" if self.endereco_numero else ""
        return f"{self.endereco_logradouro}{numero} - {self.endereco_bairro}"


class UpdateLocalEmbarqueForm(BaseLocalEmbarqueForm):
    local: model_choice(
        queryset=LocalEmbarque.objects.to_serialize(LocalEmbarqueSerializer.with_restrictions()).all()
    ) = Field(alias="id")
    cidade: model_choice(Cidade) = Field(None, alias="cidade_id")
    description: str | None
    description_ao: str | None
    mapurl: str | None
    street_view_mapurl: str | None
    ativo: bool | None
    latitude: float | None
    longitude: float | None
    nickname: str | None
    endereco_bairro: str | None
    endereco_logradouro: str | None
    reports: str | None
    observations: str | None
    status_flag: str | None
    features: list[str] | None
    contato_nome: str | None
    contato_email: str | None
    contato_telefone: str | None
    driver_description: str | None
    driver_endereco_numero: str | None
    driver_endereco_logradouro: str | None
    modelos_venda: list[str] | None
    uso_do_local: list[str] | None
    categoria_do_ponto: str | None
    tipo_de_acesso: str | None
    zona_maxima_de_restricao: bool | None
    dias_proibidos: str | None
    impede_bypass: bool | None
    aceita_embarque_ao: bool | None
    desembarque_rapido: bool | None = False

    @property
    def endereco(self):
        if self.endereco_numero or self.endereco_logradouro or self.endereco_bairro:
            new_numero = self.endereco_numero or self.local.endereco_numero
            numero = f", {new_numero}" if new_numero else ""
            logradouro = self.endereco_logradouro or self.local.endereco_logradouro
            bairro = self.endereco_bairro or self.local.endereco_bairro
            return f"{logradouro}{numero} - {bairro}"

    @validator("dias_proibidos")
    def validate_dias_proibidos(cls, value: str | None) -> list[date] | None:
        if value is None:
            return None

        dates = []
        for entry in value.split(","):
            dates.append(datetime.strptime(f"{entry} -0300", "%d/%m/%Y %z").date())
        return dates


class LocalRetiradaMarketplaceForm(BaseModel):
    id: int | None
    local_embarque: model_choice(queryset=LocalEmbarque.objects.all()) = Field(alias="local_embarque_id")
    company: model_choice(queryset=Company.objects.all()) = Field(alias="company_id")
    tipo: LocalRetiradaMarketplace.LocalRetirada
    descricao: str
    descricao_pax: str | None = None

    @validator("descricao")
    def validate_descricao(cls, descricao, values):
        if values.get("tipo") in {"guiche", "outro"} and not descricao:
            raise ValueError("Locais de retirada do tipo 'guichê' ou 'outro' devem apresentar descrição")
        return descricao


class UpdateLinkLocalForm(RequestForm):
    link_id: int
    local_embarque_buser_id: int | None
    cidade_embarque_buser_id: int | None
    local_retirada: LocalRetiradaMarketplaceForm | None = None
    editar_link: bool = True
    orientacao_pax: str | None


class PdePositionForm(RequestForm):
    latitude: float
    longitude: float


class CompanyIdForm(BaseModel):
    company: model_choice(queryset=Company.objects.all()) = Field(alias="company_id")
    search: str | None = None


class LocalRetiradaMarketplaceIdForm(BaseModel):
    local_retirada: model_choice(queryset=LocalRetiradaMarketplace.objects.all()) = Field(alias="id")


class HelpQuestionIndexUpdateForm(BaseModel):
    newItemsIndexes: list

    @property
    def new_indexes(self):
        indexes_to_update = {}
        for new_index in self.newItemsIndexes:
            id = int(new_index["id"])
            indexes_to_update[id] = new_index["newIndex"]

        return indexes_to_update


class ImagemForm(ModelForm):
    id: int | None
    tipo: Imagem.Tipo
    provider: Imagem.Provider
    descricao: str
    origin_url: str | None

    class Config:
        arbitrary_types_allowed = True
        model = Imagem


class CidadeInfoForm(BaseModel):
    cidade: model_choice(queryset=Cidade.objects.all().prefetch_related("cidadeinfo__imagem_set")) = Field(
        alias="cidade_id"
    )
    descricao: str | None = None
    sumario: str | None = None
    restaurantes: str | None = None
    eventos: str | None = None
    hospedagens: str | None = None
    imagens: list[ImagemForm] | None = []
    image_files: list[TemporaryUploadedFile | InMemoryUploadedFile] | None = []

    class Config:
        arbitrary_types_allowed = True

    @validator("imagens", pre=True, each_item=True)
    def validate_imagens_origin_url(cls, image):
        origin_url = image.get("origin_url")
        if origin_url and not image_is_valid(origin_url):
            raise ValueError(f'Extensão da imagem "{origin_url}" é inválida')
        return image

    @validator("image_files", pre=True, each_item=True)
    def validate_image_files(cls, image_file):
        filename = image_file.name
        if not image_is_valid(filename):
            raise ValueError(f'Extensão da imagem "{filename}" é inválida')
        return image_file

    @property
    def imagens_with_files(self):
        def _is_new_device_image(image):
            return image.get("id") is None and image["provider"] == Imagem.Provider.DEVICE

        images = [image.dict() for image in copy.deepcopy(self.imagens)]
        device_images = list(filter(_is_new_device_image, images))
        device_images_files = tuple(zip(device_images, self.image_files))
        for image, file in device_images_files:
            image["file_info"] = {"file": file.file, "filename": file.name}
        return images


class WikipediaPageForm(BaseModel):
    search_text: str


class Notification(BaseModel):
    title: str | None = None
    content: str | None = None
    url: str | None = None
    zap_template_id: str | None = None
    zap_ctx: dict | None = None
    contact_phone: str | None = None
    channels: list[str]

    @validator("channels", always=True)
    def validate_channels(cls, channels, values):
        if "zap" in channels and (not values["zap_template_id"] or not values["zap_ctx"]):
            raise ValueError("se for mandar zap, precisa de zap_template_id E zap_ctx")
        return channels


class Destination(BaseModel):
    type: str
    list: list[str | int]
    travelList: list[int] | None


class NotificationForm(BaseModel):
    notifications: list[Notification]
    destination: Destination = Field(alias="dest")
    deliver_at: datetime | None = None
    grupo_id: int | None = None
    marketing: bool | None = False

    @property
    def recipients(self):
        return self.destination.list

    @property
    def recipients_travel(self):
        return self.destination.travelList

    @property
    def recipient_type(self):
        return self.destination.type

    @property
    def is_scheduled(self):
        return self.deliver_at is not None

    @property
    def group_id(self):
        return self.grupo_id


class RotinasFilterForm(BaseModel):
    rotina_id: int | None
    active: bool | None
    name: str | None
    base_operacional_id: int | None
    rotas_principais_ids: list[int] | None
    reserva_min: int | None
    reserva_max: int | None
    empresas: list[int] | None
    gestores: list[int | None] | None
    regional: int | None
    modelo_venda: str | None
    rota_id: int | None = None


class AlterarFretesForm(BaseModel):
    grupo_ids: list[int]
    valor_frete: float
    justificativa: str


class ListBusUnsupportedLocalForm(BaseModel):
    rota_id_list: list[int]
    bus_id: int


class Restriction(BaseModel):
    local: model_choice(LocalEmbarque)
    label: str | None
    restriction_type: str | None = Field(alias="type")


class BypassRestrictionForm(BaseModel):
    reason: str
    restrictions: dict[RestrictionTypes, list[Restriction]]
    groups: list[model_choice(queryset=Grupo.objects.all().only("id"))]


class CheckPointForm(BaseModel):
    idx: int
    idx_parada: int
    local_id: str
    id: int | None = None
    duracao: int | None = None
    tempo_embarque: int | None = None
    distancia_km: int | None = None
    is_parada: bool | None = False
    is_conexao: bool | None = False

    @root_validator(pre=True)
    def tempo_embarque_none_vira_zero(cls, values):
        if values.get("tempo_embarque") is None:
            values["tempo_embarque"] = 0
        return values


class AnnotationForm(BaseModel):
    alerta_id: int
    annotation_contact: AlertaSeguranca.Contacts
    annotation_reason: AlertaSeguranca.Reasons
    annotation_text: str | None


class AnnotationMultipleForm(BaseModel):
    alertas_id: list[int]
    annotation_contact: AlertaSeguranca.Contacts
    annotation_reason: AlertaSeguranca.Reasons
    annotation_text: str | None


class CreateRotaCheckpointForm(CheckpointForm):
    id: int | None
    apenas_parada: bool | None


class CreateRotaTrechosForm(BaseModel):
    origem_id: int
    destino_id: int
    preco_rodoviaria: float
    secundario: bool = False

    def dict(self, **kwargs):
        return {
            "origem": {"id": self.origem_id},
            "destino": {"id": self.destino_id},
            "preco_rodoviaria": self.preco_rodoviaria,
            "secundario": self.secundario,
        }


class CreateRotaExcelForm(BaseModel):
    itinerario: list[CreateRotaCheckpointForm]
    trechos_vendidos: list[CreateRotaTrechosForm]
    ufs_intermediarios: str


class CreateRotaForm(CreateRotaExcelForm):
    criar_volta: bool | None = False
    pedagio_por_eixo: float | None
    pedagio_por_eixo_fds: float | None


class MillisecondTimedelta(timedelta):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if any(isinstance(v, t) for t in (int, float)):
            return cls(milliseconds=v)


class CreateCheckpointParadaForm(BaseModel):
    checkpoint_origem: model_choice(Checkpoint)
    checkpoint_destino: model_choice(Checkpoint)
    local: model_choice(LocalEmbarque)
    idx: conint(ge=0) = 0
    distance_km: conint(ge=0) | None
    tempo_parada: MillisecondTimedelta | None
    duracao: MillisecondTimedelta | None

    @root_validator(skip_on_failure=True)
    def check_parada_cps(cls, values):
        checkpoint_origem = values["checkpoint_origem"]
        checkpoint_destino = values["checkpoint_destino"]

        if checkpoint_origem.idx != checkpoint_destino.idx - 1:
            raise ValueError("Posição da parada no itinerario invalida")

        if checkpoint_origem.rota_id != checkpoint_destino.rota_id:
            raise ValueError("Checkpoints de rotas diferentes")

        values["rota_id"] = checkpoint_origem.rota_id

        return values


class BaseParadaListForm(BaseModel):
    checkpoint_parada_list: list[CreateCheckpointParadaForm]
    rota: model_choice(Rota)


class CreateCheckpointParadaListForm(BaseParadaListForm):
    @root_validator(skip_on_failure=True)
    def check_not_duplicated_position(cls, values):
        checkpoint_parada_list = values["checkpoint_parada_list"]

        seen = []
        parada_filter = Q()
        for parada_form in checkpoint_parada_list:
            parada_conf = (parada_form.checkpoint_origem.id, parada_form.checkpoint_destino.id, parada_form.idx)
            if parada_conf in seen:
                raise ValueError(
                    f"Paradas duplicadas entre {parada_form.checkpoint_origem.local.cidade.sigla} e "
                    f"{parada_form.checkpoint_destino.local.cidade.sigla} na posição {parada_form.idx}"
                )

            seen.append(parada_conf)
            parada_filter |= Q(
                checkpoint_origem_id=parada_form.checkpoint_origem.id,
                checkpoint_destino_id=parada_form.checkpoint_destino.id,
                idx=parada_form.idx,
            )

        existing_parada = CheckpointParada.objects.filter(parada_filter).first()
        if existing_parada:
            raise ValueError(
                f"Já existe uma parada entre {existing_parada.checkpoint_origem.local.cidade.sigla} e "
                f"{existing_parada.checkpoint_destino.local.cidade.sigla} na posição {existing_parada.idx}"
            )

        return values


class LandingPageForm(BaseModel):
    codigo: str
    ativo: bool
    titulo: str | None = None
    texto: str | None = None
    pre_input: str | None = None
    cta: str | None = None
    link_cadastro: str | None = None
    cupoms: list[constr(max_length=16)] | None = []
    cupom_start_date: date | None = None
    cupom_start_time: time | None = None
    expiring_method: LandingPage.ModeloTipoExpiracaoCupons | None = LandingPage.ModeloTipoExpiracaoCupons.DAILY
    hidden_form: bool = False

    @root_validator(pre=True)
    def valida_expiring_method_com_time(cls, values):
        if "expiring_method" in values:
            expiring_method = values["expiring_method"]
            if (
                expiring_method == LandingPage.ModeloTipoExpiracaoCupons.HOURLY
                or expiring_method == LandingPage.ModeloTipoExpiracaoCupons.ON_AND_OFF_HOURS
            ):
                if "cupom_start_time" not in values:
                    raise ValidationError(
                        "Método de expiração das promos por hora porém não foi informado um horário de início das promos."
                    )
                if not values["cupom_start_time"]:
                    raise ValidationError("Não foi informado um horário válido de início das promos.")
        return values

    @validator("codigo")
    def valida_codigo(cls, codigo):
        return codigo.upper()

    @validator("cupoms")
    def valida_cupoms(cls, cupoms):
        if cupoms:
            return [cupom for cupom in cupoms if cupom]
        return []


class ListPaxViagemRodoviariaForm(BaseModel):
    grupo: model_choice(queryset=Grupo.objects.select_related("onibus", "rota").all()) = Field(alias="grupoId")


class PaxForm(BaseModel):
    buseiro_id: int = Field(alias="buseiroId")
    name: str
    cpf: str
    rg_number: str = Field(alias="rgNumber")
    phone: str | None
    buyer_cpf: str

    @validator("cpf", "buyer_cpf", pre=True)
    def parse_cpf(cls, cpf):
        return only_numbers(cpf) or ""


class CheckPaxForm(BaseModel):
    trechoclasse_id: int = Field(alias="trechoclasseId")
    travel_id: int = Field(alias="travelId")
    valor_por_buseiro: float = Field(alias="valorPorBuseiro")
    passenger: PaxForm
    id_origem: int = Field(alias="idOrigem")
    id_destino: int = Field(alias="idDestino")
    poltrona: int | None


class SimpleCheckPaxForm(BaseModel):
    trechoclasse_id: int
    travel_id: int
    buseiro_id: int
    valor_pago: Decimal


class CheckPaxMultipleForm(BaseModel):
    __root__: list[SimpleCheckPaxForm]

    def __iter__(self):
        return iter(list(self.__root__))

    def __getitem__(self, index):
        return self.__root__[index]


class RodoviariaRemovePaxForm(BaseModel):
    grupo: model_choice(Grupo) = Field(alias="grupoId")
    travel_id: int = Field(alias="travelId")
    buseiro_id: int = Field(alias="buseiroId")


class OccurrenceAnalysisResult(str, Enum):
    FINED = "fined"
    IGNORED = "ignored"


class GetAvisosParceirosForm(PaginableModel):
    is_active: bool | None


class GerarPagamentoDividaForm(BaseModel):
    payment_method: Pagamento.Method
    cpf: Cpf
    fullname: str
    email: Email
    phone: Phone
    value: Decimal
    user: model_choice(model=User) = Field(alias="user_id")


class PagamentosDividaPendentesForm(BaseModel):
    user: model_choice(model=User) = Field(alias="user_id")


class PlataformaParadaForm(BaseModel):
    plataforma: model_choice(Plataforma) = Field(alias="plataforma_id")
    grupo: model_choice(Grupo) = Field(alias="grupo_id")


class PlataformaListGroup(BaseModel):
    local: model_choice(LocalEmbarque) = Field(alias="local_id")


class GrupoIDForm(BaseModel):
    grupo: model_choice(Grupo) = Field(alias="grupoId")


class RemoverDispersoesForm(GrupoIDForm): ...


class Liberar1MotoraForm(GrupoIDForm): ...


def _default_time():
    return timezone.localtime().time()


class ListAgendaEmbarqueForm(BaseModel):
    cidade: model_choice(Cidade) = Field(alias="cidade_id")  # type: ignore
    date_inicio: date = Field(default_factory=today)
    time_inicio: time = Field(default_factory=_default_time)
    only_group_extra: bool | None
    only_is_desembarque: bool | None

    @property
    def datetime_inicio(self):
        return to_default_tz(datetime.combine(self.date_inicio, self.time_inicio))


class SetRevendedorAttributesForm(BaseModel):
    user: model_choice(model=User) = Field(alias="user_id")
    is_revendedor: bool
    is_pdv: bool
    can_pay_with_money: bool
    team_leader: model_choice(model=User) | None = Field(alias="team_leader_user_id")
    pdv: model_choice(model=PontoDeVenda) | None = Field(alias="ponto_de_venda_id")
    taxa_revenda: Decimal | None = Field(alias="taxa_revenda")


class EscalarEmpresaOnibusRotinaForm(BaseModel):
    grupo_ids: list[int] = Field(alias="group_ids")

    company_id: int | None
    force_company: bool | None = False

    onibus_id: int | None
    onibus_ativo: bool | None
    onibus_disponivel: bool | None
    update_group_layout: bool | None = Field(alias="onibus_atualiza_layout_grupo", default=False)
    motivo_remanejamento: str | None = Field(alias="motivoRemanejamento", default="")
    motivo_description: str | None = Field(alias="motivoOutros", default="")

    rotina_id: int | None

    class Config:
        allow_population_by_field_name = True

    @validator("motivo_remanejamento")
    def valida_motivo_remanejamento(cls, motivo_remanejamento):
        motivos_code = [motivo.code for motivo in list(MOTIVOS_REMANEJAMENTO.motivos)]
        if motivo_remanejamento not in motivos_code and motivo_remanejamento != "":
            raise ValidationError(f"Motivo de remanejamento inválido. {motivo_remanejamento}")
        return motivo_remanejamento


class ListUserCommunicationLogsForm(PaginableModel):
    user: model_choice(model=User) = Field(alias="user_id")
    search: str


class SimulaAlteracaoRotaForm(BaseModel):
    send_mail: bool | None = True
    send_zap: bool | None = True
    change_routine_route: bool | None = False
    groups: list[int]
    rota: int


class TrechoVendidoForm(BaseModel):
    id: int
    max_split_value: condecimal(ge=0, max_digits=6, decimal_places=2)
    ref_split_value: condecimal(ge=0, max_digits=6, decimal_places=2) | None


class BaseClasseForm(BaseModel):
    tipo_assento: str = "executivo"
    max_capacity: int = Field(ge=0)
    max_split_value: condecimal(ge=0, max_digits=12, decimal_places=2) | None
    ref_split_value: condecimal(ge=0, max_digits=12, decimal_places=2) | None
    additional_properties: dict[str, Any] = {}

    _validate_tipo_assento = validator("tipo_assento", allow_reuse=True)(validate_tipo_assento)


class ClasseForm(BaseClasseForm):
    id: str  # esse id é uma string aleatória criada no front


class ClasseAlteradaForm(BaseClasseForm):
    trechos: list[TrechoVendidoForm] | None


class AlterarClassesForm(BaseModel):
    notify_users_class_changed: bool
    groups: list[int]
    classes: list[ClasseAlteradaForm]
    class_change_reason: str | None
    estorno_downgrade_automatico: bool = False  # deprecated
    motivo_id: int | None


class AutorizacaoHibridoForm(BaseModel):
    id: int | None
    cidade_origem_id: int | None
    cidade_destino_id: int | None
    prefixo: str | None


class CreateGroupForm(BaseModel):
    rota: int
    status: str = Grupo.Status.PENDING
    confirming_probability: str = "very_low"
    modelo_venda: str
    modelo_operacao: str
    company: int | None
    onibus: int | None
    rotina_onibus: int | None
    valor_frete: condecimal(ge=0, max_digits=12, decimal_places=2) | None
    is_extra: bool = False
    evento_extra_id: int | None = None
    departure_dates: list[date] = Field(alias="departureDates")
    departure_hour: str | None = Field(alias="departureHour")
    departure_hours: str | None = Field(alias="departureHours")
    percentual_repasse: condecimal(ge=0, le=100, max_digits=12, decimal_places=2) | None = Decimal(0)
    percentual_taxa_servico: condecimal(ge=0, le=100, max_digits=12, decimal_places=2) | None = Decimal(0)
    # o int é o ID do trecho vendido
    # a str é a string aleatória criada no front
    # e é a mesma do ID do ClasseForm
    trechos_classes: dict[int, dict[str, TrechoClasseForm]]
    classes: list[ClasseForm]
    autorizacao_hibrido: AutorizacaoHibridoForm | None

    @property
    def departure_times(self):
        return self.departure_hour or self.departure_hours

    @validator("rota", pre=True)
    def validate_rota(cls, rota):
        if isinstance(rota, int):
            return rota
        if "id" not in rota:
            raise ValidationError("Rota deve ter um id.")
        return rota["id"]

    @validator("status")
    def validate_status(cls, status):
        group_status_list = [c for c, _ in GROUP_STATUSES]
        if status not in group_status_list:
            raise ValidationError(f"Status {status} inválido.")
        return status

    @validator("confirming_probability")
    def validate_confirming_probability(cls, confirming_probability):
        group_confirming_status_list = [c for c, _ in GROUP_CONFIRMING_STATUSES]
        if confirming_probability not in group_confirming_status_list:
            raise ValidationError(f"Confirming probability {confirming_probability} inválida.")
        return confirming_probability

    @validator("modelo_venda")
    def validate_modelo_venda(cls, modelo_venda):
        if modelo_venda not in [c for c, _ in Grupo.ModeloVenda.choices]:
            raise ValidationError(f"Modelo de venda {modelo_venda} inválido.")
        return modelo_venda

    @validator("modelo_operacao")
    def validate_modelo_operacao(cls, modelo_operacao, values):
        if modelo_operacao not in [c for c, _ in Grupo.ModeloOperacao.choices]:
            raise ValidationError(f"Modelo de operacao {modelo_operacao} inválido.")
        modelo_venda = values["modelo_venda"]
        if modelo_venda != Grupo.ModeloVenda.MARKETPLACE and modelo_operacao != Grupo.ModeloOperacao.DEFAULT:
            raise ValidationError(
                f"Modelo de operacao {modelo_operacao} inválido para o modelo de venda {modelo_venda}."
            )
        return modelo_operacao

    @validator("valor_frete")
    def validate_valor_frete(cls, valor_frete):
        if not valor_frete:
            return None
        return valor_frete

    @validator("departure_dates", each_item=True)
    def data_ida_nao_pode_ser_antes_de_hoje(cls, departure_date):
        today = dateutils.today()
        if today > departure_date:
            raise ValidationError(f"A data de ida {departure_date} não pode ser anterior a hoje.")
        return departure_date

    @validator("percentual_repasse")
    def percentual_repasse_obrigatorio_para_marketplace(cls, percentual_repasse, values):
        if values["modelo_venda"] != Grupo.ModeloVenda.MARKETPLACE:
            return Decimal("0")
        if not percentual_repasse:
            raise ValidationError("Percentual de repasse é obrigatório para grupos marketplace.")
        return percentual_repasse

    @validator("percentual_taxa_servico")
    def percentual_taxa_servico_obrigatorio_para_marketplace(cls, percentual_taxa_servico, values):
        if values["modelo_venda"] != Grupo.ModeloVenda.MARKETPLACE:
            return Decimal("0")
        return percentual_taxa_servico

    @validator("classes")
    def pelo_menos_1_classe(cls, classes):
        if not len(classes):
            raise ValidationError("É necessário criar grupo com pelo menos 1 classe.")
        return classes


class ChangeBusAvailabilityForm(BaseModel):
    id: int | None = None
    available: bool
    license_plate: str | None


class CleanFormDataForm(BaseModel):
    send_mail: bool = False
    send_zap: bool = False
    send_motora_zap: bool = True
    cancel_travels: bool = False
    canceled_reason: str | None
    custom_description: str | None = Field(alias="reason_description")
    update_groups_after: str | None = Field(alias="updateGroupsAfter")
    criar_volta: bool | None
    pedagio_por_eixo: float | None
    pedagio_por_eixo_fds: float | None
    itinerario: list
    trechos_vendidos: list
    ufs_intermediarios: str

    @root_validator(pre=True)
    def reason_obrigatoria_se_deletar_travels(cls, values):
        if values.get("cancel_travels") and not values.get("canceled_reason"):
            raise ValidationError("É necessário incluir motivo de cancelamento quando há travels canceladas.")
        return values


class ItinerarioForm(BaseModel):
    id: int | None
    local_id: int
    distancia_km: int | None
    duracao: int | None
    tempo_embarque: int | None
    apenas_parada: bool = False
    is_conexao: bool | None


class Local(BaseModel):
    id: int


class TrechosVendidosForm(BaseModel):
    """
    >>> data = {
    ...     "id": 1,
    ...     "origem_id": 10,
    ...     "destino_id": 20,
    ...     "preco_rodoviaria": "29.90",
    ...     "secundario": True,
    ... }
    >>> form = TrechosVendidosForm.parse_obj(data)
    >>> assert form.id == 1
    >>> assert form.origem.id == 10
    >>> assert form.destino.id == 20
    >>> a = data.pop("origem_id")
    >>> b = data.pop("destino_id")
    >>> data.update(origem={"id": 99},
    ...             destino={"id": 199})
    >>> form = TrechosVendidosForm.parse_obj(data)
    >>> assert form.origem.id == 99
    >>> assert form.destino.id == 199
    """

    id: int | None
    origem: Local
    destino: Local
    preco_rodoviaria: Decimal
    secundario: StrictBool = False

    @root_validator(pre=True)
    def parse_origem_destino_to_local(cls, values):
        origem, destino = values.get("origem"), values.get("destino")
        if origem is None:
            origem_id = values.pop("origem_id")
            if origem_id is None:
                raise ValueError("origem não pode ser vazio")
            values["origem"] = {"id": origem_id}
        if destino is None:
            destino_id = values.pop("destino_id")
            if destino_id is None:
                raise ValueError("destino não pode ser vazio")
            values["destino"] = {"id": destino_id}
        return values


class EditRotaForm(CleanFormDataForm):
    rota_id: int | None = Field(alias="rotaId")
    ativo: bool | None
    itinerario: list[ItinerarioForm]
    trechos_vendidos: list[TrechosVendidosForm]

    @root_validator(pre=True)
    def remove_clean_form_data_key(cls, values):
        values.update(values.pop("cleanFormData", {}))
        return values


class DuracaoForm(BaseModel):
    tipo: str
    checkpoint_id: int
    duracao: MillisecondTimedelta
    tempo_embarque: MillisecondTimedelta
    dow_tzsp: str
    hora_tzsp: str

    @validator("dow_tzsp")
    def valida_dia_da_semana(cls, v):
        if v != "*" and v not in dateutils.WEEKDAYS:
            raise ValueError("Dia da semana inválido para duração dinâmica.")
        return v

    @validator("hora_tzsp")
    def valida_hora(cls, v):
        if v != "*" and v not in [str(num) for num in range(0, 23)]:
            raise ValueError("Hora inválida para duração dinâmica.")
        return v

    def to_orm(self):
        return DuracaoDinamica(
            tipo=self.tipo,
            checkpoint_id=self.checkpoint_id,
            duracao=self.duracao,
            tempo_embarque=self.tempo_embarque,
            inicio_vigencia=today(),
            dow_tzsp=self.dow_tzsp,
            hora_tzsp=self.hora_tzsp,
        )


class UpdateDuracoesForm(BaseModel):
    duracoes: list[DuracaoForm]
    update_groups_after: datetime | None
    # Lista de ids de grupos.
    update_groups: list[int] | None
    send_mail: bool = False
    send_zap: bool = False


class AlteraRotaDeGruposForm(BaseModel):
    send_mail: bool | None = True
    send_zap: bool | None = True
    change_routine_route: bool | None = False
    groups: list[int]
    rota: int
    cancel_travels: bool = False
    canceled_reason: str | None
    custom_description: str | None = Field(alias="reason_description")

    @validator("canceled_reason", always=True)
    def valida_motivo_de_cancelamento(cls, v, values):
        cancel_travels = values.get("cancel_travels")
        if cancel_travels and not v:
            raise ValueError("É necessário incluir o motivo de cancelamento quando há travels canceladas.")
        return v

    @validator("custom_description", pre=True, always=True)
    def custom_description_obrigatorio_se_motivo_for_outros(cls, v, values):
        canceled_reason = values.get("canceled_reason")
        if canceled_reason == "OTHER" and v is None:
            raise ValueError("É necessário incluir a descrição quando o motivo de cancelamento for outro.")
        return v

    class Config:
        allow_population_by_field_name = True


class ListBuseiroTravelsForm(PaginableModel):
    buseiro: model_choice(model=User) = Field(alias="buseiro_id")
    search: str


class ListUserAccountingOperationsForm(PaginableModel):
    user_id: int
    filter: str


class CheckBusConflictForm(BaseModel):
    group_ids: list[int] = []
    onibus_id: int


class BulkRemoveBusForm(BaseModel):
    bus_ids: list[int] = []
    reason: str | None


class BulkRemoveDriversForm(BaseModel):
    users_ids: list[int] = []


class CheckUserInfoForm(BaseModel):
    id: int | None
    cpf: Cpf | None
    email: Email | None
    cell_phone: Phone | None


class RemoveUsersInfoForm(BaseModel):
    users_ids: list[int] = []


class CadastrarGruposHibridosForm(RequestForm):
    company_id: int
    grupos_ids: list[int] | None = []
    rota_id_external: int | None = Field(alias="rota_id")

    @validator("grupos_ids", pre=True)
    def validate_grupos_ids(cls, grupos_ids):
        if len(grupos_ids) > 0:
            return json.loads(grupos_ids)


class AbrirTrechosForm(RequestForm):
    grupos_ids: list[int] | None = []

    @validator("grupos_ids", pre=True)
    def validate_grupos_ids(cls, grupos_ids):
        if len(grupos_ids) > 0:
            return json.loads(grupos_ids)


class RotaHibridoForm(RequestForm):
    company_id: int
    grupo_id: int
    rota_id_external: int = Field(alias="rota_id")


class PosSalvarRotaForm(RequestForm):
    params: dict = {}


class CriarGruposMarketplaceForm(RequestForm):
    class Rotina(RequestForm):
        horario: str
        datas_filtradas: list[datetime]
        classes: list[ClasseForm]
        trechos_vendidos: dict[int, dict[str, Decimal]]

    rota_id: int
    company_id: int
    percentual_repasse: int
    percentual_taxa_servico: int
    rotinas: list[Rotina]


class CriarRotaHibridoForm(RequestForm):
    company_id: int
    grupo_id: int
    cidade_origem_id: int
    cidade_destino_id: int
    prefixo: str
    rota_rodoviaria_id: int | None


class AutorizacaoForm(BaseModel):
    id: int | None = None
    cidade_origem_id: int | None = None
    cidade_destino_id: int | None = None
    prefixo: str | None = None

    @root_validator
    def check_order_id(cls, values):
        create = bool(values.get("cidade_origem_id") and values.get("cidade_destino_id") and values.get("prefixo"))
        if not values.get("id") and not create:
            raise ValidationError("Precisa passar o id ou (cidade_origem_id,cidade_destino_id,prefixo)")
        return values


class AlterarAutorizacaoGrupoHibrido(RequestForm):
    grupo_id: int
    autorizacao: Json[AutorizacaoForm]
    rota_rodoviaria_id: int | None = None

    @validator("rota_rodoviaria_id", pre=True)
    def rota_rodoviaria_null(value):
        if value in ("", "null", None):
            return None
        return value


class ListRodoviariaCompaniesForm(PaginableModel):
    name: str | None
    status: str | None
    modelo_venda: str | None


class TotalbusLoginForm(BaseModel):
    user: str
    password: str
    tenant_id: str = Field(alias="tenantId")


class VexadoLoginForm(BaseModel):
    modelo_venda: str


class SmartbusLoginForm(BaseModel):
    username: str
    password: str
    cliente: str


class ListaFormasPagamentoRodoviariaForm(BaseModel):
    integracao: str
    login: dict | None

    @validator("login", always=True)
    def validate_login(cls, login, values):
        LOGIN_FORMS = {
            "totalbus": TotalbusLoginForm,
            "smartbus": SmartbusLoginForm,
        }
        integracao = values.get("integracao")
        is_integracao_com_forma_pagamento = integracao in LOGIN_FORMS
        if not login or not is_integracao_com_forma_pagamento:
            raise ValueError("Integracao sem formas de pagamento")
        return LOGIN_FORMS[integracao].parse_obj(login)


class GuichepassLoginForm(BaseModel):
    url_base: str = Field(alias="urlBase")
    username: str
    password: str
    client_id: str = Field(alias="clientId")


class ListaEmpresasAPIParams(BaseModel):
    login_params: TotalbusLoginForm | VexadoLoginForm | GuichepassLoginForm


class TotalbusCreateLoginForm(TotalbusLoginForm):
    company_external_id: int | None = Field(alias="companyExternalId")
    forma_pagamento: str | None = Field(alias="formaPagamento")
    id_forma_pagamento: int | None = Field(alias="formaPagamentoId")
    validar_multa: bool | None = Field(alias="validarMulta", default=True)


class PraxioCreateLoginForm(BaseModel):
    name: str
    password: str
    cliente: str
    desconto_manual: bool = Field(alias="descontoManual", default=True)


class VexadoCreateLoginForm(BaseModel):
    company_external_id: int = Field(alias="companyExternalId")


class GuichepassCreateLoginForm(GuichepassLoginForm):
    company_external_id: int = Field(alias="companyExternalId")


class SmartbusCreateLoginForm(SmartbusLoginForm):
    forma_pagamento_id: int = Field(alias="formaPagamentoId")


class TiSistemasCreateLoginForm(BaseModel):
    auth_key: str = Field(alias="authKey")
    company_external_id: int = Field(alias="companyExternalId")


class CreateRodoviariaCompanyForm(BaseModel):
    name: str
    company_internal_id: int
    modelo_venda: str
    integracao: str
    features: list[str] | None
    login: dict | None
    max_percentual_divergencia: int | None = None

    @validator("integracao", always=True)
    def to_lowercase_and_snake_case(cls, value):
        if isinstance(value, str):
            return value.lower().replace(" ", "_")
        return value

    @validator("login", always=True)
    def validate_login(cls, login, values):
        LOGIN_FORMS = {
            "totalbus": TotalbusCreateLoginForm,
            "praxio": PraxioCreateLoginForm,
            "vexado": VexadoCreateLoginForm,
            "guichepass": GuichepassCreateLoginForm,
            "smartbus": SmartbusCreateLoginForm,
            "ti_sistemas": TiSistemasCreateLoginForm,
        }
        integracao = values.get("integracao")
        is_integracao_conhecida = integracao in LOGIN_FORMS
        if not login or not is_integracao_conhecida:
            return {}
        return LOGIN_FORMS[integracao].parse_obj(login)


class ListGroupNotificationsForm(BaseModel):
    group_id: int = Field(alias="groupId")


class NewIncidentsGeneralGroupInfosForm(BaseModel):
    group_id: int


class AlterarHiddenForPaxStatus(RequestForm):
    group_id: int
    status: bool


class GetHiddenPaxForStatusForm(RequestForm):
    group_id: int


class IndicarMultaForm(RequestForm):
    origem: str
    tipo: int = Field()
    incidente: str | None
    valor_multa: Decimal | None
    descricao: str | None
    anexos_removidos: list[str] = []


class IndicarMultaTorreForm(IndicarMultaForm):
    pass


class IndicarMultaDjangoForm(IndicarMultaForm):
    empresa: model_choice(Company) = Field()


class ValorMultaForm(RequestForm):
    value: Decimal


class ListLogsForm(BaseModel):
    id: int
    entity: str
    date_start: date
    date_end: date


class BaseDriverCnhForm(BaseModel):
    cpf: str


class ValidateDriverCnhForm(RequestForm):
    drivers_infos: list[BaseDriverCnhForm]


class CidadeForm(BaseModel):
    cidade_id: int = Field(alias="cidadeId")


class EditarRepasseTaxaServicoForm(RequestForm):
    lista_grupos: list[int]
    porcentagem: condecimal(ge=0, le=100, max_digits=12, decimal_places=2)
    percentual_name: PercentualTypes
    editar_todos_grupos_futuros: bool | None = False


class AtualizaPermissaoTaxaServicoCheckoutForm(BaseModel):
    company: model_choice(queryset=Company.objects.all()) = Field(alias="companyId")
    cobra_taxa_servico_checkout: bool = Field(alias="cobraTaxaServicoCheckout")


class ParametrosPrecificacaoForm(BaseModel):
    convencional: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(
        Decimal(0), alias="convencional"
    )
    convencional_individual: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(
        Decimal(0), alias="convencional individual"
    )
    executivo: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(
        Decimal(0), alias="executivo"
    )
    executivo_individual: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(
        Decimal(0), alias="executivo individual"
    )
    semi_leito: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(
        Decimal(0), alias="semi leito"
    )
    semi_leito_individual: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(
        Decimal(0), alias="semi leito individual"
    )
    leito: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(Decimal(0), alias="leito")
    leito_individual: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(
        Decimal(0), alias="leito individual"
    )
    leito_cama: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(
        Decimal(0), alias="leito cama"
    )
    leito_cama_individual: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(
        Decimal(0), alias="leito cama individual"
    )
    cama_premium: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(
        Decimal(0), alias="cama premium"
    )
    cama_premium_individual: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(
        Decimal(0), alias="cama premium individual"
    )
    carro: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(Decimal(0), alias="carro")
    carro_individual: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(
        Decimal(0), alias="carro individual"
    )
    van: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(Decimal(0), alias="van")
    van_executiva: condecimal(ge=Decimal(0), le=Decimal(1), max_digits=5, decimal_places=4) = Field(
        Decimal(0), alias="van executiva"
    )


class CategoriaTuristicaForm(BaseModel):
    titulo: str
    ativo: bool = True
    icone: str

    @validator("titulo", pre=True, always=True)
    def normaliza_titulo_lowercase(cls, v):
        if isinstance(v, str):
            return v.lower()
        return v


class CategoriaTuristicaEditForm(BaseModel):
    id: int
    titulo: str | None
    ativo: bool | None
    icone: str | None

    @validator("titulo", pre=True, always=True)
    def normaliza_titulo_lowercase(cls, v):
        if isinstance(v, str):
            return v.lower()
        return v


class CidadeCategoriaTuristicaForm(BaseModel):
    categoria_id: int
    city_ids: list[int]


class AtualizacaoPassagemRodoviariaForm(BaseModel):
    buseiro_id: int
    modelo_venda: str
    travel_id: int


class ConfiguracaoPagamentoForm(BaseModel):
    company_id: int
    modelo_venda: str
    tipo_pagamento: str
    prazo_pagamento_semana: int
    porcentagem_repasse: float | None
    taxa_antecipacao: float | None
    percentual_max_antecipacao: float | None

    @validator("porcentagem_repasse", pre=True, always=True)
    def porcentagem_repasse_validator(cls, value):
        if value in ("", "null", None):
            return None
        return float(value)


class RodoviariaListarTiposAssentosParams(BaseModel):
    paginator: PaginatorConfigForm | None = None
    search_tipo_assento: str = None
    search_company: str = None
    search_not_linked_only: bool = False
    search_active_companies_only: bool = True


class RodoviariaLinkarTiposAssentosParams(BaseModel):
    id_assento: str = None
    tipo_assento_buser_preferencial: str = None
    tipos_assentos_buser_match: list[str] = None


@dataclass
class TrechoConexaoForm:
    idx: int
    origem_id: int
    destino_id: int
    id: int | None = Field(default=None)
    min_time_between_conexoes: int | None = Field(default=0)
    max_time_between_conexoes: int | None = Field(default=0)
    company_id: int | None = Field(default=None)
    conexao_em_mesmo_local: bool | None = Field(default=True)
    modelo_venda: str | None = Field(default=None)

    @validator("origem_id", "destino_id", pre=True)
    def validate(cls, value):
        if not value:
            raise ValidationError("Id origem e id destino da perna são obrigatórios", code="parametros_invalidos")
        return value


class ConexaoCreateForm(BaseModel):
    origem_id: int
    destino_id: int
    trechos_conexao: list[TrechoConexaoForm] = Field(default_factory=list, min_items=2)
    exibir_busca_sem_data: bool = Field(default=True)

    @root_validator(pre=True)
    def valid_values(cls, values):
        origem_id = values.get("origem_id")
        destino_id = values.get("destino_id")
        if not (destino_id and origem_id):
            raise ValidationError("Id de origem e destino são obrigatórios", code="parametros_invalidos")
        trechos = values.get("trechos_conexao", [])

        if len(trechos) <= 1:
            raise ValidationError("Uma conexão é composta por pelo menos 2 trechos", code="parametros_invalidos")

        if trechos[0]["origem_id"] != origem_id:
            raise ValidationError(
                "O id de origem da primeira perna tem que ser o mesmo da conexão", code="parametros_invalidos"
            )
        if trechos[-1]["destino_id"] != destino_id:
            raise ValidationError(
                "O id do destino da última perna tem que ser o mesmo da conexão", code="parametros_invalidos"
            )

        return values


class ConexaoUpdateForm(ConexaoCreateForm):
    conexao_id: int
    delete: bool = False

    @validator("conexao_id", pre=True)
    def validate(cls, value):
        if not value:
            raise ValidationError("id da conexão é obrigatório", code="parametros_invalidos")
        return value


class RemovePassengersForm(BaseModel):
    passengers: list[dict]
    reason: str
    notify: bool | None


class DuracaoDinamicaForm(BaseModel):
    grupo_id: int
    checkpoint_id: int | None
    parada_id: int | None
    tipo: str
    duracao_seconds: int
    tempo_embarque_seconds: int

    @root_validator(pre=True)
    def tem_checkpoint_ou_parada(cls, values):
        checkpoint_id = values.get("checkpoint_id")
        parada_id = values.get("parada_id")

        if checkpoint_id is not None and parada_id is not None:
            raise ValueError("Só um 'checkpoint' ou 'parada' deve ser informado. Não os dois.")
        if checkpoint_id is None and parada_id is None:
            raise ValueError("Um 'checkpoint' ou uma 'parada' deve ser informado.")

        return values

    def to_dict(self):
        return {
            "checkpoint_id": self.checkpoint_id,
            "parada_id": self.parada_id,
            "tipo": self.tipo,
            "duracao": timedelta(seconds=self.duracao_seconds),
            "tempo_embarque": timedelta(seconds=self.tempo_embarque_seconds),
        }


class AlterarDuracoesForm(BaseModel):
    duracoes: list[DuracaoDinamicaForm]


class RemovePoltronaFretamentoForm(BaseModel):
    poltrona_id: int


class TrechoVendidoTipoAssentoForm(BaseModel):
    trecho_vendido_id: int
    tipo_assento: str


class FecharTrechosForm(BaseModel):
    grupo_ids: list[int]
    trechos_classe: list[TrechoVendidoTipoAssentoForm]
    closed_reason: str


class EventoExtraForm(BaseModel):
    id: int | None = None
    nome: str
    data_inicial: str = Field(alias="dataInicial")
    data_final: str = Field(alias="dataFinal")
    status: str
    updated_by_id: int


class EventoExtraFiltersForm(BaseModel):
    evento_id: int | None = Field(default=None, alias="eventoId")
    status: str | None = None
    data_inicial: datetime | None = Field(default=None, alias="dataInicial")
    data_final: datetime | None = Field(default=None, alias="dataFinal")


class EventoExtraListParamsForm(BaseModel):
    paginator: PaginatorForm
    filters: EventoExtraFiltersForm


class SolicitacaoPernaFiltersForm(BaseModel):
    ids: list[int] | None = None
    evento_extra_id: int | None = Field(alias="eventoExtraId", default=None)
    solicitacao_extra_id: int | None = Field(alias="solicitacaoExtraId", default=None)
    sentido: str | None = None
    turno: str | None = None
    rota_id: str | None = Field(default=None, alias="rotaId")
    min_datetime_ida: datetime | None = Field(default=None, alias="minDatetimeIda")
    max_datetime_ida: datetime | None = Field(default=None, alias="maxDatetimeIda")


class EventoExtraDetailsFilterForm(BaseModel):
    regional: str | None = None
    prioridade: str | None = None
    rota_principal: int | None = Field(default=None, alias="rotaPrincipal")


class PendenciasSolicitacaoForm(BaseModel):
    is_fechado_rotas: bool
    has_precificacao_inicial: bool
    has_precificacao_final: bool
    is_criado_staff: bool
    updated_by_id: int


class EventoExtraNegociacaoForm(BaseModel):
    id: int | None = None
    solicitacao_extra_id: int = Field(alias="solicitacaoId")
    gerente_comercial_id: int | None = None
    company_id: int | None = None
    onibus_id: int | None = None
    distancia_total: int = Field(alias="kmTotal", default=0)
    deslocamento: int
    frete_total: Decimal | None = Field(alias="freteTotal", default=Decimal("0"), max_digits=10, decimal_places=2)
    frete_km: Decimal | None = Field(alias="freteKm", default=Decimal("0"), max_digits=10, decimal_places=2)
    cask: Decimal | None = Field(default=None, max_digits=10, decimal_places=2)
    ticket_medio: Decimal | None = Field(alias="ticketMedio", default=Decimal("0"), max_digits=10, decimal_places=2)
    breakeven: Decimal | None = Field(default=Decimal("0"), max_digits=6, decimal_places=2)
    resultado_max: Decimal | None = Field(alias="resultadoMax", default=Decimal("0"), max_digits=10, decimal_places=2)
    tipos_assento: list[str] = Field(alias="tiposAssento", default_factory=list)
    capacidade: int
    is_fechado_comercial: bool
    has_empresa_escalada: bool
    has_contrato_assinado: bool
    updated_by_id: int

    @root_validator(pre=True)
    def parse_json_obj(cls, values):
        gerente_comercial = values.get("gerenteComercial")
        company = values.get("company")
        onibus = values.get("onibus")
        if gerente_comercial is not None:
            values["gerente_comercial_id"] = gerente_comercial["id"]
        if company is not None:
            values["company_id"] = company["id"]
        if onibus is not None:
            values["onibus_id"] = onibus["id"]
        return values


class SolicitacaoPernaForm(BaseModel):
    id: int | None = None
    solicitacao_extra_id: int = Field(alias="solicitacaoExtraId")
    sentido: str
    rota_id: int | None = None
    turno: str
    data: date
    hora: time | None = None
    updated_by_id: int

    @root_validator(pre=True)
    def parse_json_obj(cls, values):
        hora = values.get("hora")
        if hora == "":
            values["hora"] = None
        rota = values.get("rota")
        if rota.get("id"):
            values["rota_id"] = rota["id"]
        return values


class EventoExtraSolicitacaoForm(BaseModel):
    id: int | None = None
    regional: str
    evento_extra_id: int = Field(alias="eventoExtraId")
    prioridade: str
    distancia_por_perna: int = Field(alias="kmPerna", default=0)
    cash_in_gmv: Decimal | None = Field(alias="cashInGMV", default=Decimal("0"), max_digits=6, decimal_places=2)
    breakeven_esperado: float = Field(alias="breakevenEsperado")
    ticket_medio_estimado: Decimal = Field(alias="ticketMedioEstimado")
    tipos_assento: list[str] = Field(alias="tiposAssento")
    rota_principal_id: int
    rota_prevista: str = Field(alias="rotaPrevista")
    updated_by_id: int
    is_fechado_rotas: bool
    has_precificacao_inicial: bool
    has_precificacao_final: bool
    is_criado_staff: bool

    @root_validator(pre=True)
    def parse_json_obj(cls, values):
        rota_principal = values.get("rotaPrincipal")
        if rota_principal is not None:
            values["rota_principal_id"] = rota_principal["id"]
        return values
