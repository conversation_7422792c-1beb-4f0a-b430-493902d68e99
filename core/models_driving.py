import json
from datetime import datetime

from django.contrib.auth.models import User
from django.contrib.gis.db import models as models_gis
from django.contrib.gis.geos import Point
from django.db import models
from django.db.models import Q
from django.utils import timezone
from django_qserializer.serialization import SerializableManager
from psqlextra.models import PostgresModel
from social_django.fields import JSONField as JSONTextField

from commons.dateutils import now
from commons.django_utils import EnumChoice
from commons.storage import public_media_storage
from commons.utils import get_value_from_structure, to_list
from core.models_company import Company
from core.models_grupo import Grupo


class ParserNotFoundError(Exception):
    pass


class MissingDatetimeError(Exception):
    pass


# Rodar makemigration sempre que alterar esse enum!!
class GroupTrackingAlertMessages(EnumChoice):
    ONIBUS_PARADO = "ônibus parado há muito tempo"
    DESLIGOU_IGNICAO = "desligou ignição fora de área de controle"
    CHEGADA_EMBARQUE = "atrasado para chegada no embarque"
    SAIDA_EMBARQUE = "atrasado para saída do embarque"
    CHEGADA_DESEMBARQUE = "atrasado para chegada no desembarque"
    SAIDA_DESEMBARQUE = "atrasado para saída do desembarque"
    SAIDA_CONTROLE = "atrasado para saída de área de controle"


class DriverCheckinChekoutManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(type__in=["checkin", "checkout"])


class DriverSwitchHistory(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(Q(type__in=["driver_change"]) | Q(type__isnull=True))


class ActiveDriverHistory(models.Model):
    ACTIVE_DRIVER_HISTORY_TYPE = [(t, t) for t in ["change_driver", "checkin", "checkout"]]
    id: int
    grupo_id: int
    grupo = models.ForeignKey("Grupo", on_delete=models.CASCADE)
    active_driver_id: int
    active_driver = models.ForeignKey(User, on_delete=models.CASCADE)
    switched_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now=True)
    type = models.CharField(max_length=20, null=True, blank=True, choices=ACTIVE_DRIVER_HISTORY_TYPE)
    user_ip = models.CharField(max_length=45, null=True, blank=True)
    lat = models.FloatField(null=True, blank=True)
    long = models.FloatField(null=True, blank=True)
    jsondata = JSONTextField(null=True, blank=True)
    data = models.JSONField(null=True, blank=True)
    label = models.CharField(null=True, blank=True, max_length=20)

    objects = models.Manager()
    switches = DriverSwitchHistory()
    checkins = DriverCheckinChekoutManager()


class AvisosMotorista(models.Model):
    objects: SerializableManager = SerializableManager()

    title = models.CharField(max_length=32, null=True, blank=True)
    body = models.CharField(max_length=200, null=True, blank=True)
    ativo = models.BooleanField(default=False)
    todas_empresas = models.BooleanField(default=True)
    avisar_motorista = models.ManyToManyField(User, related_name="avisos_individuais", blank=True)
    empresas = models.ManyToManyField(Company, related_name="avisos_do_motorista", blank=True)
    lido_por_motoristas = models.ManyToManyField(User)
    created_by_id: int
    created_by = models.ForeignKey(User, related_name="avisos_motoristas_criados", on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    # TODO: Isso deveria ser público?
    imagem = models.ImageField(
        max_length=256, null=True, upload_to="public/driver_avisos", storage=public_media_storage
    )

    def to_dict_json(self):
        url = None
        if self.imagem:
            url = self.imagem.url
        return {
            "id": self.id,
            "title": self.title,
            "body": self.body,
            "updated_at": self.updated_at,
            "created_at": self.created_at,
            "created_by": self.created_by.first_name,
            "ativo": self.ativo,
            "todas_empresas": self.todas_empresas,
            "image": url,
        }


class CheckinBusStatus(EnumChoice):
    MATCH = "match"
    MISS = "miss"
    PENDING = "pending"
    ERROR = "error"
    GROUP_WITHOUT_BUS = "group_without_bus"


class CheckinBus(models.Model):
    id: int
    grupo_id: int
    grupo = models.ForeignKey("Grupo", on_delete=models.CASCADE)
    active_driver_history_id: int
    active_driver_history = models.ForeignKey("ActiveDriverHistory", on_delete=models.CASCADE)

    s3key = models.CharField(max_length=100)
    detected_text = models.CharField(max_length=512, null=True)
    processed_text = models.CharField(max_length=256, null=True)
    validation_status = models.CharField(
        max_length=20, default=CheckinBusStatus.PENDING, choices=CheckinBusStatus.choices()
    )

    class Meta:
        indexes = [
            models.Index(fields=["validation_status"]),
        ]

    objects: SerializableManager = SerializableManager()


class AlertaSeguranca(models.Model):
    class Reasons(models.TextChoices):
        NEED_MAINTENANCE = "need_maintenance", "Manutenção"
        WAITING_VIDEO = "waiting_video", "Aguardando Vídeo"
        USING_CELLPHONE = "using_cellphone", "Celular"
        INATTENTION = "inattention", "Desatenção"
        TIRED = "tired", "Cansaço"
        SLEEPING = "sleeping", "Dormindo"
        NO_SEATBELT = "no_seatbelt", "Sem Cinto"
        SPEED = "speed", "Velocidade"
        GLASSES = "glasses", "Óculos"
        ALL_FINE = "all_fine", "Tudo OK"
        UNIDENTIFIED_LICENSE_PLATE = "unidentified_license_plate", "Placa não identificada"
        COVERED_LENS = "covered_lens", "Lente coberta"
        COLLISION = "collision", "colisão"
        FALSE_POSITIVE = "false_positive", "Aguardando Vídeo"
        EQUIPMENT_VIOLATION = "equipment_violation", "Violação de equipamento"
        UNSAFE_DISTANCE = "unsafe_distance", "Distância insegura"

    class Contacts(models.TextChoices):
        GOT_CONTACT = "got_contact", "Liguei e Consegui Contato"
        NOT_CONTACT = "not_contact", "Liguei e Não Consegui Contato"
        CALL_COPILOT = "call_copilot", "Liguei para o Copiloto"
        CABINE_AUDIO_MESSAGE = "cabine_audio_message", "Acionei via áudio na cabine"
        CABINE_CALL = "cabine_call", "Acionei via chamada na cabine"
        SEND_MESSAGE = "send_message", "Mandei Mensagem"
        WRONG_NUMBER = "wrong_number", "Número de Telefone Errado"
        DONT_NEED = "dont_need", "Não precisou de contato"
        LATE = "late", "Atrasado"

    class Type(models.TextChoices):
        DESATENCAO_GRAVE = "DESATENCAO_GRAVE"  # 3
        DESATENCAO_MEDIA = "DESATENCAO_MEDIA"  # 13
        DESATENCAO_LEVE = "DESATENCAO_LEVE"  # 14
        CELULAR = "CELULAR"  # 4
        CANSACO_GRAVISSIMO = "CANSACO_GRAVISSIMO"  # 5
        CANSACO_INTENSO = "CANSACO_INTENSO"  # 6
        CANSACO_MEDIO = "CANSACO_MEDIO"  # 16
        CANSACO_LEVE = "CANSACO_LEVE"  # 15
        BOCEJO = "BOCEJO"  # 7
        VELOCIDADE_ALTA = "VELOCIDADE_ALTA"  # 8
        VELOCIDADE_MUITO_ALTA = "VELOCIDADE_MUITO_ALTA"  # 9
        PLACA_NAO_IDENTIFICADA = "PLACA_NAO_IDENTIFICADA"  # 10
        CINTO_DE_SEGURANCA = "CINTO_DE_SEGURANCA"  # 11
        USO_DE_CELULAR = "USO_DE_CELULAR"  # 12
        LENTE_COBERTA = "LENTE_COBERTA"  # 17
        RISCO_DE_COLISAO = "RISCO_DE_COLISAO"  # 18
        COLISAO_COM_PEDESTRE = "COLISAO_COM_PEDESTRE"  # 19
        ALERTA_DE_VIOLACAO = "ALERTA_DE_VIOLACAO"  # 20
        DISTANCIA_INSEGURA = "DISTANCIA_INSEGURA"  # 21
        TROCA_DE_MOTORISTAS_EM_POSICOES_DISTOANTES = "TROCA_DE_MOTORISTAS_EM_POSICOES_DISTOANTES"  # 22,

        # tablet sascar
        FREADA_BRUSCA = "FREADA_BRUSCA"  # 210006
        FORCA_G_LATERAL_FORTE = "FORCA_G_LATERAL_FORTE"  # 210007
        FORCA_G_LATERAL_MEDIA = "FORCA_G_LATERAL_MEDIA"  # 210008
        ACELERACAO_BRUSCA = "ACELERACAO_BRUSCA"  # 210009

    objects: SerializableManager = SerializableManager()
    grupo_id: int
    grupo = models.ForeignKey(Grupo, null=True, on_delete=models.CASCADE)
    alert_type = models.CharField(max_length=64, choices=Type.choices, null=True, blank=True)
    message = models.CharField(max_length=255)
    alert_datetime = models.DateTimeField()
    resolved_at = models.DateTimeField(null=True)
    resolved_by_id: int
    resolved_by = models.ForeignKey("auth.User", null=True, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    annotation_reason = models.CharField(max_length=40, choices=Reasons.choices, null=True, blank=True)
    annotation_contact = models.CharField(max_length=40, choices=Contacts.choices, null=True, blank=True)
    annotation_text = models.CharField(max_length=512, null=True, blank=True)

    origin_id = models.IntegerField(null=True, blank=True)
    origin = models.TextField(null=True, blank=True)

    class Meta:
        db_table = "core_alertafadiga"
        unique_together = ("grupo", "alert_type", "resolved_at")


class CommomDrivingIndicators(models.Model):
    id: int
    onibus_placa = models.CharField(max_length=12)
    frota = models.CharField(max_length=12, null=True, blank=True)
    matricula = models.IntegerField(null=True, blank=True)
    operador_nome = models.CharField(max_length=100, null=True, blank=True)
    gps_datetime = models.DateTimeField()
    insercao_datetime = models.DateTimeField()
    velocidade = models.FloatField(default=0)
    indicador_processado = models.BooleanField(null=True, blank=True, default=False)
    indicador_processado_at = models.DateTimeField(null=True, blank=True)
    event_id: int
    event = models.ForeignKey(AlertaSeguranca, blank=True, null=True, on_delete=models.CASCADE)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        abstract = True
        unique_together = ("gps_datetime", "onibus_placa")
        indexes = [
            models.Index(fields=["onibus_placa", "insercao_datetime"]),
        ]


class EnumDesatencaoMotorista(EnumChoice):
    LATERAL = 3
    BAIXO = 4
    VIOLACAO = 5
    OLHO_FECHADO = 6
    BOCEJO = 7
    DESATENCAO_GENERICA = 8
    NAO_DESATENCAO = 9
    USO_CELULAR = 10
    FUMANDO = 11
    SEM_CINTO_SEGURANCA = 12


class EventoDesatencaoMotorista(CommomDrivingIndicators):
    lat = models.FloatField()
    long = models.FloatField()
    odometro = models.FloatField(default=0)
    ignicao = models.BooleanField(null=True, blank=True)
    tipo_desatencao = models.IntegerField()
    descricao_tipo_desatencao = models.CharField(max_length=100, null=True, blank=True)
    verificado = models.BooleanField(null=True, blank=True)
    validado = models.BooleanField(null=True, blank=True)
    usuario_validacao = models.CharField(max_length=100, null=True, blank=True)
    url_video = models.CharField(max_length=256, null=True, blank=True)


class IndiceFadigaMotorista(CommomDrivingIndicators):
    percentual_perculos = models.FloatField()


class SmartCamDevice(PostgresModel):
    id: int
    unique_id = models.TextField(null=False, blank=False, unique=True)
    channels = models.TextField(null=False, blank=False)
    device_type = models.TextField(null=False, blank=False)
    online_state = models.IntegerField(null=False, blank=False)

    updated_at = models_gis.DateTimeField(auto_now_add=True)
    created_at = models_gis.DateTimeField(auto_now_add=True)

    deleted = models.BooleanField(default=False)
    deleted_at = models_gis.DateTimeField(null=True)

    @classmethod
    def payload_to_dict(cls, device_dict):
        _now = now()
        return {
            "unique_id": device_dict["uniqueId"],
            "channels": device_dict["channels"],
            "device_type": device_dict["deviceType"],
            "online_state": device_dict["onlineState"],
            "updated_at": _now,
            "deleted": False,
        }

    def __str__(self):
        return f"{self.unique_id} - {self.device_type}"


class SmartCamVehicle(PostgresModel):
    id: int
    vehicle_id = models.TextField(null=False, blank=False, unique=True)
    vehicle_number = models.TextField(null=False, blank=False)
    vehicle_state = models.IntegerField(null=False, blank=False)
    device_id: int
    device = models.ForeignKey(SmartCamDevice, on_delete=models.CASCADE)

    updated_at = models_gis.DateTimeField(auto_now_add=True)
    created_at = models_gis.DateTimeField(auto_now_add=True)

    @classmethod
    def payload_to_dict(cls, vehicle_dict, devices):
        _now = now()
        return {
            "vehicle_id": vehicle_dict["vehicleId"],
            "vehicle_number": vehicle_dict["vehicleNumber"],
            "vehicle_state": vehicle_dict["vehicleState"],
            "device_id": devices[vehicle_dict["uniqueId"]],
            "updated_at": _now,
            "created_at": _now,
        }

    class Meta:
        indexes = [
            models.Index(fields=["vehicle_number"]),
        ]

    def __str__(self):
        return f"{self.vehicle_id} - {self.vehicle_number}"


class SmartCamAlarm(PostgresModel):
    class Types(models.TextChoices):
        # https://drive.google.com/file/d/1bjYDGHbOf-raybnb_ttu5B0eI-Q_awy0/view
        PERDA_VIDEO = "PERDA_DE_VIDEO"  # 0
        LENTE_COBERTA = "LENTE_COBERTA"  # 1
        CELULAR = "USO_DE_CELULAR"  # 56002
        DISTRACAO = "DISTRACAO"  # 56004
        COLISAO_FRONTAL = "COLISAO_FRONTAL"  # 56006
        BOCEJO = "BOCEJO"  # 56010
        COLISAO_PEDESTRE = "COLISAO_COM_PEDESTRE"  # 56011
        CINTO = "CINTO_DE_SEGURANCA"  # 56016
        OLHO_FECHADO = "OLHO_FECHADO"  # 56000
        PERDA_GPS = "PERDA_GPS"  # 105
        TRASLADO = "TRASLADO"  # 56009
        SEM_MOTORISTA = "SEM_MOTORISTA"  # 56001
        ANORMALIDADE_DE_MEMORIA = "ANORMALIDADE_DE_MEMORIA"  # 3
        DESLIGAMENTO_ILEGAL = "DESLIGAMENTO_ILEGAL"  # 38
        SAIDA_DE_FAIXA = "SAIDA_DE_FAIXA"  # 56005
        FUMANTE = "MOTORISTA_FUMANDO"  # 56003
        BAIXA_TENSAO = "BAIXA_TENSAO"  # 9

        # Eventos vindos do tablet de telemetria Sascar
        EXCESSO_VELOCIDADE_PISTA_SECA = "EXCESSO_VELOCIDADE_PISTA_SECA"  # 210002
        EXCESSO_VELOCIDADE_PISTA_MOLHADA = "EXCESSO_VELOCIDADE_PISTA_MOLHADA"  # 210003
        EXCESSO_VELOCIDADE_ROTOGRAMA_PISTA_SECA = "EXCESSO_VELOCIDADE_ROTOGRAMA_PISTA_SECA"  # 210004
        FREADA_BRUSCA = "FREADA_BRUSCA"  # 210006
        FORCA_G_LATERAL_FORTE = "FORCA_G_LATERAL_FORTE"  # 210007
        FORCA_G_LATERAL_MEDIA = "FORCA_G_LATERAL_MEDIA"  # 210008
        ACELERACAO_BRUSCA = "ACELERACAO_BRUSCA"  # 210009
        EXCESSO_VELOCIDADE_ROTOGRAMA_PISTA_MOLHADA = "EXCESSO_VELOCIDADE_ROTOGRAMA_PISTA_MOLHADA"  # 210010

    class Provider(models.TextChoices):
        SASCAR = "sascar"
        INFLEET = "infleet"

    id: int
    alarm_id = models.TextField(null=False, blank=False, unique=True)
    type = models.TextField(choices=Types.choices, null=True, blank=True)
    geometry = models_gis.PointField(srid=4326, null=True)  # SRID WGS84
    provider = models.TextField(choices=Provider.choices, null=True, blank=True)
    speed = models.FloatField(null=False, blank=False)
    gps_time = models.DateTimeField(null=False, blank=False)
    end_time = models.DateTimeField(null=True, blank=True)
    start_time = models.DateTimeField(null=True, blank=True)
    device_id: int
    device = models.ForeignKey(SmartCamDevice, null=True, on_delete=models.CASCADE)
    plate = models.TextField(null=True, blank=True)
    event_id: int
    event = models.ForeignKey("core.AlertaSeguranca", blank=True, null=True, on_delete=models.CASCADE)

    created_at = models_gis.DateTimeField(auto_now_add=True)

    @classmethod
    def payload_to_dict(cls, alarm_dict, vehicle, provider):
        gps_info = alarm_dict["gpsInfo"]
        tz = timezone.get_default_timezone()
        end_time = datetime.fromtimestamp((alarm_dict["endTime"] or 0) / 1_000.0, tz=tz)
        start_time = datetime.fromtimestamp((alarm_dict["startTime"] or 0) / 1_000.0, tz=tz)
        gps_time = datetime.fromtimestamp((gps_info["time"] or 0) / 1_000.0, tz=tz)
        return {
            "created_at": now(),
            "alarm_id": alarm_dict["alarmId"],
            "end_time": end_time,
            "start_time": start_time,
            "geometry": Point(gps_info["lng"] * 0.000001, gps_info["lat"] * 0.000001, srid=4326),
            "speed": gps_info["speed"],
            "gps_time": gps_time,
            "plate": vehicle["vehicle_number"],
            "provider": provider,
            "type": alarm_dict["type"],
        }

    class Meta:
        indexes = [
            models.Index(fields=["-created_at"]),
            models.Index(fields=["-gps_time"]),
        ]

    def __str__(self):
        return f"{self.alarm_id} - {self.alarm_type}"


class SmartCamEvidence(PostgresModel):
    id: int
    evidence_id = models.TextField(null=False, blank=False, unique=True)
    evidence_type = models.IntegerField(null=False, blank=False)
    evidence_name = models.TextField(null=True, blank=False)
    alarm_id: int
    alarm = models.ForeignKey(SmartCamAlarm, on_delete=models.CASCADE)
    vehicle_number = models.TextField(null=False, blank=False)
    end_time = models.DateTimeField(null=False, blank=False)
    start_time = models.DateTimeField(null=False, blank=False)

    processed = models.BooleanField(default=False)

    created_at = models_gis.DateTimeField(auto_now_add=True)

    @classmethod
    def payload_to_dict(cls, evidence_dict, alarms):
        _now = now()
        return {
            "evidence_id": evidence_dict["evidenceId"],
            "evidence_name": evidence_dict["evidenceName"],
            "alarm_id": alarms[str(evidence_dict["alarmId"])],
            "vehicle_number": evidence_dict["vehicleNumber"],
            "processed": False,
            "created_at": _now,
            "end_time": _now,  # atualizar quando recuperar o arquivo
            "start_time": _now,  # atualizar quando recuperar o arquivo
            "evidence_type": 0,  # atualizar quando recuperar o arquivo
        }

    def update_start_time_end_time_evidence_type(self, start_time, end_time, evidence_type, commit=False):
        tz = timezone.get_default_timezone()
        self.start_time = datetime.fromtimestamp(start_time / 1_000.0, tz=tz)
        self.end_time = datetime.fromtimestamp(end_time / 1_000.0, tz=tz)
        self.evidence_type = evidence_type
        if commit:
            self.save(
                update_fields=[
                    "start_time",
                    "end_time",
                    "evidence_type",
                ]
            )

    class Meta:
        indexes = [
            models.Index(fields=["-created_at"]),
        ]

    def __str__(self):
        return f"{self.evidence_id} - {self.alarm.alarm_id}"


class SmartCamFile(PostgresModel):
    id: int
    file_id = models.TextField(null=False, blank=False, unique=True)
    evidence_id: int
    evidence = models.ForeignKey(SmartCamEvidence, on_delete=models.CASCADE)
    file_type = models.IntegerField(null=False, blank=False)
    start_time = models.DateTimeField(null=False, blank=False)
    end_time = models.DateTimeField(null=False, blank=False)
    channel_no = models.TextField(null=False, blank=False)
    url = models.TextField(null=False, blank=False)

    created_at = models_gis.DateTimeField(auto_now_add=True)

    @classmethod
    def payload_to_dict(cls, file_dict):
        tz = timezone.get_default_timezone()
        start_time = datetime.fromtimestamp(file_dict["startTime"] / 1_000.0, tz=tz)
        end_time = datetime.fromtimestamp(file_dict["endTime"] / 1_000.0, tz=tz)
        return {
            "created_at": now(),
            "file_id": str(file_dict["fileId"]),
            "evidence_id": file_dict["evidence_id"],
            "file_type": file_dict["fileType"],
            "start_time": start_time,
            "end_time": end_time,
            "channel_no": file_dict["channelNo"],
            "url": file_dict["url"],
        }

    class Meta:
        indexes = [
            models.Index(fields=["-created_at"]),
        ]

    def __str__(self):
        return f"{self.file_id} - {self.evidence.evidence_id}"


class TrackPushParser:
    parsers = {}

    def __init__(self, origin, data, origin_id, onibus_placa, speed, lat, long, **attrs):
        self.origin = origin
        self.data = data
        self.attributes = attrs
        self.attributes.update(
            dict(
                onibus_placa=onibus_placa,
                origin_id=origin_id,
                speed=speed,
                lat=lat,
                long=long,
            )
        )

    @classmethod
    def add_parser(cls, origin, data, **attrs):
        TrackPushParser.parsers[origin] = TrackPushParser(origin, data, **attrs)

    @classmethod
    def get_parser(cls, origin):
        if origin in cls.parsers:
            return cls.parsers[origin]
        raise ParserNotFoundError(f'Parser para origem "{origin}" não criado')

    def get_data_as_list(self, data):
        data_key = getattr(self, "data", "data")
        return [self.parse(d) for d in to_list(get_value_from_structure(data, data_key))]

    def parse(self, data):
        # Return a dict with the values parsed using the subclass configuration
        info = {}
        for model_attr, parser_attr in self.attributes.items():
            info[model_attr] = get_value_from_structure(data, parser_attr)
        info["onibus_placa"] = info["onibus_placa"].replace("-", "").strip()

        remove_keys = ["latitude", "longitude", "address", "id", "speed", "fix_time", "vehicle"]
        for k in remove_keys:
            data.pop(k, None)

        info.update(created_at=now().isoformat(), jsondata=json.dumps(data), origin=self.origin)

        if "datetime_medicao" not in info or not info["datetime_medicao"]:
            raise MissingDatetimeError("Missing Event Datetime")
        info["position"] = Point(info.pop("long"), info.pop("lat"), srid=4326)
        return info


TrackPushParser.add_parser(
    origin="golsat",
    data="positions",
    event_id="id",
    origin_id="id",
    onibus_placa="placa",
    lat="coord.0",
    long="coord.1",
    speed="info.vel",
    ignition="info.ign",
    gps="info.gps",
    datetime_medicao="dPos",
    endereco="end",
)


class InfleetTrackingParser:
    keys_to_remove = ["latitude", "longitude", "address", "id", "speed", "fix_time", "vehicle"]

    def get_data_as_list(self, payload):
        return [self.parse(payload)]

    def remove_keys(self, data):
        for k in self.keys_to_remove:
            data.pop(k, None)

    def clean_license_plate(self, data):
        return data["vehicle"]["plate"].replace("-", "").strip().upper()

    def create_point(self, data):
        return Point(data["longitude"], data["latitude"], srid=4326)

    def get_event_dict(self, data):
        parsed = {
            "type": "event",
            "origin": "infleet",
            "name": data["slug_name"],
            "origin_id": data["id"],
            "vehicle_id": data["vehicle"]["id"],
            "datetime_medicao": data["reported_at"],
            "license_plate": self.clean_license_plate(data),
            "position": self.create_point(data),
            "created_at": now().isoformat(),
        }
        self.remove_keys(data)
        parsed["jsondata"] = json.dumps(data)
        return parsed

    def get_position_dict(self, data):
        parsed = {
            "type": "position",
            "origin": "infleet",
            "origin_id": data["id"],
            "onibus_placa": self.clean_license_plate(data),
            "position": self.create_point(data),
            "speed": data["speed"],
            "ignition": data["attributes"]["ignition"],
            "datetime_medicao": data["fix_time"],
            "endereco": data["address"],
            "created_at": now().isoformat(),
        }
        self.remove_keys(data)
        parsed["jsondata"] = json.dumps(data)
        return parsed

    def parse(self, payload: bytes):
        try:
            data = json.loads(payload)
            if data["type"] == "position":
                return self.get_position_dict(data["data"])
            elif data["type"] == "event":
                return self.get_event_dict(data["data"])
            raise TrackingParserError("unknown type")

        except json.decoder.JSONDecodeError as exc:
            raise TrackingParserError(str(exc)) from exc
        except KeyError as exc:
            raise TrackingParserError(str(exc)) from exc


class TrackingParserError(RuntimeError): ...


infleet_tracking_parser = InfleetTrackingParser()


class Geofence(models.Model):
    """No MVP essa tabela vai ter um, e só um, registro com todas as geometrias"""

    feature_collection = models.TextField()


class SafetyViolationEvent(models.Model):
    id: int
    name = models.TextField()
    license_plate = models.TextField()
    event_id: int
    event = models.ForeignKey("core.AlertaSeguranca", blank=True, null=True, on_delete=models.CASCADE)
    event_datetime = models.DateTimeField(null=True, blank=True)
    jsondata = models.TextField()
    geometry = models_gis.PointField(srid=4326, null=True, spatial_index=False, db_index=False)  # SRID WGS84
    created_at = models.DateTimeField("criado_em", auto_now_add=True)
    origin = models.TextField(null=False)
    origin_id = models.TextField(null=False)
    vehicle_id = models.TextField(null=True)

    class Meta:
        indexes = [
            models.Index(fields=["-created_at"]),
        ]
        constraints = [
            models.UniqueConstraint(fields=["origin", "origin_id"], name="safetyviolationevent_unique_origin_origin_id")
        ]

    def __str__(self):
        return f"SafetyViolationEvent - {self.license_plate}"
