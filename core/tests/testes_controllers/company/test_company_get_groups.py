import json
from unittest import mock

import pytest
from model_bakery import baker

from core.controllers.company import get_groups
from core.service.itinerario_dinamico_svc import ItinerarioDinamico


@pytest.mark.usefixtures("group_marketplace", "group_hibrido")
def test_get_groups_fretamento(rf, user_granero, content_type, group_fretamento):
    params = {
        "filters": json.dumps(
            {
                "pendencies": False,
                "initialized": True,
                "busId": None,
                "driverId": None,
                "status": None,
                "modeloVenda": ["buser"],
                "groupId": None,
                "rotinaId": None,
                "departureAfter": None,
                "departureBefore": None,
            }
        ),
        "pagination": json.dumps({"limit": 10, "page": 1}),
    }
    request = rf.get("/api/v1/company/groups", params, content_type=content_type, format="json")
    request.user = user_granero
    result = get_groups(request)
    data = json.loads(result.content)
    groups = data["content"]

    assert len(groups) == 1
    assert group_fretamento.id == groups[0]["id"]


@pytest.mark.usefixtures("group_fretamento", "group_hibrido")
def test_get_groups_marketplace(rf, user_granero, content_type, group_marketplace, time_machine):
    time_machine.move_to("2023-01-14")
    params = {
        "filters": json.dumps(
            {
                "pendencies": False,
                "initialized": True,
                "busId": None,
                "driverId": None,
                "status": None,
                "modeloVenda": ["marketplace"],
                "groupId": None,
                "rotinaId": None,
                "departureAfter": None,
                "departureBefore": None,
            }
        ),
        "pagination": json.dumps({"limit": 10, "page": 1}),
    }
    request = rf.get("/api/v1/company/groups", params, content_type=content_type, format="json")
    request.user = user_granero
    result = get_groups(request)
    data = json.loads(result.content)
    groups = data["content"]
    assert len(groups) == 1
    assert group_marketplace.id == groups[0]["id"]


@pytest.mark.usefixtures("group_fretamento", "group_hibrido")
def test_get_groups_rotina_id(rf, user_granero, content_type, group_fretamento, time_machine):
    time_machine.move_to("2023-01-14")
    params = {
        "filters": json.dumps(
            {
                "pendencies": False,
                "initialized": True,
                "busId": None,
                "driverId": None,
                "status": None,
                "modeloVenda": None,
                "groupId": None,
                "rotinaId": group_fretamento.rotina_onibus_id,
                "departureAfter": None,
                "departureBefore": None,
            }
        ),
        "pagination": json.dumps({"limit": 10, "page": 1}),
    }
    request = rf.get("/api/v1/company/groups", params, content_type=content_type, format="json")
    request.user = user_granero
    result = get_groups(request)
    data = json.loads(result.content)
    groups = data["content"]
    assert len(groups) == 1
    assert group_fretamento.id == groups[0]["id"]
    assert group_fretamento.rotina_onibus_id


def test_get_groups_calls_itinerario_dinamico(rf, user_granero, content_type, group_fretamento, mocker):
    params = {
        "filters": json.dumps(
            {
                "pendencies": False,
                "initialized": True,
                "busId": None,
                "driverId": None,
                "status": None,
                "modeloVenda": ["buser"],
                "groupId": None,
                "rotinaId": None,
                "departureAfter": None,
                "departureBefore": None,
            }
        ),
        "pagination": json.dumps({"limit": 10, "page": 1}),
    }
    request = rf.get("/api/v1/company/groups", params, content_type=content_type, format="json")
    request.user = user_granero

    itinerario_dinamico_mock = mocker.spy(ItinerarioDinamico, "__init__")
    get_groups(request)

    itinerario_dinamico_mock.assert_called_once_with(mock.ANY, group_fretamento, mock.ANY)


def test_get_groups_marketplace_remove_n_mais_1(
    rf, user_granero, content_type, group_marketplace, django_assert_max_num_queries, time_machine
):
    time_machine.move_to("2023-01-14")
    BEST_NUMBER_OF_QUERIES = 24
    # tava fazendo 55 queries antes para dois trechos classaes com pedido alteração
    tc = baker.make("core.trechoclasse", grupo=group_marketplace)
    baker.make("core.pedidoalteracaopreco", trecho_classe=tc)

    params = {
        "filters": json.dumps(
            {
                "pendencies": False,
                "initialized": True,
                "busId": None,
                "driverId": None,
                "status": None,
                "modeloVenda": ["marketplace"],
                "groupId": None,
                "rotinaId": None,
                "departureAfter": None,
                "departureBefore": None,
            }
        ),
        "pagination": json.dumps({"limit": 10, "page": 1}),
    }
    request = rf.get("/api/v1/company/groups", params, content_type=content_type, format="json")
    request.user = user_granero
    with django_assert_max_num_queries(BEST_NUMBER_OF_QUERIES):
        response = get_groups(request)
    assert response.status_code == 200


@pytest.mark.parametrize(
    "status1,status2,expected",
    [
        ("PENDENTE", "APROVADO", "PENDENTE"),
        ("PENDENTE", "REJEITADO", "REJEITADO"),
        ("APROVADO", "REJEITADO", "REJEITADO"),
    ],
)
@pytest.mark.usefixtures("group_fretamento", "group_hibrido")
def test_get_groups_with_pedido_alteracao(
    status1, status2, expected, rf, user_granero, content_type, group_marketplace
):
    tc = baker.make("core.trechoclasse", grupo=group_marketplace)
    baker.make("core.pedidoalteracaopreco", trecho_classe=tc, status=status1)

    tc2 = baker.make("core.trechoclasse", grupo=group_marketplace)
    baker.make("core.pedidoalteracaopreco", trecho_classe=tc2, status=status2)

    params = {
        "filters": json.dumps(
            {
                "pendencies": False,
                "initialized": True,
                "busId": None,
                "driverId": None,
                "status": None,
                "modeloVenda": ["marketplace"],
                "groupId": group_marketplace.id,
                "rotinaId": None,
                "departureAfter": None,
                "departureBefore": None,
            }
        ),
        "pagination": json.dumps({"limit": 10, "page": 1}),
    }
    request = rf.get("/api/v1/company/groups", params, content_type=content_type, format="json")
    request.user = user_granero
    response = get_groups(request)
    serialized_groups = json.loads(response.content)["content"]
    assert serialized_groups[0]["status_pedido_alteracao_preco"] == expected
    assert response.status_code == 200
