from django.core.management import call_command
from django.utils import timezone
from django.utils.timezone import timedelta
from model_bakery import baker

from core.models_grupo import Grupo


def test_atribui_poltronas_apenas_pax_sem_poltrona(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    onibus = baker.make(
        "core.Onibus",
    )
    grupo = baker.make(
        "core.Grupo",
        modelo_venda=Grupo.ModeloVenda.BUSER,
        onibus=onibus,
    )
    trecho_classe = baker.make(
        "core.TrechoClasse",
        grupo=grupo,
        datetime_ida=timezone.now() + timedelta(days=1),
    )

    baker.make(
        "core.PoltronaOnibus",
        onibus=onibus,
        poltrona=1,
        linha=1,
        coluna=1,
        tipo="leito",
        andar=1,
        ativo=True,
    )

    pax = baker.make(
        "core.Passageiro",
        travel__trecho_classe=trecho_classe,
        travel__grupo=grupo,
    )
    atribuir_poltronas_mock = mocker.patch(
        "core.management.commands.atribui_poltrona_fretamento.FretamentoSeatController.atribui_poltronas"
    )
    call_command("atribui_poltronas_travels_do_onibus", onibus_id=onibus.id)
    atribuir_poltronas_mock.assert_called_once_with([pax])
