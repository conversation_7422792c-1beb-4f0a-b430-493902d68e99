from datetime import date, datetime, timedelta
from decimal import Decimal
from unittest.mock import <PERSON>Mock
from zoneinfo import ZoneInfo

import pytest
import time_machine
from django.core.exceptions import ValidationError
from django.utils import timezone
from model_bakery import baker

from commons.dateutils import to_default_tz_required
from commons.exceptions import CustomValidationError
from core.forms.staff_forms import ClasseForm, CreateGroupForm
from core.models_commons import AsyncTask
from core.models_company import Company, Multa
from core.models_grupo import AutorizacaoGrupo, AutorizacaoHibrido, Grupo, Rota, TrechoClasse
from core.service.grupos_staff import grupo_crud_svc
from core.tasks import cria_atualiza_grupo_cenario_task
from core.tests.fixtures import user_driver, user_driver2, user_rotas
from incidents.models import IncidenteDetalhado


def test_svc_list_grupos_filter_by_pay_group_area():
    grupo = baker.make(Grupo)
    baker.make(IncidenteDetalhado, form={"riscoPaysGroup": "risco"}, grupo=grupo)
    baker.make(IncidenteDetalhado, form={"riscoPaysGroup": "comercial"}, grupo=grupo)

    filters = {"riscoPaysGroup": True}

    response = grupo_crud_svc.list_grupos(sortby=None, descending=None, filters=filters)

    assert len(response) == 1


def test_svc_list_grupos_filter_by_nao_teve_frete():
    baker.make(Grupo)
    baker.make(Grupo)
    baker.make(Grupo, nao_teve_frete=True)

    response = grupo_crud_svc.list_grupos(sortby=None, descending=None, filters={"nao_teve_frete": True})
    assert len(response) == 1

    response = grupo_crud_svc.list_grupos(sortby=None, descending=None, filters={"nao_teve_frete": False})
    assert len(response) == 3


def test_svc_list_grupos_com_multas_1_1_omitidas():
    grupo1 = baker.make(Grupo)
    grupo2 = baker.make(Grupo)

    baker.make(Multa, motivo__codigo="1.1", grupo=grupo1)
    baker.make(Multa, motivo__codigo="1.3", grupo=grupo2)

    queryset = grupo_crud_svc.list_grupos(sortby=None, descending=None, filters={"omitir_multa_1_1": False})
    response = {"items": [grupo.serialize() for grupo in queryset]}
    assert len(response["items"]) == 2

    queryset = grupo_crud_svc.list_grupos(sortby=None, descending=None, filters={"omitir_multa_1_1": True})
    response = {"items": [grupo.serialize() for grupo in queryset]}
    assert len(response["items"]) == 1
    assert response["items"][0]["id"] == grupo2.id


@pytest.mark.parametrize(
    "filters, expected_length",
    [({"multas": ["indicada"]}, 1), ({"multas": ["aplicada"]}, 1), ({"multas": ["aplicada", "indicada"]}, 2), ({}, 3)],
)
def test_svc_list_grupos_com_filtro_de_multa(filters, expected_length):
    baker.make(Grupo)
    baker.make(Multa, status=Multa.Status.INDICADA, grupo=baker.make(Grupo))
    baker.make(Multa, status=Multa.Status.APLICADA, grupo=baker.make(Grupo))

    response = grupo_crud_svc.list_grupos(sortby=None, descending=None, filters=filters)
    assert len(response) == expected_length


def test_svc_creategroup_transbrasil_bus_interestadual_travel():
    rota = baker.make(Rota, ufs_intermediarios="RJ")
    onibus = baker.make("core.Onibus", jsondata={"transbrasil_agreement": True})

    cidade_sp = baker.make("core.Cidade", uf="SP")
    cidade_rj = baker.make("core.Cidade", uf="RJ")

    local_sp = baker.make("core.LocalEmbarque", cidade=cidade_sp)
    local_rj = baker.make("core.LocalEmbarque", cidade=cidade_rj)

    baker.make("core.Checkpoint", rota=rota, local=local_sp)
    baker.make("core.Checkpoint", rota=rota, local=local_rj)

    with pytest.raises(
        ValidationError, match="Não é possível escalar ônibus TransBrasil em viagens interestaduais de fretamento"
    ):
        grupo_crud_svc.creategroup(
            modelo_venda=Grupo.ModeloVenda.BUSER,
            onibus_id=onibus.id,
            rota_id=rota.pk,
            confirming_probability=None,
            autorizacao_hibrido=None,
            percentual_repasse=None,
            percentual_taxa_servico=None,
            rotina_onibus_id=None,
            departure_dates=None,
            departure_times=None,
            trechos_classes=None,
            valor_frete=None,
            company_id=None,
            is_extra=None,
            evento_extra_id=None,
            classes=None,
            status=None,
            dgroup=None,
            user=None,
        )


@pytest.mark.parametrize("rotina_extra", [True, False])
def test_svc_creategroup_rotina_extra_grupo_extra(rotina_extra, globalsettings_mock):
    rota = baker.make(Rota, ufs_intermediarios="RJ")
    onibus = baker.make("core.Onibus")

    cidade_sp = baker.make("core.Cidade", uf="SP")
    cidade_rj = baker.make("core.Cidade", uf="RJ")

    local_sp = baker.make("core.LocalEmbarque", cidade=cidade_sp)
    local_rj = baker.make("core.LocalEmbarque", cidade=cidade_rj)

    baker.make("core.Checkpoint", rota=rota, local=local_sp)
    baker.make("core.Checkpoint", rota=rota, local=local_rj)

    rotina = baker.make("core.RotinaOnibus", is_extra=rotina_extra)

    grupos = grupo_crud_svc.creategroup(
        modelo_venda=Grupo.ModeloVenda.BUSER,
        onibus_id=onibus.id,
        rota_id=rota.pk,
        confirming_probability="high",
        autorizacao_hibrido=False,
        percentual_repasse=0,
        percentual_taxa_servico=0,
        rotina_onibus_id=rotina.id,
        departure_dates=[date(year=2024, month=1, day=1)],
        departure_times="10:10",
        trechos_classes={},
        valor_frete=1000,
        company_id=None,
        is_extra=False,
        evento_extra_id=None,
        classes=[],
        status=Grupo.Status.PENDING,
        dgroup=None,
        user=user_rotas(),
    )
    assert grupos[0].is_extra is rotina_extra


def test_svc_creategroup_cria_cenario(globalsettings_mock):
    rota = baker.make(Rota, ufs_intermediarios="RJ")
    onibus = baker.make("core.Onibus")

    cidade_sp = baker.make("core.Cidade", uf="SP")
    cidade_rj = baker.make("core.Cidade", uf="RJ")

    local_sp = baker.make("core.LocalEmbarque", cidade=cidade_sp)
    local_rj = baker.make("core.LocalEmbarque", cidade=cidade_rj)

    tv = baker.make("core.TrechoVendido", origem=local_sp, destino=local_rj, rota=rota)

    baker.make("core.Checkpoint", rota=rota, local=local_sp, duracao=timedelta(hours=2))
    baker.make("core.Checkpoint", rota=rota, local=local_rj, duracao=timedelta(hours=2))

    rotina = baker.make("core.RotinaOnibus", rota_principal__eixo="AAA-CCC")

    grupos = grupo_crud_svc.creategroup(
        modelo_venda=Grupo.ModeloVenda.BUSER,
        onibus_id=onibus.id,
        rota_id=rota.pk,
        confirming_probability="high",
        autorizacao_hibrido=False,
        percentual_repasse=0,
        percentual_taxa_servico=0,
        rotina_onibus_id=rotina.id,
        departure_dates=[date(year=2024, month=1, day=1)],
        departure_times="10:10",
        trechos_classes={
            tv.id: {
                "max_split_value": "100.9",
                "ref_split_value": "100.9",
            }
        },
        valor_frete=1000,
        company_id=None,
        is_extra=False,
        evento_extra_id=None,
        classes=[
            ClasseForm(
                **{
                    "id": "gambiarra-aleatória-que-inventaram-coisa-feia",
                    "tipo_assento": "semi leito",
                    "max_capacity": 40,
                }
            )
        ],
        status=Grupo.Status.PENDING,
        dgroup=None,
        user=user_rotas(),
    )
    assert grupos[0].grupo_cenario_safe is not None


@time_machine.travel("2024-10-05 14:00:00", tick=False)
def test_svc_creategroup_rotina_extra_grupo_extra_precifica(mocker):
    cidade_sp = baker.make("core.Cidade", uf="SP")
    cidade_rj = baker.make("core.Cidade", uf="RJ")

    local_sp = baker.make("core.LocalEmbarque", cidade=cidade_sp)
    local_rj = baker.make("core.LocalEmbarque", cidade=cidade_rj)

    rota = baker.make(
        Rota,
        ufs_intermediarios="RJ",
        origem=local_sp,
        destino=local_rj,
    )
    onibus = baker.make("core.Onibus")

    baker.make("core.Checkpoint", rota=rota, local=local_sp, duracao=timedelta(hours=2))
    baker.make("core.Checkpoint", rota=rota, local=local_rj, duracao=timedelta(hours=2))
    rotina = baker.make("core.RotinaOnibus", is_extra=True)

    trecho_vendido = baker.make("core.TrechoVendido", rota=rota, origem=rota.origem, destino=rota.destino)

    datetime_ida = to_default_tz_required("25/10/2025 22:25")

    dgroup = {
        "modelo_venda": Grupo.ModeloVenda.BUSER,
        "modelo_operacao": Grupo.ModeloOperacao.DEFAULT,
        "onibus_id": onibus.id,
        "rota": rota.pk,
        "confirming_probability": "high",
        "autorizacao_hibrido": None,
        "percentual_repasse": 0,
        "percentual_taxa_servico": 0,
        "rotina_onibus": rotina.id,
        "departureDates": [datetime_ida.date()],
        "departureHours": f"{datetime_ida.hour}:{datetime_ida.minute}",
        "trechos_classes": {
            trecho_vendido.id: {
                "new-49820172": {
                    "max_split_value": "98.9",
                    "ref_split_value": "98.9",
                    "pessoas": 10,
                    "buckets": [
                        {"max_split_value": 89.9, "tamanho": 6, "expiration_days": None},
                        {"max_split_value": 79.9, "tamanho": 7, "expiration_days": None},
                    ],
                }
            }
        },
        "valor_frete": 1000,
        "company_id": None,
        "is_extra": True,
        "classes": [
            {
                "tipo_assento": "semi leito",
                "max_capacity": 44,
                "max_split_value": "98.9",
                "ref_split_value": "98.9",
                "additional_properties": {},
                "id": "new-49820172",
            }
        ],
        "status": Grupo.Status.PENDING,
    }

    form = CreateGroupForm.parse_obj(dgroup)
    groups = grupo_crud_svc.creategroup(
        form.rota,
        form.status,
        form.confirming_probability,
        form.modelo_venda,
        form.company,
        form.onibus,
        form.rotina_onibus,
        form.valor_frete,
        form.is_extra,
        form.evento_extra_id,
        form.departure_dates,
        form.departure_times,
        form.percentual_repasse,
        form.percentual_taxa_servico,
        form.trechos_classes,
        form.classes,
        form.autorizacao_hibrido,
        user_rotas(),
        form.dict(),
    )
    grupo = groups[0]
    trecho_classe = groups[0].trechoclasse_set.first()
    assert trecho_classe

    buckets = trecho_classe.price_manager.buckets.all()
    assert len(buckets) == 2

    assert grupo.is_extra
    assert trecho_classe.max_split_value == Decimal("98.9")
    assert buckets[0].tamanho == 7
    assert buckets[0].value == Decimal("79.90")
    assert buckets[0].expiration_days is None
    assert buckets[1].tamanho == 6
    assert buckets[1].value == Decimal("89.90")
    assert buckets[1].expiration_days is None


def test_svc_update_group_transbrasil_bus_interestadual_travel():
    rota = baker.make(Rota, ufs_intermediarios="RJ")
    grupo = baker.make(Grupo, rota=rota, modelo_venda=Grupo.ModeloVenda.BUSER)
    onibus = baker.make("core.Onibus", jsondata={"transbrasil_agreement": True})

    with pytest.raises(
        ValidationError, match="Não é possível escalar ônibus TransBrasil em viagens interestaduais de fretamento"
    ):
        grupo_crud_svc.update_group(grupo_id=grupo.pk, params={"onibus": onibus.pk}, origem_alteracao_placa="api/xpto")


def test_svc_update_group_motorista_com_block_reason():
    empresa = baker.make("core.Company", name="Furacão 2000")
    motorista = user_driver(company=empresa)
    motorista.profile.is_blocked = False
    motorista.profile.block_reason = "Deu cambalhota no teto do ônibus"
    motorista.profile.save(update_fields=["block_reason", "is_blocked"])

    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.BUSER)

    update_grupo = grupo_crud_svc.update_group(
        grupo_id=grupo.pk, params={"driver_one": motorista.id}, origem_alteracao_placa="api/xpto"
    )
    assert update_grupo.driver_one == motorista


def test_svc_update_group_motorista_um_bloqueado():
    empresa = baker.make("core.Company", name="Furacão 2000")
    motorista = user_driver(company=empresa)
    motorista.profile.is_blocked = True
    motorista.profile.block_reason = "Deu cambalhota no teto do ônibus"
    motorista.profile.save(update_fields=["block_reason", "is_blocked"])

    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.BUSER)

    with pytest.raises(
        CustomValidationError,
        match="O motorista Drivenildo está bloqueado por conta de Deu cambalhota no teto do ônibus",
    ):
        grupo_crud_svc.update_group(
            grupo_id=grupo.pk, params={"driver_one": motorista.id}, origem_alteracao_placa="api/xpto"
        )


def test_svc_update_group_motorista_dois_bloqueado():
    empresa = baker.make("core.Company", name="Furacão 2000")
    motorista_um = user_driver(company=empresa)
    motorista_dois = user_driver2(company=empresa)
    motorista_dois.profile.block_reason = "Deu cambalhota no teto do ônibus"
    motorista_dois.profile.is_blocked = True
    motorista_dois.profile.save(update_fields=["block_reason", "is_blocked"])

    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.BUSER)

    with pytest.raises(
        CustomValidationError, match="O motorista Dominic está bloqueado por conta de Deu cambalhota no teto do ônibus"
    ):
        grupo_crud_svc.update_group(
            grupo_id=grupo.pk,
            params={"driver_one": motorista_um.id, "driver_two": motorista_dois.id},
            origem_alteracao_placa="api/xpto",
        )


def test_svc_escalar_empresa_onibus_rotina_task_transbrasil_bus_interestadual_travel():
    rota = baker.make(Rota, ufs_intermediarios="RJ")
    grupo = baker.make(Grupo, rota=rota, modelo_venda=Grupo.ModeloVenda.BUSER)
    onibus = baker.make("core.Onibus", jsondata={"transbrasil_agreement": True})

    async_task = baker.make(
        AsyncTask,
        input_data={"grupo_ids": [grupo.pk], "onibus_id": onibus.pk},
        endpoint="api/staff/groups/escalar_empresa_onibus_rotina",
    )

    grupo_crud_svc.escalar_empresa_onibus_rotina_task(async_task.id)

    async_task.refresh_from_db()
    assert async_task.finished_at is not None
    assert async_task.status == AsyncTask.Status.FAILURE
    assert async_task.result == {
        "error": "Não é possível escalar ônibus TransBrasil em viagens interestaduais de fretamento"
    }


def test_svc_creategroup_nao_cria_grupo_de_rota_com_trecho_vendido_duplicado(rota_bhz_sao):
    baker.make(
        "core.TrechoVendido", rota=rota_bhz_sao, origem=rota_bhz_sao.origem, destino=rota_bhz_sao.destino, _quantity=2
    )

    with pytest.raises(ValidationError, match="Trecho Vendido duplicado na rota"):
        grupo_crud_svc.creategroup(
            modelo_venda=Grupo.ModeloVenda.BUSER,
            onibus_id=None,
            rota_id=rota_bhz_sao.pk,
            confirming_probability=None,
            autorizacao_hibrido=None,
            percentual_repasse=None,
            percentual_taxa_servico=None,
            rotina_onibus_id=None,
            departure_dates=None,
            departure_times=None,
            trechos_classes=None,
            valor_frete=None,
            company_id=None,
            is_extra=None,
            evento_extra_id=None,
            classes=None,
            status=None,
            dgroup=None,
            user=None,
        )


def test_grupo_crud_svc_valida_trecho_vendido_duplicado(rota_bhz_sao):
    baker.make(
        "core.TrechoVendido", rota=rota_bhz_sao, origem=rota_bhz_sao.origem, destino=rota_bhz_sao.destino, _quantity=2
    )

    with pytest.raises(ValidationError, match="Trecho Vendido duplicado na rota"):
        grupo_crud_svc.valida_trecho_vendido_duplicado([rota_bhz_sao])


def test_grupo_crud_svc_valida_local_embarque_duplicado(rota_bhz_sao):
    baker.make("core.Checkpoint", rota=rota_bhz_sao, local=rota_bhz_sao.origem, _quantity=2)

    with pytest.raises(ValidationError, match="Local de embarque duplicado na rota"):
        grupo_crud_svc.valida_local_embarque_duplicado([rota_bhz_sao])


@pytest.mark.parametrize(
    "filters",
    [({"autorizacao_status_e_hibrido": ["PENDENTE", "APROVADO", "APROVADO_COM_PENDENCIAS"]})],
)
def test_grupo_crud_svc_pega_grupo_hibrido_e_ignora_reprovado(filters):
    company = baker.make(Company)
    grupo_buser_aprovado_com_pendencia = baker.make(Grupo, status="done", company_id=company.id)
    grupo_buser_aprovado = baker.make(Grupo, status="done", company_id=company.id, modelo_venda=Grupo.ModeloVenda.BUSER)
    grupo_buser_reprovado = baker.make(
        Grupo, status="done", company_id=company.id, modelo_venda=Grupo.ModeloVenda.BUSER
    )
    grupo_pendente = baker.make(Grupo, status="done", company_id=company.id, modelo_venda=Grupo.ModeloVenda.BUSER)
    baker.make(Grupo, status="done", company_id=company.id, modelo_venda=Grupo.ModeloVenda.HIBRIDO)

    baker.make(AutorizacaoGrupo, status=AutorizacaoGrupo.Status.APROVADO, grupo=grupo_buser_aprovado)
    baker.make(
        AutorizacaoGrupo,
        status=AutorizacaoGrupo.Status.APROVADO_COM_PENDENCIAS,
        grupo=grupo_buser_aprovado_com_pendencia,
    )
    baker.make(AutorizacaoGrupo, status=AutorizacaoGrupo.Status.PENDENTE, grupo=grupo_pendente)
    baker.make(AutorizacaoGrupo, status=AutorizacaoGrupo.Status.REJEITADO, grupo=grupo_buser_reprovado)

    response = grupo_crud_svc.list_grupos(sortby=None, descending=None, filters=filters)

    assert len(response) == 4


@pytest.mark.parametrize(
    "filters, expected_result",
    [
        ({"autorizacao_status": ["PENDENTE"]}, 1),
        ({"autorizacao_status": ["APROVADO"]}, 1),
        ({"autorizacao_status": ["APROVADO_COM_PENDENCIAS"]}, 1),
        ({"autorizacao_status": ["REJEITADO"]}, 1),
        ({"autorizacao_status": None, "modelo_venda": "buser", "somente_com_licenca": True}, 5),
    ],
)
def test_grupo_crud_svc_mostra_grupos(filters, expected_result):
    company = baker.make(Company)
    company2 = baker.make(Company, vinculo=Company.VinculoReal.HIBRIDO)
    autorizacao_hibrido = baker.make(AutorizacaoHibrido)
    rota = baker.make(Rota)
    buser = Grupo.ModeloVenda.BUSER

    grupo_buser_aprovado_com_pendencia = baker.make(
        Grupo,
        status="done",
        autorizacao_s3key="aaaaa",
        contar_dia_parado=True,
        company_id=company.id,
        modelo_venda=buser,
    )
    grupo_buser_aprovado = baker.make(
        Grupo,
        status="done",
        autorizacao_s3key="aaaaa",
        contar_dia_parado=True,
        company_id=company.id,
        modelo_venda=buser,
    )
    grupo_buser_reprovado = baker.make(
        Grupo,
        status="done",
        autorizacao_s3key="aaaaa",
        contar_dia_parado=True,
        company_id=company.id,
        modelo_venda=buser,
    )
    grupo_pendente = baker.make(
        Grupo,
        status="done",
        autorizacao_s3key="aaaaa",
        contar_dia_parado=True,
        company_id=company.id,
        modelo_venda=buser,
    )
    baker.make(
        Grupo,
        status="done",
        autorizacao_s3key=None,
        contar_dia_parado=True,
        company_id=company2.id,
        modelo_venda=Grupo.ModeloVenda.HIBRIDO,
        autorizacao_hibrido_id=autorizacao_hibrido.id,
        valor_frete="1000.00",
        rota_id=rota.id,
    )

    baker.make(AutorizacaoGrupo, status=AutorizacaoGrupo.Status.APROVADO, grupo=grupo_buser_aprovado)
    baker.make(
        AutorizacaoGrupo,
        status=AutorizacaoGrupo.Status.APROVADO_COM_PENDENCIAS,
        grupo=grupo_buser_aprovado_com_pendencia,
    )
    baker.make(AutorizacaoGrupo, status=AutorizacaoGrupo.Status.PENDENTE, grupo=grupo_pendente)
    baker.make(AutorizacaoGrupo, status=AutorizacaoGrupo.Status.REJEITADO, grupo=grupo_buser_reprovado)

    response = grupo_crud_svc.list_grupos(sortby=None, descending=None, filters=filters)

    assert len(response) == expected_result


def test_svc_update_group_fecha_abre_grupo_classe():
    horario_inicio_teste = timezone.now()
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    grupo_classe = baker.make("core.GrupoClasse", grupo=grupo, tipo_assento="executivo", closed=False)

    # Testa fechar o grupo classe
    grupo_crud_svc.update_group(
        grupo_id=grupo.pk,
        params={"classes": [{"id": grupo_classe.id, "closed": True}]},
        origem_alteracao_placa="api/xpto",
    )
    grupo_classe.refresh_from_db()
    assert grupo_classe.closed is True
    assert grupo_classe.closed_at > horario_inicio_teste

    # Testa reabrir o grupo classe
    grupo_crud_svc.update_group(
        grupo_id=grupo.pk,
        params={"classes": [{"id": grupo_classe.id, "closed": False}]},
        origem_alteracao_placa="api/xpto",
    )
    grupo_classe.refresh_from_db()
    assert grupo_classe.closed is False
    assert grupo_classe.closed_at is None


def test_svc_creategroup_duracao_dinamica(bypass_auto_now):
    rota = baker.make(Rota, ufs_intermediarios="RJ", distancia_total=50, duracao_total=timedelta(minutes=120))
    onibus = baker.make("core.Onibus")

    cidade_sp = baker.make("core.Cidade", uf="SP")
    cidade_rj = baker.make("core.Cidade", uf="RJ")

    local_sp = baker.make("core.LocalEmbarque", cidade=cidade_sp)
    local_rj = baker.make("core.LocalEmbarque", cidade=cidade_rj)

    c1 = baker.make(
        "core.Checkpoint",
        rota=rota,
        idx=0,
        distancia_km=0,
        duracao=timedelta(0),
        local=local_sp,
        tempo_embarque=timedelta(minutes=0),
    )
    c2 = baker.make(
        "core.Checkpoint",
        rota=rota,
        idx=1,
        distancia_km=50,
        duracao=timedelta(minutes=60),
        local=local_rj,
        tempo_embarque=timedelta(minutes=0),
    )
    trecho_vendido = baker.make("core.TrechoVendido", rota=rota, origem=local_sp, destino=local_rj)

    datetime_ida = datetime(year=2024, month=1, day=1, hour=10, minute=10, tzinfo=ZoneInfo("America/Sao_Paulo"))
    duracao_dinamica = bypass_auto_now(
        "core.DuracaoDinamica",
        datetime=datetime_ida,
        checkpoint=c1,
        dow_tzsp="monday",
        hora_tzsp=datetime_ida.hour,
        duracao=c2.duracao + timedelta(minutes=30),
    )

    rotina = baker.make("core.RotinaOnibus")

    classes = [MagicMock(id="new-111", max_capacity=31, tipo_assento="leito")]
    trechos_classes = {
        trecho_vendido.id: {
            classes[0].id: MagicMock(
                max_split_value=69.90,
                ref_split_value=69.90,
                buckets=[],
                additional_properties={"datetime_ida": datetime_ida},
            )
        }
    }

    grupos = grupo_crud_svc.creategroup(
        modelo_venda=Grupo.ModeloVenda.BUSER,
        onibus_id=onibus.id,
        rota_id=rota.pk,
        confirming_probability="high",
        autorizacao_hibrido=False,
        percentual_repasse=0,
        percentual_taxa_servico=0,
        rotina_onibus_id=rotina.id,
        departure_dates=[datetime_ida.date()],
        departure_times=f"{datetime_ida.hour}:{datetime_ida.minute}",
        trechos_classes=trechos_classes,
        valor_frete=1000,
        company_id=None,
        is_extra=False,
        evento_extra_id=None,
        classes=classes,
        status=Grupo.Status.PENDING,
        dgroup=None,
        user=user_rotas(),
    )

    grupo = grupos[0]
    tc = TrechoClasse.objects.all().first()
    assert grupo.duracoes == [duracao_dinamica.id]
    assert grupo.duracao_total == duracao_dinamica.duracao
    assert tc.duracao_ida == duracao_dinamica.duracao


def test_cria_atualiza_grupo_cenario_task():
    c1, c2, c3 = (
        baker.make("core.Cidade", name="bbb", uf="bb", sigla="BBB"),
        baker.make("core.Cidade", name="aaa", uf="aa", sigla="AAA"),
        baker.make("core.Cidade", name="ccc", uf="cc", sigla="CCC"),
    )
    l1, l2, l3 = (
        baker.make("core.LocalEmbarque", cidade=c1),
        baker.make("core.LocalEmbarque", cidade=c2),
        baker.make("core.LocalEmbarque", cidade=c3),
    )
    rota = baker.make("core.Rota")

    baker.make("core.Checkpoint", idx=0, local_id=l1.pk, rota_id=rota.pk)
    baker.make("core.Checkpoint", idx=1, local_id=l2.pk, rota_id=rota.pk, distancia_km=200)
    baker.make("core.Checkpoint", idx=2, local_id=l3.pk, rota_id=rota.pk, distancia_km=200)

    trecho_vendido_1 = baker.make("core.TrechoVendido", origem=l1, destino=l3, rota=rota)
    trecho_vendido_2 = baker.make("core.TrechoVendido", origem=l1, destino=l2, rota=rota)
    trecho_vendido_3 = baker.make("core.TrechoVendido", origem=l2, destino=l3, rota=rota)

    grupo = baker.make(
        "core.Grupo",
        rota=rota,
        rotina_onibus__rota_principal__eixo="AAA-CCC",
        datetime_ida=to_default_tz_required("2024-05-10 10:10:00"),
        _fill_optional=["rotina_onibus"],
    )

    tc1 = baker.make(
        "core.TrechoClasse",
        trecho_vendido=trecho_vendido_1,
        grupo_classe__grupo=grupo,
        grupo_classe__capacidade=10,
        grupo_classe__tipo_assento="leito",
        grupo=grupo,
        pessoas=5,
        max_split_value="100.9",
        ref_split_value="100.9",
        datetime_ida=grupo.datetime_ida,
        _fill_optional=["price_manager"],
    )
    baker.make(
        "core.TrechoClasse",
        trecho_vendido=trecho_vendido_2,
        grupo_classe=tc1.grupo_classe,
        grupo=grupo,
        pessoas=5,
        max_split_value="100.9",
        ref_split_value="100.9",
        datetime_ida=grupo.datetime_ida,
        _fill_optional=["price_manager"],
    )
    baker.make(
        "core.TrechoClasse",
        trecho_vendido=trecho_vendido_3,
        grupo_classe=tc1.grupo_classe,
        grupo=grupo,
        pessoas=5,
        max_split_value="100.9",
        ref_split_value="100.9",
        datetime_ida=grupo.datetime_ida,
        _fill_optional=["price_manager"],
    )
    tc3 = baker.make(
        "core.TrechoClasse",
        trecho_vendido=trecho_vendido_1,
        grupo_classe__grupo=grupo,
        grupo_classe__capacidade=30,
        grupo_classe__tipo_assento="semi leito",
        grupo=grupo,
        pessoas=5,
        max_split_value="100.9",
        ref_split_value="100.9",
        datetime_ida=grupo.datetime_ida,
        _fill_optional=["price_manager"],
    )
    baker.make(
        "core.TrechoClasse",
        trecho_vendido=trecho_vendido_2,
        grupo_classe__grupo=grupo,
        grupo_classe=tc3.grupo_classe,
        grupo=grupo,
        pessoas=5,
        max_split_value="100.9",
        ref_split_value="100.9",
        datetime_ida=grupo.datetime_ida,
        _fill_optional=["price_manager"],
    )
    baker.make(
        "core.TrechoClasse",
        trecho_vendido=trecho_vendido_3,
        grupo_classe__grupo=grupo,
        grupo_classe=tc3.grupo_classe,
        grupo=grupo,
        pessoas=5,
        max_split_value="100.9",
        ref_split_value="100.9",
        datetime_ida=grupo.datetime_ida,
        _fill_optional=["price_manager"],
    )

    cria_atualiza_grupo_cenario_task(grupo.id)

    cg = grupo.grupocenario

    assert cg.eixo == "AAA-CCC"
    assert cg.sentido == "bbb-bb>ccc-cc"
    assert cg.distancia_km == 400
    assert cg.tipo_carro == "semi leito"
    assert cg.capacidade_total == 40
    assert cg.load_factor_curve == []
    assert cg.dia_semana == 4
    assert cg.turno == "manhã"
    assert not cg.feriado
    assert cg.current_load_factor == 0.5
