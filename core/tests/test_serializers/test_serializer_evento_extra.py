from datetime import date, time

from model_bakery import baker

from core.forms.staff_forms import EventoExtraDetailsFilterForm
from core.models_grupo import EventoExtra
from core.serializers import serializer_evento_extra
from core.service.evento_extra_svc import list_evento_extra_details


def test_serializer_solicitacao_evento_extra_num_queries(django_assert_num_queries):
    evento = baker.make("core.EventoExtra", nome="São João", status=EventoExtra.EventoStatus.DOING)

    solicitacoes = baker.make(
        "core.EventoExtraSolicitacao",
        rota_prevista="SAO-RIO",
        evento_extra=evento,
        _quantity=2,
        _bulk_create=True,
    )
    baker.make(
        "core.EventoExtraNegociacao",
        solicitacao_extra=iter(solicitacoes),
        evento_extra=evento,
        _quantity=2,
        _bulk_create=True,
    )

    for solicitacao in solicitacoes:
        baker.make(
            "core.EventoExtraSolicitacaoPerna",
            data=date(2025, 6, 26),
            hora=time(12, 0),
            sentido=iter(["RIO-SAO", "SAO-RIO"]),
            solicitacao_extra=solicitacao,
            _quantity=2,
        )

    filters = EventoExtraDetailsFilterForm()
    eventos_extra = list_evento_extra_details(evento.id, filters)
    # add select_related evento_extra na solicitação e na perna
    with django_assert_num_queries(8):
        serialized_evento = [evento_extra.serialize() for evento_extra in eventos_extra]

    assert {s["id"] for s in serialized_evento} == {s.id for s in solicitacoes}


def test_serializer_evento_extra_num_queries(django_assert_num_queries):
    nomes = ["São João", "Carnaval", "Páscoa"]
    status = ["pending", "doing", "done"]
    eventos = baker.make("core.EventoExtra", nome=iter(nomes), status=iter(status), _quantity=3, _bulk_create=True)
    eventos_ids = [evento.id for evento in eventos]
    baker.make("core.EventoExtraSolicitacao", evento_extra_id=iter(eventos_ids), _quantity=3, _bulk_create=True)
    eventos_extra = EventoExtra.objects.all()
    with django_assert_num_queries(6):
        extras_serialized = serializer_evento_extra.serialize_eventos_extra(eventos_extra)

    assert extras_serialized is not None
