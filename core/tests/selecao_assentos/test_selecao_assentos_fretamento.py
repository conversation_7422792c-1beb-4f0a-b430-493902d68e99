import itertools
import random
from datetime import timedelta
from decimal import Decimal
from unittest.mock import MagicMock

import pytest
from django.core.management import call_command
from django.utils import timezone
from model_bakery import baker

from core.forms.remanejamento_forms import FormRemanejamentoConstraints, RemanejamentoForm
from core.models_travel import ItemAdicional, Passageiro, Travel
from core.service.remanejamento import remanejamento_svc
from core.service.selecao_assento import NoSeatLayoutAvailable, SeatAlreadyTaken
from core.service.selecao_assento.fretamento import FretamentoSeatController, SeatControllerManager
from core.service.selecao_assento.models import Assento, MapaPoltronasOnibus


@pytest.fixture
def user():
    return baker.make("auth.User")


def cria_assentos_para_onibus(trecho_classe, andares=1):
    onibus = baker.make(
        "core.Onibus",
    )
    trecho_classe.grupo.onibus = onibus
    trecho_classe.grupo.save()

    tipo_assento = trecho_classe.grupo_classe.tipo_assento

    poltronas = []

    for poltrona, (x, y, z) in enumerate(itertools.product(range(4), range(10), range(1, andares + 1)), start=1):
        poltronas.append(
            baker.prepare(
                "PoltronaOnibus",
                onibus=onibus,
                poltrona=poltrona,
                linha=x,
                coluna=y,
                tipo=tipo_assento,
                andar=z,
                ativo=True,
            )
        )
    onibus.poltronas.bulk_create(poltronas)


def testa_pega_vagas_disponiveis_do_trecho_classe(grupo_multitrecho, user):
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    ultimo_trecho_classe = grupo_multitrecho.trechoclasse_set.last()
    cria_assentos_para_onibus(primeiro_trecho_classe)
    manager = FretamentoSeatController(primeiro_trecho_classe, user)

    assert manager.get_trecho_classe_idx(grupo_multitrecho.trechoclasse_set.first())[0] == 0
    assert manager.get_trecho_classe_idx(grupo_multitrecho.trechoclasse_set.first())[1] == 1

    assert len(manager.checkpoints) == 3

    travel = baker.make(
        "core.Travel",
        trecho_classe=ultimo_trecho_classe,
    )

    baker.make(
        "core.Passageiro",
        poltrona=1,
        travel=travel,
    )

    manager = FretamentoSeatController(primeiro_trecho_classe, user)
    segundo_manager = FretamentoSeatController(ultimo_trecho_classe, user)
    assert len(manager.assentos_ocupados) == 0
    assert len(segundo_manager.assentos_ocupados) == 1


def testa_layout_assentos(mocker, grupo_multitrecho, user):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe, 2)
    manager = FretamentoSeatController(primeiro_trecho_classe, user)
    layout = manager.get_layout_onibus()

    assert isinstance(layout, MapaPoltronasOnibus)
    assert len(layout.layout) == 2
    assert layout.layout[0].andar == 1
    assert layout.layout[1].andar == 2
    assert len(layout.layout[0].assentos) == 40
    assert len(layout.layout[1].assentos) == 40


def testa_layout_assentos_com_poltronas_ocupadas(mocker, grupo_multitrecho, user):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe, 2)
    poltronas_ocupadas = random.sample(list(range(1, 41)), 2)
    for poltrona in poltronas_ocupadas:
        baker.make(
            "core.Passageiro",
            poltrona=poltrona,
            travel__trecho_classe=primeiro_trecho_classe,
        )

    primeiro_trecho_classe.refresh_from_db()
    manager = FretamentoSeatController(primeiro_trecho_classe, user)
    layout = manager.get_layout_onibus()

    for andar in layout.layout:
        for assento in andar.assentos:
            if assento.numero in poltronas_ocupadas:
                assert not assento.livre
            else:
                assert assento.livre

    assert sorted(poltronas_ocupadas) == sorted(manager.assentos_ocupados)


def test_request_layout_em_onibus_sem_marcacao_de_assento(mocker, grupo_multitrecho, user):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=False)
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    onibus = baker.make(
        "core.Onibus",
    )
    primeiro_trecho_classe.grupo.onibus = onibus
    primeiro_trecho_classe.grupo.save()
    primeiro_trecho_classe.refresh_from_db()
    manager = FretamentoSeatController(primeiro_trecho_classe, user)
    with pytest.raises(NoSeatLayoutAvailable):
        manager.get_layout_onibus()


def test_request_layout_em_onibus_com_feature_flag_funcionando(mocker, grupo_multitrecho, user):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe, 1)
    manager = FretamentoSeatController(primeiro_trecho_classe, user)
    layout = manager.get_layout_onibus()
    assert isinstance(layout.layout, list)
    assert len(layout.layout) == 1
    assert len(layout.layout[0].assentos) == 40


@pytest.mark.parametrize(
    "idx, expected_idx",
    [
        [0, (0, 1)],
        [1, (0, 2)],
        [2, (1, 2)],
    ],
)
def test_get_idx_trecho_classe(mocker, grupo_multitrecho, user, idx, expected_idx):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe, 1)
    manager = FretamentoSeatController(primeiro_trecho_classe, user)
    trecho_classe = grupo_multitrecho.trechoclasse_set.all()[idx]
    assert manager.get_trecho_classe_idx(trecho_classe) == expected_idx


def test_bloquear_poltrona(grupo_multitrecho, user):
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe)
    manager = FretamentoSeatController(primeiro_trecho_classe, user)

    poltrona = manager._poltronas_onibus[0]
    assert poltrona is not None
    assento = Assento.from_poltrona_onibus(poltrona, livre=True)
    blocked_seat = manager.bloquear_poltrona(assento)

    assert blocked_seat.poltrona == assento

    assert poltrona.poltrona in manager._collect_poltronas_blocked(primeiro_trecho_classe.id)


def test_desbloquear_poltrona(grupo_multitrecho, user):
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe)
    manager = FretamentoSeatController(primeiro_trecho_classe, user)

    poltrona = manager._poltronas_onibus[0]
    assert poltrona is not None
    assento = Assento.from_poltrona_onibus(poltrona, livre=True)
    blocked_seat = manager.bloquear_poltrona(assento)

    assert blocked_seat.poltrona == assento
    result = manager.desbloquear_poltrona(blocked_seat)

    assert result is True

    assert str(poltrona.poltrona) not in manager._collect_poltronas_blocked(primeiro_trecho_classe.id)


def test_collect_poltronas_blocked(grupo_multitrecho, user):
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe)
    manager = FretamentoSeatController(primeiro_trecho_classe, user)

    blocked_seats = manager._collect_poltronas_blocked(primeiro_trecho_classe.id)

    assert len(blocked_seats) == 0
    poltrona = manager._poltronas_onibus[0]
    assert poltrona is not None
    assento = Assento.from_poltrona_onibus(poltrona, livre=True)
    manager.bloquear_poltrona(assento)
    blocked_seats = manager._collect_poltronas_blocked(primeiro_trecho_classe.id)
    assert len(blocked_seats) == 1


def test_bloquear_poltrona_ja_bloqueada(grupo_multitrecho, user):
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe)
    manager = FretamentoSeatController(primeiro_trecho_classe, user)

    poltrona = manager._poltronas_onibus[0]
    assert poltrona is not None
    assento = Assento.from_poltrona_onibus(poltrona, livre=True)

    result = manager.bloquear_poltrona(assento)
    assert result.poltrona == assento

    with pytest.raises(SeatAlreadyTaken):
        manager.bloquear_poltrona(assento)


def test_unset_mapa_poltronas_cache(mocker, grupo_multitrecho, user):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    redis_client_mock = MagicMock()
    redis_client_mock.delete.return_value = 1
    mocker.patch("commons.redis.get_master_client", return_value=redis_client_mock)
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    manager = FretamentoSeatController(primeiro_trecho_classe, user)
    manager.unset_mapa_poltronas_cache()
    redis_client_mock.delete.assert_called_once_with(f"FRETAMENTO_BUSER_{primeiro_trecho_classe.id}")


def test_atribui_poltronas_a_pax_sem_poltrona(mocker, grupo_multitrecho, user):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe)
    baker.make(
        "core.Passageiro",
        poltrona=None,
        travel__trecho_classe=primeiro_trecho_classe,
        _quantity=2,
    )
    passageiros = Passageiro.objects.filter(travel__trecho_classe=primeiro_trecho_classe, poltrona__isnull=True)
    assert len(passageiros) == 2
    manager = FretamentoSeatController(primeiro_trecho_classe, user)
    manager.atribui_poltronas(list(passageiros))
    passageiros_sem_poltrona = Passageiro.objects.filter(
        travel__trecho_classe=primeiro_trecho_classe, poltrona__isnull=True
    )
    assert len(passageiros_sem_poltrona) == 0
    passageiros_com_poltrona = Passageiro.objects.filter(
        travel__trecho_classe=primeiro_trecho_classe, poltrona__isnull=False
    )
    assert len(passageiros_com_poltrona) == 2


def test_comando_atribui_poltrona_para_travel_proxima(mocker, grupo_multitrecho):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    primeiro_trecho_classe.datetime_ida = timezone.now() + timedelta(hours=23)
    primeiro_trecho_classe.save()
    primeiro_trecho_classe.refresh_from_db()
    cria_assentos_para_onibus(primeiro_trecho_classe)
    baker.make(
        "core.Passageiro",
        poltrona=None,
        travel__trecho_classe=primeiro_trecho_classe,
        _quantity=2,
    )
    call_command("atribui_poltrona_fretamento")
    assert Passageiro.objects.filter(travel__trecho_classe=primeiro_trecho_classe, poltrona__isnull=False).count() == 2


def test_nao_atribui_poltronas_para_paxs_ja_atribuidos(mocker, grupo_multitrecho):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe)
    baker.make(
        "core.Passageiro",
        poltrona=1,
        travel__trecho_classe=primeiro_trecho_classe,
    )
    primeiro_trecho_classe.datetime_ida = timezone.now() + timedelta(hours=23)
    primeiro_trecho_classe.save()
    atribuir_poltronas_mock = mocker.patch(
        "core.management.commands.atribui_poltrona_fretamento.FretamentoSeatController.atribui_poltronas"
    )
    call_command("atribui_poltrona_fretamento")
    assert atribuir_poltronas_mock.call_count == 0


def test_atribui_poltronas_apenas_pax_sem_poltrona(mocker, grupo_multitrecho):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe)
    pax_1 = baker.make(
        "core.Passageiro",
        poltrona=1,
        travel__trecho_classe=primeiro_trecho_classe,
    )
    pax_2 = baker.make(
        "core.Passageiro",
        travel=pax_1.travel,
    )
    primeiro_trecho_classe.datetime_ida = timezone.now() + timedelta(hours=23)
    primeiro_trecho_classe.save()
    atribuir_poltronas_mock = mocker.patch(
        "core.management.commands.atribui_poltrona_fretamento.FretamentoSeatController.atribui_poltronas"
    )
    call_command("atribui_poltrona_fretamento")
    atribuir_poltronas_mock.assert_called_once_with([pax_2])


def test_nao_atribui_poltronas_para_travel_cancelada(mocker, grupo_multitrecho):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe)
    pax_1 = baker.make(
        "core.Passageiro",
        travel__trecho_classe=primeiro_trecho_classe,
    )
    pax_1.travel.status = Travel.Status.CANCELED
    pax_1.travel.save()
    primeiro_trecho_classe.datetime_ida = timezone.now() + timedelta(hours=23)
    primeiro_trecho_classe.save()
    atribuir_poltronas_mock = mocker.patch(
        "core.management.commands.atribui_poltrona_fretamento.FretamentoSeatController.atribui_poltronas"
    )
    call_command("atribui_poltrona_fretamento")
    assert atribuir_poltronas_mock.call_count == 0


def test_nao_atribui_poltronas_para_pax_removido(mocker, grupo_multitrecho):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe)
    baker.make("core.Passageiro", travel__trecho_classe=primeiro_trecho_classe, removed=True)
    primeiro_trecho_classe.datetime_ida = timezone.now() + timedelta(hours=23)
    primeiro_trecho_classe.save()
    atribuir_poltronas_mock = mocker.patch(
        "core.management.commands.atribui_poltrona_fretamento.FretamentoSeatController.atribui_poltronas"
    )
    call_command("atribui_poltrona_fretamento")
    assert atribuir_poltronas_mock.call_count == 0


def test_nao_atribui_poltronas_para_pax_com_mais_de_24h(mocker, grupo_multitrecho):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe)
    baker.make("core.Passageiro", poltrona=None, travel__trecho_classe=primeiro_trecho_classe)
    primeiro_trecho_classe.datetime_ida = timezone.now() + timedelta(hours=30)
    atribuir_poltronas_mock = mocker.patch(
        "core.management.commands.atribui_poltrona_fretamento.FretamentoSeatController.atribui_poltronas"
    )
    call_command("atribui_poltrona_fretamento")
    assert atribuir_poltronas_mock.call_count == 0


def test_remanejamento_com_assento_fluxo_completo(
    mocker, grupo_multitrecho, travel_factory, django_capture_on_commit_callbacks, globalsettings_mock
):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    mocker.patch("core.service.notifications.user_notification_svc.notifica_remanejamento_buseiro")
    mocker.patch("core.service.notifications.pcd_notification_svc.notify_staff_if_pcd_pax_lost_seat.delay")
    mocker.patch("core.service.remanejamento.remanejamento_svc._atualiza_vagas")
    globalsettings_mock("novo_remanejamento_contabil", True)

    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    travel = travel_factory(qtd_pax=4)
    travel.grupo.onibus = baker.make("core.Onibus", placa="MHS2024")
    travel.grupo.save()

    passageiros = list(travel.passageiro_set.all())

    cria_assentos_para_onibus(primeiro_trecho_classe)

    for index, pax in enumerate(passageiros, start=1):
        # 2 pax sem poltrona associada
        if index > 2:
            continue
        pax.poltrona = index
        pax.save()

        baker.make(
            "core.PoltronaOnibus",
            onibus=travel.grupo.onibus,
            poltrona=pax.poltrona,
            linha=1,
            coluna=pax.poltrona,
            andar=1,
            tipo=travel.trecho_classe.grupo_classe.tipo_assento,
            ativo=True,
        )

        baker.make(
            "core.PoltronaOnibus",
            onibus=grupo_multitrecho.onibus,
            poltrona=pax.poltrona,
            linha=1,
            coluna=pax.poltrona,
            andar=1,
            tipo=travel.trecho_classe.grupo_classe.tipo_assento,
            ativo=True,
        )

        item_marcacao_assento = baker.make(
            "core.ItemAdicional",
            travel=travel,
            pax=pax,
            tipo=ItemAdicional.TipoItemAdicional.MARCACAO_ASSENTO,
            status=ItemAdicional.StatusItemAdicional.CONCLUIDO,
            valor=Decimal("14.9"),
            quantidade=1,
            is_upsell=False,
            poltrona=pax.poltrona,
        )

        baker.make(
            "core.AccountingOperation",
            travel=travel,
            source=item_marcacao_assento.tipo,
            value=-Decimal("14.9"),
            value_real=-Decimal("14.9"),
            passageiro=pax,
        )

        baker.make(
            "accounting.AccountingOperation",
            travel=travel,
            source=item_marcacao_assento.tipo,
            value_real=-Decimal("14.9"),
            passageiro=pax,
        )

    form = RemanejamentoForm(
        travel_id=travel.id,
        trecho_classe_destino=primeiro_trecho_classe.id,
        whatsapp=False,
        push_inbox_sms_email=False,
    )
    constraints = FormRemanejamentoConstraints(
        marketplace=True,
        hibrido=True,
        downgrade=True,
        grupo_fechado=False,
        range_minutos=5,
        range_km=2,
    )

    with django_capture_on_commit_callbacks(execute=True):
        remanejamento_svc.remanejamento_automatico([form], constraints)

    old_accops_reserva = travel.accountingoperation_set.all()
    new_travel = Travel.objects.get(status="pending")
    new_accops = new_travel.accountingoperation_set.all()
    new_paxes = new_travel.passageiro_set.all()
    new_pax_1 = new_paxes.filter(buseiro=passageiros[0].buseiro).first()
    new_pax_2 = new_paxes.filter(buseiro=passageiros[1].buseiro).first()
    new_pax_3 = new_paxes.filter(buseiro=passageiros[2].buseiro).first()
    new_pax_4 = new_paxes.filter(buseiro=passageiros[3].buseiro).first()

    new_item_adicional_1 = ItemAdicional.objects.filter(
        pax=new_pax_1, tipo=ItemAdicional.TipoItemAdicional.MARCACAO_ASSENTO
    ).first()
    new_item_adicional_2 = ItemAdicional.objects.filter(
        pax=new_pax_2, tipo=ItemAdicional.TipoItemAdicional.MARCACAO_ASSENTO
    ).first()
    new_item_adicional_3 = ItemAdicional.objects.filter(
        pax=new_pax_3, tipo=ItemAdicional.TipoItemAdicional.MARCACAO_ASSENTO
    )
    new_item_adicional_4 = ItemAdicional.objects.filter(
        pax=new_pax_4, tipo=ItemAdicional.TipoItemAdicional.MARCACAO_ASSENTO
    )

    assert len(new_accops) < len(old_accops_reserva)
    assert len(new_accops.filter(source__in=["MARCACAO_ASSENTO"])) == 2
    assert new_pax_1 and new_pax_1.poltrona
    assert new_pax_1 and new_pax_1.poltrona
    assert new_pax_2 and new_pax_2.poltrona
    assert new_pax_3 and new_pax_3.poltrona
    assert new_pax_4 and new_pax_4.poltrona
    assert new_item_adicional_1 and new_item_adicional_1.poltrona == new_pax_1.poltrona
    assert new_item_adicional_2 and new_item_adicional_2.poltrona == new_pax_2.poltrona
    assert new_item_adicional_2
    assert not new_item_adicional_3
    assert not new_item_adicional_4


# Testes para SeatControllerManager
def test_seat_controller_manager_get_cria_novo_controller(grupo_multitrecho):
    """Testa se o manager cria um novo controller quando não está em cache."""
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe)

    manager = SeatControllerManager(FretamentoSeatController)
    controller = manager.get(primeiro_trecho_classe)

    assert isinstance(controller, FretamentoSeatController)
    assert controller.trecho_classe == primeiro_trecho_classe


def test_seat_controller_manager_get_usa_cache(grupo_multitrecho, user):
    """Testa se o manager reutiliza o controller do cache."""
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe)

    manager = SeatControllerManager(FretamentoSeatController)

    controller1 = manager.get(primeiro_trecho_classe)

    controller2 = manager.get(primeiro_trecho_classe)

    assert controller1 is controller2
    assert manager.cache_size() == 1


def test_seat_controller_manager_remove_from_cache(grupo_multitrecho, user):
    """Testa se o método remove_from_cache remove um controller específico."""
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    segundo_trecho_classe = grupo_multitrecho.trechoclasse_set.last()
    cria_assentos_para_onibus(primeiro_trecho_classe)
    cria_assentos_para_onibus(segundo_trecho_classe)

    manager = SeatControllerManager(FretamentoSeatController)

    manager.get(primeiro_trecho_classe)
    manager.get(segundo_trecho_classe)
    assert manager.cache_size() == 2

    manager.remove_from_cache(primeiro_trecho_classe.id)
    assert manager.cache_size() == 1

    controller = manager.get(segundo_trecho_classe)
    assert controller.trecho_classe == segundo_trecho_classe


def test_seat_controller_manager_remove_from_cache_inexistente(grupo_multitrecho, user):
    """Testa se remove_from_cache não falha quando o ID não existe no cache."""
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe)

    manager = SeatControllerManager(FretamentoSeatController)
    manager.get(primeiro_trecho_classe)

    assert manager.cache_size() == 1
    manager.remove_from_cache(99999)
    assert manager.cache_size() == 1


def test_seat_controller_manager_multiplos_trechos_classe(grupo_multitrecho, user):
    """Testa se o manager funciona corretamente com múltiplos trechos_classe."""
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    segundo_trecho_classe = grupo_multitrecho.trechoclasse_set.last()
    cria_assentos_para_onibus(primeiro_trecho_classe)
    cria_assentos_para_onibus(segundo_trecho_classe)

    manager = SeatControllerManager(FretamentoSeatController)

    # Obtém controllers para diferentes trechos_classe
    controller1 = manager.get(primeiro_trecho_classe)
    controller2 = manager.get(segundo_trecho_classe)

    assert controller1 != controller2  # Instâncias diferentes
    assert controller1.trecho_classe == primeiro_trecho_classe
    assert controller2.trecho_classe == segundo_trecho_classe
    assert manager.cache_size() == 2


def test_seat_controller_manager_factory_chamada_apenas_uma_vez(grupo_multitrecho, user):
    """Testa se a factory é chamada apenas uma vez por trecho_classe."""
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe)

    factory_calls = []

    def factory(trecho_classe):
        factory_calls.append(trecho_classe.id)
        return FretamentoSeatController(trecho_classe, user)

    manager = SeatControllerManager(factory)

    # Chama get múltiplas vezes
    manager.get(primeiro_trecho_classe)
    manager.get(primeiro_trecho_classe)
    manager.get(primeiro_trecho_classe)

    # A factory deve ter sido chamada apenas uma vez
    assert len(factory_calls) == 1
    assert factory_calls[0] == primeiro_trecho_classe.id


def test_atribui_poltronas_a_multiplas_travels(mocker, grupo_multitrecho, user):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    primeiro_trecho_classe = grupo_multitrecho.trechoclasse_set.first()
    cria_assentos_para_onibus(primeiro_trecho_classe)
    pax_travel_1 = baker.make(
        "core.Passageiro",
        poltrona=None,
        _quantity=2,
    )
    pax_travel_2 = baker.make(
        "core.Passageiro",
        poltrona=None,
        _quantity=2,
    )

    travel_1 = baker.make("core.Travel", trecho_classe=primeiro_trecho_classe, passageiro_set=pax_travel_1)
    travel_2 = baker.make("core.Travel", trecho_classe=primeiro_trecho_classe, passageiro_set=pax_travel_2)
    manager = FretamentoSeatController(primeiro_trecho_classe, user)
    manager.atribui_poltronas(list(travel_1.passageiro_set.all()))
    manager.atribui_poltronas(list(travel_2.passageiro_set.all()))
    passageiros = Passageiro.objects.filter(travel__in=[travel_1, travel_2])
    passageiros_sem_poltrona = passageiros.filter(poltrona__isnull=True)
    passageiros_com_poltrona = passageiros.filter(poltrona__isnull=False)

    assert len(passageiros) == 4
    assert len(passageiros_sem_poltrona) == 0
    assert len(passageiros_com_poltrona) == 4
    assert len(set(passageiros_com_poltrona.values_list("poltrona", flat=True))) == 4
