from datetime import datetime, timedelta

import pytest
from django.conf import settings
from django.utils.timezone import get_default_timezone
from model_bakery import baker

from core.models_driving import AlertaSeguranca, SmartCamAlarm
from core.models_grupo import Grupo
from core.models_rota import Rota
from core.service import fadiga_anomalia_svc
from core.tests import fixtures


def test_get_alert_context():
    rota = baker.make(Rota, duracao_total=timedelta(hours=3))
    drive_1 = fixtures.user_driver()
    grupo = baker.make(
        Grupo,
        rota=rota,
        active_driver=drive_1.id,
        driver_one=drive_1,
        driver_two=fixtures.user_driver2(),
        _fill_optional=["onibus"],
    )
    alerta = baker.make(
        AlertaSeguranca,
        grupo=grupo,
        alert_type=AlertaSeguranca.Type.CANSACO_LEVE,
    )

    context = fadiga_anomalia_svc._get_alert_context(alerta, alerta.grupo)

    assert context["group_str"] == str(alerta.grupo)
    assert context["alert_datetime"] == alerta.alert_datetime
    assert context["alert_message"] == alerta.message
    assert context["moto_strs"] == [
        f"{alerta.grupo.driver_one.get_full_name()}: {alerta.grupo.driver_one.profile.cell_phone} (dirigindo)",
        f"{alerta.grupo.driver_two.get_full_name()}: {alerta.grupo.driver_two.profile.cell_phone} (co-piloto)",
    ]
    assert context["resolve_url"] == f"http://localhost:8000/api/staff/fadiga/anomalias/{alerta.id}/resolve"
    assert context["group_url"] == f"{settings.STAFF_BASE_URL}/staff/grupos/{alerta.grupo.id}"


@pytest.mark.parametrize(
    "method, alerta_type, smartCam_type",
    [
        (
            fadiga_anomalia_svc.verifica_alerta_nmr_15,
            AlertaSeguranca.Type.CANSACO_LEVE,
            SmartCamAlarm.Types.OLHO_FECHADO,
        ),
        (fadiga_anomalia_svc.verifica_alerta_nmr_16, AlertaSeguranca.Type.CANSACO_MEDIO, SmartCamAlarm.Types.BOCEJO),
    ],
)
def test_verifica_alerta_nmr_15_and_16(method, alerta_type, smartCam_type):
    data = datetime(2024, 12, 5, 18, 52, 36, tzinfo=get_default_timezone())
    grupo = baker.make("core.Grupo", _fill_optional=["onibus"])
    alarm = baker.make(
        SmartCamAlarm,
        gps_time=data,
        type=smartCam_type,
        plate=grupo.onibus.placa,
    )

    alarm_antigo = baker.make(
        SmartCamAlarm,
        gps_time=data - timedelta(minutes=10),
        type=smartCam_type,
        plate=grupo.onibus.placa,
    )

    baker.make(
        AlertaSeguranca,
        grupo=grupo,
        alert_type=alerta_type,
    )

    deve_criar, alert_type_result, _, cansaco_ids, alarm_gps_time = method(grupo, alarm)

    assert deve_criar is True
    assert alert_type_result == alerta_type
    assert cansaco_ids == [alarm.id, alarm_antigo.id]
    assert alarm_gps_time == data


def test_verifica_alerta_sem_eventos_suficientes():
    data = datetime(2024, 12, 5, 18, 52, 36, tzinfo=get_default_timezone())
    grupo = baker.make("core.Grupo", _fill_optional=["onibus"])
    alarm = baker.make(
        "core.SmartCamAlarm",
        gps_time=data,
        type=SmartCamAlarm.Types.OLHO_FECHADO,
        plate=grupo.onibus.placa,
    )

    deve_criar, _, _, cansaco_ids, _ = fadiga_anomalia_svc.verifica_alerta_nmr_15(grupo, alarm)

    assert deve_criar is False
    assert cansaco_ids == [alarm.id]


def test_verifica_alerta_de_velocidade_tablet_sascar():
    data = datetime(2024, 12, 5, 18, 52, 36, tzinfo=get_default_timezone())
    grupo = baker.make("core.Grupo", _fill_optional=["onibus"])
    alarm = baker.make(
        "core.SmartCamAlarm",
        gps_time=data,
        type=SmartCamAlarm.Types.EXCESSO_VELOCIDADE_PISTA_MOLHADA,
        plate=grupo.onibus.placa,
        speed=900,  # 90 km/h
    )

    deve_criar, _, _, cansaco_ids, _ = fadiga_anomalia_svc.verifica_alerta_velocidade_tablet_sascar(grupo, alarm)

    assert deve_criar is True


def test_nao_cria_alarm_velocidade_tablet_sascar():
    data = datetime(2024, 12, 5, 18, 52, 36, tzinfo=get_default_timezone())
    grupo = baker.make("core.Grupo", _fill_optional=["onibus"])
    alarm = baker.make(
        "core.SmartCamAlarm",
        gps_time=data,
        type=SmartCamAlarm.Types.EXCESSO_VELOCIDADE_PISTA_MOLHADA,
        plate=grupo.onibus.placa,
        speed=700,  # 70 km/h nao deve gerar alerta
    )

    deve_criar, _, _, cansaco_ids, _ = fadiga_anomalia_svc.verifica_alerta_velocidade_tablet_sascar(grupo, alarm)

    assert deve_criar is False


def test_verifica_alerta_generico_tablet_sascar():
    data = datetime(2024, 12, 5, 18, 52, 36, tzinfo=get_default_timezone())
    grupo = baker.make("core.Grupo", _fill_optional=["onibus"])
    alarm = baker.make(
        "core.SmartCamAlarm",
        gps_time=data,
        type=SmartCamAlarm.Types.FREADA_BRUSCA,
        plate=grupo.onibus.placa,
        speed=500,
    )

    deve_criar, _, _, _, _ = fadiga_anomalia_svc.verifica_alerta_tablet_sascar(grupo, alarm)

    assert deve_criar is True


def test_verifica_nao_cria_alerta_tablet_sascar():
    data = datetime(2024, 12, 5, 18, 52, 36, tzinfo=get_default_timezone())
    grupo = baker.make("core.Grupo", _fill_optional=["onibus"])
    baker.make(
        "core.AlertaSeguranca",
        grupo=grupo,
        alert_type=AlertaSeguranca.Type.FREADA_BRUSCA,
    )

    alarm = baker.make(
        "core.SmartCamAlarm",
        gps_time=data,
        type=SmartCamAlarm.Types.FREADA_BRUSCA,
        plate=grupo.onibus.placa,
        speed=500,
    )

    deve_criar, _, _, _, _ = fadiga_anomalia_svc.verifica_alerta_tablet_sascar(grupo, alarm)

    assert deve_criar is False
