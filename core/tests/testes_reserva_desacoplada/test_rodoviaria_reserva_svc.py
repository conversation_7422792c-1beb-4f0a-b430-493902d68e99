import json
from datetime import date, timedelta
from unittest.mock import call

import pytest
from django.contrib.auth.models import User
from model_bakery import baker
from tenacity import stop_after_attempt, wait_none

from commons.dateutils import now
from commons.django_utils import error_str
from commons.enum import ModeloVenda
from core.enums import CategoriaEspecial
from core.forms.rodoviaria_forms import BuseiroForm, ComprarForm, DadosBeneficioForm
from core.models_grupo import TrechoClasse
from core.models_travel import Buseiro, Pagamento, Passageiro, Reserva, Travel
from core.service.reserva.reserva_svc import (
    emitir_passagens_async,
    emitir_passagens_rodoviaria_retry_wrapper,
)
from core.service.reserva.rodoviaria_reserva_svc import (
    _emitir_passagem_pax,
    _get_buyer_cpf,
    emitir_passagem_staff,
    emitir_passagens_rodoviaria_passagem_unica,
    form_emissao_pax_v2,
    get_vagas_por_categoria_especial,
    passagens_emitidas,
)
from core.service.selecao_assento.marketplace import MarketplaceSeatsController
from core.service.selecao_assento.models import Assento, BlockedSeat
from integrations.rodoviaria_client import RodoviariaClient
from integrations.rodoviaria_client.exceptions import (
    PoltronaExpiradaException,
    RodoviariaConnectionException,
    RodoviariaException,
    RodoviariaViagemBloqueada,
)


def test_emitir_passagens_async_sucesso_external_payload(
    user,
    trecho_classe_sp_bh,
    client_with_logged_user,
    trecho_vendido_sp_bh,
    mocker,
    retry_sem_wait,
    retry_sem_novas_tentativas,
):
    _, trecho_classe, travels, buseiro, passageiros = _cria_reserva_completa(
        user, trecho_classe_sp_bh, trecho_vendido_sp_bh
    )
    expected_blocked_seat = BlockedSeat(
        poltrona=Assento(numero=42, x=1, y=1, tipo_assento="convencional"),
        tempo_limite_bloqueio=(now() + timedelta(hours=24)),
        external_payload={"External": "Payload"},
    )
    for pax in passageiros:
        pax.extra = {"bloqueio_poltrona": expected_blocked_seat.model_dump()}
        pax.poltrona = expected_blocked_seat.poltrona.numero
        pax.save()

    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True)
    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.passagens_emitidas", return_value=False)
    efetua_compra_rodoviaria = mocker.patch("core.service.rodoviaria_svc.efetua_compra_unica", return_value={})

    emitir_passagens_async([t.id for t in travels])

    expected_calls = []
    for travel in travels:
        expected_calls.append(
            call(
                _mount_comprar_form(
                    user.profile.cpf,
                    trecho_classe,
                    travel,
                    buseiro,
                    [expected_blocked_seat.poltrona.numero],
                    expected_blocked_seat.external_payload,
                ),
                timeout=mocker.ANY,
            )
        )

    efetua_compra_rodoviaria.assert_has_calls(expected_calls, any_order=True)


def test_emitir_passagens_async_sucesso(
    user,
    trecho_classe_sp_bh,
    client_with_logged_user,
    trecho_vendido_sp_bh,
    mocker,
    retry_sem_wait,
    retry_sem_novas_tentativas,
):
    _, trecho_classe, travels, buseiro, paxes = _cria_reserva_completa(user, trecho_classe_sp_bh, trecho_vendido_sp_bh)

    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True)
    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.passagens_emitidas", return_value=False)
    efetua_compra_rodoviaria = mocker.patch("core.service.rodoviaria_svc.efetua_compra_unica", return_value={})

    emitir_passagens_async([t.id for t in travels])

    expected_calls = []
    for travel, pax in zip(travels, paxes):
        expected_calls.append(
            call(
                _mount_comprar_form(user.profile.cpf, trecho_classe, travel, buseiro, [pax.poltrona]),
                timeout=mocker.ANY,
            )
        )

    efetua_compra_rodoviaria.assert_has_calls(expected_calls, any_order=True)


def test_emitir_passagens_async_emissao_parcial(
    user,
    trecho_classe_sp_bh,
    client_with_logged_user,
    trecho_vendido_sp_bh,
    mocker,
    retry_sem_wait,
    retry_sem_novas_tentativas,
):
    _, trecho_classe, travels, buseiro, _ = _cria_reserva_completa(user, trecho_classe_sp_bh, trecho_vendido_sp_bh)
    # marca a primeira travel como já emitida
    pax = travels[0].passageiro_set.first()
    pax.extra["emissao"] = {"sucesso": True}
    pax.save()

    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True)
    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.passagens_emitidas", return_value=False)
    efetua_compra_rodoviaria = mocker.patch("core.service.rodoviaria_svc.efetua_compra_unica", return_value={})

    emitir_passagens_async([t.id for t in travels])

    polt = [1]
    # como a primeira já está emitida, tenta apenas emitir a segunda
    efetua_compra_rodoviaria.assert_has_calls(
        [
            call(
                _mount_comprar_form(user.profile.cpf, trecho_classe, travels[1], buseiro, polt),
                timeout=mocker.ANY,
            )
        ],
        any_order=False,
    )


def test_emitir_passagens_async_tenta_novamente_emissao_com_erro(
    user,
    trecho_classe_sp_bh,
    client_with_logged_user,
    trecho_vendido_sp_bh,
    mocker,
    retry_sem_wait,
    retry_sem_novas_tentativas,
):
    _, trecho_classe, travels, buseiro, _ = _cria_reserva_completa(user, trecho_classe_sp_bh, trecho_vendido_sp_bh)
    # marca a primeira travel como emitida com erro
    pax = travels[0].passageiro_set.first()
    pax.extra["emissao"] = {"sucesso": False}
    pax.save()

    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True)
    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.passagens_emitidas", return_value=False)
    efetua_compra_rodoviaria = mocker.patch("core.service.rodoviaria_svc.efetua_compra_unica", return_value={})

    emitir_passagens_async([t.id for t in travels])

    expected_calls = []
    polt = [1]
    for travel in travels:
        expected_calls.append(
            call(
                _mount_comprar_form(user.profile.cpf, trecho_classe, travel, buseiro, polt),
                timeout=mocker.ANY,
            )
        )
    efetua_compra_rodoviaria.assert_has_calls(expected_calls, any_order=False)


def test_emitir_passagens_async_sucesso_sem_cache_poltronas(
    user,
    trecho_classe_sp_bh,
    client_with_logged_user,
    trecho_vendido_sp_bh,
    mocker,
):
    _, trecho_classe, travels, buseiro, _ = _cria_reserva_completa(user, trecho_classe_sp_bh, trecho_vendido_sp_bh)

    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.passagens_emitidas", return_value=False)
    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True)
    efetua_compra_rodoviaria = mocker.patch("core.service.rodoviaria_svc.efetua_compra_unica", return_value={})

    emitir_passagens_async.delay([t.id for t in travels])

    expected_calls = []
    polt = [1]
    for travel in travels:
        expected_calls.append(
            call(
                _mount_comprar_form(user.profile.cpf, trecho_classe, travel, buseiro, polt),
                timeout=mocker.ANY,
            )
        )
    efetua_compra_rodoviaria.assert_has_calls(expected_calls, any_order=True)


def test_emitir_passagens_async_erro_efetua_compra(
    user,
    trecho_classe_sp_bh,
    client_with_logged_user,
    trecho_vendido_sp_bh,
    mocker,
    retry_sem_wait,
    retry_sem_novas_tentativas,
):
    _, trecho_classe, travels, buseiro, _ = _cria_reserva_completa(user, trecho_classe_sp_bh, trecho_vendido_sp_bh)

    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True)
    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.passagens_emitidas", return_value=False)
    solicita_cancelamento_rodov = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._solicita_cancelamento"
    )
    cancel_travels = mocker.patch("core.service.reserva.reserva_svc._cancel_travel")
    efetua_compra_rodoviaria = mocker.patch(
        "core.service.rodoviaria_svc.efetua_compra_unica",
        side_effect=RodoviariaException("Caiu o poste na frente da Eulabs", type="fallen_poste"),
    )

    emitir_passagens_async.delay([t.id for t in travels])

    # como deu erro, apenas rodou a primeira travel,
    travel_assert_efetua_compra = travels[0]
    polt = [1]
    efetua_compra_rodoviaria.assert_called_with(
        _mount_comprar_form(
            user.profile.cpf,
            trecho_classe,
            travel_assert_efetua_compra,
            buseiro,
            polt,
        ),
        timeout=mocker.ANY,
    )
    assert cancel_travels.call_count == len(travels)
    solicita_cancelamento_rodov.assert_not_called()


def test_emitir_passagens_async_erro_cancelamento(
    user,
    trecho_classe_sp_bh,
    client_with_logged_user,
    trecho_vendido_sp_bh,
    mocker,
    retry_sem_wait,
    retry_sem_novas_tentativas,
):
    _, _, travels, _, _ = _cria_reserva_completa(user, trecho_classe_sp_bh, trecho_vendido_sp_bh)

    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.passagens_emitidas", return_value=False)
    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True)
    cancel_travels = mocker.patch("core.service.reserva.reserva_svc._cancel_travel")

    emitir_passagens_async.delay([t.id for t in travels])
    assert cancel_travels.call_count == len(travels)


def test_emitir_passagens_async_passagens_ja_emitidas(
    user, trecho_classe_sp_bh, client_with_logged_user, trecho_vendido_sp_bh, mocker
):
    _, trecho_classe, travels, _, _ = _cria_reserva_completa(user, trecho_classe_sp_bh, trecho_vendido_sp_bh)

    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.passagens_emitidas", return_value=True)
    emitir_passagens_rodoviaria_retry_wrapper = mocker.patch(
        "core.service.reserva.reserva_svc.emitir_passagens_rodoviaria_retry_wrapper"
    )

    emitir_passagens_async.delay([t.id for t in travels])
    emitir_passagens_rodoviaria_retry_wrapper.assert_not_called()


def _mount_comprar_form(default_buyer_cpf, trecho_classe, travel, buseiro, poltronas: list[int], extra_poltronas=None):
    return ComprarForm(
        trechoclasse_id=trecho_classe.id,
        travel_id=travel.id,
        valor_cheio=travel.max_split_value,
        poltronas=poltronas,
        extra_poltronas=extra_poltronas,
        categoria_especial=CategoriaEspecial.NORMAL,
        buseiros=[
            BuseiroForm(
                id=buseiro.id,
                name=buseiro.name,
                cpf=None,
                buyer_cpf=default_buyer_cpf,
                rg_number=buseiro.rg_number,
                rg_orgao=buseiro.rg_orgao,
                tipo_documento=buseiro.tipo_documento,
                phone="88923282383",
                birthday=None,
                dados_beneficio=None,
            )
        ],
    )


def _cria_reserva_completa(user, trecho_classe_sp_bh, trecho_vendido_sp_bh, status_reserva=Reserva.Status.CONCLUIDA):
    reserva = baker.make(Reserva, status=status_reserva, user=user)
    pagamento = baker.make(Pagamento, status=Pagamento.Status.PAID, reserva=reserva)

    trecho_classe = trecho_classe_sp_bh()

    travels_len = 2
    travels = baker.make(
        "core.Travel",
        user=user,
        trecho_vendido=trecho_vendido_sp_bh,
        pagamento=pagamento,
        trecho_classe=trecho_classe,
        reserva=reserva,
        _quantity=travels_len,
    )
    buseiro = baker.make(Buseiro)
    passageiros = [
        baker.make(
            Passageiro,
            travel=travels[0],
            buseiro=buseiro,
            poltrona=1,
            extra={
                "bloqueio_poltrona": {
                    "poltrona": {"numero": 1, "x": 1, "y": 1, "tipo_assento": "convencional"},
                    "tempo_limite_bloqueio": now() + timedelta(hours=24),
                }
            },
        ),
        baker.make(
            Passageiro,
            travel=travels[1],
            buseiro=buseiro,
            poltrona=1,
            extra={
                "bloqueio_poltrona": {
                    "poltrona": {"numero": 1, "x": 1, "y": 1, "tipo_assento": "convencional"},
                    "tempo_limite_bloqueio": now() + timedelta(hours=24),
                }
            },
        ),
    ]
    return reserva, trecho_classe, travels, buseiro, passageiros


def test_deve_emitir_passagens_rodoviaria_nao_emite_se_nao_for_rodoviaria_integrado(mocker):
    travel = baker.prepare(Travel)
    assert passagens_emitidas([travel]) is True


def test_existepassagens_emitidas_rodoviaria_nao_existe(mocker):
    travel = baker.prepare(Travel)
    passagens_rodov = []
    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True)
    mocker.patch("core.service.rodoviaria_svc.get_passagens_rodoviaria", return_value=passagens_rodov)
    assert passagens_emitidas([travel]) is False


def test_existepassagens_emitidas_rodoviaria_ja_existepassagens_emitidas(mocker):
    travel = baker.prepare(Travel)
    passagens_rodov = [
        {
            "travel_internal_id": 1,
            "buseiro_internal_id": 1,
            "status": "confirmada",
            "localizador": 1,
            "numero_passagem": 1,
        }
    ]
    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True)
    mocker.patch("core.service.rodoviaria_svc.get_passagens_rodoviaria", return_value=passagens_rodov)
    assert passagens_emitidas([travel]) is True


def test_existepassagens_emitidas_rodoviaria_ja_existepassagens_emitidas_conexao(mocker):
    travel = baker.prepare(Travel)
    passagens_rodov = [
        {
            "travel_internal_id": 1,
            "buseiro_internal_id": 1,
            "status": "confirmada",
            "localizador": 1,
            "numero_passagem": 1,
        },
        {
            "travel_internal_id": 1,
            "buseiro_internal_id": 1,
            "status": "confirmada",
            "localizador": 2,
            "numero_passagem": 2,
        },
    ]
    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True)
    mocker.patch("core.service.rodoviaria_svc.get_passagens_rodoviaria", return_value=passagens_rodov)
    assert passagens_emitidas([travel]) is True


def test_emitir_passagens_rodoviaria_passagem_unica(mocker):
    reserva = baker.make(Reserva)
    user = baker.prepare(User, profile=baker.prepare("Profile", _fill_optional=["cell_phone"]))
    travel = baker.make(Travel, trecho_classe=baker.make(TrechoClasse), reserva=reserva)
    buseiro = baker.make(Buseiro)
    baker.make(
        Passageiro,
        travel=travel,
        buseiro=buseiro,
        categoria_especial=CategoriaEspecial.NORMAL,
        poltrona=1,
        extra={
            "bloqueio_poltrona": {
                "poltrona": {"numero": 1, "x": 1, "y": 1, "tipo_assento": "convencional"},
                "tempo_limite_bloqueio": now() + timedelta(hours=24),
            }
        },
    )

    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True)
    efetua_compra_rodoviaria = mocker.patch(
        "core.service.rodoviaria_svc.efetua_compra_unica", return_value={"detalhes_emissao": True}
    )

    emitir_passagens_rodoviaria_passagem_unica(user, reserva, [travel])

    efetua_compra_rodoviaria.assert_called_with(
        ComprarForm(
            trechoclasse_id=travel.trecho_classe_id,
            travel_id=travel.id,
            valor_cheio=None,
            poltronas=[1],
            categoria_especial=CategoriaEspecial.NORMAL,
            buseiros=[
                BuseiroForm(
                    id=buseiro.id,
                    name=buseiro.name,
                    buyer_cpf=buseiro.cpf,
                    cpf=buseiro.cpf,
                    rg_number=buseiro.rg_number,
                    rg_orgao=buseiro.rg_orgao,
                    tipo_documento=buseiro.tipo_documento,
                    phone=user.profile.cell_phone,
                    birthday=None,
                    dados_beneficio=None,
                )
            ],
        ),
        timeout=mocker.ANY,
    )


def test_emitir_passagens_rodoviaria_com_beneficios(mocker):
    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True)
    user = baker.prepare(User, profile=baker.prepare("Profile", _fill_optional=["cell_phone"]))
    travel = baker.make(Travel, trecho_classe=baker.make(TrechoClasse))
    buseiro = baker.make(Buseiro)
    baker.make(
        Passageiro,
        travel=travel,
        buseiro=buseiro,
        categoria_especial=CategoriaEspecial.IDOSO_50,
        poltrona=1,
        extra={
            "bloqueio_poltrona": {
                "poltrona": {"numero": 1, "x": 1, "y": 1, "tipo_assento": "convencional"},
                "tempo_limite_bloqueio": now() + timedelta(hours=24),
            }
        },
    )
    reserva = baker.make(
        Reserva,
        input_payload={
            "categoria_especial": CategoriaEspecial.IDOSO_50,
            "passengers": [{"id": buseiro.id, "dados_beneficio": {"renda": 2000, "data_expiracao": "02042030"}}],
        },
    )

    efetua_compra_rodoviaria = mocker.patch("core.service.rodoviaria_svc.efetua_compra_unica", return_value="retorno")

    emitir_passagens_rodoviaria_passagem_unica(user, reserva, [travel])

    efetua_compra_rodoviaria.assert_called_with(
        ComprarForm(
            trechoclasse_id=travel.trecho_classe_id,
            travel_id=travel.id,
            valor_cheio=None,
            poltronas=[1],
            categoria_especial=CategoriaEspecial.IDOSO_50,
            buseiros=[
                BuseiroForm(
                    id=buseiro.id,
                    name=buseiro.name,
                    buyer_cpf=buseiro.cpf,
                    cpf=buseiro.cpf,
                    rg_number=buseiro.rg_number,
                    rg_orgao=buseiro.rg_orgao,
                    tipo_documento=buseiro.tipo_documento,
                    phone=user.profile.cell_phone,
                    birthday=None,
                    dados_beneficio=DadosBeneficioForm(
                        renda=2000,
                        data_expiracao=date(2030, 4, 2),
                        data_expedicao=None,
                        numero_beneficio=None,
                        tipo_passe_livre=None,
                        auxilio_embarque=None,
                    ),
                )
            ],
        ),
        timeout=mocker.ANY,
    )


def test_form_emissao_pax_v2():
    reserva = baker.make(
        Reserva,
        input_payload={
            "categoria_especial": "crianca",
            "passengers": [
                {
                    "dados_beneficio": {
                        "renda": None,
                        "data_expiracao": None,
                        "data_expedicao": None,
                        "numero_beneficio": "numero",
                        "tipo_passe_livre": None,
                        "auxilio_embarque": None,
                    }
                }
            ],
        },
    )
    user = baker.prepare(User, profile=baker.prepare("Profile", _fill_optional=["cell_phone"]))
    travel = baker.prepare(Travel, id=1, reserva_id=reserva.id, trecho_classe=baker.prepare(TrechoClasse, id=1))
    buseiro = baker.prepare(Buseiro, id=1)
    pax = baker.prepare(Passageiro, travel=travel, buseiro=buseiro, poltrona=42)
    blocked_seat = BlockedSeat(
        poltrona=Assento(numero=42, x=1, y=1, tipo_assento="convencional"),
        tempo_limite_bloqueio=(now() + timedelta(hours=24)),
        external_payload={"External": "Payload"},
    )
    dto = form_emissao_pax_v2(
        reserva,
        user,
        "1991010921",
        pax,
        blocked_seat,
    )

    assert dto == ComprarForm(
        trechoclasse_id=travel.trecho_classe_id,
        travel_id=travel.id,
        valor_cheio=None,
        poltronas=[pax.poltrona],
        extra_poltronas=blocked_seat.external_payload,
        categoria_especial=CategoriaEspecial.CRIANCA,
        buseiros=[
            BuseiroForm(
                id=buseiro.id,
                name=buseiro.name,
                cpf=buseiro.cpf,
                buyer_cpf="1991010921",
                rg_number=buseiro.rg_number,
                rg_orgao=buseiro.rg_orgao,
                tipo_documento=buseiro.tipo_documento,
                phone=user.profile.cell_phone,
                birthday=None,
                dados_beneficio=DadosBeneficioForm(
                    renda=None,
                    data_expiracao=None,
                    data_expedicao=None,
                    numero_beneficio="numero",
                    tipo_passe_livre=None,
                    auxilio_embarque=None,
                ),
            )
        ],
    )


def test_get_vagas_por_categoria_especial(mocker):
    mock_client = mocker.patch.object(
        RodoviariaClient,
        "get_vagas_por_categoria_especial",
        return_value={"categorias_especiais": [{"nome": "idoso 100%", "vagas": 2, "id": "idoso_100"}]},
    )
    mocker.patch("core.service.rodoviaria_svc.get_empresa_info", return_value={"features": ["active"]})

    resposta = get_vagas_por_categoria_especial(1, 123)

    mock_client.assert_called_once_with(1)
    assert resposta == {"categorias_especiais": [{"nome": "idoso 100%", "vagas": 2, "id": "idoso_100"}]}


def test_emitir_passagens_rodoviaria_retry_wrapper(
    user, trecho_classe_sp_bh, client_with_logged_user, trecho_vendido_sp_bh, mocker, retry_sem_wait
):
    reserva, _, travels, _, _ = _cria_reserva_completa(user, trecho_classe_sp_bh, trecho_vendido_sp_bh)
    mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.emitir_passagens_rodoviaria_passagem_unica",
        side_effect=RodoviariaConnectionException(),
    )

    with pytest.raises(RodoviariaConnectionException):
        emitir_passagens_rodoviaria_retry_wrapper(user, reserva, travels)

    assert emitir_passagens_rodoviaria_retry_wrapper.retry.statistics["attempt_number"] == 4  # type: ignore


def test_emitir_passagens_rodoviaria_retry_wrapper_viagem_bloqueada_nao_faz_retry(
    user, trecho_classe_sp_bh, client_with_logged_user, trecho_vendido_sp_bh, mocker, retry_sem_wait
):
    reserva, _, travels, _, _ = _cria_reserva_completa(user, trecho_classe_sp_bh, trecho_vendido_sp_bh)
    mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.emitir_passagens_rodoviaria_passagem_unica",
        side_effect=RodoviariaViagemBloqueada(),
    )

    with pytest.raises(RodoviariaViagemBloqueada):
        emitir_passagens_rodoviaria_retry_wrapper(user, reserva, travels)

    assert emitir_passagens_rodoviaria_retry_wrapper.retry.statistics["attempt_number"] == 1  # type: ignore


@pytest.fixture
def retry_sem_wait(mocker):
    mocker.patch(
        "core.service.reserva.reserva_svc.emitir_passagens_rodoviaria_retry_wrapper.retry.wait",
        side_effect=wait_none(),
    )


@pytest.fixture
def retry_sem_novas_tentativas(mocker):
    mocker.patch(
        "core.service.reserva.reserva_svc.emitir_passagens_rodoviaria_retry_wrapper.retry.stop",
        side_effect=stop_after_attempt(1),
    )


def test_core_passageiro_set_emissao_marketplace():
    pax = baker.make(Passageiro, extra={})
    pax.set_emissao_marketplace({"dados_das_passagens": 1})
    pax.refresh_from_db()
    assert pax.typed_extra["emissao"]["sucesso"] is True
    assert pax.typed_extra["emissao"]["detalhes_emissao"] == {"dados_das_passagens": 1}
    assert pax.foi_emitido


def test_core_passageiro_set_erro_emissao_marketplace():
    pax = baker.make(Passageiro, extra={})
    pax.set_erro_emissao_marketplace(RodoviariaException())
    pax.refresh_from_db()
    assert pax.typed_extra["emissao"]["sucesso"] is False
    assert pax.typed_extra["emissao"]["erro_emissao"] == error_str(RodoviariaException())
    assert not pax.foi_emitido


@pytest.mark.parametrize(
    "user_config, passageiros_config, expected_result",
    [
        (
            {"cpf": "12345678900"},
            [{"cpf": None}],
            "12345678900",
        ),
        (
            {"cpf": None},
            [{"cpf": None}],
            None,
        ),
        (
            None,
            [{"cpf": "08201101"}],
            "08201101",
        ),
        (
            {"cpf": None},
            [{"cpf": "08201101"}, {"cpf": None}],
            "08201101",
        ),
    ],
)
def test_get_buyer_cpf(user, user_config, passageiros_config, expected_result):
    if user_config is not None:
        user.profile.cpf = user_config.get("cpf")

    passageiros = []
    for pax_config in passageiros_config:
        passageiros.append(baker.prepare(Passageiro, buseiro=baker.prepare(Buseiro, cpf=pax_config.get("cpf"))))

    assert _get_buyer_cpf(user=user if user_config is not None else None, passageiros=passageiros) == expected_result


def test_emitir_passagem_pax(mocker):
    mocker.patch("core.service.reserva.rodoviaria_reserva_svc.form_emissao_pax_v2")
    mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.efetua_compra_unica", return_value="retorno"
    )
    expected_blocked_seat = BlockedSeat(
        poltrona=Assento(numero=42, x=1, y=1, tipo_assento="convencional"),
        tempo_limite_bloqueio=(now() + timedelta(hours=24)),
        external_payload={"External": "Payload"},
    )
    pax = baker.prepare(Passageiro)
    assert _emitir_passagem_pax(None, None, None, None, pax, expected_blocked_seat) == "retorno"


def test_emitir_passagem_pax_poltrona_expirada_tempo_limite(mocker):
    expired_blocked_seat = BlockedSeat(
        poltrona=Assento(numero=42, x=1, y=1, tipo_assento="convencional"),
        tempo_limite_bloqueio=(now() - timedelta(hours=24)),
        external_payload={"External": "Payload"},
    )
    trecho_classe = baker.prepare(TrechoClasse, grupo__modelo_venda=ModeloVenda.MARKETPLACE)
    pax = baker.prepare(Passageiro, travel=baker.prepare(Travel, trecho_classe=trecho_classe))
    mocker.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe")
    controller = MarketplaceSeatsController(trecho_classe, None)
    expected_blocked_seat = BlockedSeat(
        poltrona=Assento(numero=42, x=1, y=1, tipo_assento="convencional"),
        tempo_limite_bloqueio=(now() - timedelta(hours=24)),
        external_payload={"External": "Payload"},
    )
    mocker.patch.object(controller, "desbloquear_poltrona")
    mocker.patch.object(controller, "bloquear_poltrona", return_value=expected_blocked_seat)
    mocker.patch("core.service.selecao_assento.marketplace.MarketplaceSeatsController", return_value=controller)

    mock_form = mocker.patch("core.service.reserva.rodoviaria_reserva_svc.form_emissao_pax_v2")
    mock_compra = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.efetua_compra_unica", return_value="retorno"
    )

    assert _emitir_passagem_pax(None, None, None, None, pax, expired_blocked_seat) == "retorno"
    assert mock_compra.call_count == 1
    assert mock_form.call_count == 1
    assert mock_form.mock_calls[0][1][4] == expected_blocked_seat


def test_emitir_passagem_pax_poltrona_expirada_erro_compra(mocker):
    expired_blocked_seat = BlockedSeat(
        poltrona=Assento(numero=42, x=1, y=1, tipo_assento="convencional"),
        tempo_limite_bloqueio=(now() + timedelta(hours=24)),
        external_payload={"External": "Payload"},
    )
    expected_blocked_seat = BlockedSeat(
        poltrona=Assento(numero=42, x=1, y=1, tipo_assento="convencional"),
        tempo_limite_bloqueio=(now() + timedelta(hours=24)),
        external_payload={"External": "Payload Atualizado"},
    )
    trecho_classe = baker.prepare(TrechoClasse, grupo__modelo_venda=ModeloVenda.MARKETPLACE)
    pax = baker.prepare(Passageiro, travel=baker.prepare(Travel, trecho_classe=trecho_classe))
    mocker.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe")
    controller = MarketplaceSeatsController(trecho_classe, None)

    mocker.patch.object(controller, "desbloquear_poltrona")
    mocker.patch.object(controller, "bloquear_poltrona", return_value=expected_blocked_seat)
    mocker.patch("core.service.selecao_assento.marketplace.MarketplaceSeatsController", return_value=controller)

    mock_form = mocker.patch("core.service.reserva.rodoviaria_reserva_svc.form_emissao_pax_v2")
    mock_compra = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.efetua_compra_unica",
        side_effect=PoltronaExpiradaException,
    )

    with pytest.raises(PoltronaExpiradaException):
        _emitir_passagem_pax(None, None, None, None, pax, expired_blocked_seat)
    assert mock_compra.call_count == 2
    assert mock_form.call_count == 2
    assert mock_form.mock_calls[0][1][4] == expired_blocked_seat
    assert mock_form.mock_calls[1][1][4] == expected_blocked_seat


# Testes para emitir_passagem_staff


def test_emitir_passagem_staff_sucesso_com_poltrona_existente(mocker):
    """Testa emissão staff quando passageiro já tem poltrona."""
    # Arrange
    user = baker.make(User)
    buseiro = baker.make(Buseiro, user=user, cpf="12345678901")
    reserva = baker.make(Reserva)
    trecho_classe = baker.make(TrechoClasse)
    travel = baker.make(Travel, reserva=reserva, trecho_classe=trecho_classe)

    # Passageiro já tem poltrona e bloqueio
    blocked_seat_data = {
        "poltrona": {"numero": 15, "x": 1, "y": 1, "tipo_assento": "convencional"},
        "tempo_limite_bloqueio": (now() + timedelta(hours=24)).isoformat(),
        "external_payload": {"test": "data"},
    }

    pax = baker.make(
        Passageiro, travel=travel, buseiro=buseiro, poltrona=15, extra={"bloqueio_poltrona": blocked_seat_data}
    )

    # Mock das dependências
    mock_passagens_emitidas = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.passagens_emitidas", return_value=True
    )
    mock_emitir_pax = mocker.patch("core.service.reserva.rodoviaria_reserva_svc._emitir_pax")
    mock_set_emissao_staff = mocker.patch.object(Passageiro, "set_emissao_staff")

    buyer_cpf = "98765432100"

    # Act
    emitir_passagem_staff(travel.id, buseiro.id, buyer_cpf)

    # Assert
    mock_passagens_emitidas.assert_not_called()  # Não deve ser chamado pois pax.foi_emitido é False por padrão
    mock_emitir_pax.assert_called_once_with(pax, user, reserva, 180, buyer_cpf)
    mock_set_emissao_staff.assert_called_once()


def test_emitir_passagem_staff_sucesso_sem_poltrona(mocker):
    """Testa emissão staff quando passageiro não tem poltrona (precisa bloquear)."""
    user = baker.make(User)
    buseiro = baker.make(Buseiro, user=user, cpf="12345678901")
    reserva = baker.make(Reserva)
    trecho_classe = baker.make(TrechoClasse, grupo__modelo_venda=ModeloVenda.MARKETPLACE)
    travel = baker.make(Travel, reserva=reserva, trecho_classe=trecho_classe)

    pax = baker.make(Passageiro, travel=travel, buseiro=buseiro, poltrona=None, extra={})

    blocked_seat = BlockedSeat(
        poltrona=Assento(numero=20, x=2, y=1, tipo_assento="convencional"),
        tempo_limite_bloqueio=now() + timedelta(hours=24),
        external_payload={"blocked": "seat"},
    )

    mock_passagens_emitidas = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.passagens_emitidas", return_value=True
    )
    mocker.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe")
    mock_bloqueia_poltronas = mocker.patch.object(
        MarketplaceSeatsController, "escolhe_e_bloqueia_poltronas", return_value=[blocked_seat]
    )

    mocker.patch.object(Reserva, "categoria_especial_info", return_value=("idoso", None))

    mock_emitir_pax = mocker.patch("core.service.reserva.rodoviaria_reserva_svc._emitir_pax")
    mock_set_emissao_staff = mocker.patch.object(Passageiro, "set_emissao_staff")

    buyer_cpf = "98765432100"

    emitir_passagem_staff(travel.id, buseiro.id, buyer_cpf)

    mock_passagens_emitidas.assert_not_called()  # Não deve ser chamado pois pax.foi_emitido é False por padrão
    mock_bloqueia_poltronas.assert_called_once_with(1, "idoso")

    pax.refresh_from_db()
    assert pax.poltrona == 20

    assert pax.extra["bloqueio_poltrona"] == json.loads(json.dumps(blocked_seat.model_dump()))

    mock_emitir_pax.assert_called_once_with(pax, user, reserva, 180, buyer_cpf)
    mock_set_emissao_staff.assert_called_once()


def test_emitir_passagem_staff_passagem_cancelada_na_rodoviaria_mas_ativa_no_buser(mocker):
    """Testa cenário onde passagem foi emitida no buser mas está cancelada na rodoviária."""
    # Arrange
    user = baker.make(User)
    buseiro = baker.make(Buseiro, user=user, cpf="12345678901")
    reserva = baker.make(Reserva)
    trecho_classe = baker.make(TrechoClasse)
    travel = baker.make(Travel, reserva=reserva, trecho_classe=trecho_classe)

    # Passageiro já tem poltrona e foi emitido (foi_emitido=True)
    blocked_seat_data = {
        "poltrona": {"numero": 15, "x": 1, "y": 1, "tipo_assento": "convencional"},
        "tempo_limite_bloqueio": (now() + timedelta(hours=24)).isoformat(),
        "external_payload": {"test": "data"},
    }

    pax = baker.make(
        Passageiro,
        travel=travel,
        buseiro=buseiro,
        poltrona=15,
        extra={
            "bloqueio_poltrona": blocked_seat_data,
            "emissao": {"sucesso": True},  # Marca como emitido no buser
        },
    )

    # Mock das dependências
    # passagens_emitidas retorna False, indicando que a passagem não existe mais na rodoviária
    mock_passagens_emitidas = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.passagens_emitidas", return_value=False
    )
    mock_set_cancelamento = mocker.patch.object(Passageiro, "set_cancelamento_emissao_marketplace")
    mock_emitir_pax = mocker.patch("core.service.reserva.rodoviaria_reserva_svc._emitir_pax")
    mock_set_emissao_staff = mocker.patch.object(Passageiro, "set_emissao_staff")

    buyer_cpf = "98765432100"

    # Act
    emitir_passagem_staff(travel.id, buseiro.id, buyer_cpf)

    # Assert
    mock_passagens_emitidas.assert_called_once_with([pax.travel])
    mock_set_cancelamento.assert_called_once()  # Deve marcar como cancelado
    mock_emitir_pax.assert_called_once_with(pax, user, reserva, 180, buyer_cpf)
    mock_set_emissao_staff.assert_called_once()


def test_emitir_passagem_staff_erro_reserva_nao_encontrada():
    """Testa erro quando travel não tem reserva."""
    user = baker.make(User)
    buseiro = baker.make(Buseiro, user=user)
    trecho_classe = baker.make(TrechoClasse)
    travel = baker.make(Travel, reserva=None, trecho_classe=trecho_classe)  # Sem reserva

    baker.make(Passageiro, travel=travel, buseiro=buseiro)

    buyer_cpf = "98765432100"

    with pytest.raises(Exception, match="Reserva não encontrada"):
        emitir_passagem_staff(travel.id, buseiro.id, buyer_cpf)


def test_emitir_passagem_staff_erro_passageiro_nao_encontrado():
    """Testa erro quando passageiro não é encontrado."""
    buyer_cpf = "98765432100"

    with pytest.raises(Passageiro.DoesNotExist):
        emitir_passagem_staff(99999, 99999, buyer_cpf)


def test_emitir_passagem_staff_erro_bloqueio_poltrona(mocker):
    """Testa erro durante bloqueio de poltrona."""
    user = baker.make(User)
    buseiro = baker.make(Buseiro, user=user)
    reserva = baker.make(Reserva)
    trecho_classe = baker.make(TrechoClasse, grupo__modelo_venda=ModeloVenda.MARKETPLACE)
    travel = baker.make(Travel, reserva=reserva, trecho_classe=trecho_classe)

    baker.make(Passageiro, travel=travel, buseiro=buseiro, poltrona=None, extra={})

    mock_passagens_emitidas = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.passagens_emitidas", return_value=True
    )
    mocker.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe")
    mocker.patch.object(
        MarketplaceSeatsController, "escolhe_e_bloqueia_poltronas", side_effect=Exception("Erro no bloqueio")
    )
    mocker.patch.object(Reserva, "categoria_especial_info", return_value=(None, None))

    buyer_cpf = "98765432100"

    with pytest.raises(Exception, match="Erro no bloqueio"):
        emitir_passagem_staff(travel.id, buseiro.id, buyer_cpf)

    mock_passagens_emitidas.assert_not_called()  # Não deve ser chamado pois pax.foi_emitido é False por padrão


def test_emitir_passagem_staff_erro_emissao(mocker):
    """Testa erro durante a emissão da passagem."""
    user = baker.make(User)
    buseiro = baker.make(Buseiro, user=user)
    reserva = baker.make(Reserva)
    trecho_classe = baker.make(TrechoClasse)
    travel = baker.make(Travel, reserva=reserva, trecho_classe=trecho_classe)

    blocked_seat_data = {
        "poltrona": {"numero": 15, "x": 1, "y": 1, "tipo_assento": "convencional"},
        "tempo_limite_bloqueio": (now() + timedelta(hours=24)).isoformat(),
        "external_payload": {"test": "data"},
    }

    baker.make(Passageiro, travel=travel, buseiro=buseiro, poltrona=15, extra={"bloqueio_poltrona": blocked_seat_data})

    mock_passagens_emitidas = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.passagens_emitidas", return_value=True
    )
    mock_emitir_pax = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc._emitir_pax", side_effect=RodoviariaException("Erro na emissão")
    )

    buyer_cpf = "98765432100"

    with pytest.raises(RodoviariaException, match="Erro na emissão"):
        emitir_passagem_staff(travel.id, buseiro.id, buyer_cpf)

    mock_passagens_emitidas.assert_not_called()  # Não deve ser chamado pois pax.foi_emitido é False por padrão
    mock_emitir_pax.assert_called_once()
