from datetime import timedelta

import pytest
from django.core.management import call_command
from model_bakery import baker

from commons.dateutils import now
from core.models_driving import AlertaSeguranca


@pytest.fixture
def group_with_bus_and_route():
    origem = baker.make("core.LocalEmbarque", cidade__sigla="SP")
    destino = baker.make("core.LocalEmbarque", cidade__sigla="SJK")
    rota = baker.make("core.Rota", origem=origem, destino=destino, duracao_total=timedelta(hours=1))
    baker.make("core.Checkpoint", idx=0, rota=rota, local=origem, _fill_optional=["duracao"])
    baker.make("core.Checkpoint", idx=1, rota=rota, local=destino, _fill_optional=["duracao"])
    return baker.make(
        "core.Grupo",
        rota=rota,
        datetime_ida=now() - timedelta(hours=2),
        status="travel_confirmed",
        _fill_optional=["onibus"],
    )


@pytest.mark.parametrize(
    "alarm_type",
    [
        "USO_DE_CELULAR",
        "CINTO_DE_SEGURANCA",
        "PERDA_DE_VIDEO",
        "LENTE_COBERTA",
        "COLISAO_COM_PEDESTRE",
        "COLISAO_FRONTAL",
        "FREADA_BRUSCA",
    ],
)
def test_process_smartcam_indicators(group_with_bus_and_route, alarm_type):
    baker.make("core.TrechoClasse", grupo=group_with_bus_and_route, pessoas=10)
    # Porque grupos sem pax nao geram alertas
    baker.make(
        "core.SmartCamAlarm",
        gps_time=now(),
        type=alarm_type,
        speed=70 * 10,
        plate=group_with_bus_and_route.onibus.placa,
    )

    assert AlertaSeguranca.objects.count() == 0
    call_command("processa_indicadores_smartcam")
    assert AlertaSeguranca.objects.count() == 1


@pytest.mark.parametrize("alarm_type", ["DISTRACAO", "COLISAO_FRONTAL", "BOCEJO", "OLHO_FECHADO"])
def test_process_smartcam_indicator_more_than_one_alarm(group_with_bus_and_route, alarm_type):
    baker.make("core.TrechoClasse", grupo=group_with_bus_and_route, pessoas=10)
    # Porque grupos sem pax nao geram alertas

    baker.make(
        "core.SmartCamAlarm",
        gps_time=now() - timedelta(minutes=2),
        type=alarm_type,
        speed=70 * 10,
        plate=group_with_bus_and_route.onibus.placa,
    )
    baker.make(
        "core.SmartCamAlarm",
        gps_time=now() - timedelta(minutes=1),
        type=alarm_type,
        speed=70 * 10,
    )
    baker.make(
        "core.SmartCamAlarm",
        gps_time=now(),
        type=alarm_type,
        speed=70 * 10,
        plate=group_with_bus_and_route.onibus.placa,
    )

    assert AlertaSeguranca.objects.count() == 0
    call_command("processa_indicadores_smartcam")
    assert AlertaSeguranca.objects.count() == 1
