import json
from unittest import mock

import pytest
import responses
from django.conf import settings
from model_bakery import baker

from commons.utils import hashint
from core import views, views_staff, views_staff_remanejamento
from core.forms.staff_forms import (
    AutorizacaoForm,
    CreateRodoviariaCompanyForm,
    CriarRotaHibridoForm,
    ListaEmpresasAPIParams,
    ListRodoviariaCompaniesForm,
    RotaHibridoForm,
    TotalbusCreateLoginForm,
    UpdateLinkLocalForm,
)
from core.models_travel import Travel
from core.service import rodoviaria_svc
from core.tests import fixtures
from integrations.rodoviaria_client import RodoviariaClient
from integrations.rodoviaria_client.exceptions import (
    RodoviariaClientError,
    RodoviariaConnectionException,
    RodoviariaLoginNotFoundException,
    RodoviariaMoverBuseiroException,
    RodoviariaUnauthorizedError,
)
from www import views_reserva


@pytest.fixture
def user_rotas():
    return fixtures.user_rotas()


@pytest.fixture
def user_revendedor():
    return fixtures.user_revendedor()


def test_view_remover_pax_da_lista(rf, user_staff, trechoclasse_mock, passageiro_mock):
    with mock.patch("core.service.rodoviaria_svc.remover_pax_da_lista", return_value={"sucesso": True}):
        request_body = {
            "grupoId": trechoclasse_mock.grupo_id,
            "buseiroId": passageiro_mock.buseiro.id,
            "travelId": passageiro_mock.travel.id,
        }
        req = rf.post(
            "api/staff/integracao_rodoviaria/removepassenger",
            request_body,
            content_type="application/json",
        )
        req.user = user_staff
        resp = views_staff.remove_pax_da_lista_rodoviaria(req)
        content = json.loads(resp.content)
        assert content["sucesso"] is True


def test_view_staff_remaneja_passageiro_async_overbooking(
    globalsettings_mock, rf, user_staff, travels_mock, trechoclasse_mock
):
    globalsettings_mock("remanejamento_async_rodoviaria", True)
    travel = travels_mock[0]
    with mock.patch("core.service.rodoviaria_svc.simula_mover_buseiro_marketplace") as mock_simular_rodoviaria:
        mock_simular_rodoviaria.side_effect = RodoviariaMoverBuseiroException("Sem vagas disponíveis na API")
        params = {
            "travel_ids": [travel.id],
            "trecho_classe_id": trechoclasse_mock.id,
            "motivo": "Teste marketplace",
        }
        assert Travel.objects.count() == 2
        req = rf.post("/api/staff/groups/mover_buseiros", {"params": json.dumps(params)})
        req.user = user_staff
        resp = views_staff_remanejamento.mover_buseiros(req)
        content = json.loads(resp.content)
        assert Travel.objects.count() == 2
        assert resp.status_code == 400
        assert "Sem vagas disponíveis na API" in content["error"]


def test_view_staff_mover_buseiro_async(
    globalsettings_mock, rf, mocker, user_staff, passageiro_mock, travels_mock, trechoclasse_mock
):
    globalsettings_mock("remanejamento_async_rodoviaria", True)
    travel = travels_mock[0]
    mock_remanejamento_async = mocker.patch("core.service.reserva.rodoviaria_reserva_svc.remanejamento_async")
    mocker.patch("core.service.remanejamento.remanejamento_svc.RemanejamentoBuseiro._cria_evento_contabil")
    mocker.patch("accounting.service.accounting_svc.cria_registros_contabeis")
    mocker.patch("core.service.rodoviaria_svc.simula_mover_buseiro_marketplace")
    mocker.patch("core.service.rodoviaria_svc.remaneja_passageiros_hibrido_batch")
    mock_efetua_cancelamento_rodoviaria = mocker.patch("core.service.rodoviaria_svc.efetua_cancelamento")
    params = {
        "travel_ids": [travel.id],
        "trecho_classe_id": trechoclasse_mock.id,
        "motivo": "Teste marketplace",
    }
    assert Travel.objects.count() == 2
    req = rf.post("/api/staff/groups/mover_buseiros", {"params": json.dumps(params)})
    req.user = user_staff
    resp = views_staff_remanejamento.mover_buseiros(req)
    assert resp.status_code == 200
    mock_remanejamento_async.assert_called_once_with(travel.trecho_classe, trechoclasse_mock, mock.ANY)
    mock_efetua_cancelamento_rodoviaria.assert_not_called()


def test_view_staff_remaneja_passageiro_async_connection_error(
    globalsettings_mock, rf, user_staff, travels_mock, trechoclasse_mock
):
    globalsettings_mock("remanejamento_async_rodoviaria", True)
    travel = travels_mock[0]
    with mock.patch("core.service.rodoviaria_svc.simula_mover_buseiro_marketplace") as mock_simular_rodoviaria:
        mock_simular_rodoviaria.side_effect = RodoviariaConnectionException
        params = {
            "travel_ids": [travel.id],
            "trecho_classe_id": trechoclasse_mock.id,
            "motivo": "Teste marketplace",
        }
        assert Travel.objects.count() == 2
        req = rf.post("/api/staff/groups/mover_buseiros", {"params": json.dumps(params)})
        req.user = user_staff
        resp = views_staff_remanejamento.mover_buseiros(req)
        content = json.loads(resp.content)
        assert Travel.objects.count() == 2
        assert resp.status_code == 400
        assert "Erro na conexão com a empresa parceira. (connection_error)" in content["error"]


def test_view_staff_mover_buseiro_sync(rf, mocker, user_staff, passageiro_mock, travels_mock, trechoclasse_mock):
    travel = travels_mock[0]
    mocker.patch("core.service.rodoviaria_svc.simula_mover_buseiro_marketplace")
    mocker.patch("core.service.rodoviaria_svc.remaneja_passageiros_hibrido_batch")
    mock_efetua_cancelamento_rodoviaria = mocker.patch("core.service.rodoviaria_svc.efetua_cancelamento")
    with (
        mock.patch(
            "core.service.reserva.rodoviaria_reserva_svc.remaneja_passageiro",
        ) as mock_remaneja_passageiro,
        mock.patch("core.service.remanejamento.commons.remanejamento_base.RemanejamentoBuseiro._cria_evento_contabil"),
        mock.patch("accounting.service.accounting_svc.cria_registros_contabeis"),
    ):
        mock_remaneja_passageiro.return_value = {"internal_relocation_permission": True}
        params = {
            "travel_ids": [travel.id],
            "trecho_classe_id": trechoclasse_mock.id,
            "motivo": "Teste marketplace",
        }
        assert Travel.objects.count() == 2
        req = rf.post("/api/staff/groups/mover_buseiros", {"params": json.dumps(params)})
        req.user = user_staff
        resp = views_staff_remanejamento.mover_buseiros(req)
        assert resp.status_code == 200
    mock_efetua_cancelamento_rodoviaria.assert_called_once()


def test_view_staff_remaneja_passageiro_sync_connection_error(rf, mocker, user_staff, travels_mock, trechoclasse_mock):
    mocker.patch("core.service.rodoviaria_svc.simula_mover_buseiro_marketplace")
    mocker.patch("core.service.rodoviaria_svc.remaneja_passageiros_hibrido_batch")
    travel = travels_mock[0]
    with mock.patch("core.service.reserva.rodoviaria_reserva_svc.remaneja_passageiro") as mock_remaneja_passageiro:
        mock_remaneja_passageiro.return_value = {
            "reservation_code": travel.reservation_code,
            "external_purchase_success": False,
            "external_cancelation_success": False,
            "external_error_reason": "Reserva PARKER não movida, por perda de conexão com a rodoviária",
            "is_trechoclasse_origem_integrado": True,
            "is_trechoclasse_destino_integrado": True,
            "internal_relocation_permission": False,
            "overbooking_on_purchase": False,
            "connection_error_on_purchase": True,
            "blocked_travel_on_purchase": False,
        }
        params = {
            "travel_ids": [travel.id],
            "trecho_classe_id": trechoclasse_mock.id,
            "motivo": "Teste marketplace",
        }
        req = rf.post("/api/staff/groups/mover_buseiros", {"params": json.dumps(params)})
        req.user = user_staff
        resp = views_staff_remanejamento.mover_buseiros(req)
        content = json.loads(resp.content)
        assert resp.status_code == 400
        assert "Perda de conexão" in content["error"]


def test_view_staff_remaneja_passageiro_sync_viagem_bloqueada(rf, mocker, user_staff, travels_mock, trechoclasse_mock):
    mocker.patch("core.service.rodoviaria_svc.simula_mover_buseiro_marketplace")
    mocker.patch("core.service.rodoviaria_svc.remaneja_passageiros_hibrido_batch")
    travel = travels_mock[0]
    with mock.patch("core.service.reserva.rodoviaria_reserva_svc.remaneja_passageiro") as mock_remaneja_passageiro:
        mock_remaneja_passageiro.return_value = {
            "reservation_code": travel.reservation_code,
            "external_purchase_success": False,
            "external_cancelation_success": False,
            "external_error_reason": "Reserva PARKER não movida, por perda de conexão com a rodoviária",
            "is_trechoclasse_origem_integrado": True,
            "is_trechoclasse_destino_integrado": True,
            "internal_relocation_permission": False,
            "overbooking_on_purchase": False,
            "connection_error_on_purchase": False,
            "blocked_travel_on_purchase": True,
        }
        params = {
            "travel_ids": [travel.id],
            "trecho_classe_id": trechoclasse_mock.id,
            "motivo": "Teste marketplace",
        }
        req = rf.post("/api/staff/groups/mover_buseiros", {"params": json.dumps(params)})
        req.user = user_staff
        resp = views_staff_remanejamento.mover_buseiros(req)
        content = json.loads(resp.content)
        assert resp.status_code == 400
        assert "Bloqueio do parceiro" in content["error"]


def test_has_bpe(rf, user_staff):
    travels = baker.make("core.Travel", reservation_code="ABACAT", _quantity=3)
    with mock.patch("core.service.rodoviaria_svc.has_bpe") as mock_has_bpe:
        mock_has_bpe.return_value = {"has_bpe": False}
        req = rf.post("api/reserva/hasbpe/ABACAT", content_type="application/json")
        req.user = user_staff
        views.has_bpe(req, "ABACAT")
    mock_has_bpe.assert_called_once_with(travels[-1])


def test_cadastrar_grupos_hibridos_rodoviaria_params(rf, user_rotas):
    req = rf.get(
        "api/staff/integracao_rodoviaria/cadastrar-grupos-hibridos-params", {"company_id": 231, "rota_id": 8392}
    )
    req.user = user_rotas
    with mock.patch("core.service.rodoviaria_svc.cadastrar_grupos_hibridos_rodoviaria_params") as infos_mock:
        infos_mock.return_value = {"teste": True}
        response = views_staff.cadastrar_grupos_hibridos_rodoviaria_params(req)
    assert json.loads(response.content) == {"teste": True}
    infos_mock.assert_called_once_with("231", "8392")


def test_cadastrar_grupos_hibridos_rodoviaria(rf, user_rotas):
    json_data = {"company_id": "321", "grupos_ids": "[4323, 4231]", "rota_id": "542"}
    req = rf.post("api/staff/integracao_rodoviaria/cadastrar-grupos-hibridos", json_data)
    req.user = user_rotas
    with mock.patch("core.service.rodoviaria_svc.cadastrar_grupos_hibridos_rodoviaria") as criar_grupos_hibridos_mock:
        criar_grupos_hibridos_mock.return_value = None
        response = views_staff.cadastrar_grupos_hibridos_rodoviaria(req)
    assert json.loads(response.content) == {}
    criar_grupos_hibridos_mock.assert_called_once_with(321, [4323, 4231])


def test_abrir_trechos(rf, user_rotas):
    json_data = {
        "grupos_ids": "[4323, 4231]",
    }
    req = rf.post("api/staff/integracao_rodoviaria/abrir-trechos", json_data)
    req.user = user_rotas
    with mock.patch("core.service.rodoviaria_svc.abrir_trechos") as mock_criar_trechos:
        response = views_staff.abrir_trechos(req)
    assert json.loads(response.content) == {}
    mock_criar_trechos.assert_called_once_with([4323, 4231])


def test_cadastrar_grupos_hibridos_rodoviaria_rota_nao_cadastrada(rf, user_rotas):
    json_data = {"company_id": "321", "grupos_ids": "[4323, 4231]", "rota_id": "542"}
    req = rf.post("api/staff/integracao_rodoviaria/cadastrar-grupos-hibridos", json_data)
    req.user = user_rotas
    with mock.patch("core.service.rodoviaria_svc.cadastrar_grupos_hibridos_rodoviaria") as criar_grupos_hibridos_mock:
        criar_grupos_hibridos_mock.side_effect = rodoviaria_svc.RotaNaoCadastradaRodoviaria(542, 321)
        response = views_staff.cadastrar_grupos_hibridos_rodoviaria(req)
    assert response.status_code == 412
    assert json.loads(response.content) == {
        "error": "rota 542 nao cadastrada no rodoviaria para empresa 321",
        "type": "rota_nao_cadastrada",
        "rota_id": 542,
        "company_id": 321,
    }
    criar_grupos_hibridos_mock.assert_called_once_with(321, [4323, 4231])


def test_criar_itinerario_hibrido(rf, user_rotas):
    json_data = {"company_id": "321", "grupo_id": "4323", "rota_id": "542"}
    req = rf.post("api/staff/integracao_rodoviaria/criar-itinerario-hibrido", json_data)
    req.user = user_rotas
    with mock.patch("core.service.rodoviaria_svc.criar_itinerario_hibrido") as criar_itinerario_hibrido_mock:
        criar_itinerario_hibrido_mock.return_value = None
        response = views_staff.criar_itinerario_hibrido(req)
    assert json.loads(response.content) == {}
    criar_itinerario_hibrido_mock.assert_called_once_with(RotaHibridoForm.parse_obj(json_data))


def test_atualizar_embarques_hibrido(rf, user_rotas):
    json_data = {"company_id": "321", "grupo_id": "4323", "rota_id": "542"}
    req = rf.post("api/staff/integracao_rodoviaria/atualizar-embarques-hibrido", json_data)
    req.user = user_rotas
    with mock.patch("core.service.rodoviaria_svc.atualizar_embarques_hibrido") as atualizar_embarques_hibrido:
        atualizar_embarques_hibrido.return_value = None
        response = views_staff.atualizar_embarques_hibrido(req)
    assert json.loads(response.content) == {}
    atualizar_embarques_hibrido.assert_called_once_with(RotaHibridoForm.parse_obj(json_data))


def test_criar_rota_hibrido(rf, user_rotas):
    json_data = {
        "company_id": "321",
        "grupo_id": "4323",
        "cidade_origem_id": 1,
        "cidade_destino_id": 1,
        "prefixo": "19-25",
    }
    with mock.patch("core.service.rodoviaria_svc.criar_rota_hibrido", return_value={}) as mock_criar_rota_hibrido:
        req = rf.post(
            "/api/staff/integracao_rodoviaria/criar-rota-hibrido",
            json_data,
        )
        req.user = user_rotas
        views_staff.criar_rota_hibrido(req)
    mock_criar_rota_hibrido.assert_called_once_with(CriarRotaHibridoForm.parse_obj(json_data))


def test_sincronizar_rota(rf, user_rotas):
    json_data = {
        "grupo_id": "4323",
    }
    with mock.patch(
        "core.service.rodoviaria_svc.sincronizar_rota_hibrido_por_grupo", return_value={}
    ) as mock_sincronizar_rota_hibrido:
        req = rf.post(
            "/api/staff/integracao_rodoviaria/sincronizar-rota-hibrido",
            json_data,
        )
        req.user = user_rotas
        views_staff.sincronizar_rota_hibrido(req)
    mock_sincronizar_rota_hibrido.assert_called_once_with(grupo_id=4323)


def test_sincronizar_rota_error(rf, user_rotas):
    json_data = {
        "grupo_id": "4323",
    }
    with mock.patch(
        "core.service.rodoviaria_svc.sincronizar_rota_hibrido_por_grupo",
        return_value={"error": "Erro ao Sincronizar Rota"},
    ) as mock_sincronizar_rota_hibrido:
        req = rf.post(
            "/api/staff/integracao_rodoviaria/sincronizar-rota-hibrido",
            json_data,
        )
        req.user = user_rotas
        response = views_staff.sincronizar_rota_hibrido(req)
    mock_sincronizar_rota_hibrido.assert_called_once_with(grupo_id=4323)
    assert response.status_code == 422


def test_alterar_autorizacao_grupo_hibrido(rf, user_rotas):
    autorizacao_json = {"id": None, "cidade_origem_id": 1, "cidade_destino_id": 2, "prefixo": "19-25"}
    json_data = {"grupo_id": "4323", "autorizacao": json.dumps(autorizacao_json), "rota_rodoviaria_id": "null"}
    with mock.patch(
        "core.service.autorizacao_hibrido_svc.alterar_autorizacao_grupo_hibrido", return_value={}
    ) as mock_alterar_autorizacao_grupo_hibrido:
        req = rf.post("/api/staff/hibrido/alterar-autorizacao-hibrido", json_data)
        req.user = user_rotas
        views_staff.alterar_autorizacao_grupo_hibrido(req)
    mock_alterar_autorizacao_grupo_hibrido.assert_called_once_with(4323, AutorizacaoForm.parse_obj(autorizacao_json))


def test_cancelar_bpes(rf, user_rotas):
    grupo = baker.make("core.Grupo")
    with mock.patch("core.service.rodoviaria_svc.cancelar_bpes", return_value={}) as mock_cancelar_bpes:
        req = rf.post(
            "/api/staff/integracao_rodoviaria/cancelar-bpes",
            {"grupo_ids": [grupo.id]},
        )
        req.user = user_rotas
        views_staff.cancelar_bpes(req)
    mock_cancelar_bpes.assert_called_once_with([str(grupo.id)])


def test_cadastrar_grupos_hibridos_rodoviaria_error(rf, user_rotas):
    json_data = {"company_id": "321", "grupos_ids": "[4323, 4231]", "rota_id": "542"}
    req = rf.post("api/staff/integracao_rodoviaria/cadastrar-grupos-hibridos", json_data)
    req.user = user_rotas
    with mock.patch("core.service.rodoviaria_svc.cadastrar_grupos_hibridos_rodoviaria") as criar_grupos_hibridos_mock:
        criar_grupos_hibridos_mock.return_value = {"error": "Erro vindo da API Rodoviaria"}
        response = views_staff.cadastrar_grupos_hibridos_rodoviaria(req)
    assert response.status_code == 422
    assert json.loads(response.content) == {"error": "Erro vindo da API Rodoviaria"}


def test_update_links_locais_embarque(rf, user_staff):
    params = {"link_id": 1872, "local_embarque_buser_id": 1872, "cidade_embarque_buser_id": 1872}
    with mock.patch("core.service.rodoviaria_svc.update_link_local_embarque") as mock_update_link_local_embarque:
        mock_update_link_local_embarque.return_value = True
        request = rf.post("api/staff/links_locais/update_link", {"link": json.dumps(params)})
        request.user = user_staff
        resp = views_staff.update_links_locais_embarque(request)
    mock_update_link_local_embarque.assert_called_once_with(UpdateLinkLocalForm.parse_raw(request.POST.get("link")))
    assert resp.status_code == 200
    content = json.loads(resp.content)
    assert content


def test_pos_salvar_rota(rf, user_staff):
    json_data = {
        "params": {"rota": {"rodoviaria_rota_id": 100, "id_internal": 120}, "trechos_vendidos": {"12": 100, "15": 120}}
    }
    req = rf.post("api/staff/integracao_rodoviaria/pos_salvar_rota", json_data, content_type="application/json")
    req.user = user_staff
    with mock.patch("core.service.rodoviaria_svc.pos_salvar_rota") as pos_salvar_rota_mock:
        pos_salvar_rota_mock.return_value = {"teste": True}
        response = views_staff.pos_salvar_rota(req)
    assert json.loads(response.content) == {}
    pos_salvar_rota_mock.assert_called_once_with(json_data["params"])


def test_get_classes_e_precos_rota(rf, user_staff):
    req = rf.get("api/staff/integracao_rodoviaria/classes_e_precos_rota?rota_id=99")
    req.user = user_staff
    with mock.patch("core.service.rodoviaria_svc.get_classes_e_precos_rota") as get_classes_e_precos_rota_mock:
        get_classes_e_precos_rota_mock.return_value = {"teste": True}
        response = views_staff.get_classes_e_precos_rota(req)
    assert json.loads(response.content) == {"teste": True}
    get_classes_e_precos_rota_mock.assert_called_once_with(99)


def test_list_companies_rodoviaria(rf, user_staff):
    params = {
        "paginator": {"rowsPerPage": 10, "sortBy": "pk", "descending": True, "page": 1},
        "name": "",
        "status": "",
        "modelo_venda": "",
    }

    req = rf.get(f"api/staff/companies-rodoviaria?params={json.dumps(params)}")
    req.user = user_staff
    expected_response = {
        "nome": "Basilio Tur",
        "company_internal_id": 44,
        "modelo_venda": "marketplace",
        "integracao": "vexado",
        "features": "[itinerario,buscar_servico,add_pax_staff,ignora_classe,bpe,active]",
    }
    with mock.patch("core.service.rodoviaria_svc.list_empresas_paginator") as mock_list_empresas_paginator:
        mock_list_empresas_paginator.return_value = expected_response
        response = views_staff.list_companies_rodoviaria(req)
    form = ListRodoviariaCompaniesForm.parse_raw(json.dumps(params))
    mock_list_empresas_paginator.assert_called_once_with(
        name=form.name,
        status=form.status,
        modelo_venda=form.modelo_venda,
        rows_per_page=form.paginator.rows_per_page,
        page=form.paginator.page,
        order_by=form.paginator.order_by[0],
    )
    assert response.status_code == 200
    assert json.loads(response.content) == expected_response


def test_list_all_possible_company_features(rf, user_staff):
    req = rf.get("api/staff/features")
    req.user = user_staff
    expected_resp = {
        "features": [
            "itinerario",
            "buscar_servico",
            "add_pax_staff",
            "bpe",
            "motorista",
            "atualizar_preco",
            "ignora_classe",
            "active",
            "escalar_veiculos",
        ]
    }
    with mock.patch(
        "core.service.rodoviaria_svc.list_all_possible_empresa_features"
    ) as mock_list_all_possible_features:
        mock_list_all_possible_features.return_value = expected_resp
        response = views_staff.list_all_possible_company_features(req)

    assert response.status_code == 200
    assert json.loads(response.content) == expected_resp


def test_create_rodoviaria_company(rf, user_staff):
    totalbus_login = {
        "user": "admin",
        "password": "senha",
        "tenantId": "ffffffff-ffff-ffff-ffff-ffffffffffff",
        "idFormaPagamento": 1,
        "formaPagamento": "BUSER",
        "validarMulta": True,
    }
    req_body = {
        "name": "empresa ltda",
        "company_internal_id": 42,
        "modelo_venda": "marketplace",
        "features": ["active"],
        "login": totalbus_login,
        "max_percentual_divergencia": 45,
        "integracao": "totalbus",
    }
    form = CreateRodoviariaCompanyForm.parse_raw(json.dumps(req_body))
    request = rf.post("api/staff/create_rodoviaria_company", req_body, content_type="application/json")
    request.user = user_staff

    with mock.patch("core.service.rodoviaria_svc.create_empresa") as mock_create_empresa:
        mock_create_empresa.return_value = {"success": True}
        response = views_staff.create_rodoviaria_company(request)

        mock_create_empresa.assert_called_once_with(
            name=form.name,
            company_internal_id=form.company_internal_id,
            modelo_venda=form.modelo_venda,
            features=form.features,
            login=dict(form.login),
            max_percentual_divergencia=form.max_percentual_divergencia,
            integracao=form.integracao,
        )

    assert response.status_code == 200
    assert json.loads(response.content) == {"success": True}


def test_get_rodoviaria_company_login(rf, user_staff):
    integracao = "totalbus"
    company_id = 13
    modelo_venda = "marketplace"
    params = {"integracao": integracao, "company_id": company_id, "modelo_venda": modelo_venda}
    request = rf.get("api/staff/rodoviaria_company_login", {"params": json.dumps(params)})
    request.user = user_staff
    expected_result = {"login": {}}

    with mock.patch("core.service.rodoviaria_svc.get_empresa_login") as mock_get_empresa_login:
        mock_get_empresa_login.return_value = expected_result
        response = views_staff.get_rodoviaria_company_login(request)
    assert response.status_code == 200
    assert json.loads(response.content) == expected_result


def test_get_rodoviaria_company_login_error_400(rf, user_staff):
    integracao = "totalbus"
    company_id = 13
    modelo_venda = "marketplace"
    params = {"integracao": integracao, "company_id": company_id, "modelo_venda": modelo_venda}
    request = rf.get("api/staff/rodoviaria_company_login", {"params": json.dumps(params)})
    request.user = user_staff
    error_msg = "Login não encontrado"
    error_type = "(desconhecido)"

    with mock.patch("core.service.rodoviaria_svc.get_empresa_login") as mock_get_empresa_login:
        mock_get_empresa_login.side_effect = RodoviariaLoginNotFoundException(message=error_msg)
        response = views_staff.get_rodoviaria_company_login(request)

    assert response.status_code == 400
    assert json.loads(response.content) == {"error": f"{error_msg} {error_type}"}


def test_create_rodoviaria_company_hibrido(rf, user_staff):
    req_body = {"name": "empresa ltda", "company_internal_id": 43, "modelo_venda": "hibrido", "integracao": "totalbus"}
    form = CreateRodoviariaCompanyForm.parse_raw(json.dumps(req_body))
    request = rf.post("api/staff/create_rodoviaria_company", req_body, content_type="application/json")
    request.user = user_staff

    with mock.patch("core.service.rodoviaria_svc.create_empresa") as mock_create_empresa:
        mock_create_empresa.return_value = {"success": True}
        response = views_staff.create_rodoviaria_company(request)

        mock_create_empresa.assert_called_once_with(
            name=form.name,
            company_internal_id=form.company_internal_id,
            modelo_venda=form.modelo_venda,
            features=None,
            login=None,
            max_percentual_divergencia=None,
            integracao=form.integracao,
        )
        assert response.status_code == 200
        assert json.loads(response.content) == {"success": True}


def test_fetch_rodoviaria_locais(rf, user_staff):
    request = rf.get(
        "api/staff/integracao_rodoviaria/locais/fetch",
        {"company_rodoviaria_id": 18},
    )
    request.user = user_staff
    expected_resp = {"count": 5}

    with mock.patch(
        "core.service.rodoviaria_svc.fetch_locais_empresa", return_value=expected_resp
    ) as mock_fetch_locais_empresa:
        response = views_staff.fetch_rodoviaria_locais(request)
    mock_fetch_locais_empresa.assert_called_once_with(company_rodoviaria_id=18)
    assert response.status_code == 200
    assert json.loads(response.content) == expected_resp


def test_get_rodoviaria_locais_retirada(rf, user_staff):
    request = rf.get(
        "api/staff/integracao_rodoviaria/locais_retirada",
        {"company_internal_id": 18, "company_rodoviaria_id": 3, "associado_rota": "true", "linkado_buser": "false"},
    )
    request.user = user_staff
    expected_resp = {
        "cidade": "Belo Horizonte",
        "local_embarque": "Lagoa dos Patos # BH",
        "description": "Embarque no km 3 da Lagoa dos Patos",
    }

    with mock.patch(
        "core.service.rodoviaria_svc.get_locais_retirada_empresa", return_value=expected_resp
    ) as mock_get_locais_retirada_empresa:
        views_staff.get_rodoviaria_locais_retirada(request)
    mock_get_locais_retirada_empresa.assert_called_once_with(18, 3, "true", "false")


def test_add_pax_na_lista_rodoviaria_success(rf, mocker, user_staff):
    """Testa o sucesso da função add_pax_na_lista_rodoviaria quando emitir_passagem_staff funciona."""
    trechoclasse_id = 123
    travel_id = 456
    buseiro_id = 789
    buyer_cpf = "*********01"
    grupo_id = 999

    request_data = {
        "trechoclasseId": trechoclasse_id,
        "travelId": travel_id,
        "valorPorBuseiro": 50.0,
        "passenger": {
            "buseiroId": buseiro_id,
            "name": "João Silva",
            "cpf": "*********01",
            "rgNumber": "*********",
            "phone": "11999999999",
            "buyer_cpf": buyer_cpf,
        },
        "idOrigem": 1,
        "idDestino": 2,
        "poltrona": 15,
    }

    request = rf.post(
        "api/staff/add_pax_na_lista_rodoviaria", json.dumps(request_data), content_type="application/json"
    )
    request.user = user_staff

    expected_passengers_list = [{"buseiro_id": buseiro_id, "name": "João Silva", "poltrona": 15}]
    mock_emitir = mocker.patch("core.service.reserva.rodoviaria_reserva_svc.emitir_passagem_staff")
    mock_filter = mocker.patch("core.models_travel.TrechoClasse.objects.filter")
    mock_filter.return_value.values_list.return_value = [grupo_id]
    mock_lista = mocker.patch(
        "core.service.rodoviaria_svc.lista_passageiros_viagem", return_value=expected_passengers_list
    )

    response = views_staff.add_pax_na_lista_rodoviaria(request)

    mock_emitir.assert_called_once_with(travel_id, buseiro_id, buyer_cpf, 15)
    mock_filter.assert_called_once_with(id=trechoclasse_id)
    mock_lista.assert_called_once_with(grupo_id)

    assert json.loads(response.content) == expected_passengers_list


def test_add_pax_na_lista_rodoviaria_fallback_on_exception(rf, mocker, user_staff):
    """Testa o fallback da função add_pax_na_lista_rodoviaria quando emitir_passagem_staff falha."""
    trechoclasse_id = 123
    travel_id = 456
    buseiro_id = 789
    buyer_cpf = "*********01"

    request_data = {
        "trechoclasseId": trechoclasse_id,
        "travelId": travel_id,
        "valorPorBuseiro": 50.0,
        "passenger": {
            "buseiroId": buseiro_id,
            "name": "João Silva",
            "cpf": "*********01",
            "rgNumber": "*********",
            "phone": "11999999999",
            "buyer_cpf": buyer_cpf,
        },
        "idOrigem": 1,
        "idDestino": 2,
        "poltrona": 15,
    }

    request = rf.post(
        "api/staff/add_pax_na_lista_rodoviaria", json.dumps(request_data), content_type="application/json"
    )
    request.user = user_staff

    expected_fallback_response = {"success": True, "message": "Passageiro adicionado via fallback"}

    mock_emitir = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.emitir_passagem_staff", side_effect=Exception("Erro na emissão")
    )
    mock_add_pax = mocker.patch("core.service.rodoviaria_svc.add_pax_na_lista", return_value=expected_fallback_response)
    mock_logger = mocker.patch("core.views_staff.buserlogger")

    response = views_staff.add_pax_na_lista_rodoviaria(request)

    mock_emitir.assert_called_once_with(travel_id, buseiro_id, buyer_cpf, 15)

    expected_converted_data = {
        "trechoclasse_id": trechoclasse_id,
        "travel_id": travel_id,
        "valor_por_buseiro": 50.0,
        "passenger": {
            "buseiro_id": buseiro_id,
            "name": "João Silva",
            "cpf": "*********01",
            "rg_number": "*********",
            "phone": "11999999999",
            "buyer_cpf": buyer_cpf,
        },
        "id_origem": 1,
        "id_destino": 2,
        "poltrona": 15,
    }
    mock_add_pax.assert_called_once_with(expected_converted_data)

    mock_logger.error.assert_called_once()

    assert response.status_code == 200
    assert json.loads(response.content) == expected_fallback_response


def test_fetch_formas_pagamento_totalbus(rf, user_staff):
    login = {"user": "user_test", "password": "pass_test", "tenantId": "9e48bc2b-2707-4b79-85fd-6cf34253d8c5"}
    params = {"integracao": "totalbus", "login": login}
    request = rf.post("api/staff/integracao_rodoviaria/locais/fetch", params, content_type="application/json")
    request.user = user_staff
    expected_resp = [{"id": 2, "descricao": "CRÉDITO"}, {"id": 12, "descricao": "FATURADO BUSER", "tipoPago": 11}]

    with mock.patch(
        "core.service.rodoviaria_svc.fetch_formas_pagamento", return_value={"formas_pagamento": expected_resp}
    ):
        response = views_staff.fetch_rodoviaria_formas_pagamento(request)

    assert response.status_code == 200
    assert json.loads(response.content) == {"formas_pagamento": expected_resp}


def test_fetch_formas_pagamento_smartbus(rf, user_staff):
    login = {"username": "buser", "password": "buser", "cliente": "buser"}
    params = {"integracao": "smartbus", "login": login}
    request = rf.post("api/staff/integracao_rodoviaria/fetch_formas_pagamento", params, content_type="application/json")
    request.user = user_staff
    expected_resp = [{"id": 2, "descricao": "CRÉDITO"}, {"id": 12, "descricao": "FATURADO BUSER"}]

    with mock.patch(
        "core.service.rodoviaria_svc.fetch_formas_pagamento", return_value={"formas_pagamento": expected_resp}
    ):
        response = views_staff.fetch_rodoviaria_formas_pagamento(request)

    assert response.status_code == 200
    assert json.loads(response.content) == {"formas_pagamento": expected_resp}


def test_fetch_rodoviaria_trechos_vendidos_por_rota_fluxo(rf, user_staff):
    rodoviaria_rota_id = 18
    request = rf.get(
        "api/staff/integracao_rodoviaria/fetch_trechos_vendidos_por_rota", {"rodoviaria_rota_id": rodoviaria_rota_id}
    )
    request.user = user_staff
    task_info = {
        "task_id": "34558cd9-95f7-44da-b853-d4a9b457536e",
        "name": "rodoviaria.service.fetch_trechos_vendidos_svc.fetch_trecho_vendido_task",
        "queue": "bp_trechos_vendidos",
        "is_group_task": True,
        "status": "NOT_STARTED",
        "date_done": None,
        "mensagem": f"Fetch de trechos vendidos da rota {rodoviaria_rota_id}",
    }
    responses.get(f"{settings.RODOVIARIA_API_URL}/v1/fetch_trechos_vendidos_uma_rota", json=task_info)

    response = views_staff.fetch_rodoviaria_trechos_vendidos_por_rota(request)

    assert response.status_code == 200
    assert json.loads(response.content) == {"task_info": task_info}


def test_fetch_rodoviaria_trechos_vendidos_por_rota_ja_em_execucao(rf, user_staff):
    rodoviaria_rota_id = 18
    request = rf.get(
        "api/staff/integracao_rodoviaria/fetch_trechos_vendidos_por_rota", {"rodoviaria_rota_id": rodoviaria_rota_id}
    )
    request.user = user_staff
    with mock.patch(
        "core.service.rodoviaria_svc.fetch_trechos_vendidos_por_rota"
    ) as mock_fetch_trechos_vendidos_por_rota:
        mock_fetch_trechos_vendidos_por_rota.side_effect = RodoviariaClientError("Task já em execução")
        responses.get(f"{settings.RODOVIARIA_API_URL}/v1/fetch_trechos_vendidos_uma_rota")
        response = views_staff.fetch_rodoviaria_trechos_vendidos_por_rota(request)

    assert response.status_code == 400
    assert json.loads(response.content)["error"] != ""


def test_verify_praxio_login_fluxo(rf, user_staff):
    req_body = {"name": "empresa_fantasma ltda", "password": "le_senhe", "cliente": "empresa_vr"}
    api_resp = {
        "id_sessao_op": "9EC144F1C974A9D141004DC76ACE83A98kFZldUbRvOPE",
        "id_estabelecimento": 601,
        "new_login": True,
    }
    request = rf.post("api/staff/integracao_rodoviaria/verify_praxio_login", req_body, content_type="application/json")
    request.user = user_staff
    responses.post(f"{settings.RODOVIARIA_API_URL}/v1/login/praxio", json=api_resp)

    response = views_staff.verify_praxio_login(request)

    assert response.status_code == 200
    assert json.loads(response.content) == {"success": True}


def test_get_auth_key_ti_sistemas(rf, user_staff):
    request = rf.get("api/staff/integracao_rodoviaria/get_auth_key_ti_sistemas", content_type="application/json")
    request.user = user_staff

    with mock.patch.object(RodoviariaClient, "get_auth_key_ti_sistemas", return_value={"auth_key": "abc123"}):
        response = views_staff.get_auth_key_ti_sistemas(request)

    assert response.status_code == 200
    assert json.loads(response.content) == {"auth_key": "abc123"}


def test_lista_empresas_api_vexado(rf, user_staff):
    req_body = {
        "params": json.dumps(
            {
                "login_params": {
                    "modelo_venda": "marketplace",
                }
            }
        )
    }
    api_resp = {
        "external_companies": [
            {"name": "Empresa 1", "id": 1},
            {"name": "Empresa 2", "id": 2},
        ]
    }
    request = rf.post("api/staff/integracao_rodoviaria/lista_empresas_api", req_body)
    request.user = user_staff
    with mock.patch(
        "core.service.rodoviaria_svc.lista_empresas_api", return_value=api_resp
    ) as mock_list_empresas_vexado:
        response = views_staff.lista_empresas_api(request)

    assert response.status_code == 200
    mock_list_empresas_vexado.assert_called_once_with(ListaEmpresasAPIParams.parse_raw(req_body["params"]))
    assert json.loads(response.content) == api_resp


def test_lista_empresas_api_totalbus(rf, user_staff):
    req_body = {
        "params": json.dumps({"login_params": {"user": "usuario", "password": "senha", "tenantId": "tenant-id"}})
    }
    api_resp = {
        "external_companies": [
            {"name": "Empresa 1", "id": 1},
            {"name": "Empresa 2", "id": 2},
        ]
    }
    request = rf.post("api/staff/integracao_rodoviaria/lista_empresas_api", req_body)
    request.user = user_staff
    with mock.patch(
        "core.service.rodoviaria_svc.lista_empresas_api", return_value=api_resp
    ) as mock_list_empresas_vexado:
        response = views_staff.lista_empresas_api(request)

    assert response.status_code == 200
    mock_list_empresas_vexado.assert_called_once_with(ListaEmpresasAPIParams.parse_raw(req_body["params"]))
    assert json.loads(response.content) == api_resp


def test_verify_praxio_login_error_fluxo(rf, user_staff):
    req_body = {"name": "empresa_fantasma ltda", "password": "le_senhe", "cliente": "empresa_vr"}
    api_resp = {"error": f"{RodoviariaUnauthorizedError.message}"}
    request = rf.post("api/staff/integracao_rodoviaria/verify_praxio_login", req_body, content_type="application/json")
    request.user = user_staff
    responses.post(f"{settings.RODOVIARIA_API_URL}/v1/login/praxio", json=api_resp, status=401)

    response = views_staff.verify_praxio_login(request)

    assert response.status_code == 400
    assert json.loads(response.content) == {"error": RodoviariaUnauthorizedError.message}


def test_get_empresas_integradas_rodoviaria(rf, user_staff, globalsettings_mock):
    globalsettings_mock("rodoviaria_prod", True)
    api_resp = {"empresas": [{"company_internal_id": 123, "modelo_venda": "marketplace", "company_external_id": 5321}]}
    request = rf.get("api/staff/integracao_rodoviaria/get-empresas-integradas-rodoviaria")
    request.user = user_staff
    responses.get(f"{settings.RODOVIARIA_API_URL}/v1/empresas", json=api_resp, status=200)

    response = views_staff.get_empresas_integradas_rodoviaria(request)

    assert response.status_code == 200
    assert json.loads(response.content) == {"empresas": [{"company_id": 123, "modelo_venda": "marketplace"}]}


def test_get_empresas_integradas_rodoviaria_error(rf, user_staff, globalsettings_mock):
    globalsettings_mock("rodoviaria_prod", True)
    request = rf.get("api/staff/integracao_rodoviaria/get-empresas-integradas")
    request.user = user_staff
    responses.get(f"{settings.RODOVIARIA_API_URL}/v1/empresas", status=500)

    response = views_staff.get_empresas_integradas_rodoviaria(request)

    assert response.status_code == 200
    assert json.loads(response.content) == {"empresas": []}


def test_get_rotinas_integradas_rodoviaria(rf, user_staff, globalsettings_mock):
    globalsettings_mock("rodoviaria_prod", True)
    api_resp_rotinas = {"313": [123, 321]}
    api_resp_empresas = {"empresas": [{"company_internal_id": 313, "modelo_venda": "hibrido"}]}
    request = rf.get("api/staff/integracao_rodoviaria/get-rotinas-integradas")
    request.user = user_staff
    responses.get(
        f"{settings.RODOVIARIA_API_URL}/rodoviaria/v1/map_rotinas_integradas", json=api_resp_rotinas, status=200
    )
    responses.get(f"{settings.RODOVIARIA_API_URL}/rodoviaria/v1/empresas", json=api_resp_empresas, status=200)

    response = views_staff.get_rotinas_integradas_rodoviaria(request)

    assert response.status_code == 200
    assert json.loads(response.content) == {"rotinas": [123, 321]}


def test_get_rotinas_integradas_rodoviaria_error_rotinas(rf, user_staff, globalsettings_mock):
    globalsettings_mock("rodoviaria_prod", True)
    request = rf.get("api/staff/integracao_rodoviaria/get-rotinas-integradas")
    request.user = user_staff
    responses.get(f"{settings.RODOVIARIA_API_URL}/rodoviaria/v1/map_rotinas_integradas", status=500)

    response = views_staff.get_rotinas_integradas_rodoviaria(request)

    assert response.status_code == 200
    assert json.loads(response.content) == {"rotinas": []}


def test_get_rotinas_integradas_rodoviaria_error_empresas(rf, user_staff, globalsettings_mock):
    globalsettings_mock("rodoviaria_prod", True)
    request = rf.get("api/staff/integracao_rodoviaria/get-empresas-integradas")
    request.user = user_staff
    api_resp_rotinas = {"313": [123, 321]}
    responses.get(
        f"{settings.RODOVIARIA_API_URL}/rodoviaria/v1/map_rotinas_integradas", json=api_resp_rotinas, status=200
    )
    responses.get(f"{settings.RODOVIARIA_API_URL}/v1/empresas", status=500)

    response = views_staff.get_rotinas_integradas_rodoviaria(request)

    assert response.status_code == 200
    assert json.loads(response.content) == {"rotinas": []}


def test_get_itinerarios_marketplace(rf, user_staff):
    request = rf.get("api/staff/integracao_rodoviaria/itinerarios_marketplace", {"company_rodoviaria_id": 123})
    request.user = user_staff
    with mock.patch(
        "core.service.rodoviaria_svc.get_itinerarios_marketplace", return_value={}
    ) as mock_get_itinerarios_marketplace:
        response = views_staff.get_itinerarios_marketplace(request)
    mock_get_itinerarios_marketplace.assert_called_once_with(123, "{}")
    assert response.status_code == 200


def test_get_itinerarios_marketplace_error(rf, user_staff):
    request = rf.get("api/staff/integracao_rodoviaria/itinerarios_marketplace", {"company_rodoviaria_id": 123})
    request.user = user_staff
    with mock.patch(
        "core.service.rodoviaria_svc.get_itinerarios_marketplace", side_effect=RodoviariaClientError("Improcessável")
    ) as mock_get_itinerarios_marketplace:
        response = views_staff.get_itinerarios_marketplace(request)
    mock_get_itinerarios_marketplace.assert_called_once_with(123, "{}")
    assert response.status_code == 422


def test_list_integracao_rodoviaria(rf, user_staff):
    request = rf.get("api/staff/integracao_rodoviaria/integracao/list")
    request.user = user_staff
    api_resp = {"integracoes": [{"name": "TotalBus", "id": 1}]}
    responses.get(f"{settings.RODOVIARIA_API_URL}/v1/integracoes", json=api_resp, status=200)
    response = views_staff.list_integracoes_rodoviaria(request)
    assert response.status_code == 200
    assert json.loads(response.content) == {"integracoes": [{"name": "TotalBus", "id": 1}]}


def test_get_integracao_empresa_rodoviaria(rf, user_staff, globalsettings_mock):
    globalsettings_mock("rodoviaria_prod", True)
    company_id = 24
    request = rf.get(f"api/staff/integracao_rodoviaria/empresas/{company_id}")
    request.user = user_staff
    api_resp = {"company_internal_id": company_id, "integracao": {"name": "Vexato", "id": 12}, "active": True}
    responses.get(f"{settings.RODOVIARIA_API_URL}/rodoviaria/v1/empresas", json=api_resp, status=200)

    response = views_staff.get_integracao_empresa_rodoviaria(request, company_id)

    assert response.status_code == 200
    assert json.loads(response.content) == {
        "active": True,
        "company_id": 24,
        "nome_integracao": "Vexato",
        "id_integracao": 12,
    }


def test_get_integracao_empresa_rodoviaria_sem_company(rf, user_staff, globalsettings_mock):
    company_id = 24
    globalsettings_mock("rodoviaria_prod", True)
    request = rf.get(f"api/staff/integracao_rodoviaria/empresas/{company_id}")
    request.user = user_staff
    api_resp = {"error": "Essa empresa não contém integração mapeada"}
    responses.get(f"{settings.RODOVIARIA_API_URL}/rodoviaria/v1/empresas", json=api_resp, status=404)

    response = views_staff.get_integracao_empresa_rodoviaria(request, company_id)

    assert response.status_code == 404
    assert json.loads(response.content) == {"message": "Essa empresa não contém integração marketplace mapeada"}


def test_normalizacao_CreateRodoviariaCompanyForm_integracao_existente():
    totalbus_login = {
        "user": "admin",
        "password": "senha",
        "tenantId": "ffffffff-ffff-ffff-ffff-ffffffffffff",
        "idFormaPagamento": 1,
        "formaPagamento": "BUSER",
        "validarMulta": True,
    }
    req_body = {
        "name": "Clarinha ltda",
        "company_internal_id": 42,
        "modelo_venda": "marketplace",
        "features": ["active"],
        "login": totalbus_login,
        "max_percentual_divergencia": 45,
        "integracao": "ToTaLbUS",
    }
    form = CreateRodoviariaCompanyForm.parse_raw(json.dumps(req_body))
    assert form.integracao == "totalbus"
    assert type(form.login) is TotalbusCreateLoginForm


def test_get_rodoviaria_atualizacao_passagem_api_parceiro_view_success_path(
    rf,
    user_staff,
):
    request = rf.get(
        "/api/staff/integracao_rodoviaria/get_rodoviaria_atualizacao_passagem_api_parceiro",
        {
            "buseiro_id": 1,
            "modelo_venda": "marketplace",
            "travel_id": 2,
        },
    )
    request.user = user_staff
    with mock.patch(
        "core.views_staff.rodoviaria_svc.get_informacoes_passagem_api_parceiro",
        return_value=[
            {
                "integracao": "GuichePass",
                "numero_passagem": "",
                "sale_id": "",
                "reserva_id": "",
                "localizador": "1VZ6CD",
                "status": "CONFIRMED",
                "numero_assento": "13",
                "primeiro_nome_pax": "Clarinha",
                "ultimo_nome_pax": "",
                "tipo_documento": "",
                "numero_documento": "34773213086",
                "birth": "",
                "bpe_id": "11920",
                "bpe_public_url": "",
                "bpe_authorization": "",
                "bpe_description": "",
                "data_partida": "2023-07-24T06:00:00",
                "data_chegada": "2023-07-24T21:30:00",
                "origem": "RECIFE",
                "Estado_origem": "",
                "volta_id": "",
                "volta_code": "",
                "destino": "UBERLANDIA",
                "Estado_destino": "",
                "duracao": None,
                "empresa_name": "",
                "valor_passagem": "82.52",
                "taxa_de_cancelamento": "0.0",
                "taxa_embarque": "3.32",
                "servico": "790",
                "paradas": "False",
            }
        ],
    ) as get_informacoes_passagem_api_parceiro_mock:
        response = views_staff.get_rodoviaria_atualizacao_passagem_api_parceiro(request)

        get_informacoes_passagem_api_parceiro_mock.assert_called_once_with(1, "marketplace", 2)
        assert response.status_code == 200


def test_get_rodoviaria_atualizacao_passagem_api_parceiro_view_when_rodoviaria_is_not_implemented(
    rf,
    user_staff,
):
    request = rf.get(
        "/api/staff/integracao_rodoviaria/get_rodoviaria_atualizacao_passagem_api_parceiro",
        {
            "buseiro_id": 1,
            "modelo_venda": "marketplace",
            "travel_id": 2,
        },
    )
    request.user = user_staff
    with mock.patch(
        "core.views_staff.rodoviaria_svc.get_informacoes_passagem_api_parceiro",
        side_effect=rodoviaria_svc.ServiceNotImplementedInRodoviaria,
    ):
        response = views_staff.get_rodoviaria_atualizacao_passagem_api_parceiro(request)
        assert response.status_code == 501


def test_get_informacoes_passagem_api_parceiro_svc_success_path(responses):
    return_value = [
        {
            "integracao": "GuichePass",
            "numero_passagem": "",
            "sale_id": "",
            "reserva_id": "",
            "localizador": "1VZ6CD",
            "status": "CONFIRMED",
            "numero_assento": "13",
            "primeiro_nome_pax": "Clarinha",
            "ultimo_nome_pax": "",
            "tipo_documento": "",
            "numero_documento": "34773213086",
            "birth": "",
            "bpe_id": "11920",
            "bpe_public_url": "",
            "bpe_description": "",
            "data_partida": "2023-07-24T06:00:00",
            "data_chegada": "2023-07-24T21:30:00",
            "origem": "RECIFE",
            "estado_origem": "",
            "volta_id": "",
            "destino": "UBERLANDIA",
            "Estado_destino": "",
            "duracao": None,
            "empresa_name": "",
            "valor_passagem": "82.52",
            "taxa_de_cancelamento": "0.0",
            "taxa_embarque": "3.32",
            "paradas": "False",
        }
    ]
    responses.get(
        "http://buserpassagens.service.consul/rodoviaria/v1/get-atualizacao-passagem-api-parceiro",
        json=return_value,
        status=200,
    )
    result = rodoviaria_svc.get_informacoes_passagem_api_parceiro(1, "marketplace", 2)
    assert result == return_value


def test_get_informacoes_passagem_api_parceiro_svc_not_found(responses):
    responses.get(
        "http://buserpassagens.service.consul/rodoviaria/v1/get-atualizacao-passagem-api-parceiro",
        json={},
        status=404,
    )
    result = rodoviaria_svc.get_informacoes_passagem_api_parceiro(42, "marketplace", 12)
    assert result == []


def test_get_informacoes_passagem_api_parceiro_svc_not_implemented_error(responses):
    responses.get(
        "http://buserpassagens.service.consul/rodoviaria/v1/get-atualizacao-passagem-api-parceiro",
        json={"error": "Método de consultar informações na API desse parceiro ainda não foi implementada"},
        status=501,
    )
    with pytest.raises(rodoviaria_svc.ServiceNotImplementedInRodoviaria):
        rodoviaria_svc.get_informacoes_passagem_api_parceiro(42, "marketplace", 12)


def test_get_informacoes_passagem_api_parceiro_svc_integration_error(responses):
    responses.get(
        "http://buserpassagens.service.consul/rodoviaria/v1/get-atualizacao-passagem-api-parceiro",
        json={},
        status=503,
    )
    with pytest.raises(rodoviaria_svc.IntegrationErrorRodoviaria):
        rodoviaria_svc.get_informacoes_passagem_api_parceiro(42, "marketplace", 12)


def test_get_cronograma_atualizacao_rodoviaria(rf, responses, user_staff):
    responses.get(
        "http://buserpassagens.service.consul/rodoviaria/v1/get-cronograma-atualizazao-operacao",
        json=[{"empresa": {"name": "EMPRESA"}}],
        status=200,
    )
    params = {
        "paginator": {"rowsPerPage": 10, "sortBy": "pk", "descending": True, "page": 1},
        "empresa_busca": "",
        "integracao_id": None,
    }
    req = rf.get(f"api/staff/integracao_rodoviaria/cronograma_atualizacao/list?params={json.dumps(params)}")
    req.user = user_staff
    resp = views_staff.get_cronograma_atualizacao_rodoviaria(req)
    content = json.loads(resp.content)
    assert content == [{"empresa": {"name": "EMPRESA"}}]


def test_update_cronograma_atualizacao_rodoviaria(rf, responses, user_staff):
    responses.post(
        "http://buserpassagens.service.consul/rodoviaria/v1/update-cronograma-atualizazao-operacao",
        json=[{"empresa": {"name": "EMPRESA"}}],
        status=200,
    )
    request_body = {
        "params": json.dumps([{"company_id": 1, "horario": "11:00"}]),
    }
    req = rf.post("api/staff/integracao_rodoviaria/cronograma_atualizacao/update", request_body)
    req.user = user_staff
    resp = views_staff.update_cronograma_atualizacao_rodoviaria(req)
    content = json.loads(resp.content)
    assert content == [{"empresa": {"name": "EMPRESA"}}]


def test_get_vagas_por_categoria_especial_marketplace(rf, responses, user_revendedor, mocker):
    responses.get(
        "http://buserpassagens.service.consul/rodoviaria/v1/get_vagas_por_categoria_especial",
        json={"categorias_especiais": [{"nome": "idoso 100%", "vagas": 2, "id": "idoso_100"}]},
        status=200,
    )
    baker.make("core.TrechoClasse", id=3129123, grupo=baker.make("core.Grupo", company=baker.make("core.Company")))
    mocker.patch("core.service.rodoviaria_svc.get_empresa_info", return_value={"features": ["active"]})

    req = rf.get("api/integracao_rodoviaria/get-vagas-por-categoria-especial", {"trecho_classe_id": hashint(3129123)})
    req.user = user_revendedor
    resp = views_reserva.get_vagas_por_categoria_especial_marketplace(req)

    content = json.loads(resp.content)
    assert content == {"categorias_especiais": [{"nome": "idoso 100%", "vagas": 2, "id": "idoso_100"}]}


def test_get_vagas_por_categoria_especial_marketplace_not_found(rf, responses, user_revendedor, mocker):
    responses.get(
        "http://buserpassagens.service.consul/rodoviaria/v1/get_vagas_por_categoria_especial",
        json={"error": "Serviço não encontrado na API"},
        status=404,
    )
    tc = baker.make("core.TrechoClasse", id=3129123, grupo=baker.make("core.Grupo", company=baker.make("core.Company")))
    mock_get_empresa = mocker.patch(
        "core.service.rodoviaria_svc.get_empresa_info", return_value={"features": ["active"]}
    )

    req = rf.get("api/integracao_rodoviaria/get-vagas-por-categoria-especial", {"trecho_classe_id": hashint(3129123)})
    req.user = user_revendedor
    resp = views_reserva.get_vagas_por_categoria_especial_marketplace(req)
    content = json.loads(resp.content)

    assert resp.status_code == 400
    assert content == {"error": "Essa viagem não está mais disponível (service_not_found)"}
    mock_get_empresa.assert_called_once_with(tc.grupo.company_id)


def test_get_vagas_por_categoria_especial_marketplace_error(rf, responses, user_revendedor, mocker):
    responses.get(
        "http://buserpassagens.service.consul/rodoviaria/v1/get_vagas_por_categoria_especial",
        json={"error": "Erro ao buscar categorias"},
        status=500,
    )
    baker.make("core.TrechoClasse", id=3129123, grupo=baker.make("core.Grupo", company=baker.make("core.Company")))
    mocker.patch("core.service.rodoviaria_svc.get_empresa_info", return_value={"features": ["active"]})

    req = rf.get("api/integracao_rodoviaria/get-vagas-por-categoria-especial", {"trecho_classe_id": hashint(3129123)})
    req.user = user_revendedor
    resp = views_reserva.get_vagas_por_categoria_especial_marketplace(req)
    content = json.loads(resp.content)

    assert resp.status_code == 500
    assert content == {"error": "Não foi possível buscar categoria especiais disponíveis (desconhecido)"}
