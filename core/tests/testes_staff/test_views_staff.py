import json
from datetime import date, datetime, time, timedelta, timezone
from decimal import Decimal
from http import HTTPStatus
from unittest import mock
from uuid import uuid4
from zoneinfo import ZoneInfo

import pytest
import time_machine
from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import SimpleUploadedFile
from django.shortcuts import resolve_url
from django.utils.timezone import get_default_timezone
from model_bakery import baker

from commons.dateutils import now, to_default_tz_required
from commons.enum import ModeloVenda
from commons.guard import assign_role, remove_role
from core import views_staff
from core.models_commons import ActivityLog, AsyncTask
from core.models_grupo import (
    ClosedReasons,
    EventoExtra,
    EventoExtraNegociacao,
    EventoExtraSolicitacao,
    EventoExtraSolicitacaoPerna,
    Grupo,
)
from core.models_rota import DuracaoDinamica
from core.service.log_svc import log_reduzir_frete
from core.views_staff import (
    alterar_duracoes,
    alterar_rota_de_grupos_async,
    async_task_detail,
    atualizar_permissao_taxa_servico_checkout,
    contabilizar_fidelidade,
    edit_rota_async,
    encerrar_viagem,
    escalar_empresa_onibus_rotina_async,
    fechar_trechos,
    forecast_e_gmv_previsto_grupos,
    geocode,
    get_closed_reasons,
    get_group_details,
    get_itinerario_dinamico,
    get_passengers,
    get_rodoviaria_atualizacao_passagem_api_parceiro,
    get_rodoviaria_passagem_info,
    hard_stop_empresa_rodoviaria,
    houve_reducao_de_frete,
    importar_leads,
    list_group_notifications_pigeon,
    multas_qualidade_torre,
    removepassengers,
    replace_local_for_selected_routes_async,
    revert_hard_stop_empresa_rodoviaria,
    save_taxa_servico_referencia_empresa_marketplace,
    simular_bulk_escalar_onibus_async,
)
from integrations.rodoviaria_client.exceptions import RodoviariaException
from promo.models import ProgressoFidelidade, ProgressoFidelidadeTravel
from promo.tests.test_contabiliza_programa_fidelidade import programa_fidelidade  # noqa: F401


@pytest.fixture
def evento_extra_payload():
    return {
        "nome": "Natal 2025",
        "dataInicial": date(2025, 12, 24),
        "dataFinal": date(2025, 12, 27),
        "status": "pending",
    }


@pytest.fixture
def rota_principal():
    return baker.make("core.rotaprincipal", eixo="SAO-RIO")


@pytest.fixture
def evento_extra():
    return baker.make("core.EventoExtra", nome="Natal")


@pytest.fixture
def evento_extra_solicitacao(evento_extra):
    return baker.make("core.EventoExtraSolicitacao", evento_extra_id=evento_extra.id)


@pytest.fixture
def solicitacao_extra_payload(evento_extra, rota_principal):
    return {
        "id": None,
        "regional": EventoExtraSolicitacao.Regional.SP,
        "eventoExtraId": evento_extra.id,
        "prioridade": EventoExtraSolicitacao.Prioridade.ALTA,
        "breakevenEsperado": 0.66,
        "ticketMedioEstimado": 120,
        "tiposAssento": ["semi leito", "executivo"],
        "rotaPrincipal": {"id": rota_principal.id, "eixo": rota_principal.eixo},
        "rotaPrevista": "SAO-RIO",
        "createdAt": "2023-04-15 10:30:00",
        "is_fechado_rotas": False,
        "is_fechado_comercial": False,
        "has_precificacao_inicial": False,
        "has_precificacao_final": False,
        "has_empresa_escalada": False,
        "is_criado_staff": False,
        "has_contrato_assinado": False,
    }


@pytest.fixture
def negociacao_extra_payload(user_rotas, evento_extra, evento_extra_solicitacao):
    onibus = baker.make("core.Onibus", placa="ABC1235")
    return {
        "eventoExtraId": evento_extra.id,
        "solicitacaoId": evento_extra_solicitacao.id,
        "gerenteComercial": {"id": user_rotas.id, "name": "Stafiana da Silva"},
        "company": {"id": None, "name": ""},
        "kmPerna": 430,
        "kmTotal": 860,
        "deslocamento": 50,
        "freteTotal": "8500.00",
        "freteKm": "19.77",
        "cask": "0.43",
        "ticketMedio": "89.90",
        "breakeven": "75",
        "resultadoMax": "12345.00",
        "tiposAssento": ["semi leito", "executivo"],
        "capacidade": 46,
        "onibus": {"id": onibus.id, "placa": onibus.placa},
        "is_fechado_comercial": False,
        "has_empresa_escalada": False,
        "has_contrato_assinado": False,
    }


@pytest.fixture
def solicitacao_perna_payload(evento_extra_solicitacao):
    return {
        "solicitacaoExtraId": evento_extra_solicitacao.id,
        "sentido": "SAO-RIO",
        "rota": {"id": None, "sigla": ""},
        "turno": "tarde",
        "data": "2023-05-01",
        "hora": "14:00",
    }


@pytest.fixture
def trechos_vendidos(db):
    def factory(rota):
        c1, c2, c3 = baker.make("core.Cidade", _quantity=3)
        l1, l2, l3 = (
            baker.make("core.LocalEmbarque", cidade=c1),
            baker.make("core.LocalEmbarque", cidade=c2),
            baker.make("core.LocalEmbarque", cidade=c3),
        )

        # itinerario
        baker.make("core.Checkpoint", idx=0, local_id=l1.id, rota_id=rota.id)
        baker.make("core.Checkpoint", idx=1, local_id=l2.id, rota_id=rota.id, distancia_km=200)
        baker.make("core.Checkpoint", idx=2, local_id=l3.id, rota_id=rota.id, distancia_km=400)
        tv1 = baker.make("core.TrechoVendido", origem=l1, destino=l2, rota=rota)
        tv2 = baker.make("core.TrechoVendido", origem=l2, destino=l3, rota=rota)
        tv3 = baker.make("core.TrechoVendido", origem=l1, destino=l3, rota=rota)
        return tv1, tv2, tv3

    return factory


@pytest.fixture
def trecho_classe():
    datetime_ida = datetime(2023, 10, 19, 23, 55, 59, 342380, tzinfo=timezone.utc)
    return baker.make("core.TrechoClasse", datetime_ida=datetime_ida)


def test_get_group_details_invalid_hashed_group_id(rf, user_staff, mocker):
    data = {"group_id": "d3GLLSl"}

    with pytest.raises(ValidationError) as excinfo:
        request = rf.get("/api/staff/efops/groups/group", {"params": json.dumps(data)})
        request.user = user_staff
        get_group_details(request)

    assert excinfo.value.message == "ID do grupo inválido"


def test_get_group_details_no_group_query_found(rf, user_staff, mocker):
    mock = mocker.patch("core.service.grupos_staff.grupo_crud_svc.get_group_details")
    mock.side_effect = ValidationError(message="Grupo matching query does not exist")

    data = {"group_id": "2052492"}

    with pytest.raises(ValidationError) as excinfo:
        request = rf.get("/api/staff/efops/groups/group", {"params": json.dumps(data)})
        request.user = user_staff
        get_group_details(request)

    assert excinfo.value.message == "Grupo matching query does not exist"


def test_get_group_details_with_locadora(rf, user_staff, grupo_rota_parcial, onibus_leito):
    onibus_leito.locadora = True
    onibus_leito.save(update_fields=["locadora"])
    grupo_rota_parcial.onibus = onibus_leito
    grupo_rota_parcial.save()
    data = {"group_id": grupo_rota_parcial.id}
    request = rf.get("/api/staff/efops/groups/group", {"params": json.dumps(data)})
    request.user = user_staff
    response = get_group_details(request)
    assert response.status_code == HTTPStatus.OK
    group = json.loads(response.content.decode("utf-8"))
    assert group["onibus_locadora"] is True


@mock.patch("core.service.notifications.user_notification_svc._send_travel_notifications")
def test_get_group_details_with_incident_grade_change(mock_n, rf, user_staff, mocker, grupo_rota_parcial):
    mock = mocker.patch("adapters.new_incidents_adapter.group_seat_types")
    mock.return_value = {"group_seat_type": "leito", "incidents_seat_types": [["executivo", 1], ["van", 2]]}
    data = {"group_id": grupo_rota_parcial.id}
    request = rf.get("/api/staff/efops/groups/group", {"params": json.dumps(data)})
    request.user = user_staff
    response = get_group_details(request)
    assert response.status_code == HTTPStatus.OK
    group = json.loads(response.content.decode("utf-8"))
    assert group["incident_grade_change"]["downgrades"][0] == "Downgrade de 1 passageiros de leito para executivo"
    assert group["incident_grade_change"]["downgrades"][1] == "Downgrade de 2 passageiros de leito para van"


def test_get_passengers(rf, user_staff, grupo_rota_parcial):
    grupo = grupo_rota_parcial
    trecho_classe = baker.make("core.TrechoClasse", datetime_ida=to_default_tz_required(now()))
    travel = baker.make("core.Travel", trecho_classe=trecho_classe, grupo=grupo, user=user_staff)
    passenger = baker.make("core.Passageiro", travel_id=travel.id)
    lista_itinerario = list(grupo.rota.itinerario.all())

    locais_itinerario = [f"{i.local.cidade.name} ({i.local.nickname})" for i in lista_itinerario]
    origem = locais_itinerario[0]
    destino = locais_itinerario[-1]
    embarques_expected = [local for local in locais_itinerario if local != destino]
    desembarques_expected = [local for local in locais_itinerario if local != origem]

    request = rf.get("api/staff/get_passengers", {"grupo_id": grupo.id})
    request.user = user_staff
    response = get_passengers(request)
    resp_data = json.loads(response.content)
    assert response.status_code == HTTPStatus.OK
    assert resp_data["possiveis_embarques"] == embarques_expected
    assert resp_data["possiveis_desembarques"] == desembarques_expected
    assert resp_data["passengers"][0]["pid"] == passenger.id
    assert resp_data["passengers"][0]["travel_id"] == travel.id


def test_list_group_notifications_pigeon_must_be_staff_user(rf, user_factory, user_staff, mocker):
    # Faz o request com um usuário que não é staff e dá errado
    grupo_id = 123
    request = rf.get(f"/api/staff/groups/{grupo_id}/notification/list", {})
    request.user = user_factory(profile=True)
    response = list_group_notifications_pigeon(request, grupo_id)
    response_dict = json.loads(response.content)

    assert response.status_code == HTTPStatus.FORBIDDEN
    assert response_dict["error"] == "Acesso proibido."

    # Faz o request com um usuário staff e dá certo
    request.user = user_staff
    response = list_group_notifications_pigeon(request, grupo_id)
    response_dict = json.loads(response.content)

    assert response.status_code == HTTPStatus.OK
    assert response_dict == {}


@pytest.mark.parametrize(
    "paginator", [{}, {"lastId": "125"}, {"rowsPerPage": 150}, {"lastId": "12515", "rowsPerPage": 20, "prevPage": True}]
)
def test_list_group_notifications_service_call(rf, mocker, user_staff, paginator, time_machine):
    time_machine.move_to("2022-06-01 21:00:00")
    grupo_id = 123
    date_interval = {
        "fromDatetime": (now() - timedelta(days=1)).isoformat(),
        "toDatetime": (now() + timedelta(days=1)).isoformat(),
    }
    request = rf.get(
        f"/api/staff/groups/{grupo_id}/notification/list", {"paginator": json.dumps(paginator), **date_interval}
    )
    request.user = user_staff
    response = list_group_notifications_pigeon(request, grupo_id)
    response.content = b"{}"


def test_hard_stop_empresa_rodoviaria(rf, mocker, user_staff):
    request = rf.post(
        "/api/staff/integracao_rodoviaria/hard_stop_empresa",
        {"company_internal_id": 1, "mensagem": "Aracy da Toptherm"},
    )
    request.user = user_staff
    mocker.patch("core.service.rodoviaria_svc.hard_stop_empresa", return_value=0)
    response = hard_stop_empresa_rodoviaria(request)
    response_dict = json.loads(response.content)
    assert response_dict["count"] == 0


def test_hard_stop_empresa_rodoviaria_exception(rf, mocker, user_staff):
    request = rf.post(
        "/api/staff/integracao_rodoviaria/hard_stop_empresa",
        {"company_internal_id": 1, "mensagem": "Aracy da Toptherm"},
    )
    request.user = user_staff
    mocker.patch(
        "core.service.rodoviaria_svc.hard_stop_empresa",
        side_effect=RodoviariaException("Famoso arquiteto catalão"),
    )
    response = hard_stop_empresa_rodoviaria(request)
    response_dict = json.loads(response.content)
    assert response_dict["error"] == "Famoso arquiteto catalão"


def test_atualizar_permissao_taxa_servico_checkout(rf, mocker, user_staff):
    company = baker.make("core.Company", name="Empresa com taxa")
    request = rf.post(
        "/api/ops/atualiza_permissao_taxa_servico_checkout",
        content_type="application/json",
        data={"companyId": company.id, "cobraTaxaServicoCheckout": True},
    )
    mocker.patch("core.service.company_svc.update_company_taxa_servico_checkout")
    assign_role(user_staff, "Rotas")
    request.user = user_staff
    response = atualizar_permissao_taxa_servico_checkout(request)
    assert response.status_code == 200

    assign_role(user_staff, "Comercial")
    request.user = user_staff
    response = atualizar_permissao_taxa_servico_checkout(request)
    assert response.status_code == 200

    remove_role(user_staff, "Rotas")
    remove_role(user_staff, "Comercial")
    request.user = user_staff
    response = atualizar_permissao_taxa_servico_checkout(request)
    assert response.status_code == 403


def test_views_multas_qualidade_torre(rf, mocker, user_staff):
    grupo = baker.make("core.Grupo")
    headers = {"HTTP_X-API-KEY": "torre-local"}
    payload = {"origem": "torre", "tipo": 1}
    request = rf.post(f"/api/staff/{grupo.id}/multas-qualidade-torre", payload, "application/json", **headers)
    request.user = user_staff
    mocker.patch("core.service.multa_svc.indicar_multa")
    response = multas_qualidade_torre(request, grupo.id)

    assert response.status_code == 201


def test_views_edita_multas_qualidade_django(client, mocker, user_qualidade):
    mocker.patch("core.service.multa_svc.editar_multa")

    motivo_antigo = baker.make("core.MotivoMulta")
    empresa_antiga = baker.make("core.Company")
    multa = baker.make("core.Multa", company=empresa_antiga, motivo=motivo_antigo)

    motivo_novo = baker.make("core.MotivoMulta")
    empresa_nova = baker.make("core.Company")

    payload = {
        "multa": json.dumps({"tipo": motivo_novo.id, "empresa": empresa_nova.id, "origem": "teste", "incidente": "1"}),
        "anexos": SimpleUploadedFile("anexo.txt", b"conteudo do arquivo", content_type="text/plain"),
    }

    client.force_login(user_qualidade)
    response = client.post(f"/api/staff/multas-qualidade/{multa.id}/editar", data=payload)

    assert response.status_code == 200


def test_view_save_taxa_servico_referencia_empresa_marketplace(rf, mocker, user_comercial):
    mocker.patch("core.service.editar_porcentagem_repasse_taxa_servico_svc.editar_percentual_referencia_marketplace")
    request = rf.post(
        "/api/staff/integracao_rodoviaria/save-taxa_servico-referencia-empresa-marketplace/1",
        {"percentual_taxa_servico": 10.85},
    )
    request.user = user_comercial
    response = save_taxa_servico_referencia_empresa_marketplace(request, 1)
    assert response.status_code == 200


def test_view_save_taxa_servico_referencia_empresa_marketplace_sem_percentual(rf, mocker, user_comercial):
    mocker.patch("core.service.editar_porcentagem_repasse_taxa_servico_svc.editar_percentual_referencia_marketplace")
    request = rf.post(
        "/api/staff/integracao_rodoviaria/save-taxa_servico-referencia-empresa-marketplace/1",
        content_type="application/json",
    )
    request.user = user_comercial
    response = save_taxa_servico_referencia_empresa_marketplace(request, 1)
    assert response.status_code == 422


def test_get_rodoviaria_passagem_info(rf, user_staff):
    travel_id = "1"
    buseiro_id = "2"
    rodoviaria_response = [{"numero_passagem": 12233}]
    params = {"travel_id": travel_id, "buseiro_id": buseiro_id}
    request = rf.get("/api/staff/integracao_rodoviaria/get_passagem_info", params)
    request.user = user_staff
    with mock.patch(
        "core.service.rodoviaria_svc.get_passagem_info", return_value=rodoviaria_response
    ) as mock_get_passagem_info:
        response = get_rodoviaria_passagem_info(request)
    mock_get_passagem_info.assert_called_once_with(travel_id, buseiro_id)
    assert json.loads(response.content) == rodoviaria_response


def test_view_houve_reducao_frete_grupo_com_log_reduzir_frete(rf, user_staff):
    novo_onibus = baker.make("core.Onibus")
    grupo = baker.make("core.Grupo", valor_frete=Decimal(1010), onibus__placa="ABC1234", _fill_optional=["rota"])
    log_reduzir_frete(
        grupo=grupo,
        frete=grupo.valor_frete,
        novo_frete=Decimal(1000),
        reducao=Decimal(10),
        old_bus_id=grupo.onibus.id,
        old_bus_placa=grupo.onibus.placa,
        new_bus_id=novo_onibus.id,
        new_bus_placa=novo_onibus.placa,
    )
    ActivityLog.objects.all().update(created_at=grupo.created_on + timedelta(minutes=12))

    request = rf.get(f"api/staff/logs/reducao-frete/{grupo.id}")
    request.user = user_staff
    response = houve_reducao_de_frete(request, grupo.id)
    assert response.status_code == HTTPStatus.OK
    assert json.loads(response.content) == {"houve_reducao_de_frete": True}


def test_view_houve_reducao_frete_grupo_sem_log_reduzir_frete(rf, user_staff):
    grupo = baker.make("core.Grupo")

    request = rf.get(f"api/staff/logs/reducao-frete/{grupo.id}")
    request.user = user_staff
    response = houve_reducao_de_frete(request, grupo_id=grupo.id)
    assert response.status_code == HTTPStatus.OK
    assert json.loads(response.content) == {"houve_reducao_de_frete": False}


def test_geocode(rf, user_staff):
    request = rf.get("api/staff/geocode?address=Test+Address")
    request.user = user_staff
    response = geocode(request)

    assert response.status_code == HTTPStatus.OK
    assert json.loads(response.content)["status"] == "OK"


def test_importar_leads(rf, user_staff):
    request = rf.post("api/importar_leads")
    request.FILES["file"] = SimpleUploadedFile(
        "leads.csv",
        content=open("core/tests/data/import_leads_latin1.csv", "rb").read(),
        content_type="text/plain",
    )
    request.user = user_staff

    with mock.patch("core.views_staff.lead_svc.import_marketing_lead") as import_marketing_lead:
        response = importar_leads(request)

    import_marketing_lead.assert_has_calls(
        [
            mock.call(
                {
                    "id": "0",
                    "codigo_para_indicar": "SFEWE",
                    "codigo_indicacao": "",
                    "name": "Alice Ä Test",
                    "cpf": "53373341078",
                    "birth_date": "1998-02-18",
                    "cep": "12345678",
                    "phone": "11923456789",
                    "email": "<EMAIL>",
                    "aceite_contato": "t",
                    "aceite_regulamento": "t",
                    "date": "2023-07-14",
                    "data_atualizacao": "2023-07-14",
                },
                create_user=True,
                default_referrer="buser6anos",
                default_utm_source="https://buser6anos.com.br",
            ),
            mock.call(
                {
                    "id": "1",
                    "codigo_para_indicar": "KSERN",
                    "codigo_indicacao": "SFEWE",
                    "name": "Bob Test",
                    "cpf": "73649479060",
                    "birth_date": "1994-08-15",
                    "cep": "1224501",
                    "phone": "11900128922",
                    "email": "<EMAIL>",
                    "aceite_contato": "t",
                    "aceite_regulamento": "t",
                    "date": "2023-07-14",
                    "data_atualizacao": "2023-07-15",
                },
                create_user=True,
                default_referrer="buser6anos",
                default_utm_source="https://buser6anos.com.br",
            ),
        ],
    )

    assert response.status_code == 200


@mock.patch("core.service.notifications.user_notification_svc.confirmar_telefone")
def test_staff_reenvia_confirmar_telefone(mock_notification, rf, user, user_staff):
    request = rf.post("/api/staff/resend_phone_confirmation", {"user_id": user.id})
    request.user = user_staff
    response = views_staff.resend_phone_confirmation(request)

    assert 200 == response.status_code
    mock_notification.assert_called_once()


def test_error_enconding_importar_leads(rf, user_staff):
    request = rf.post("api/importar_leads")
    request.FILES["file"] = SimpleUploadedFile(
        "leads.csv",
        content=open("core/tests/data/import_leads_latin1.csv", "rb").read(),
        content_type="text/plain",
    )
    request.user = user_staff

    with mock.patch("core.views_staff.lead_svc.import_marketing_lead_chunk"):
        importar_leads(request)


def test_get_informacoes_passagem_api_parceiro_marketplace(rf, user_staff):
    buseiro_id = 2
    modelo_venda = "marketplace"
    travel_id = 1
    params = {
        "buseiro_id": buseiro_id,
        "modelo_venda": modelo_venda,
        "travel_id": travel_id,
    }
    request = rf.get("api/staff/integracao_rodoviaria/get_informacoes_passagem_api_parceiro", params)
    request.user = user_staff
    with mock.patch(
        "core.service.rodoviaria_svc.get_informacoes_passagem_api_parceiro",
        return_value=[{"numero_passagem": 6666}],
    ) as mock_method:
        response = get_rodoviaria_atualizacao_passagem_api_parceiro(request)
    mock_method.assert_called_once_with(buseiro_id, modelo_venda, travel_id)
    assert json.loads(response.content) == [{"numero_passagem": 6666}]


def test_revert_hardstop_empresa_rodoviaria(rf, user_staff):
    request = rf.post(
        "/api/staff/integracao_rodoviaria/revert_hard_stop_empresa",
        {"company_internal_id": 1},
    )
    request.user = user_staff
    with mock.patch("core.service.rodoviaria_svc.revert_hardstop_empresa", return_value=0):
        response = revert_hard_stop_empresa_rodoviaria(request)
        response_dict = json.loads(response.content)
        assert response_dict["count"] == 0


def test_revert_hardstop_empresa_rodoviaria_exception(rf, user_staff):
    comunicacao = baker.make("comunicacao.Comunicacao", codigo="progresso_fidelidade", tipo_disparo="transacional")
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        comunicacao=comunicacao,
        titulo="fidelidade",
        corpo="Parabéns!",
    )
    request = rf.post(
        "/api/staff/integracao_rodoviaria/revert_hard_stop_empresa",
        {"company_internal_id": 1},
    )
    request.user = user_staff
    with mock.patch(
        "core.service.rodoviaria_svc.revert_hardstop_empresa",
        side_effect=ValidationError(message="Aqui tem um erro"),
    ):
        response = revert_hard_stop_empresa_rodoviaria(request)
        assert json.loads(response.content)["error"] == "Aqui tem um erro"
        assert response.status_code == 422


def test_contabiliza_fidelidade_do_grupo(rf, user_staff, user_joao, trecho_classe, programa_fidelidade):  # noqa: F811
    comunicacao = baker.make("comunicacao.Comunicacao", codigo="progresso_fidelidade", tipo_disparo="transacional")
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        comunicacao=comunicacao,
        titulo="fidelidade",
        corpo="Parabéns!",
    )
    grupo = baker.make("core.Grupo", modelo_venda=ModeloVenda.BUSER, status=Grupo.Status.DONE)
    travel = baker.make(
        "core.Travel", user=user_joao, grupo_id=grupo.id, trecho_classe_id=trecho_classe.id, count_seats=3
    )
    request = rf.post(f"api/staff/groups/{grupo.id}/contabilizar-fidelidade")
    request.user = user_staff
    response = contabilizar_fidelidade(request, grupo.id)
    progresso = ProgressoFidelidade.objects.filter(user_id=user_joao.id, fidelidade__title="3 viagens").first()

    assert response.status_code == 200
    assert ProgressoFidelidadeTravel.objects.filter(progresso_fidelidade=progresso, travel=travel).count() == 1
    assert progresso.fidelidade == programa_fidelidade
    assert progresso.user_id == user_joao.id


def test_integracao_forecast_e_gmv_previsto_grupos(
    rf, user_staff, grupo_factory_com_travels_e_buckets, mocker, trechos_vendidos
):
    datetimes_ida = [now() + timedelta(minutes=n * 15) for n in range(3)]
    rota = baker.make("core.Rota")
    grupo_1, grupo_2, grupo_3 = baker.make(
        "core.Grupo", rota=rota, datetime_ida=iter(datetimes_ida), _quantity=3, _bulk_create=True
    )

    tvs = trechos_vendidos(rota)

    pms_1 = baker.make("core.PriceManager", value=Decimal("89.90"), _quantity=3, _bulk_create=True)
    pms_2 = baker.make("core.PriceManager", value=Decimal("89.90"), _quantity=3, _bulk_create=True)
    pms_3 = baker.make("core.PriceManager", value=Decimal("89.90"), _quantity=3, _bulk_create=True)

    tcs_grupo_1 = baker.make(
        "core.TrechoClasse",
        grupo=grupo_1,
        grupo_classe__tipo_assento="executivo",
        grupo_classe__capacidade=10,
        price_manager=iter(pms_1),
        trecho_vendido=iter(tvs),
        _quantity=3,
        _bulk_create=True,
    )
    tcs_grupo_2 = baker.make(
        "core.TrechoClasse",
        grupo=grupo_2,
        grupo_classe__tipo_assento="executivo",
        grupo_classe__capacidade=10,
        price_manager=iter(pms_2),
        trecho_vendido=iter(tvs),
        _quantity=3,
        _bulk_create=True,
    )
    tcs_grupo_3 = baker.make(
        "core.TrechoClasse",
        grupo=grupo_3,
        grupo_classe__tipo_assento="executivo",
        grupo_classe__capacidade=10,
        price_manager=iter(pms_3),
        trecho_vendido=iter(tvs),
        _quantity=3,
        _bulk_create=True,
    )

    travels_grupo_1 = baker.make(
        "core.Travel",
        grupo=grupo_1,
        trecho_classe=iter(tcs_grupo_1),
        grupo_classe=iter([tc.grupo_classe for tc in tcs_grupo_1]),
        trecho_vendido=iter([tc.trecho_vendido for tc in tcs_grupo_1]),
        max_split_value=Decimal("99.90"),
        count_seats=1,
        status="pending",
        _quantity=3,
        _bulk_create=True,
    )

    travels_grupo_2 = baker.make(
        "core.Travel",
        grupo=grupo_2,
        trecho_classe=iter(tcs_grupo_2),
        grupo_classe=iter([tc.grupo_classe for tc in tcs_grupo_2]),
        trecho_vendido=iter([tc.trecho_vendido for tc in tcs_grupo_2]),
        max_split_value=Decimal("99.90"),
        count_seats=1,
        status="pending",
        _quantity=3,
        _bulk_create=True,
    )
    travels_grupo_3 = baker.make(
        "core.Travel",
        grupo=grupo_3,
        trecho_classe=iter(tcs_grupo_3),
        grupo_classe=iter([tc.grupo_classe for tc in tcs_grupo_3]),
        trecho_vendido=iter([tc.trecho_vendido for tc in tcs_grupo_3]),
        max_split_value=Decimal("99.90"),
        count_seats=1,
        status="pending",
        _quantity=3,
        _bulk_create=True,
    )

    baker.make(
        "core.AccountingOperation",
        travel=iter(travels_grupo_1 + travels_grupo_2 + travels_grupo_3),
        source="RESERVA",
        value=-Decimal("99.90"),
        value_real=-Decimal("99.90"),
        _quantity=9,
        _bulk_create=True,
    )

    # dada a solicitação de forecast e GMV previsto usando o forecaster
    request = rf.post(
        "api/staff/groups/forecast_e_gmv_previsto_grupos", {"grupo_ids": [grupo_1.id, grupo_2.id, grupo_3.id]}
    )
    request.user = user_staff

    # os 3 grupos possuem as 3 mesmas combinações de trecho e classe (X, "executivo"), (Y, "semi leito"), (Z, "leito")
    trecho_keys = [(trecho_vendido.id, "executivo") for trecho_vendido in tvs]
    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento",
        return_value={
            grupo_1.id: {trecho_keys[0]: 1, trecho_keys[1]: 0, trecho_keys[2]: 0},
            grupo_2.id: {trecho_keys[0]: 0, trecho_keys[1]: 2, trecho_keys[2]: 0},
            grupo_3.id: {trecho_keys[0]: 0, trecho_keys[1]: 0, trecho_keys[2]: 3},
        },
    )
    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.forecast_prediction_svc.get_grupo_predictions",
        return_value={
            grupo_1.id: 1,
            grupo_2.id: 2,
            grupo_3.id: 3,
        },
    )
    response = forecast_e_gmv_previsto_grupos(request)
    # espero que seja retornada uma resposta válida e no formato {str: [int, float]}
    # sendo {grupo_id: [forecast_ocupacao, GMV_previsto]}
    assert response.status_code == HTTPStatus.OK
    # e nos tcs onde o forecast for maior que a quantidade de pax reais, que o gmv_previsto
    # seja igual ao gmv_atual + qtd_dummies * accop.value
    assert json.loads(response.content) == {
        str(grupo_1.id): {"forecast_grupo": 1, "gmv_previsto": 299.7, "gmv_atual": 299.7},
        str(grupo_2.id): {"forecast_grupo": 2, "gmv_previsto": 389.6, "gmv_atual": 299.7},
        str(grupo_3.id): {"forecast_grupo": 3, "gmv_previsto": 479.5, "gmv_atual": 299.7},
    }


def test_integracao_forecast_e_gmv_previsto_grupos_com_forecast_trecho(rf, user_staff, mocker):
    cidades = baker.make("core.Cidade", slug=iter(["SAO", "SJK", "RIO"]), _quantity=3, _bulk_create=True)
    sao, sjk, rio = baker.make("core.LocalEmbarque", cidade=iter(cidades), _quantity=3, _bulk_create=True)
    rota = baker.make("core.Rota", distancia_total=347, origem=sao, destino=rio)
    # itinerario
    baker.make("core.Checkpoint", idx=0, local=sao, rota=rota)
    baker.make("core.Checkpoint", idx=1, local=sjk, rota=rota, distancia_km=91)
    baker.make("core.Checkpoint", idx=2, local=rio, rota=rota, distancia_km=256)
    sao_sjk = baker.make("core.TrechoVendido", origem=sao, destino=sjk, rota=rota, secundario=True)
    sao_rio = baker.make("core.TrechoVendido", origem=sao, destino=rio, rota=rota, secundario=False)
    sjk_rio = baker.make("core.TrechoVendido", origem=sjk, destino=rio, rota=rota, secundario=False)

    grupo = baker.make("core.Grupo", rota=rota)
    executivo = baker.make("core.GrupoClasse", grupo=grupo, capacidade=42, tipo_assento="executivo")
    pms = baker.make(
        "core.PriceManager",
        value=iter([Decimal("3.11"), Decimal("5.13"), Decimal("20.90")]),
        ref_value=Decimal("15.90"),
        _quantity=3,
        _bulk_create=True,
    )
    trechos_vendidos = [sao_sjk, sao_rio, sjk_rio]
    # executivo
    baker.make(
        "core.TrechoClasse",
        grupo=grupo,
        grupo_classe=executivo,
        trecho_vendido=iter(trechos_vendidos),
        pessoas=0,
        vagas=42,
        price_manager=iter(pms),
        max_split_value="99.99",
        _quantity=3,
        _bulk_create=True,
    )

    # dada a solicitação de forecast e GMV previsto usando o forecaster
    request = rf.post("api/staff/groups/forecast_e_gmv_previsto_grupos", {"grupo_ids": [grupo.id]})
    request.user = user_staff

    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento",
        return_value={
            grupo.id: {(sao_sjk.id, "executivo"): 0.1, (sao_rio.id, "executivo"): 2.0, (sjk_rio.id, "executivo"): 5.0},
        },
    )
    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.forecast_prediction_svc.get_grupo_predictions",
        return_value={grupo.id: 7},
    )
    response = forecast_e_gmv_previsto_grupos(request)

    # espero que seja retornada uma resposta válida e no formato {str: [int, float]}
    # sendo {grupo_id: [forecast_ocupacao, GMV_previsto]}
    assert response.status_code == HTTPStatus.OK
    #  predictions = [0.1, 2.0, 5.0]
    # 114.76 => 0 * Decimal("3.11") + 2 * Decimal("5.13") + 5 * Decimal("20.90")
    assert json.loads(response.content) == {
        str(grupo.id): {"forecast_grupo": 7, "gmv_previsto": 114.76, "gmv_atual": 0}
    }


def test_forecast_e_gmv_previsto_grupos_grupo_sem_forecast_de_todos(
    rf, user_staff, grupo_factory_com_travels_e_buckets, mocker
):
    grupos = grupo_factory_com_travels_e_buckets(timediff=15, quantity=3)
    trecho_keys = grupos[0].trechoclasse_set.values_list("trecho_vendido_id", "grupo_classe__tipo_assento")

    request = rf.post("api/staff/groups/forecast_e_gmv_previsto_grupos", {"grupo_ids": [grupo.id for grupo in grupos]})
    request.user = user_staff

    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento",
        return_value={
            grupos[0].id: {trecho_keys[0]: 1, trecho_keys[1]: 2, trecho_keys[2]: 3},
        },
    )
    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.forecast_prediction_svc.get_grupo_predictions",
        return_value={grupos[0].id: 6},
    )
    response = forecast_e_gmv_previsto_grupos(request)

    assert response.status_code == HTTPStatus.OK
    assert json.loads(response.content) == {
        str(grupos[0].id): {"forecast_grupo": 6, "gmv_previsto": 596.7, "gmv_atual": 299.7}
    }


def test_forecast_e_gmv_previsto_grupos_nenhum_forecast(rf, user_staff, grupo_factory_com_travels_e_buckets, mocker):
    grupos = grupo_factory_com_travels_e_buckets(timediff=15, quantity=3)

    request = rf.post("api/staff/groups/forecast_e_gmv_previsto_grupos", {"grupo_ids": [grupo.id for grupo in grupos]})
    request.user = user_staff

    mocker.patch("adapters.ml_models_adapter.forecaster.get_trecho_predictions", return_value=[None] * 3)
    response = forecast_e_gmv_previsto_grupos(request)

    assert response.status_code == HTTPStatus.OK
    assert json.loads(response.content) == {}


def test_forecast_e_gmv_previsto_grupos_chama_forecast_uma_vez(
    rf, user_staff, grupo_factory_com_travels_e_buckets, mocker
):
    grupos = grupo_factory_com_travels_e_buckets(timediff=15, quantity=2)

    tc = grupos[0].trechoclasse_set.first()
    tc.trecho_vendido.secundario = True
    tc.trecho_vendido.save(update_fields=["secundario"])
    mock_get_trechos_forecast = mocker.patch("adapters.ml_models_adapter.forecaster.get_trecho_predictions")
    mock_get_trechos_forecast.return_value = [1, 1]

    request = rf.post("api/staff/groups/forecast_e_gmv_previsto_grupos", {"grupo_ids": [grupo.id for grupo in grupos]})
    request.user = user_staff

    forecast_e_gmv_previsto_grupos(request)
    mock_get_trechos_forecast.assert_called_once()


@mock.patch("core.service.grupos_staff.escalar_onibus_svc.simular_escalar_onibus_task.delay")
def test_simular_bulk_escalar_onibus_async(mock_simular_escalar_onibus_task, rf, user_staff):
    input_data = {"group_ids": [1, 2, 3, 4], "onibus_id": 5}
    request = rf.post(
        "/api/staff/groups/bulk_escalar_onibus/simular-async", data=input_data, content_type="application/json"
    )
    request.user = user_staff
    request.correlation_id = str(uuid4())

    mock_task = mock.MagicMock(id=str(uuid4()))
    mock_simular_escalar_onibus_task.return_value = mock_task
    response = simular_bulk_escalar_onibus_async(request)
    async_task = AsyncTask.objects.get(
        input_data=input_data,
        endpoint=request.path,
        status=AsyncTask.Status.PENDING,
        created_by=user_staff,
    )

    mock_simular_escalar_onibus_task.assert_called_once_with(async_task.id)
    assert response.status_code == HTTPStatus.ACCEPTED
    assert response.headers["Location"] == resolve_url("staff:async-task", async_task.id)
    assert response.headers["Retry-After"] == "5"


def test_async_task_detail_success(rf, user_staff):
    async_task = baker.make(
        "core.AsyncTask", celery_task_id=str(uuid4()), status=AsyncTask.Status.SUCCESS, result={"ok": True}
    )
    request = rf.get(f"/api/staff/async-tasks/{async_task.id}")
    request.user = user_staff

    response = async_task_detail(request, async_task.id)
    payload = json.loads(response.content)

    assert response.status_code == HTTPStatus.OK
    assert response.headers["Cache-Control"] == "max-age=0, no-cache, no-store, must-revalidate, private"
    assert payload["status"] == "SUCCESS"
    assert payload["result"] == {"ok": True}


def test_async_task_detail_failure(rf, user_staff):
    async_task = baker.make(
        "core.AsyncTask",
        celery_task_id=str(uuid4()),
        status=AsyncTask.Status.FAILURE,
        result={"error": "KeyError: 'name'"},
    )
    request = rf.get(f"/api/staff/async-tasks/{async_task.id}")
    request.user = user_staff

    response = async_task_detail(request, async_task.id)
    payload = json.loads(response.content)

    assert response.status_code == HTTPStatus.BAD_REQUEST
    assert response.headers["Cache-Control"] == "max-age=0, no-cache, no-store, must-revalidate, private"
    assert payload["status"] == "FAILURE"
    assert payload["result"] == {"error": "KeyError: 'name'"}


@mock.patch("core.service.grupos_staff.grupo_crud_svc.escalar_empresa_onibus_rotina_task.delay")
def test_escalar_empresa_onibus_rotina_async(mock_escalar_empresa_onibus_rotina_task, rf, user_staff):
    input_data = {"grupo_ids": [1, 2, 3, 4], "onibus_id": 5}
    request = rf.post(
        "/api/staff/groups/escalar_empresa_onibus_rotina_async", data=input_data, content_type="application/json"
    )
    request.user = user_staff
    request.correlation_id = str(uuid4())

    mock_task = mock.MagicMock(id=str(uuid4()))
    mock_escalar_empresa_onibus_rotina_task.return_value = mock_task
    response = escalar_empresa_onibus_rotina_async(request)
    async_task = AsyncTask.objects.get(
        input_data=input_data,
        endpoint=request.path,
        status=AsyncTask.Status.PENDING,
        created_by=user_staff,
    )

    mock_escalar_empresa_onibus_rotina_task.assert_called_once_with(async_task.id)
    assert response.status_code == HTTPStatus.ACCEPTED
    assert response.headers["Location"] == resolve_url("staff:async-task", async_task.id)
    assert response.headers["Retry-After"] == "5"


@mock.patch("core.service.grupos_staff.grupo_crud_svc.escalar_empresa_onibus_rotina_task.delay")
def test_escalar_empresa_onibus_rotina_async_duplicate(mock_escalar_empresa_onibus_rotina_task, rf, user_staff):
    input_data = {"grupo_ids": [1, 2, 3, 4], "onibus_id": 5}
    request = rf.post(
        "/api/staff/groups/escalar_empresa_onibus_rotina_async", data=input_data, content_type="application/json"
    )
    request.user = user_staff
    request.correlation_id = str(uuid4())
    mock_task = mock.MagicMock(id=str(uuid4()))
    mock_escalar_empresa_onibus_rotina_task.return_value = mock_task

    response_1 = escalar_empresa_onibus_rotina_async(request)
    response_2 = escalar_empresa_onibus_rotina_async(request)

    async_tasks = AsyncTask.objects.filter(
        input_data=input_data,
        endpoint=request.path,
        status=AsyncTask.Status.PENDING,
        created_by=user_staff,
    )

    assert len(async_tasks) == 1
    mock_escalar_empresa_onibus_rotina_task.assert_called_once_with(async_tasks[0].id)
    assert response_1.headers == response_2.headers
    assert response_1.status_code == response_2.status_code


@mock.patch("core.service.rotasadm_svc.edit_rota_task.delay")
def test_edit_rota_async(mock_edit_rota_task, rf, user_rotas):
    input_data = {
        "rotaId": 1,
        "itinerario": [
            {
                "local_id": idx,
                "distancia_km": None,
                "duracao": None,
                "tempo_embarque": None,
            }
            for idx in range(1, 3)
        ],
        "trechos_vendidos": [],
        "ufs_intermediarios": "",
        "updateGroupsAfter": "2024-10-10",
    }
    request = rf.post("/api/staff/rotas/edit/rota/async", data=input_data, content_type="application/json")
    request.user = user_rotas
    request.correlation_id = str(uuid4())

    mock_task = mock.MagicMock(id=str(uuid4()))
    mock_edit_rota_task.return_value = mock_task
    response = edit_rota_async(request)
    async_task = AsyncTask.objects.get(
        input_data=input_data,
        endpoint=request.path,
        status=AsyncTask.Status.PENDING,
        created_by=user_rotas,
    )

    mock_edit_rota_task.assert_called_once_with(async_task.id)
    assert response.status_code == HTTPStatus.ACCEPTED
    assert response.headers["Location"] == resolve_url("staff:async-task", async_task.id)
    assert response.headers["Retry-After"] == "5"


@mock.patch("core.service.rotasadm_svc.edit_rota_task.delay")
def test_edit_rota_async_duplicate(mock_edit_rota_task, rf, user_rotas):
    input_data = {
        "rotaId": 1,
        "itinerario": [
            {
                "local_id": idx,
                "distancia_km": None,
                "duracao": None,
                "tempo_embarque": None,
            }
            for idx in range(1, 3)
        ],
        "trechos_vendidos": [],
        "ufs_intermediarios": "",
        "updateGroupsAfter": "2024-10-10",
    }
    request = rf.post("/api/staff/rotas/edit/rota/async", data=input_data, content_type="application/json")
    request.user = user_rotas
    request.correlation_id = str(uuid4())
    mock_task = mock.MagicMock(id=str(uuid4()))
    mock_edit_rota_task.return_value = mock_task

    response_1 = edit_rota_async(request)
    response_2 = edit_rota_async(request)

    async_tasks = AsyncTask.objects.filter(
        input_data=input_data,
        endpoint=request.path,
        status=AsyncTask.Status.PENDING,
        created_by=user_rotas,
    )

    assert len(async_tasks) == 1
    mock_edit_rota_task.assert_called_once_with(async_tasks[0].id)
    assert response_1.headers == response_2.headers
    assert response_1.status_code == response_2.status_code


@mock.patch("core.service.rotasadm_svc.alterar_rota_de_grupos_task.delay")
def test_alterar_rota_de_grupos_async(mock_alterar_rota, rf, user_rotas):
    rota = baker.make("core.Rota")
    grupos = baker.make("core.Grupo", rota=rota, modelo_venda="buser", _quantity=3, _bulk_create=True)
    input_data = {
        "groups": [grupo.id for grupo in grupos],
        "rota": rota.id,
    }

    request = rf.post("api/staff/efops/groups/alterarrota/async", data=input_data, content_type="application/json")
    request.user = user_rotas
    request.correlation_id = str(uuid4())

    mock_task = mock.MagicMock(id=str(uuid4()))
    mock_alterar_rota.return_value = mock_task
    response = alterar_rota_de_grupos_async(request)
    async_task = AsyncTask.objects.get(
        input_data=input_data,
        endpoint=request.path,
        status=AsyncTask.Status.PENDING,
        created_by=user_rotas,
    )

    mock_alterar_rota.assert_called_once_with(async_task.id)
    assert response.status_code == HTTPStatus.ACCEPTED
    assert response.headers["Location"] == resolve_url("staff:async-task", async_task.id)
    assert response.headers["Retry-After"] == "5"


@mock.patch("core.service.rotasadm_svc.alterar_rota_de_grupos_task.delay")
def test_alterar_rota_de_grupos_async_duplicate(mock_alterar_rota, rf, user_rotas):
    rota = baker.make("core.Rota")
    grupos = baker.make("core.Grupo", rota=rota, modelo_venda="buser", _quantity=3, _bulk_create=True)
    input_data = {
        "groups": [grupo.id for grupo in grupos],
        "rota": rota.id,
    }

    request = rf.post("api/staff/efops/groups/alterarrota/async", data=input_data, content_type="application/json")
    request.user = user_rotas
    request.correlation_id = str(uuid4())
    mock_task = mock.MagicMock(id=str(uuid4()))
    mock_alterar_rota.return_value = mock_task

    response_1 = alterar_rota_de_grupos_async(request)
    response_2 = alterar_rota_de_grupos_async(request)

    async_tasks = AsyncTask.objects.filter(
        input_data=input_data,
        endpoint=request.path,
        status=AsyncTask.Status.PENDING,
        created_by=user_rotas,
    )

    assert len(async_tasks) == 1
    mock_alterar_rota.assert_called_once_with(async_tasks[0].id)
    assert response_1.headers == response_2.headers
    assert response_1.status_code == response_2.status_code


@mock.patch("core.service.rotasadm_svc.replace_local_from_routes_task.delay")
def test_replace_local_for_selected_routes_async(mock_replace_local_from_routes_task, rf, user_rotas):
    input_data = {
        "local": 1,
        "route_id_list": [2],
        "group_id_list": [3],
        "should_send_mail_alert": False,
        "should_send_zap_alert": False,
        "inactivate_existing_local": False,
        "affect_group_from_date": datetime(2024, 10, 10, tzinfo=get_default_timezone()),
        "is_new_local": False,
        "replacing_local": 4,
    }
    request = rf.post("/api/staff/replacelocal/async", data=input_data, content_type="application/json")
    request.user = user_rotas
    request.correlation_id = str(uuid4())

    mock_task = mock.MagicMock(id=str(uuid4()))
    mock_replace_local_from_routes_task.return_value = mock_task
    response = replace_local_for_selected_routes_async(request)
    async_task = AsyncTask.objects.get(
        input_data=input_data,
        endpoint=request.path,
        status=AsyncTask.Status.PENDING,
        created_by=user_rotas,
    )

    mock_replace_local_from_routes_task.assert_called_once_with(async_task.id)
    assert response.status_code == HTTPStatus.ACCEPTED
    assert response.headers["Location"] == resolve_url("staff:async-task", async_task.id)
    assert response.headers["Retry-After"] == "5"


@mock.patch("core.service.rotasadm_svc.replace_local_from_routes_task.delay")
def test_replace_local_for_selected_routes_async_duplicate(mock_replace_local_from_routes_task, rf, user_rotas):
    input_data = {
        "local": 1,
        "route_id_list": [2],
        "group_id_list": [3],
        "should_send_mail_alert": False,
        "should_send_zap_alert": False,
        "inactivate_existing_local": False,
        "affect_group_from_date": datetime(2024, 10, 10, tzinfo=get_default_timezone()),
        "is_new_local": False,
        "replacing_local": 4,
    }
    request = rf.post("/api/staff/replacelocal/async", data=input_data, content_type="application/json")
    request.user = user_rotas
    request.correlation_id = str(uuid4())
    mock_task = mock.MagicMock(id=str(uuid4()))
    mock_replace_local_from_routes_task.return_value = mock_task

    response_1 = replace_local_for_selected_routes_async(request)
    response_2 = replace_local_for_selected_routes_async(request)

    async_tasks = AsyncTask.objects.filter(
        input_data=input_data,
        endpoint=request.path,
        status=AsyncTask.Status.PENDING,
        created_by=user_rotas,
    )

    assert len(async_tasks) == 1
    mock_replace_local_from_routes_task.assert_called_once_with(async_tasks[0].id)
    assert response_1.headers == response_2.headers
    assert response_1.status_code == response_2.status_code


@pytest.mark.parametrize(
    "checkin_status, checkin_status_expected, status_code_expected",
    [
        ("going", "travel_done", HTTPStatus.OK),
        ("pending", "pending", HTTPStatus.BAD_REQUEST),
        ("boarding", "travel_done", HTTPStatus.OK),
        ("travel_done", "travel_done", HTTPStatus.BAD_REQUEST),
    ],
)
def test_encerrar_viagem(rf, user_risco, checkin_status, checkin_status_expected, status_code_expected):
    grupo = baker.make(Grupo, checkin_status=checkin_status)
    request = rf.post("/api/staff/groups/encerrar_viagem", {"grupo_id": grupo.id})
    request.user = user_risco

    response = encerrar_viagem(request)
    grupo.refresh_from_db()

    assert response.status_code == status_code_expected
    assert grupo.checkin_status == checkin_status_expected


def test_removepassengers_with_json_data(rf, user_risco, mocker):
    travel = baker.make("core.Travel")
    passenger = baker.make("core.Passageiro", travel=travel, id=2)
    mock_notificar_viagem_cancelada_risco = mocker.patch(
        "core.service.notifications.user_notification_svc.viagem_cancelada_por_risco"
    )
    request = rf.post(
        "/api/staff/removepassengers",
        data={
            "passengers": [{"travel_id": travel.id, "passenger_id": passenger.id}],
            "reason": "ACAO_RISCO",
            "notify": False,
        },
        content_type="application/json",
    )
    request.user = user_risco

    response = removepassengers(request)
    assert response.status_code == HTTPStatus.OK
    mock_notificar_viagem_cancelada_risco.assert_not_called()


def test_removepassengers_with_old_format_data(rf, user_risco, mocker):
    travel = baker.make("core.Travel")
    passenger = baker.make("core.Passageiro", travel=travel, id=2)
    mock_notificar_viagem_cancelada_risco = mocker.patch(
        "core.service.notifications.user_notification_svc.viagem_cancelada_por_risco"
    )
    request = rf.post(
        "/api/staff/removepassengers",
        data={
            "passengers": json.dumps([{"travel_id": travel.id, "passenger_id": passenger.id}]),
            "reason": "ACAO_RISCO",
            "notify": False,
        },
    )
    request.user = user_risco

    response = removepassengers(request)
    assert response.status_code == HTTPStatus.OK
    mock_notificar_viagem_cancelada_risco.assert_not_called()


def test_removepassengers_with_invalid_json_data(rf, user_risco, mocker):
    request = rf.post(
        "/api/staff/removepassengers",
        data={"passengers": "invalid_data"},
        content_type="application/json",
    )
    mock_viagem_cancelada_por_risco = mocker.patch(
        "core.service.notifications.user_notification_svc.viagem_cancelada_por_risco"
    )
    request.user = user_risco

    response = removepassengers(request)
    assert response.status_code == HTTPStatus.BAD_REQUEST
    mock_viagem_cancelada_por_risco.assert_not_called()


def test_removepassengers_risco_called_notification_on_last_pax(rf, user_risco, mocker):
    travel = baker.make("core.Travel")
    passengers = baker.make("core.Passageiro", travel=travel, _quantity=2)
    passengers[0].set_removed()

    mock_notificar_viagem_cancelada_risco = mocker.patch(
        "core.service.notifications.user_notification_svc.viagem_cancelada_por_risco"
    )

    request = rf.post(
        "/api/staff/removepassengers",
        data={
            "passengers": json.dumps([{"travel_id": travel.id, "passenger_id": passengers[1].id}]),
            "reason": "ACAO_RISCO",
            "notify": True,
        },
    )
    request.user = user_risco

    response = removepassengers(request)
    assert response.status_code == HTTPStatus.OK
    mock_notificar_viagem_cancelada_risco.assert_called_once_with(travel, evento_comunicacao="reserva.CancelByRisco")


def test_removepassengers_risco_not_called_notification_when_not_last_pax(rf, user_risco, mocker):
    travel = baker.make("core.Travel")
    passengers = baker.make("core.Passageiro", travel=travel, _quantity=2)

    mock_notificar_viagem_cancelada_risco = mocker.patch(
        "core.service.notifications.user_notification_svc.viagem_cancelada_por_risco"
    )

    request = rf.post(
        "/api/staff/removepassengers",
        data={
            "passengers": json.dumps([{"travel_id": travel.id, "passenger_id": passengers[1].id}]),
            "reason": "ACAO_RISCO",
            "notify": True,
        },
    )
    request.user = user_risco

    response = removepassengers(request)
    assert response.status_code == HTTPStatus.OK
    mock_notificar_viagem_cancelada_risco.assert_not_called()


def test_get_itinerario_dinamico(rf, user_rotas, django_assert_num_queries):
    cidades = baker.make(
        "core.Cidade",
        name=iter(["São Paulo", "Rio de Janeiro"]),
        uf=iter(["SP", "RJ"]),
        slug=iter(["sao-paulo-sp", "rio-de-janeiro-rj"]),
        _quantity=2,
        _bulk_create=True,
    )
    local_sao, local_rio = baker.make("core.LocalEmbarque", cidade=iter(cidades), _quantity=2, _bulk_create=True)
    rota = baker.make(
        "core.Rota", origem=local_sao, destino=local_rio, distancia_total=420, duracao_total=timedelta(hours=6)
    )
    checkpoint_sao = baker.make("core.Checkpoint", idx=0, local=local_sao, rota=rota)
    baker.make("core.Checkpoint", idx=1, local=local_rio, rota=rota, distancia_km=420, duracao=timedelta(hours=6))
    duracao_dinamica = baker.make("core.DuracaoDinamica", checkpoint=checkpoint_sao, duracao=timedelta(hours=7))
    grupo = baker.make("core.Grupo", rota=rota, duracoes=[duracao_dinamica.id])

    request = rf.get("/api/staff/get_itinerario_dinamico", {"grupo_id": grupo.id})
    request.user = user_rotas

    with django_assert_num_queries(6):
        response = get_itinerario_dinamico(request)

    itinerario_dinamico = json.loads(response.content)

    assert response.status_code == HTTPStatus.OK
    assert len(itinerario_dinamico) == 2
    assert itinerario_dinamico[1]["duracao"] == duracao_dinamica.duracao / timedelta(milliseconds=1)


def test_alterar_duracoes(rf, user_rotas, django_assert_num_queries):
    cidades = baker.make(
        "core.Cidade",
        name=iter(["São Paulo", "São José dos Campos", "Rio de Janeiro"]),
        uf=iter(["SP", "SP", "RJ"]),
        slug=iter(["sao-paulo-sp", "sao-jose-dos-campos-sp", "rio-de-janeiro-rj"]),
        _quantity=3,
        _bulk_create=True,
    )
    local_sao, local_sjk, local_rio = baker.make(
        "core.LocalEmbarque", cidade=iter(cidades), _quantity=3, _bulk_create=True
    )
    rota = baker.make(
        "core.Rota", origem=local_sao, destino=local_rio, distancia_total=440, duracao_total=timedelta(hours=6)
    )
    duracao_sao_sjk = timedelta(hours=1, minutes=30)
    duracao_sjk_rio = timedelta(hours=5, minutes=30)
    tempo_embarque_sjk = timedelta(minutes=10)
    checkpoint_sao = baker.make("core.Checkpoint", idx=0, local=local_sao, rota=rota)
    checkpoint_sjk = baker.make(
        "core.Checkpoint",
        idx=1,
        local=local_sjk,
        rota=rota,
        distancia_km=90,
        duracao=duracao_sao_sjk,
        tempo_embarque=tempo_embarque_sjk,
    )
    checkpoint_rio = baker.make(
        "core.Checkpoint", idx=2, local=local_rio, rota=rota, distancia_km=350, duracao=duracao_sjk_rio
    )
    grupos = baker.make("core.Grupo", rota=rota, _quantity=2, _bulk_create=True)

    payload = {
        "duracoes": [
            {
                "grupo_id": grupo.id,
                "checkpoint_id": checkpoint.id,
                "tipo": "DEFAULT",
                "duracao_seconds": duracao,
                "tempo_embarque_seconds": tempo_embarque,
            }
            for grupo in grupos
            for checkpoint, duracao, tempo_embarque in [
                (checkpoint_sao, 0, 0),
                (checkpoint_sjk, duracao_sao_sjk.total_seconds(), tempo_embarque_sjk.total_seconds()),
                (checkpoint_rio, duracao_sjk_rio.total_seconds(), 0),
            ]
        ]
    }
    request = rf.post("/api/staff/alterar_duracoes", payload, content_type="application/json")
    request.user = user_rotas

    # 63 -> 18 -> 21
    with django_assert_num_queries(21):
        response = alterar_duracoes(request)

    assert response.status_code == HTTPStatus.OK
    for grupo in grupos:
        grupo.refresh_from_db()
        duracoes = list(
            DuracaoDinamica.objects.filter(id__in=grupo.duracoes)
            .values_list("checkpoint", "duracao", "tempo_embarque")
            .order_by("id")
        )
        assert duracoes == [
            (checkpoint_sao.id, duracao_sao_sjk, timedelta(0)),
            (checkpoint_sjk.id, duracao_sjk_rio, tempo_embarque_sjk),
            (checkpoint_rio.id, timedelta(0), timedelta(0)),
        ]


def test_get_closed_reasons_grupo(rf, user_staff):
    request = rf.get("/api/staff/groups/get_closed_reasons", {"reason_type": "grupo"})
    request.user = user_staff

    response = get_closed_reasons(request)

    assert response.status_code == HTTPStatus.OK
    assert json.loads(response.content) == ClosedReasons.to_staff(reason_type="grupo")


def test_get_closed_reasons_trecho_classe(rf, user_staff):
    request = rf.get("/api/staff/groups/get_closed_reasons", {"reason_type": "trecho_classe"})
    request.user = user_staff

    response = get_closed_reasons(request)

    assert response.status_code == HTTPStatus.OK
    assert json.loads(response.content) == ClosedReasons.to_staff(reason_type="trecho_classe")


@time_machine.travel("2025-06-19 16:00", tick=False)
def test_fechar_trechos(rf, user_staff):
    cidades = baker.make("core.Cidade", slug=iter(["SAO", "SJK", "RIO"]), _quantity=3, _bulk_create=True)
    local_sao, local_sjk, local_rio = baker.make(
        "core.LocalEmbarque", cidade=iter(cidades), _quantity=len(cidades), _bulk_create=True
    )
    rota = baker.make("core.Rota", origem=local_sao, destino=local_rio)

    baker.make(
        "core.Checkpoint",
        rota=rota,
        idx=iter([0, 1, 2]),
        local=iter([local_sao, local_sjk, local_rio]),
        duracao=iter([timedelta(0), timedelta(minutes=90), timedelta(hours=5, minutes=30)]),
        distancia_km=iter([0, 90, 350]),
        _quantity=3,
        _bulk_create=True,
    )

    tv_sao_sjk, tv_sjk_rio = baker.make(
        "core.TrechoVendido",
        rota=rota,
        origem=iter([local_sao, local_sjk]),
        destino=iter([local_sjk, local_rio]),
        _quantity=2,
        _bulk_create=True,
    )

    grupo = baker.make(
        "core.Grupo",
        rota=rota,
        status=Grupo.Status.PENDING,
        modelo_venda=Grupo.ModeloVenda.BUSER,
        datetime_ida=now(),
    )

    gc_sao_sjk, gc_sjk_rio = baker.make(
        "core.GrupoClasse", grupo=grupo, tipo_assento=iter(["semi leito", "leito"]), _quantity=2, _bulk_create=True
    )

    tc_sao_sjk, tc_sjk_rio = baker.make(
        "core.TrechoClasse",
        grupo=grupo,
        grupo_classe=iter([gc_sao_sjk, gc_sjk_rio]),
        trecho_vendido=iter([tv_sao_sjk, tv_sjk_rio]),
        datetime_ida=now(),
        _quantity=2,
        _bulk_create=True,
    )

    params = {
        "grupo_ids": [grupo.id],
        "trechos_classe": [{"trecho_vendido_id": tv_sjk_rio.id, "tipo_assento": gc_sjk_rio.tipo_assento}],
        "closed_reason": "Fiscalização",
    }
    request = rf.post("api/staff/groups/fechartrechos", params, content_type="application/json")
    request.user = user_staff

    response = fechar_trechos(request)
    assert response.status_code == HTTPStatus.OK

    tc_sao_sjk.refresh_from_db()
    assert tc_sao_sjk.closed is False

    tc_sjk_rio.refresh_from_db()
    assert tc_sjk_rio.closed is True
    assert tc_sjk_rio.closed_reason == "Fiscalização"
    assert tc_sjk_rio.closed_by == user_staff
    assert tc_sjk_rio.closed_at == now()


@time_machine.travel("2025-06-19 16:00", tick=False)
def test_fechar_trechos_dois_grupos_com_duas_classes(rf, user_staff):
    cidades = baker.make("core.Cidade", slug=iter(["SAO", "SJK", "RIO"]), _quantity=3, _bulk_create=True)
    local_sao, local_sjk, local_rio = baker.make(
        "core.LocalEmbarque", cidade=iter(cidades), _quantity=len(cidades), _bulk_create=True
    )
    rota = baker.make("core.Rota", origem=local_sao, destino=local_rio)

    baker.make(
        "core.Checkpoint",
        rota=rota,
        idx=iter([0, 1, 2]),
        local=iter([local_sao, local_sjk, local_rio]),
        duracao=iter([timedelta(0), timedelta(minutes=90), timedelta(hours=5, minutes=30)]),
        distancia_km=iter([0, 90, 350]),
        _quantity=3,
        _bulk_create=True,
    )

    tv_sao_sjk, tv_sjk_rio = baker.make(
        "core.TrechoVendido",
        rota=rota,
        origem=iter([local_sao, local_sjk]),
        destino=iter([local_sjk, local_rio]),
        _quantity=2,
        _bulk_create=True,
    )

    grupo1, grupo2 = baker.make(
        "core.Grupo",
        rota=rota,
        status=Grupo.Status.PENDING,
        modelo_venda=Grupo.ModeloVenda.BUSER,
        datetime_ida=now(),
        _quantity=2,
        _bulk_create=True,
    )

    gc1_leito, gc1_semi_leito = baker.make(
        "core.GrupoClasse", grupo=grupo1, tipo_assento=iter(["leito", "semi leito"]), _quantity=2, _bulk_create=True
    )

    gc2_leito, gc2_semi_leito = baker.make(
        "core.GrupoClasse", grupo=grupo2, tipo_assento=iter(["leito", "semi leito"]), _quantity=2, _bulk_create=True
    )

    tc1_leito, tc1_semi_leito, tc2_leito, tc2_semi_leito = baker.make(
        "core.TrechoClasse",
        grupo=iter([grupo1, grupo1, grupo2, grupo2]),
        grupo_classe=iter([gc1_leito, gc1_semi_leito, gc2_leito, gc2_semi_leito]),
        trecho_vendido=iter([tv_sao_sjk, tv_sao_sjk, tv_sjk_rio, tv_sjk_rio]),
        datetime_ida=now(),
        _quantity=4,
        _bulk_create=True,
    )

    params = {
        "grupo_ids": [grupo1.id, grupo2.id],
        "trechos_classe": [
            {"trecho_vendido_id": tv_sao_sjk.id, "tipo_assento": gc1_leito.tipo_assento},
            {"trecho_vendido_id": tv_sjk_rio.id, "tipo_assento": gc2_semi_leito.tipo_assento},
        ],
        "closed_reason": "Fiscalização",
    }
    request = rf.post("api/staff/groups/fechartrechos", params, content_type="application/json")
    request.user = user_staff

    response = fechar_trechos(request)
    assert response.status_code == HTTPStatus.OK

    tc1_leito.refresh_from_db()
    assert tc1_leito.closed is True
    assert tc1_leito.closed_reason == "Fiscalização"
    assert tc1_leito.closed_by == user_staff
    assert tc1_leito.closed_at == now()

    tc2_semi_leito.refresh_from_db()
    assert tc2_semi_leito.closed is True
    assert tc2_semi_leito.closed_reason == "Fiscalização"
    assert tc2_semi_leito.closed_by == user_staff
    assert tc2_semi_leito.closed_at == now()

    tc1_semi_leito.refresh_from_db()
    assert tc1_semi_leito.closed is False

    tc2_leito.refresh_from_db()
    assert tc2_leito.closed is False


def test_list_eventos_extra(rf, user_rotas):
    nomes = ["São João", "Carnaval", "Páscoa"]
    status = ["pending", "doing", "done"]
    eventos = baker.make("core.EventoExtra", nome=iter(nomes), status=iter(status), _quantity=3, _bulk_create=True)
    eventos_ids = [evento.id for evento in eventos]
    baker.make("core.EventoExtraSolicitacao", evento_extra_id=iter(eventos_ids), _quantity=3, _bulk_create=True)
    params = {"paginator": {"rowsPerPage": 10, "page": 1}, "filters": {}}
    request = rf.get("/api/staff/evento-extra/list", {"params": json.dumps(params)})
    request.user = user_rotas
    response = views_staff.evento_extra_list(request)

    assert response.status_code == HTTPStatus.OK
    items = json.loads(response.content)["items"]
    count = json.loads(response.content)["count"]

    assert count == 3
    assert set(nomes) == {item["nome"] for item in items}


def test_create_or_update_evento_extra(rf, user_rotas, evento_extra_payload):
    request = rf.post("/api/staff/evento-extra/save", evento_extra_payload, content_type="application/json")
    request.user = user_rotas
    res = views_staff.evento_extra_save(request)
    assert res.status_code == HTTPStatus.OK
    evento_extra_qs = EventoExtra.objects.all()
    assert evento_extra_qs.count() == 1

    evento_extra = evento_extra_qs[0]
    assert evento_extra_payload["nome"] == evento_extra.nome
    assert evento_extra_payload["dataInicial"] == evento_extra.data_inicial
    assert evento_extra_payload["dataFinal"] == evento_extra.data_final
    assert evento_extra_payload["status"] == evento_extra.status

    new_data_inicial = date(2025, 6, 20)
    new_data_final = date(2025, 6, 25)
    evento_extra_payload["id"] = evento_extra.id
    evento_extra_payload["nome"] = "São João"
    evento_extra_payload["dataInicial"] = new_data_inicial
    evento_extra_payload["dataFinal"] = new_data_final

    request_update = rf.post("/api/staff/evento-extra/save", evento_extra_payload, content_type="application/json")
    request_update.user = user_rotas
    res = views_staff.evento_extra_save(request_update)
    assert res.status_code == HTTPStatus.OK
    assert EventoExtra.objects.count() == 1

    evento_extra.refresh_from_db()
    assert evento_extra_payload["nome"] == "São João"
    assert evento_extra_payload["dataInicial"] == new_data_inicial
    assert evento_extra_payload["dataFinal"] == new_data_final
    assert evento_extra_payload["status"] == evento_extra.status


def test_list_evento_extra_details(rf, user_rotas):
    # dado um evento, com solicitações e negociações cadastradas
    nomes = ["São João", "Carnaval", "Páscoa"]
    status = EventoExtra.EventoStatus
    eventos = baker.make("core.EventoExtra", nome=iter(nomes), status=iter(status), _quantity=3, _bulk_create=True)

    eventos_ids = [evento.id for evento in eventos]
    evento_extra_id = eventos_ids[0]
    solicitacao_extra = baker.make(
        "core.EventoExtraSolicitacao",
        rota_prevista="SAO-RIO",
        rota_principal__eixo="RIO-SAO",
        evento_extra_id=evento_extra_id,
    )
    negociacao = baker.make(
        "core.EventoExtraNegociacao", solicitacao_extra=solicitacao_extra, evento_extra_id=evento_extra_id
    )

    perna = baker.make(
        "core.EventoExtraSolicitacaoPerna",
        data=date(2025, 6, 26),
        hora=time(12, 0),
        sentido="RIO-SAO",
        solicitacao_extra=solicitacao_extra,
    )
    # ao solicitar detalhes desse evento
    params = {"filters": {"regional": None, "prioridade": None, "rotaPrincipal": None}}
    request = rf.get(f"/api/staff/evento-extra/{evento_extra_id}/details", {"filters": json.dumps(params)})
    request.user = user_rotas
    response = views_staff.evento_extra_details(request, evento_extra_id)

    assert response.status_code == HTTPStatus.OK
    resp_json = json.loads(response.content)[0]

    # devem ser retornado todos os objetos relacionados serializados corretamente
    assert resp_json["eventoExtraId"] == evento_extra_id
    assert resp_json["id"] == solicitacao_extra.id
    assert resp_json["negociacoes"][0]["id"] == negociacao.id
    assert resp_json["pernas"][0]["id"] == perna.id


def test_list_evento_extra_details_datetime_to_local_tz(rf, user_rotas):
    # dado um evento com solicitação de veículos extra
    evento_extra = baker.make("core.EventoExtra", nome="Páscoa", status=EventoExtra.EventoStatus.DOING)

    evento_extra_id = evento_extra.id
    solicitacao_extra = baker.make(
        "core.EventoExtraSolicitacao",
        rota_prevista="SAO-RIO",
        rota_principal__eixo="RIO-SAO",
        evento_extra_id=evento_extra_id,
    )
    baker.make("core.EventoExtraNegociacao", solicitacao_extra=solicitacao_extra, evento_extra_id=evento_extra_id)

    # dado que na solicitação existam pernas:
    # 1. com rota, com origem no tz America/Sao_Paulo
    tz_sp = ZoneInfo("America/Sao_Paulo")
    rota1 = baker.make(
        "core.Rota", origem__cidade__timezone=tz_sp, origem__cidade__name="RIO", destino__cidade__name="SAO"
    )
    # com rota, com origem no tz America/Cuiaba
    tz_cuiaba = ZoneInfo("America/Cuiaba")
    rota2 = baker.make(
        "core.Rota", origem__cidade__timezone=tz_cuiaba, origem__cidade__name="RIO", destino__cidade__name="SAO"
    )
    # sem rota -> sem tz definido
    rota3 = None

    # e que todas as pernas terão o mesmo horario cadastrado (UTC-0)
    perna1, perna2, perna3 = baker.make(
        "core.EventoExtraSolicitacaoPerna",
        data="2025-06-26",
        hora="12:00:00",
        sentido="RIO-SAO",
        rota_id=iter([rota1.id, rota2.id, rota3]),
        solicitacao_extra=solicitacao_extra,
        _quantity=3,
        _bulk_create=True,
    )

    params = {"filters": {"regional": None, "prioridade": None, "rotaPrincipal": None}}
    request = rf.get(f"/api/staff/evento-extra/{evento_extra_id}/details", {"filters": json.dumps(params)})
    request.user = user_rotas
    response = views_staff.evento_extra_details(request, evento_extra_id)

    assert response.status_code == HTTPStatus.OK
    # Ao solicitar os dados serializados, espero que sejam apresentados
    # sem timezone (pro dayjs não bagunçar o front).
    # independente de houver ou não rota, deve manter mesmo horario
    resp_json = json.loads(response.content)[0]
    perna_1, perna_2, perna_3 = resp_json["pernas"]
    assert perna_1["hora"] == "12:00:00"
    assert perna_2["hora"] == "12:00:00"
    assert perna_3["hora"] == "12:00:00"

    assert perna_1["data"] == "2025-06-26"
    assert perna_2["data"] == "2025-06-26"
    assert perna_3["data"] == "2025-06-26"


def test_delete_eventos_extra(rf, user_rotas):
    nomes = ["São João", "Carnaval"]
    status = ["pending", "doing"]
    evento_com_solicitacao, evento_sem_solicitacao = baker.make(
        "core.EventoExtra", nome=iter(nomes), status=iter(status), _quantity=2, _bulk_create=True
    )
    baker.make("core.EventoExtraSolicitacao", evento_extra_id=evento_com_solicitacao.id)
    request = rf.delete(f"/api/staff/evento-extra/{evento_com_solicitacao.id}/delete")
    request.user = user_rotas
    response = views_staff.evento_extra_delete(request, evento_extra_id=evento_com_solicitacao.id)

    # Não é permitido excluir evento com solicitações ativas.
    assert response.status_code == HTTPStatus.BAD_REQUEST
    assert json.loads(response.content)["error"] == "Não é possível excluir evento com solicitações ativas."

    # Evento sem solicitações pendentes deve ser excluído com sucesso.
    request = rf.delete(f"/api/staff/evento-extra/{evento_sem_solicitacao.id}/delete")
    request.user = user_rotas
    response = views_staff.evento_extra_delete(request, evento_extra_id=evento_sem_solicitacao.id)
    assert response.status_code == HTTPStatus.OK

    # se o evento não existir, a requisição deve retornar o status NOT_FOUND
    response = views_staff.evento_extra_delete(request, evento_extra_id=evento_sem_solicitacao.id)
    assert response.status_code == HTTPStatus.NOT_FOUND


def test_create_or_update_evento_extra_solicitacao(rf, user_rotas, solicitacao_extra_payload):
    request = rf.post(
        "/api/staff/evento-extra/solicitacao/save", solicitacao_extra_payload, content_type="application/json"
    )
    request.user = user_rotas
    res = views_staff.evento_extra_solicitacao_save(request)
    assert res.status_code == HTTPStatus.OK
    evento_extra_solicitacao_qs = EventoExtraSolicitacao.objects.all()
    assert evento_extra_solicitacao_qs.count() == 1

    evento_extra_solicitacao = evento_extra_solicitacao_qs[0]
    assert evento_extra_solicitacao.evento_extra_id == solicitacao_extra_payload["eventoExtraId"]
    assert evento_extra_solicitacao.ticket_medio_estimado == solicitacao_extra_payload["ticketMedioEstimado"]
    assert evento_extra_solicitacao.tipos_assento == solicitacao_extra_payload["tiposAssento"]
    assert evento_extra_solicitacao.rota_principal.id == solicitacao_extra_payload["rotaPrincipal"]["id"]
    assert evento_extra_solicitacao.is_fechado_rotas is False

    new_ticket_medio_estimado = Decimal("150")
    new_tipos_assento = ["leito cama", "semi leito"]
    rp = baker.make("core.RotaPrincipal", eixo="BHZ-RIO")

    solicitacao_extra_payload["id"] = evento_extra_solicitacao.id
    solicitacao_extra_payload["ticketMedioEstimado"] = new_ticket_medio_estimado
    solicitacao_extra_payload["tiposAssento"] = new_tipos_assento
    solicitacao_extra_payload["rotaPrincipal"] = {"id": rp.id, "eixo": rp.eixo}
    solicitacao_extra_payload["is_fechado_rotas"] = True

    request_update = rf.post(
        "/api/staff/evento-extra/solicitacao/save", solicitacao_extra_payload, content_type="application/json"
    )
    request_update.user = user_rotas
    res = views_staff.evento_extra_solicitacao_save(request_update)
    assert res.status_code == HTTPStatus.OK
    assert EventoExtraSolicitacao.objects.count() == 1

    evento_extra_solicitacao.refresh_from_db()
    assert evento_extra_solicitacao.ticket_medio_estimado == new_ticket_medio_estimado
    assert evento_extra_solicitacao.tipos_assento == new_tipos_assento
    assert evento_extra_solicitacao.rota_principal.id == rp.id
    assert evento_extra_solicitacao.is_fechado_rotas is True


def test_evento_extra_solicitacao_historico_alteracoes(rf, user_factory, solicitacao_extra_payload):
    user_1 = user_factory(
        roles=["Rotas"],
        username="rotas",
        first_name="Alguém",
        last_name="De Rotas",
        email="<EMAIL>",
        password="password",
    )

    user_2 = user_factory(
        roles=["Rotas"],
        username="rotas2",
        first_name="Outro alguém",
        last_name="De Rotas",
        email="<EMAIL>",
        password="password",
    )
    BASE_DATETIME = datetime(2025, 6, 30, 12, 30, 0, tzinfo=timezone.utc)
    # dado um evento extra solicitação criado por um usuário
    with time_machine.travel(BASE_DATETIME, tick=False) as traveler:
        request = rf.post(
            "/api/staff/evento-extra/solicitacao/save", solicitacao_extra_payload, content_type="application/json"
        )
        request.user = user_1
        res = views_staff.evento_extra_solicitacao_save(request)
        assert res.status_code == HTTPStatus.OK
        evento_extra_solicitacao = EventoExtraSolicitacao.objects.all()[0]
        ONE_DAY_AHEAD = BASE_DATETIME + timedelta(days=1)
        traveler.move_to(ONE_DAY_AHEAD)
        # modificado por outro usuário
        solicitacao_extra_payload["id"] = evento_extra_solicitacao.id
        solicitacao_extra_payload["is_fechado_rotas"] = True
        first_change = rf.post(
            "/api/staff/evento-extra/solicitacao/save", solicitacao_extra_payload, content_type="application/json"
        )
        first_change.user = user_2
        res = views_staff.evento_extra_solicitacao_save(first_change)
        assert res.status_code == HTTPStatus.OK

        TWO_DAYS_AHEAD = BASE_DATETIME + timedelta(days=1)
        traveler.move_to(TWO_DAYS_AHEAD)
        # e atualizado uma terceira vez
        solicitacao_extra_payload["id"] = evento_extra_solicitacao.id
        solicitacao_extra_payload["prioridade"] = EventoExtraSolicitacao.Prioridade.BAIXA
        solicitacao_extra_payload["has_precificacao_inicial"] = True

        second_change = rf.post(
            "/api/staff/evento-extra/solicitacao/save", solicitacao_extra_payload, content_type="application/json"
        )
        second_change.user = user_1
        res = views_staff.evento_extra_solicitacao_save(second_change)
        assert res.status_code == HTTPStatus.OK

    # espero conseguir visualizar todo o histórico de atualizações.
    # sabendo ao menos quando e por quem cada alteração foi realizada.
    assert EventoExtraSolicitacao.objects.count() == 1
    assert EventoExtraSolicitacao.objects.all()[0].history.count() == 3
    second_change = EventoExtraSolicitacao.objects.all()[0].history.all()[0]
    first_change = EventoExtraSolicitacao.objects.all()[0].history.all()[1]
    creation = EventoExtraSolicitacao.objects.all()[0].history.all()[2]
    # TODO: investigar pq não está salvando history_user_id
    # usuário que criou o primeiro evento.
    assert creation.history_user_id == user_1.id
    assert creation.history_date == BASE_DATETIME
    # primeira atualização feita por quem e quando
    assert first_change.history_user_id == user_2.id
    assert first_change.history_date == ONE_DAY_AHEAD
    # e a segunda.
    assert second_change.history_user_id == user_1.id
    assert second_change.history_date == TWO_DAYS_AHEAD
    # e quais atributos foram modificados a cada mudança.
    delta_1 = first_change.diff_against(creation).changes
    assert len(delta_1) == 2
    assert {delta.field for delta in delta_1} == {"is_fechado_rotas", "updated_by"}

    delta_2 = second_change.diff_against(first_change).changes
    assert len(delta_2) == 3
    assert {delta.field for delta in delta_2} == {"prioridade", "has_precificacao_inicial", "updated_by"}


def test_delete_eventos_extra_solicitacao(rf, user_rotas):
    nomes = ["São João", "Carnaval"]
    # dadas duas solicitações de extras, uma com negociação em andamento e outra sem.
    solicitacao_com_negociacao, solicitacao_sem_negociacao = baker.make(
        "core.EventoExtraSolicitacao",
        evento_extra__nome=iter(nomes),
        _quantity=2,
        _bulk_create=True,
    )
    # ambas com pernas cadastradas.
    baker.make(
        "core.EventoExtraSolicitacaoPerna",
        solicitacao_extra=iter([solicitacao_com_negociacao, solicitacao_sem_negociacao]),
    )
    baker.make(
        "core.EventoExtraNegociacao",
        solicitacao_extra=solicitacao_com_negociacao,
        evento_extra_id=solicitacao_com_negociacao.id,
    )

    request = rf.delete(f"/api/staff/evento-extra/solicitacao/{solicitacao_com_negociacao.id}/delete")
    request.user = user_rotas
    response = views_staff.evento_extra_solicitacao_delete(request, solicitacao_id=solicitacao_com_negociacao.id)

    assert response.status_code == HTTPStatus.OK

    # mas se não houver negociação, pode excluir. Mesmo que existam pernas cadastradas.
    request = rf.delete(f"/api/staff/evento-extra/solicitacao/{solicitacao_sem_negociacao.id}/delete")
    request.user = user_rotas
    response = views_staff.evento_extra_solicitacao_delete(request, solicitacao_id=solicitacao_sem_negociacao.id)
    assert response.status_code == HTTPStatus.OK

    # se o evento não existir, a requisição deve retornar o status NOT_FOUND
    response = views_staff.evento_extra_solicitacao_delete(request, solicitacao_id=solicitacao_sem_negociacao.id)
    assert response.status_code == HTTPStatus.NOT_FOUND
    assert EventoExtraNegociacao.objects.count() == 0
    assert EventoExtraSolicitacaoPerna.objects.count() == 0


def test_create_or_update_evento_extra_negociacao(rf, user_rotas, negociacao_extra_payload):
    request = rf.post(
        "/api/staff/evento-extra/negociacao/save", negociacao_extra_payload, content_type="application/json"
    )
    request.user = user_rotas
    res = views_staff.evento_extra_negociacao_save(request)
    assert res.status_code == HTTPStatus.OK
    evento_extra_negociacao_qs = EventoExtraNegociacao.objects.all()
    assert evento_extra_negociacao_qs.count() == 1

    evento_extra_negociacao = evento_extra_negociacao_qs[0]
    assert evento_extra_negociacao.evento_extra_id == negociacao_extra_payload["eventoExtraId"]
    assert evento_extra_negociacao.deslocamento == negociacao_extra_payload["deslocamento"]
    assert evento_extra_negociacao.frete_total == Decimal(negociacao_extra_payload["freteTotal"])
    assert evento_extra_negociacao.capacidade == negociacao_extra_payload["capacidade"]

    company = baker.make("core.Company", name="Buser Brasil")
    new_deslocamento = 150
    new_frete_total = Decimal("2000")
    new_tipos_assento = ["leito cama", "semi leito"]

    negociacao_extra_payload["id"] = evento_extra_negociacao.id
    negociacao_extra_payload["company"] = {"id": company.id, "name": company.name}
    negociacao_extra_payload["freteTotal"] = new_frete_total
    negociacao_extra_payload["deslocamento"] = new_deslocamento
    negociacao_extra_payload["tiposAssento"] = new_tipos_assento

    request_update = rf.post(
        "/api/staff/evento-extra/negociacao/save", negociacao_extra_payload, content_type="application/json"
    )
    request_update.user = user_rotas
    res = views_staff.evento_extra_negociacao_save(request_update)
    assert res.status_code == HTTPStatus.OK
    assert EventoExtraSolicitacao.objects.count() == 1

    evento_extra_negociacao.refresh_from_db()
    assert evento_extra_negociacao.company.id == company.id
    assert evento_extra_negociacao.frete_total == new_frete_total
    assert evento_extra_negociacao.deslocamento == new_deslocamento
    assert evento_extra_negociacao.tipos_assento == new_tipos_assento


def test_delete_eventos_extra_negociacao(rf, user_rotas):
    nomes = ["São João", "Carnaval"]
    # dadas duas solicitações de extras, uma com negociação em andamento e outra sem.
    solicitacao = baker.make(
        "core.EventoExtraSolicitacao",
        evento_extra__nome=iter(nomes),
    )
    # ambas com pernas cadastradas.
    baker.make("core.EventoExtraSolicitacaoPerna", solicitacao_extra=solicitacao)
    negociacao = baker.make(
        "core.EventoExtraNegociacao",
        solicitacao_extra=solicitacao,
        evento_extra=solicitacao.evento_extra,
    )

    request = rf.delete(f"/api/staff/evento-extra/negociacao/{negociacao.id}/delete")
    request.user = user_rotas
    response = views_staff.evento_extra_negociacao_delete(request, negociacao_id=negociacao.id)

    assert response.status_code == HTTPStatus.OK

    # se o evento não existir, a requisição deve retornar o status NOT_FOUND
    request = rf.delete(f"/api/staff/evento-extra/negociacao/{negociacao.id}/delete")
    request.user = user_rotas
    response = views_staff.evento_extra_negociacao_delete(request, negociacao_id=negociacao.id)
    assert response.status_code == HTTPStatus.NOT_FOUND

    assert EventoExtraNegociacao.objects.count() == 0
    assert EventoExtraSolicitacaoPerna.objects.count() == 1
    assert EventoExtraSolicitacao.objects.count() == 1
    assert EventoExtra.objects.count() == 1


def test_create_or_update_evento_extra_solicitacao_perna(rf, user_rotas, solicitacao_perna_payload):
    request = rf.post("/api/staff/evento-extra/perna/save", solicitacao_perna_payload, content_type="application/json")
    request.user = user_rotas
    res = views_staff.evento_extra_solicitacao_perna_save(request)
    assert res.status_code == HTTPStatus.OK
    evento_extra_perna_qs = EventoExtraSolicitacaoPerna.objects.all()
    assert evento_extra_perna_qs.count() == 1

    evento_extra_perna = evento_extra_perna_qs[0]
    assert evento_extra_perna.sentido == solicitacao_perna_payload["sentido"]
    assert evento_extra_perna.rota is None
    assert evento_extra_perna.turno == solicitacao_perna_payload["turno"]
    assert evento_extra_perna.data == date.fromisoformat(solicitacao_perna_payload["data"])
    assert evento_extra_perna.hora == time.fromisoformat(solicitacao_perna_payload["hora"])

    rota = baker.make("core.Rota", origem__local__cidade__sigla="SAO", destino__local__cidade__sigla="RIO")
    new_datetime_ida = datetime(2025, 6, 30, 12, 0, 0, tzinfo=timezone.utc)
    new_data = new_datetime_ida.date()
    new_time = new_datetime_ida.time()

    solicitacao_perna_payload["id"] = evento_extra_perna.id
    solicitacao_perna_payload["sentido"] = "SAO-RIO"
    solicitacao_perna_payload["rota"] = {"id": rota.id, "sigla": "SAO-RIO"}
    solicitacao_perna_payload["turno"] = EventoExtraSolicitacaoPerna.Turno.MADRUGADA
    solicitacao_perna_payload["datetimeIda"] = new_datetime_ida
    solicitacao_perna_payload["data"] = new_data
    solicitacao_perna_payload["hora"] = new_time

    request_update = rf.post(
        "/api/staff/evento-extra/perna/save", solicitacao_perna_payload, content_type="application/json"
    )
    request_update.user = user_rotas
    res = views_staff.evento_extra_solicitacao_perna_save(request_update)
    assert res.status_code == HTTPStatus.OK
    assert EventoExtraSolicitacaoPerna.objects.count() == 1

    evento_extra_perna.refresh_from_db()
    assert evento_extra_perna.sentido == "SAO-RIO"
    assert evento_extra_perna.rota.id == rota.id
    assert evento_extra_perna.turno == EventoExtraSolicitacaoPerna.Turno.MADRUGADA
    assert evento_extra_perna.data == new_data
    assert evento_extra_perna.hora == new_time


def test_delete_eventos_extra_solicitacao_perna(rf, user_rotas):
    nomes = ["São João", "Carnaval"]
    # dadas duas solicitações de extras, uma com negociação em andamento e outra sem.
    solicitacao = baker.make(
        "core.EventoExtraSolicitacao",
        evento_extra__nome=iter(nomes),
    )
    # ambas com pernas cadastradas.
    solicitacao_perna = baker.make("core.EventoExtraSolicitacaoPerna", solicitacao_extra=solicitacao)
    baker.make(
        "core.EventoExtraNegociacao",
        solicitacao_extra=solicitacao,
        evento_extra=solicitacao.evento_extra,
    )

    request = rf.delete(f"/api/staff/evento-extra/perna/{solicitacao_perna.id}/delete")
    request.user = user_rotas
    response = views_staff.evento_extra_solicitacao_perna_delete(request, perna_id=solicitacao_perna.id)

    assert response.status_code == HTTPStatus.OK

    # se o evento não existir, a requisição deve retornar o status NOT_FOUND
    request = rf.delete(f"/api/staff/evento-extra/perna/{solicitacao_perna.id}/delete")
    request.user = user_rotas
    response = views_staff.evento_extra_solicitacao_perna_delete(request, perna_id=solicitacao_perna.id)
    assert response.status_code == HTTPStatus.NOT_FOUND

    assert EventoExtraSolicitacaoPerna.objects.count() == 0
    assert EventoExtraNegociacao.objects.count() == 1
    assert EventoExtraSolicitacao.objects.count() == 1
    assert EventoExtra.objects.count() == 1


def test_list_solicitacao_pernas(rf, user_rotas):
    # dado 3 eventos com solicitação de extras
    # para cada evento uma solicitação e para cada
    # solicitação uma perna
    nomes = ["São João", "Carnaval", "Páscoa"]
    status = ["pending", "doing", "done"]
    eventos = baker.make("core.EventoExtra", nome=iter(nomes), status=iter(status), _quantity=3, _bulk_create=True)
    eventos_ids = [evento.id for evento in eventos]
    solicitacoes = baker.make(
        "core.EventoExtraSolicitacao", evento_extra_id=iter(eventos_ids), _quantity=3, _bulk_create=True
    )
    baker.make(
        "core.EventoExtraSolicitacaoPerna",
        solicitacao_extra=iter(solicitacoes),
        sentido=iter(["RIO-SAO", "SAO-RIO", "BHZ-RIO"]),
        _quantity=3,
        _bulk_create=True,
    )
    paginator = {"rowsPerPage": 10, "page": 1, "sortBy": "data", "descending": False}
    filters = {"ids": None, "eventoExtraId": eventos_ids[0]}
    # ao solicitar a list de todas as pernas de um evento especifico
    request = rf.get(
        "api/staff/evento-extra/perna/list", {"filters": json.dumps(filters), "paginator": json.dumps(paginator)}
    )
    request.user = user_rotas
    response = views_staff.evento_extra_solicitacao_perna_list(request)

    assert response.status_code == HTTPStatus.OK
    # que seja retornado apenas as pernas do evento especifico
    items = json.loads(response.content)["items"]
    count = json.loads(response.content)["count"]
    assert count == 1
    assert solicitacoes[0].id == items[0]["solicitacaoExtraId"]
