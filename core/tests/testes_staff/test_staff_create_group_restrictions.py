import json
from datetime import date, datetime, time, timedelta

import time_machine
from django.test import Client, TestCase
from django.utils.timezone import get_default_timezone
from model_bakery import baker

from commons import dateutils
from core import views_staff
from core.models_company import Onibus
from core.models_grupo import Grupo, RestrictionBypass, RestrictionTypes
from core.models_rota import HUMANIZE_WEEKDAY, Checkpoint, LocalEmbarque, OnibusSuportado, Rota, Weekdays
from core.service.grupos_staff.grupo_crud_svc import get_datetime_ida_list
from core.tests import fixtures
from core.tests.prevent_useless_logs import prevent_request_warnings


class BaseListRestrictionTestCase(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.b = fixtures.cidades()
        cls.user_infra = fixtures.user_infra()

    def _make_local(self, **attrs):
        return baker.make(LocalEmbarque, cidade=self.b.cidade_sp, **attrs)

    @staticmethod
    def _make_route(*local_list):
        assert len(local_list) > 1
        rota = baker.make(Rota, origem=local_list[0], destino=local_list[-1])

        itinerario = []
        for idx, local in enumerate(local_list):
            cp = baker.make(
                Checkpoint,
                duracao=timedelta(hours=1) if idx else None,
                tempo_embarque=timedelta(minutes=15),
                rota=rota,
                local=local,
                idx=idx,
            )
            itinerario.append(cp)

        return rota, itinerario

    @staticmethod
    def _make_group(**attrs):
        return baker.make(Grupo, **attrs)

    def request_list_restrictions(self, rota_id, date_ida_list=None, time_ida=None, bus_id=None):
        client_infra = Client()
        client_infra.force_login(self.user_infra)

        if date_ida_list is None:
            today = date.today() + timedelta(days=1)
            date_ida_list = [today.strftime("%Y-%m-%d")]

        if time_ida is None:
            time_ida = "08:00"

        params = {"rota_id": rota_id, "date_ida_list": date_ida_list, "time_ida": time_ida}

        if bus_id:
            params["bus_id"] = bus_id

        return client_infra.post("/api/staff/rota/restricoes", {"params": json.dumps(params)})


class TestListRestrictionsOverbooking(BaseListRestrictionTestCase):
    def test_list_overbooking_at_first_boarding_point(self):
        local_a = self._make_local(max_embarque_simultaneo=2, nickname="Ponto TOP")
        local_b = self._make_local()
        route, itinerary = self._make_route(local_a, local_b)
        tomorrow = date.today() + timedelta(days=1)
        base_time_ida = time(8)
        base_dtida = dateutils.to_default_tz_required(datetime.combine(tomorrow, base_time_ida))
        base_time_ida_str = base_time_ida.strftime("%H:%M")
        dt_ida_gp2 = base_dtida - timedelta(minutes=5)
        self._make_group(rota=route, datetime_ida=base_dtida, status="travel_confirmed")
        self._make_group(rota=route, datetime_ida=dt_ida_gp2, status="travel_confirmed")

        response = self.request_list_restrictions(
            route.id, [base_dtida.strftime("%Y-%m-%d")], f"{base_time_ida_str} 11:11+3"
        )

        listed_restrictions = response.json()
        overbooking_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "max_embarque_simultaneo"]
        expected = (
            f'O Local de embarque "{local_a.nickname}" ficará sobrecarregado '
            f"partindo às {base_time_ida_str} em {base_dtida.strftime('%d/%m/%Y')}"
        )
        self.assertIn(expected, overbooking_restrictions)

    @time_machine.travel(datetime(2025, 5, 7, 13))
    def test_list_overbooking_at_first_boarding_point_bypass_not_allowed(self):
        local_a = self._make_local(max_embarque_simultaneo=2, nickname="Ponto TOP", impede_bypass=True)
        local_b = self._make_local()
        route, itinerary = self._make_route(local_a, local_b)
        tomorrow = date.today() + timedelta(days=1)
        base_time_ida = time(8)
        base_dtida = dateutils.to_default_tz_required(datetime.combine(tomorrow, base_time_ida))
        base_time_ida_str = base_time_ida.strftime("%H:%M")
        dt_ida_gp2 = base_dtida - timedelta(minutes=5)
        self._make_group(rota=route, datetime_ida=base_dtida, status="travel_confirmed")
        self._make_group(rota=route, datetime_ida=dt_ida_gp2, status="travel_confirmed")

        response = self.request_list_restrictions(
            route.id, [base_dtida.strftime("%Y-%m-%d")], f"{base_time_ida_str} 11:11+3"
        )

        listed_restrictions = response.json()
        assert listed_restrictions == [
            {
                "label": 'O Local de embarque "Ponto TOP" ficará sobrecarregado partindo às 08:00 em 08/05/2025',
                "local": local_a.id,
                "type": "max_embarque_simultaneo",
            },
            {
                "label": 'O Local de embarque "Ponto TOP" não pode ser usado com bypass nas restrições que foram quebradas',
                "local": local_a.id,
                "type": "local_embarque_impede_bypass",
            },
        ]

    def test_no_overbooking_restrictions_without_time_ida(self):
        local_a = self._make_local(max_embarque_simultaneo=2, nickname="Ponto TOP")
        local_b = self._make_local()
        route, _ = self._make_route(local_a, local_b)
        tomorrow = date.today() + timedelta(days=1)
        base_time_ida = time(8)
        base_dtida = dateutils.to_default_tz_required(datetime.combine(tomorrow, base_time_ida))
        dt_ida_gp2 = base_dtida - timedelta(minutes=5)
        self._make_group(rota=route, datetime_ida=base_dtida, status="travel_confirmed")
        self._make_group(rota=route, datetime_ida=dt_ida_gp2, status="travel_confirmed")

        response = self.request_list_restrictions(route.id, [base_dtida.strftime("%Y-%m-%d")], "2")

        listed_restrictions = response.json()
        self.assertEqual(len(listed_restrictions), 0)

    def test_list_overbooking_desconsider_canceled_groups(self):
        local_a = self._make_local(max_embarque_simultaneo=2, nickname="Ponto TOP")
        local_b = self._make_local()
        route, itinerary = self._make_route(local_a, local_b)
        tomorrow = date.today() + timedelta(days=1)
        base_time_ida = time(8)
        base_dtida = dateutils.to_default_tz_required(datetime.combine(tomorrow, base_time_ida))
        base_time_ida_str = base_time_ida.strftime("%H:%M")
        dt_ida_gp2 = base_dtida - timedelta(minutes=5)
        self._make_group(rota=route, datetime_ida=base_dtida, status="canceled", confirming_probability="high")
        self._make_group(rota=route, datetime_ida=dt_ida_gp2, status="canceled", confirming_probability="high")

        response = self.request_list_restrictions(route.id, [base_dtida.strftime("%Y-%m-%d")], base_time_ida_str)

        listed_restrictions = response.json()
        overbooking_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "max_embarque_simultaneo"]
        self.assertFalse(overbooking_restrictions)

    def test_list_overbooking_over_multiple_dates(self):
        local_a = self._make_local(max_embarque_simultaneo=2, nickname="Ponto TOP")
        local_b = self._make_local()
        route, itinerary = self._make_route(local_a, local_b)
        tomorrow = date.today() + timedelta(days=1)
        next_week = date.today() + timedelta(days=7)
        base_time_ida = time(8)
        base_dtida = dateutils.to_default_tz_required(datetime.combine(tomorrow, base_time_ida))
        base_dtida_1 = dateutils.to_default_tz_required(datetime.combine(next_week, base_time_ida))
        base_time_ida_str = base_dtida.strftime("%H:%M")
        dt_ida_gp2 = base_dtida - timedelta(minutes=5)
        dt_ida_gp4 = base_dtida_1 - timedelta(minutes=5)
        self._make_group(rota=route, datetime_ida=base_dtida, status="travel_confirmed")
        self._make_group(rota=route, datetime_ida=dt_ida_gp2, status="travel_confirmed")
        self._make_group(rota=route, datetime_ida=base_dtida_1, status="travel_confirmed")
        self._make_group(rota=route, datetime_ida=dt_ida_gp4, status="travel_confirmed")

        response = self.request_list_restrictions(
            route.id, [base_dtida.strftime("%Y-%m-%d"), base_dtida_1.strftime("%Y-%m-%d")], base_time_ida_str
        )

        listed_restrictions = response.json()
        overbooking_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "max_embarque_simultaneo"]

        expected = (
            f'O Local de embarque "{local_a.nickname}" ficará sobrecarregado '
            f"partindo às {base_time_ida_str} em {base_dtida.strftime('%d/%m/%Y')}"
        )
        self.assertIn(expected, overbooking_restrictions)

        expected = (
            f'O Local de embarque "{local_a.nickname}" ficará sobrecarregado '
            f"partindo às {base_time_ida_str} em {base_dtida_1.strftime('%d/%m/%Y')}"
        )
        self.assertIn(expected, overbooking_restrictions)

    def test_list_overbooking_over_multiple_boarding_points(self):
        local_a = self._make_local(max_embarque_simultaneo=2, nickname="Ponto TOP")
        local_b = self._make_local(max_embarque_simultaneo=2, nickname="Ponto não tão TOP")
        route, itinerary = self._make_route(local_a, local_b)
        tomorrow = date.today() + timedelta(days=1)
        base_time_ida = time(8)
        base_dtida = dateutils.to_default_tz_required(datetime.combine(tomorrow, base_time_ida))
        base_time_ida_str = base_time_ida.strftime("%H:%M")
        dt_ida_gp2 = base_dtida - timedelta(minutes=5)
        dt_ida_gp3 = base_dtida - timedelta(hours=6)
        self._make_group(rota=route, datetime_ida=base_dtida, status="travel_confirmed")
        self._make_group(rota=route, datetime_ida=dt_ida_gp2, status="travel_confirmed")
        self._make_group(rota=route, datetime_ida=dt_ida_gp3, status="travel_confirmed")

        response = self.request_list_restrictions(route.id, [base_dtida.strftime("%Y-%m-%d")], base_time_ida_str)

        listed_restrictions = response.json()
        overbooking_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "max_embarque_simultaneo"]

        expected = (
            f'O Local de embarque "{local_a.nickname}" ficará sobrecarregado '
            f"partindo às {base_time_ida_str} em {base_dtida.strftime('%d/%m/%Y')}"
        )
        self.assertIn(expected, overbooking_restrictions)

        expected = (
            f'O Local de embarque "{local_b.nickname}" ficará sobrecarregado '
            f"partindo às {base_time_ida_str} em {base_dtida.strftime('%d/%m/%Y')}"
        )
        self.assertIn(expected, overbooking_restrictions)

    def test_list_overbooking_limit_of_one(self):
        local_a = self._make_local(max_embarque_simultaneo=1, nickname="Ponto TOP")
        local_b = self._make_local()
        route, itinerary = self._make_route(local_a, local_b)
        tomorrow = date.today() + timedelta(days=1)
        base_time_ida = time(8)
        base_dtida = dateutils.to_default_tz_required(datetime.combine(tomorrow, base_time_ida))
        base_time_ida_str = base_time_ida.strftime("%H:%M")
        self._make_group(rota=route, datetime_ida=base_dtida, status="travel_confirmed")

        response = self.request_list_restrictions(route.id, [base_dtida.strftime("%Y-%m-%d")], base_time_ida_str)

        listed_restrictions = response.json()
        overbooking_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "max_embarque_simultaneo"]
        expected = (
            f'O Local de embarque "{local_a.nickname}" ficará sobrecarregado '
            f"partindo às {base_time_ida_str} em {base_dtida.strftime('%d/%m/%Y')}"
        )
        self.assertIn(expected, overbooking_restrictions)

    def test_list_overbooking_within_limit(self):
        local_a = self._make_local(max_embarque_simultaneo=2, nickname="Ponto TOP")
        local_b = self._make_local()
        route, itinerary = self._make_route(local_a, local_b)
        date_ida = date.today() + timedelta(days=2)
        base_time_ida = time(8)
        base_dtida = dateutils.to_default_tz_required(datetime.combine(date_ida, base_time_ida))
        base_time_ida_str = base_time_ida.strftime("%H:%M")
        self._make_group(rota=route, datetime_ida=base_dtida, status="travel_confirmed")
        self._make_group(rota=route, datetime_ida=base_dtida - timedelta(days=1), status="travel_confirmed")

        response = self.request_list_restrictions(route.id, [base_dtida.strftime("%Y-%m-%d")], base_time_ida_str)

        listed_restrictions = response.json()
        overbooking_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "max_embarque_simultaneo"]
        self.assertFalse(overbooking_restrictions)

    def test_list_overbooking_within_limit_and_off_limit(self):
        local_a = self._make_local(max_embarque_simultaneo=1, nickname="Ponto TOP")
        local_b = self._make_local()
        route, itinerary = self._make_route(local_a, local_b)
        date_ida = date.today() + timedelta(days=2)
        base_time_ida = time(8)
        base_dtida = dateutils.to_default_tz_required(datetime.combine(date_ida, base_time_ida))
        base_time_ida_str = base_time_ida.strftime("%H:%M")
        self._make_group(rota=route, datetime_ida=base_dtida, status="travel_confirmed")

        within_limit_date = base_dtida - timedelta(days=1)
        response = self.request_list_restrictions(
            route.id, [within_limit_date.strftime("%Y-%m-%d"), base_dtida.strftime("%Y-%m-%d")], base_time_ida_str
        )

        listed_restrictions = response.json()
        overbooking_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "max_embarque_simultaneo"]
        expected = (
            f'O Local de embarque "{local_a.nickname}" ficará sobrecarregado '
            f"partindo às {base_time_ida_str} em {base_dtida.strftime('%d/%m/%Y')}"
        )
        self.assertIn(expected, overbooking_restrictions)


class TestListRestrictionsBusType(BaseListRestrictionTestCase):
    @staticmethod
    def _make_supported_bus(**attrs):
        return baker.make(OnibusSuportado, **attrs)

    @staticmethod
    def _make_bus(**attrs):
        return baker.make(Onibus, **attrs)

    def test_list_overbooking_unsupported_bus(self):
        local_a = self._make_local(nickname="Ponto TOP")
        self._make_supported_bus(local=local_a, tipo="trucado")
        local_b = self._make_local(nickname="Ponto não tão TOP")
        self._make_supported_bus(local=local_b, tipo="toco")
        route, _ = self._make_route(local_a, local_b)
        bus = self._make_bus(tipo="trucado")

        response = self.request_list_restrictions(route.id, bus_id=bus.id)

        listed_restrictions = response.json()
        supported_bus_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "onibus_suportados"]
        self.assertEqual(1, len(supported_bus_restrictions))

        expected = f'O Local de embarque "{local_b.nickname}" não suporta onibus do tipo "trucado"'
        self.assertIn(expected, supported_bus_restrictions)

    def test_list_overbooking_no_supported_bus(self):
        local_a = self._make_local(nickname="Ponto TOP")
        local_b = self._make_local(nickname="Ponto não tão TOP")
        route, _ = self._make_route(local_a, local_b)
        bus = self._make_bus(tipo="trucado")

        response = self.request_list_restrictions(route.id, bus_id=bus.id)

        listed_restrictions = response.json()
        supported_bus_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "onibus_suportados"]
        self.assertFalse(supported_bus_restrictions)

    def test_list_overbooking_supported_bus(self):
        local_a = self._make_local(nickname="Ponto TOP")
        self._make_supported_bus(local=local_a, tipo="trucado")
        local_b = self._make_local(nickname="Ponto não tão TOP")
        self._make_supported_bus(local=local_b, tipo="trucado")
        route, _ = self._make_route(local_a, local_b)
        bus = self._make_bus(tipo="trucado")

        response = self.request_list_restrictions(route.id, bus_id=bus.id)

        listed_restrictions = response.json()
        supported_bus_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "onibus_suportados"]
        self.assertEqual(0, len(supported_bus_restrictions))

    def test_list_overbooking_supported_bus_case_sensitive(self):
        local_a = self._make_local(nickname="Ponto TOP")
        self._make_supported_bus(local=local_a, tipo="dd")
        local_b = self._make_local(nickname="Ponto não tão TOP")
        self._make_supported_bus(local=local_b, tipo="dd")
        route, _ = self._make_route(local_a, local_b)
        bus = self._make_bus(tipo="DD")

        response = self.request_list_restrictions(route.id, bus_id=bus.id)

        listed_restrictions = response.json()
        supported_bus_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "onibus_suportados"]
        self.assertEqual(0, len(supported_bus_restrictions))

    def test_list_supported_bus_type_null(self):
        local_a = self._make_local(nickname="Ponto TOP")
        self._make_supported_bus(local=local_a, tipo="dd")
        local_b = self._make_local()
        route, _ = self._make_route(local_a, local_b)
        bus = self._make_bus(tipo=None)

        response = self.request_list_restrictions(route.id, bus_id=bus.id)

        listed_restrictions = response.json()
        supported_bus_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "onibus_suportados"]
        self.assertEqual(0, len(supported_bus_restrictions))

    def test_list_restrictions_includes_local_in_response(self):
        local_a = self._make_local(nickname="Ponto TOP")
        self._make_supported_bus(local=local_a, tipo="trucado")
        local_b = self._make_local(nickname="Ponto não tão TOP")
        self._make_supported_bus(local=local_b, tipo="trucado")
        route, _ = self._make_route(local_a, local_b)
        bus = self._make_bus(tipo="dd")

        response = self.request_list_restrictions(route.id, bus_id=bus.id)

        listed_restrictions = response.json()
        listed_local = [r["local"] for r in listed_restrictions if r["type"] == "onibus_suportados"]
        self.assertCountEqual([local_a.id, local_b.id], listed_local)


class TestListRestrictionsWorkingHours(BaseListRestrictionTestCase):
    @staticmethod
    def _make_working_days(local, wh_list):
        return [baker.make(Weekdays, local=local, **wh) for wh in wh_list]

    def test_list_group_not_within_weekdays(self):
        local_a = self._make_local(nickname="Ponto TOP", start_time=time(8), end_time=time(18))
        self._make_working_days(local_a, [{"dia": 1}, {"dia": 3}, {"dia": 5}])  # Seg, Qua, Sex
        local_b = self._make_local(nickname="Ponto não tão TOP")
        route, _ = self._make_route(local_a, local_b)
        today = date.today()
        dtida = today + timedelta((1 - today.weekday()) % 7)  # proxima terça
        start_use_datetime = datetime.combine(dtida, time(8, 30))
        end_use_datetime = datetime.combine(dtida, time(9, 00))
        strf_pattern = "%d/%m/%Y %H:%M"

        response = self.request_list_restrictions(route.id, date_ida_list=[dtida], time_ida="09:00")

        listed_restrictions = response.json()
        supported_bus_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "weekdays"]
        expected = (
            f'O Local de embarque "{local_a.nickname}" não funciona Terça '
            f"nesta configuração o ônibus chegará no local em {start_use_datetime.strftime(strf_pattern)} "
            f"e partirá as {end_use_datetime.strftime(strf_pattern)}"
        )
        self.assertIn(expected, supported_bus_restrictions)

    def test_list_group_not_within_weekdays_sunday(self):
        local_a = self._make_local(nickname="Ponto TOP", start_time=time(8), end_time=time(18))
        self._make_working_days(local_a, [{"dia": 1}, {"dia": 3}, {"dia": 5}])  # Seg, Qua, Sex
        local_b = self._make_local(nickname="Ponto não tão TOP")
        route, _ = self._make_route(local_a, local_b)
        today = date.today()
        dtida = today + timedelta((6 - today.weekday()) % 7)  # proximo domingo
        start_use_datetime = datetime.combine(dtida, time(8, 30))
        end_use_datetime = datetime.combine(dtida, time(9, 0))
        strf_pattern = "%d/%m/%Y %H:%M"

        response = self.request_list_restrictions(route.id, date_ida_list=[dtida], time_ida="09:00")

        listed_restrictions = response.json()
        supported_bus_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "weekdays"]
        expected = (
            f'O Local de embarque "{local_a.nickname}" não funciona Domingo '
            f"nesta configuração o ônibus chegará no local em {start_use_datetime.strftime(strf_pattern)} "
            f"e partirá as {end_use_datetime.strftime(strf_pattern)}"
        )
        self.assertIn(expected, supported_bus_restrictions)

    def test_list_group_not_within_weekdays_midnight(self):
        local_a = self._make_local(nickname="Ponto TOP")
        local_b = self._make_local(nickname="Ponto não tão TOP", start_time=time(22), end_time=time(5))
        self._make_working_days(local_b, [{"dia": 7}, {"dia": 3}, {"dia": 5}])  # não funciona segunda
        route, _ = self._make_route(local_a, local_b)
        today = date.today()
        dtida = today + timedelta((6 - today.weekday()) % 7)  # proximo domingo
        start_use_datetime = datetime.combine(dtida + timedelta(days=1), time(0, 45))
        end_use_datetime = datetime.combine(dtida + timedelta(days=1), time(1, 5))
        strf_pattern = "%d/%m/%Y %H:%M"

        response = self.request_list_restrictions(route.id, date_ida_list=[dtida], time_ida="23:30")

        listed_restrictions = response.json()
        supported_bus_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "weekdays"]
        expected = (
            f'O Local de embarque "{local_b.nickname}" não funciona Segunda '
            f"nesta configuração o ônibus chegará no local em {start_use_datetime.strftime(strf_pattern)} "
            f"e partirá as {end_use_datetime.strftime(strf_pattern)}"
        )
        self.assertIn(expected, supported_bus_restrictions)

    def test_list_group_not_within_working_hours(self):
        local_a = self._make_local(nickname="Ponto TOP", start_time=time(8), end_time=time(18))
        wh_list = [
            {"dia": 1, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 2, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 3, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 4, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 5, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 6, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 7, "start_time": "08:00", "end_time": "18:00"},
        ]
        self._make_working_days(local_a, wh_list)
        local_b = self._make_local(nickname="Ponto não tão TOP")
        route, _ = self._make_route(local_a, local_b)
        tomorrow = date.today() + timedelta(days=1)
        dia_str = HUMANIZE_WEEKDAY[tomorrow.isoweekday()]

        response = self.request_list_restrictions(route.id, date_ida_list=[tomorrow], time_ida="07:00")

        listed_restrictions = response.json()
        supported_bus_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "working_hours"]
        expected = (
            f'O Local de embarque "Ponto TOP" não funciona {dia_str} das 06:30 às 07:00 '
            f"(funciona {dia_str} das 08:00 às 18:00)"
        )
        self.assertIn(expected, supported_bus_restrictions)

    def test_list_group_not_within_working_hours_midnight(self):
        local_a = self._make_local(nickname="Ponto TOP", start_time=time(8), end_time=time(18))
        wh_list = [
            {"dia": 1, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 2, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 3, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 4, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 5, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 6, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 7, "start_time": "08:00", "end_time": "18:00"},
        ]
        self._make_working_days(local_a, wh_list)
        local_b = self._make_local(nickname="Ponto não tão TOP")
        route, _ = self._make_route(local_a, local_b)
        tomorrow = date.today() + timedelta(days=1)

        response = self.request_list_restrictions(route.id, date_ida_list=[tomorrow], time_ida="00:10")

        listed_restrictions = response.json()
        dia_ant_str = HUMANIZE_WEEKDAY[date.today().isoweekday()]
        dia_str = HUMANIZE_WEEKDAY[tomorrow.isoweekday()]
        supported_bus_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "working_hours"]
        expected = (
            f'O Local de embarque "Ponto TOP" não funciona {dia_ant_str}/{dia_str} das 23:40 às 00:10 '
            f"(funciona {dia_ant_str} das 08:00 às 18:00 e {dia_str} das 08:00 às 18:00)"
        )
        self.assertIn(expected, supported_bus_restrictions)

    def test_list_group_within_working_hours(self):
        local_a = self._make_local(nickname="Ponto TOP", start_time=time(8), end_time=time(18))
        wh_list = [
            {"dia": 1, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 2, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 3, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 4, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 5, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 6, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 7, "start_time": "08:00", "end_time": "18:00"},
        ]
        self._make_working_days(local_a, wh_list)
        local_b = self._make_local(nickname="Ponto não tão TOP")
        route, _ = self._make_route(local_a, local_b)
        tomorrow = date.today() + timedelta(days=1)

        response = self.request_list_restrictions(route.id, date_ida_list=[tomorrow], time_ida="09:00")

        listed_restrictions = response.json()
        supported_bus_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "working_hours"]
        self.assertEqual(0, len(supported_bus_restrictions))

    def test_list_group_within_working_hours_limit(self):
        local_a = self._make_local(nickname="Ponto TOP", start_time=time(8), end_time=time(18))
        wh_list = [
            {"dia": 1, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 2, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 3, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 4, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 5, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 6, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 7, "start_time": "08:00", "end_time": "18:00"},
        ]
        self._make_working_days(local_a, wh_list)
        local_b = self._make_local(nickname="Ponto não tão TOP")
        route, _ = self._make_route(local_a, local_b)
        tomorrow = date.today() + timedelta(days=1)

        response = self.request_list_restrictions(route.id, date_ida_list=[tomorrow], time_ida="08:30")

        listed_restrictions = response.json()
        supported_bus_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "working_hours"]
        self.assertEqual(0, len(supported_bus_restrictions))

    def test_list_group_working_hours_start_time_null(self):
        local_a = self._make_local(nickname="Ponto TOP")
        wh_list = [
            {"dia": 1, "start_time": "08:00", "end_time": "18:00"},
            {"dia": 2, "start_time": None, "end_time": "18:00"},
        ]
        self._make_working_days(local_a, wh_list)
        local_b = self._make_local(nickname="Ponto não tão TOP")
        route, _ = self._make_route(local_a, local_b)
        tomorrow = date.today() + timedelta(days=1)

        response = self.request_list_restrictions(route.id, date_ida_list=[tomorrow], time_ida="08:30")

        listed_restrictions = response.json()
        supported_bus_restrictions = [r["label"] for r in listed_restrictions if r["type"] == "working_hours"]
        self.assertEqual(0, len(supported_bus_restrictions))

    @time_machine.travel("2025-09-01 06:00:00", tick=False)  # Segunda
    def test_restricoes_embarques_simultaneos_por_dia_e_horario(self):
        pde_partida = self._make_local(max_embarque_simultaneo=0, nickname="Ponto partida")
        pde_chegada = self._make_local(max_embarque_simultaneo=0, nickname="Ponto chegada")

        resticoes_pde_partida = [
            {"dia": 1, "start_time": "08:00", "end_time": "18:00", "max_embarque_simultaneo": 3},  # Segunda
            {"dia": 5, "start_time": "08:00", "end_time": "18:00", "max_embarque_simultaneo": 1},  # Sexta
            {"dia": 6, "start_time": "08:00", "end_time": "18:00", "max_embarque_simultaneo": 0},  # Sábado(ilimitado)
        ]
        # Ponto de chegada Segunda a sexta limitado a 1, sábado ilimitado
        restricoes_pde_chegada = [
            {"dia": i, "start_time": "08:00", "end_time": "22:00", "max_embarque_simultaneo": 1 if i != 6 else 0}
            for i in range(1, 7)
        ]

        self._make_working_days(pde_partida, resticoes_pde_partida)
        self._make_working_days(pde_chegada, restricoes_pde_chegada)
        route, _ = self._make_route(pde_partida, pde_chegada)

        # 1 Grupo na segunda (dia 1) e 2 grupos na sexta (dia 5)
        dt_ida_sexta = datetime(2025, 9, 5, 10, 0, tzinfo=get_default_timezone())
        self._make_group(rota=route, datetime_ida=dt_ida_sexta, status="travel_confirmed")
        dt_ida_segunda = datetime(2025, 9, 1, 10, 0, tzinfo=get_default_timezone())
        self._make_group(rota=route, datetime_ida=dt_ida_segunda, status="travel_confirmed")
        self._make_group(rota=route, datetime_ida=dt_ida_segunda - timedelta(minutes=5), status="travel_confirmed")

        # Tentando encaixar grupos na segunda (dia 1) e sexta (dia 5) partindo às 10:00
        response = self.request_list_restrictions(route.id, ["2025-09-01", "2025-09-05"], "10:00")

        expected_restrictions = [
            {
                "type": "max_embarque_simultaneo",
                "label": 'O Local de embarque "Ponto partida" ficará sobrecarregado partindo às 10:00 em 05/09/2025',
                "local": pde_partida.id,
            },
            {
                "type": "max_embarque_simultaneo",
                "label": 'O Local de embarque "Ponto chegada" ficará sobrecarregado partindo às 10:00 em 01/09/2025',
                "local": pde_chegada.id,
            },
            {
                "type": "max_embarque_simultaneo",
                "label": 'O Local de embarque "Ponto chegada" ficará sobrecarregado partindo às 10:00 em 05/09/2025',
                "local": pde_chegada.id,
            },
        ]

        assert response.json() == expected_restrictions

        # Sexta e segunda, partindo uma da tarde não deve haver overlapping de horários
        response = self.request_list_restrictions(route.id, ["2025-09-01", "2025-09-05"], "13:00")
        assert len(response.json()) == 0

        # Sábado não deve ter restrições, pois o local de partida é ilimitado (0)
        dt_sabado = datetime(2025, 9, 6, 10, 0, tzinfo=get_default_timezone())
        [self._make_group(rota=route, datetime_ida=dt_sabado, status="travel_confirmed") for _ in range(3)]
        response = self.request_list_restrictions(route.id, ["2025-09-06"], "10:00")
        assert len(response.json()) == 0


class TestBypassRestriction(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user_staff = fixtures.user_staff()

    def request_create_bypass(self, bypass_reason, restriction_type, groups, custom_props=None):
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        local_id = baker.make(LocalEmbarque).id

        rtype_list = restriction_type if isinstance(restriction_type, list) else [restriction_type]
        params = {
            "reason": bypass_reason,
            "restrictions": {rtype: [{"label": "", "local": local_id}] for rtype in rtype_list},
            "groups": [g.id for g in (groups if isinstance(groups, list) else [groups])],
        }

        if custom_props:
            params.update(custom_props)

        return client_staff.post("/api/staff/rota/restricoes/bypass", params, content_type="application/json")

    def test_bypass_restriction(self):
        group = baker.make(Grupo)
        expected_reason = "Algum motivo"

        response = self.request_create_bypass(expected_reason, RestrictionTypes.WEEKDAYS, group)

        self.assertEqual(200, response.status_code)
        bypass_id = response.json()["bypass"][0]["id"]
        bypass = RestrictionBypass.objects.get(pk=bypass_id)
        self.assertEqual(expected_reason, bypass.reason)
        self.assertEqual(bypass.user_id, self.user_staff.id)

    def test_bypass_restriction_no_type_should_raise(self):
        group = baker.make(Grupo)

        with prevent_request_warnings():
            response = self.request_create_bypass("whatever", None, group)

        self.assertEqual(400, response.status_code)

    def test_bypass_restriction_invalid_type_should_raise(self):
        group = baker.make(Grupo)

        with prevent_request_warnings():
            response = self.request_create_bypass("whatever", "TIPOINVALIDO", group)

        self.assertEqual(400, response.status_code)

    def test_bypass_restriction_valid_types(self):
        group = baker.make(Grupo)

        for r_type in RestrictionTypes.values:
            response = self.request_create_bypass("whatever", r_type, group)

            self.assertEqual(200, response.status_code)
            bypass_id = response.json()["bypass"][0]["id"]
            bypass = RestrictionBypass.objects.get(id=bypass_id)
            self.assertIn(r_type, bypass.restriction_types)

    def test_bypass_restriction_no_reason_should_raise(self):
        group = baker.make(Grupo)

        with prevent_request_warnings():
            response = self.request_create_bypass(None, RestrictionTypes.WEEKDAYS, group)

        self.assertEqual(400, response.status_code)

    def test_bypass_restriction_multiple_types(self):
        group = baker.make(Grupo)
        restriction_type = [RestrictionTypes.WEEKDAYS, RestrictionTypes.WORKING_HOURS]

        response = self.request_create_bypass("Algum motivo", restriction_type, group)

        self.assertEqual(200, response.status_code)
        bypass_id = response.json()["bypass"][0]["id"]
        bypass = RestrictionBypass.objects.get(pk=bypass_id)
        self.assertCountEqual(restriction_type, bypass.restriction_types)

    def test_bypass_restriction_multiple_groups_and_types(self):
        group_a = baker.make(Grupo)
        group_b = baker.make(Grupo)
        restriction_type = [RestrictionTypes.WEEKDAYS, RestrictionTypes.WORKING_HOURS]
        expected_reason = "Algum motivo"

        response = self.request_create_bypass(expected_reason, restriction_type, [group_a, group_b])

        self.assertEqual(200, response.status_code)
        bypass_id = response.json()["bypass"][0]["id"]
        bypass = RestrictionBypass.objects.get(pk=bypass_id)
        groups = list(bypass.groups.all())
        self.assertEqual(2, len(groups))

    def test_bypass_restriction_created_at(self):
        group = baker.make(Grupo)
        response = self.request_create_bypass("Algum motivo", RestrictionTypes.WEEKDAYS, group)

        self.assertEqual(200, response.status_code)
        bypass_id = response.json()["bypass"][0]["id"]
        bypass = RestrictionBypass.objects.get(pk=bypass_id)
        self.assertTrue(bypass.created_at)

    def test_bypass_restriction_null(self):
        group = baker.make(Grupo)

        with prevent_request_warnings():
            response = self.request_create_bypass(None, None, group, custom_props={"bypass": "null"})

        self.assertEqual(400, response.status_code)

    def test_bypass_restriction_non_existing_groups(self):
        with prevent_request_warnings():
            response = self.request_create_bypass(
                "asdf", RestrictionTypes.WORKING_HOURS, [], custom_props={"groups": [1, 2, 3]}
            )

        self.assertEqual(400, response.status_code)


def test_get_datetime_ida_list():
    date_ida_list = [date(2022, 2, 2), date(2022, 2, 3)]
    time_ida_str = "11:11-1 21:21 22:22 r93xh2 13:13+1"
    datetime_ida_list = get_datetime_ida_list(date_ida_list, time_ida_str)
    assert sorted(datetime_ida_list) == [
        datetime(2022, 2, 1, 11, 11),
        datetime(2022, 2, 2, 11, 11),
        datetime(2022, 2, 2, 21, 21),
        datetime(2022, 2, 2, 22, 22),
        datetime(2022, 2, 3, 13, 13),
        datetime(2022, 2, 3, 21, 21),
        datetime(2022, 2, 3, 22, 22),
        datetime(2022, 2, 4, 13, 13),
    ]


def test_local_datas_proibidas(rf, user_rotas):
    local_liberado = baker.make(LocalEmbarque)
    local_proibido = baker.make(
        LocalEmbarque,
        dias_proibidos=[date(2025, 3, 1), date(2025, 3, 2)],  # Carnaval
    )

    rota = baker.make(Rota, origem=local_liberado, destino=local_proibido)

    itinerario = []
    for idx, local in enumerate([local_liberado, local_proibido]):
        cp = baker.make(
            Checkpoint,
            duracao=timedelta(hours=1) if idx else None,
            tempo_embarque=timedelta(minutes=15),
            rota=rota,
            local=local,
            idx=idx,
        )
        itinerario.append(cp)

    params = {"rota_id": rota.id, "date_ida_list": [date(2025, 3, 1)], "time_ida": "11:00", "bus_id": None}

    request = rf.post("/api/staff/rota/restricoes", {"params": json.dumps(params)})
    request.user = user_rotas
    response = views_staff.list_route_restrictions(request)

    assert response.status_code == 200
    assert json.loads(response.getvalue()) == [
        {
            "type": "dias_proibidos",
            "label": f'O Local de embarque "{local_proibido.nickname}" não pode ser usado nos dias {local_proibido.dias_proibidos}',
            "local": local_proibido.id,
        }
    ]


def test_local_modelo_venda_proibido(rf, user_rotas):
    local_liberado = baker.make(LocalEmbarque)
    local_proibido = baker.make(LocalEmbarque, modelos_venda=["Marketplace"])

    rota = baker.make(Rota, origem=local_liberado, destino=local_proibido)

    itinerario = []
    for idx, local in enumerate([local_liberado, local_proibido]):
        cp = baker.make(Checkpoint, rota=rota, local=local, idx=idx)
        itinerario.append(cp)

    params = {
        "rota_id": rota.id,
        "date_ida_list": [date(2025, 3, 1)],
        "time_ida": "11:00",
        "bus_id": None,
        "modelo_venda": "buser",
    }

    request = rf.post("/api/staff/rota/restricoes", {"params": json.dumps(params)})
    request.user = user_rotas
    response = views_staff.list_route_restrictions(request)

    assert response.status_code == 200
    assert json.loads(response.getvalue()) == [
        {
            "type": "modelo_venda_nao_suportado_no_local",
            "label": f'O Local de embarque "{local_proibido.nickname}" não pode ser para grupos do modelo de venda buser',
            "local": local_proibido.id,
        }
    ]
