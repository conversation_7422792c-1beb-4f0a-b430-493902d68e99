import json
from datetime import date, datetime, time, timedelta, timezone

import pytest
from django.test import TestCase
from django.test.client import Client
from model_bakery import baker

from commons.bunch import Bunch
from commons.dateutils import now, today
from core.forms.staff_forms import UpdateLocalEmbarqueForm
from core.models_rota import Cidade, GruposLocalEmbarque, LocalEmbarque, LocalEmbarqueInternalDetails, Weekdays
from core.serializers.serializer_locais import LocalEmbarqueSerializer
from core.service import localadm_svc
from core.service.locais_embarque_svc import calculate_arrival_for_first_pde
from core.tests import fixtures
from core.tests.common_operations import assert_has_expected_log_types_occurrences
from core.tests.prevent_useless_logs import prevent_request_warnings
from core.tests.testes_reserva.test_reserva_base import TestReservaBase
from core.views_staff import create_local, list_locais, list_locais_simple, update_local

BASE_DATE = now() + timedelta(hours=1)
BASE_DATE_STR = BASE_DATE.strftime("%Y-%m-%d")


class TestStaffLocais(TestReservaBase):
    @classmethod
    def setUpTestData(cls):
        cls.b = Bunch()
        fixtures.cidades(cls.b)
        cls.user_staff = fixtures.user_staff()
        cls.user_infra = fixtures.user_infra()

    def test_staff_create_list_locais(self):
        b = TestStaffLocais.b
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        local = self._create_local_embarque(b.cidade_bh.id)
        self.assertEqual(local["cidade_id"], b.cidade_bh.id)
        self.assertEqual(local["endereco_slug"], "rua-voluntarios-da-patria-344-santana")
        self.assertEqual(local["bairro_slug"], "santana")
        self.assertEqual(local["nickname_slug"], "estacionamento-tiete")

        local_obj = LocalEmbarque.objects.get(pk=local["id"])
        self.assertEqual(f"estacionamento-tiete-{b.cidade_bh.slug}", local_obj.slug)

        locais = self.list_locais(
            client_staff, {"paginator": {"descending": False, "page": 1, "rowsPerPage": 100, "sortBy": "nickname"}}
        )
        self.assertEqual(len(locais), 1)

    @pytest.mark.skip
    def test_staff_create_list_locais_sort_by_list(self):
        b = TestStaffLocais.b
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        local = self._create_local_embarque(b.cidade_bh.id, nickname="0aa1 Primeiro da lista :d")
        active_local2 = self._create_local_embarque(b.cidade_bh.id, nickname="0aa1 Segundo da lista :d")
        update_local_dict = {"id": active_local2["id"], "ativo": False}
        self._update_local_embarque(update_local_dict)
        self._create_local_embarque(b.cidade_bh.id)
        locais = self.list_locais(
            client_staff,
            {"paginator": {"descending": False, "page": 1, "rowsPerPage": 100, "sortBy": "nickname"}},
        )
        self.assertEqual(3, len(locais))
        self.assertEqual(local["id"], locais[0]["id"])
        self.assertEqual(active_local2["id"], locais[1]["id"])

    def test_staff_create_list_locais_count_grupos_consider_only_active_groups(self):
        b = fixtures.grupos_e_trechos()
        baker.make(GruposLocalEmbarque, count_date=today(), count=1, local_id=b.local_bh_lagoapatos.id)

        fixtures.grupo_classe_bhsp(b, now(), 0, "canceled")
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        locais = self.list_locais(
            client_staff,
            {"paginator": {"descending": False, "page": 1, "rowsPerPage": 100, "sortBy": "nickname"}},
        )

        local = [local for local in locais if local["id"] == b.local_bh_lagoapatos.id][0]
        self.assertEqual(1, local["count_groups"])

    def test_staff_list_locais_paginator_show_all(self):
        self._create_local_embarque(self.b.cidade_bh.id)
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        locais = self.list_locais(
            client_staff,
            {"paginator": {"descending": False, "page": 1, "rowsPerPage": -1, "sortBy": "nickname"}},
        )

        self.assertEqual(1, len(locais))

    def test_staff_list_locais_com_plataformas(self):
        self._create_local_embarque(self.b.cidade_bh.id)
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        locais = self.list_locais(
            client_staff,
            {"paginator": {"descending": False, "page": 1, "rowsPerPage": -1, "sortBy": "nickname"}},
        )

        self.assertEqual(1, len(locais))
        self.assertIn("platforms", locais[0])

    def test_staff_create_list_locais_without_pagination(self):
        b = TestStaffLocais.b
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        local = self._create_local_embarque(b.cidade_bh.id)
        self.assertEqual(local["cidade_id"], b.cidade_bh.id)
        self.assertEqual(local["endereco_slug"], "rua-voluntarios-da-patria-344-santana")
        self.assertEqual(local["bairro_slug"], "santana")
        self.assertEqual(local["nickname_slug"], "estacionamento-tiete")
        locais = self.list_locais(client_staff, status_filter=True)  # lista apenas os ativos
        self.assertEqual(len(locais), 1)

    def test_staff_update_local(self):
        b = TestStaffLocais.b
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        local = self._create_local_embarque(b.cidade_bh.id)
        assert local["aceita_embarque_ao"] is False
        new_nickname = "novo_nickname"
        new_data_dict = {"id": local["id"], "nickname": new_nickname, "aceita_embarque_ao": True}
        self._update_local_embarque(new_data_dict)
        updated_local_from_list_locais = [
            l_dict
            for l_dict in self.list_locais(client_staff, include_group_count=False)
            if l_dict["id"] == local["id"]
        ][0]
        self.assertEqual(new_nickname, updated_local_from_list_locais["nickname"])
        self.assertEqual(True, updated_local_from_list_locais["aceita_embarque_ao"])

    def test_staff_update_local_change_start_time(self):
        b = TestStaffLocais.b
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        working_hours_dict = {
            "working_hour_list": [
                {"dia": 2, "start_time": "10:00", "end_time": "18:00"},
                {"dia": 3, "start_time": "10:00", "end_time": "18:00"},
                {"dia": 4, "start_time": "10:00", "end_time": "18:00"},
                {"dia": 5, "start_time": "10:00", "end_time": "18:00"},
                {"dia": 6, "start_time": "10:00", "end_time": "18:00"},
            ],
            "always_working": False,
        }
        local = self._create_local_embarque(b.cidade_bh.id, **working_hours_dict)
        new_data_dict = {
            "id": local["id"],
            "working_hour_list": [
                {"dia": 2, "start_time": "13:00", "end_time": "18:00"},
                {"dia": 3, "start_time": "13:00", "end_time": "18:00"},
                {"dia": 4, "start_time": "13:00", "end_time": "18:00"},
                {"dia": 5, "start_time": "13:00", "end_time": "18:00"},
                {"dia": 6, "start_time": "13:00", "end_time": "18:00"},
            ],
        }
        self._update_local_embarque(new_data_dict)
        updated_local_from_list_locais = [
            l_dict
            for l_dict in self.list_locais(client_staff, include_group_count=False)
            if l_dict["id"] == local["id"]
        ][0]
        for wh in updated_local_from_list_locais["working_hour_list"]:
            self.assertEqual("13:00", wh["start_time"])

    def test_staff_update_local_change_end_time(self):
        b = TestStaffLocais.b
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        working_hours_dict = {
            "working_hour_list": [
                {"dia": 2, "start_time": "10:00", "end_time": "18:00"},
                {"dia": 3, "start_time": "10:00", "end_time": "18:00"},
                {"dia": 4, "start_time": "10:00", "end_time": "18:00"},
                {"dia": 5, "start_time": "10:00", "end_time": "18:00"},
                {"dia": 6, "start_time": "10:00", "end_time": "18:00"},
            ],
            "always_working": False,
        }
        local = self._create_local_embarque(b.cidade_bh.id, **working_hours_dict)
        new_data_dict = {
            "id": local["id"],
            "working_hour_list": [
                {"dia": 2, "start_time": "10:00", "end_time": "22:00"},
                {"dia": 3, "start_time": "10:00", "end_time": "22:00"},
                {"dia": 4, "start_time": "10:00", "end_time": "22:00"},
                {"dia": 5, "start_time": "10:00", "end_time": "22:00"},
                {"dia": 6, "start_time": "10:00", "end_time": "22:00"},
            ],
        }
        self._update_local_embarque(new_data_dict)
        updated_local_from_list_locais = [
            l_dict
            for l_dict in self.list_locais(client_staff, include_group_count=False)
            if l_dict["id"] == local["id"]
        ][0]
        for wh in updated_local_from_list_locais["working_hour_list"]:
            self.assertEqual("22:00", wh["end_time"])

    def test_staff_update_local_change_weekdays(self):
        b = TestStaffLocais.b
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        working_hours_dict = {
            "working_hour_list": [
                {
                    "dia": 2,
                    "start_time": "10:00",
                    "end_time": "18:00",
                    "max_minutos_permanencia": 20,
                    "max_embarque_simultaneo": 4,
                },
                {
                    "dia": 3,
                    "start_time": "10:00",
                    "end_time": "18:00",
                    "max_minutos_permanencia": 20,
                    "max_embarque_simultaneo": 4,
                },
                {
                    "dia": 4,
                    "start_time": "10:00",
                    "end_time": "18:00",
                    "max_minutos_permanencia": 20,
                    "max_embarque_simultaneo": 3,
                },
                {
                    "dia": 5,
                    "start_time": "10:00",
                    "end_time": "18:00",
                    "max_minutos_permanencia": 20,
                    "max_embarque_simultaneo": 2,
                },
                {
                    "dia": 6,
                    "start_time": "10:00",
                    "end_time": "18:00",
                    "max_minutos_permanencia": 20,
                    "max_embarque_simultaneo": 1,
                },
            ],
            "always_working": False,
        }
        local = self._create_local_embarque(b.cidade_bh.id, **working_hours_dict)
        new_wh_list = [
            {
                "dia": 1,
                "start_time": "10:00",
                "end_time": "18:00",
                "max_minutos_permanencia": 15,
                "max_embarque_simultaneo": 4,
            },
            {
                "dia": 2,
                "start_time": "10:00",
                "end_time": "18:00",
                "max_minutos_permanencia": 15,
                "max_embarque_simultaneo": 2,
            },
        ]
        new_data_dict = {"id": local["id"], "working_hour_list": new_wh_list}
        self._update_local_embarque(new_data_dict)
        updated_local_from_list_locais = [
            l_dict
            for l_dict in self.list_locais(client_staff, include_group_count=False)
            if l_dict["id"] == local["id"]
        ][0]
        self.assertCountEqual(new_wh_list, updated_local_from_list_locais["working_hour_list"])

    def test_staff_update_local_do_not_change_weekdays(self):
        b = TestStaffLocais.b
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        working_hours_dict = {
            "working_hour_list": [
                {
                    "dia": 2,
                    "start_time": "08:00",
                    "end_time": "13:00",
                    "max_minutos_permanencia": 15,
                    "max_embarque_simultaneo": 4,
                },
                {
                    "dia": 3,
                    "start_time": "08:00",
                    "end_time": "13:00",
                    "max_minutos_permanencia": 15,
                    "max_embarque_simultaneo": 3,
                },
                {
                    "dia": 4,
                    "start_time": "08:00",
                    "end_time": "13:00",
                    "max_minutos_permanencia": 20,
                    "max_embarque_simultaneo": 2,
                },
                {
                    "dia": 5,
                    "start_time": "08:00",
                    "end_time": "13:00",
                    "max_minutos_permanencia": 15,
                    "max_embarque_simultaneo": 4,
                },
                {
                    "dia": 6,
                    "start_time": "08:00",
                    "end_time": "13:00",
                    "max_minutos_permanencia": 15,
                    "max_embarque_simultaneo": 1,
                },
            ],
            "always_working": False,
        }
        local = self._create_local_embarque(b.cidade_bh.id, **working_hours_dict)
        new_wh_list = [
            {
                "dia": 2,
                "start_time": "08:00",
                "end_time": "13:30",
                "max_minutos_permanencia": 15,
                "max_embarque_simultaneo": 4,
            },
            {
                "dia": 3,
                "start_time": "08:00",
                "end_time": "13:30",
                "max_minutos_permanencia": 15,
                "max_embarque_simultaneo": 3,
            },
            {
                "dia": 4,
                "start_time": "08:00",
                "end_time": "13:30",
                "max_minutos_permanencia": 20,
                "max_embarque_simultaneo": 2,
            },
            {
                "dia": 5,
                "start_time": "08:00",
                "end_time": "13:30",
                "max_minutos_permanencia": 15,
                "max_embarque_simultaneo": 4,
            },
            {
                "dia": 6,
                "start_time": "08:00",
                "end_time": "13:30",
                "max_minutos_permanencia": 15,
                "max_embarque_simultaneo": 1,
            },
        ]
        new_data_dict = {"id": local["id"], "working_hour_list": new_wh_list}
        self._update_local_embarque(new_data_dict)
        updated_local_from_list_locais = [
            l_dict
            for l_dict in self.list_locais(client_staff, include_group_count=False)
            if l_dict["id"] == local["id"]
        ][0]
        self.assertCountEqual(new_wh_list, updated_local_from_list_locais["working_hour_list"])

    def test_staff_update_local_change_onibus_suportados(self):
        b = TestStaffLocais.b
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        original_onibus_suportado = ["toco"]
        local = self._create_local_embarque(b.cidade_bh.id, onibus_suportados=original_onibus_suportado)
        self.assertCountEqual(original_onibus_suportado, local["onibus_suportados"])
        new_onibus_suportado = ["toco", "trucado"]
        new_data_dict = {"id": local["id"], "onibus_suportados": new_onibus_suportado}
        self._update_local_embarque(new_data_dict)
        updated_local_from_list_locais = [
            l_dict
            for l_dict in self.list_locais(client_staff, include_group_count=False)
            if l_dict["id"] == local["id"]
        ][0]
        self.assertCountEqual(new_onibus_suportado, updated_local_from_list_locais["onibus_suportados"])

    def test_staff_update_local_do_not_change_onibus_suportados(self):
        b = TestStaffLocais.b
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        original_onibus_suportado = ["toco"]
        local = self._create_local_embarque(
            b.cidade_bh.id,
            onibus_suportados=original_onibus_suportado,
            max_embarque_simultaneo=4,
            max_minutos_permanencia=15,
        )
        self.assertCountEqual(original_onibus_suportado, local["onibus_suportados"])
        new_max_embarque_simultaneo = 2
        new_data_dict = {
            "id": local["id"],
            "onibus_suportados": original_onibus_suportado,
            "max_embarque_simultaneo": new_max_embarque_simultaneo,
            "max_minutos_permanencia": 30,
        }
        self._update_local_embarque(new_data_dict)
        updated_local_from_list_locais = [
            l_dict
            for l_dict in self.list_locais(client_staff, include_group_count=False)
            if l_dict["id"] == local["id"]
        ][0]
        self.assertCountEqual(original_onibus_suportado, updated_local_from_list_locais["onibus_suportados"])
        self.assertEqual(new_max_embarque_simultaneo, updated_local_from_list_locais["max_embarque_simultaneo"])
        self.assertEqual(30, updated_local_from_list_locais["max_minutos_permanencia"])

    def test_list_locais_by_city_id(self):
        fixtures.grupos_e_trechos()
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        full_local_list = LocalEmbarque.objects.to_serialize(LocalEmbarqueSerializer.with_restrictions()).all()
        reference_city_id = full_local_list[0].cidade.id
        local_list = self.list_locais_by_city_id(reference_city_id, client_staff)
        expected_list = [
            local.serialize() for local in full_local_list if local.cidade.id == reference_city_id and local.ativo
        ]
        self.assertCountEqual(expected_list, local_list)

    def list_locais_by_city_id(self, city_id, client):
        r = client.get(f"/api/staff/locaisbycity/{city_id}")
        self.assertEqual(200, r.status_code)
        return r.json()

    def list_locais(self, client, pagination_config=None, status_filter=None, search=None, include_group_count=True):
        payload_params = {}
        if pagination_config:
            payload_params.update(pagination_config)
        if status_filter is not None:
            payload_params.update({"status_filter": status_filter})
        if search:
            payload_params["search"] = search
        if include_group_count:
            payload_params["include_group_count"] = True

        payload = {"params": json.dumps(payload_params)} if payload_params else None
        r = client.get("/api/staff/locais", data=payload or None)
        self.assertEqual(200, r.status_code)
        res = r.json()["items"]
        if len(res) > 0 and include_group_count:
            self.assertIn("count_groups", res[0])
        return res

    def list_locais_select_options(self, client, search=None):
        payload_params = {}
        if search:
            payload_params["search"] = search

        payload = {"params": json.dumps(payload_params)} if payload_params else None
        r = client.get("/api/staff/efops/locais_select_options", data=payload or None)
        self.assertEqual(200, r.status_code)
        res = r.json()["items"]
        return res

    def test_staff_list_locais_status_filter(self):
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        active_local = self._create_local_embarque(self.b.cidade_sp.id)
        active_local_to_update = self._create_local_embarque(self.b.cidade_bh.id)
        update_local_dict = {"id": active_local_to_update["id"], "ativo": False}
        self._update_local_embarque(update_local_dict)

        expect_result_id_list = (
            (False, [active_local_to_update["id"]]),
            (True, [active_local["id"]]),
            (None, [active_local["id"], active_local_to_update["id"]]),
        )

        for status_filter, expected_list in expect_result_id_list:
            with self.subTest(f"Listando locais utilizando status_filter = {status_filter}"):
                listed_local = self.list_locais(client_staff, status_filter=status_filter)
                self.assertCountEqual(expected_list, [local["id"] for local in listed_local])

    def test_staff_list_locais_search(self):
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        active_local = self._create_local_embarque(self.b.cidade_sp.id)

        listed_local = self.list_locais(client_staff, search=active_local["nickname"])
        self.assertIn(active_local["id"], [local["id"] for local in listed_local])

    def test_staff_list_locais_include_city_name(self):
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        active_local = self._create_local_embarque(self.b.cidade_sp.id)

        listed_local = self.list_locais(client_staff, search=active_local["nickname"])
        local = [local for local in listed_local if local["id"] == active_local["id"]][0]
        self.assertEqual(active_local["id"], local["id"])
        self.assertIn("name", local)
        self.assertEqual(self.b.cidade_sp.name, local["name"])

    def test_staff_update_local_should_update_endereco_in_model(self):
        b = TestStaffLocais.b
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        local = self._create_local_embarque(b.cidade_bh.id)
        new_number = "9999"
        new_data_dict = {"id": local["id"], "endereco_numero": new_number}
        self._update_local_embarque(new_data_dict)
        updated_local_from_model = LocalEmbarque.objects.filter(pk=local["id"]).first()
        self.assertNotEqual(local["endereco"], updated_local_from_model.endereco)
        self.assertIn(new_number, updated_local_from_model.endereco)

    def test_staff_get_local(self):
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        created_local = self._create_local_embarque(
            self.b.cidade_sp.id, max_embarque_simultaneo=4, max_minutos_permanencia=20
        )

        local_from_get = self._get_local(created_local["id"], client_staff)
        self.assertDictEqual(local_from_get, created_local)

    def test_staff_local_select_options(self):
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        created_local = self._create_local_embarque(self.b.cidade_sp.id)

        listed_local = self.list_locais_select_options(client_staff, search=created_local["nickname"])
        self.assertIn(created_local["id"], [local["id"] for local in listed_local])

    def test_staff_create_local_with_classification(self):
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        created_local = self._create_local_embarque(self.b.cidade_sp.id, classification="posto_de_gasolina")

        local = LocalEmbarque.objects.get(pk=created_local["id"])
        self.assertEqual(LocalEmbarque.Classification.POSTO_DE_GASOLINA, local.classification)

    def test_staff_create_local_with_invalid_classification(self):
        client_staff = Client()
        client_staff.force_login(self.user_staff)

        with prevent_request_warnings():
            res = self._request_create_local_embarque(self.b.cidade_sp.id, classification="invalido")

        self.assertEqual(400, res.status_code)

    def test_staff_update_local_classification(self):
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        created_local = self._create_local_embarque(self.b.cidade_sp.id)
        update_data = {
            "id": created_local["id"],
            "classification": LocalEmbarque.Classification.ESTABELECIMENTO_COMERCIAL,
        }

        self._update_local_embarque(update_data)
        updated_local = LocalEmbarque.objects.get(pk=created_local["id"])
        self.assertEqual(LocalEmbarque.Classification.ESTABELECIMENTO_COMERCIAL, updated_local.classification)

    def test_staff_update_local_classification_to_none(self):
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        created_local = self._create_local_embarque(
            self.b.cidade_sp.id, classification=LocalEmbarque.Classification.TERMINAL_RODOVIARIO
        )

        update_data = {"id": created_local["id"], "classification": None}

        self._update_local_embarque(update_data)
        updated_local = LocalEmbarque.objects.get(pk=created_local["id"])
        self.assertEqual(LocalEmbarque.Classification.TERMINAL_RODOVIARIO, updated_local.classification)

    def test_staff_list_locais_start_time_none(self):
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        active_local = self._create_local_embarque(self.b.cidade_sp.id)
        active_local_model = LocalEmbarque.objects.get(id=active_local["id"])
        baker.make(Weekdays, local=active_local_model, dia=1, start_time=None, end_time="18:00")

        listed_local = self.list_locais(client_staff, search=active_local["nickname"])
        self.assertIn(active_local["id"], [local["id"] for local in listed_local])

    def test_staff_create_local_ativa_cidade(self):
        self.b.cidade_mongagua.ativo = False
        self.b.cidade_mongagua.save()

        client_staff = Client()
        client_staff.force_login(self.user_staff)
        self._create_local_embarque(self.b.cidade_mongagua.id)

        self.b.cidade_mongagua.refresh_from_db()
        self.assertTrue(self.b.cidade_mongagua.ativo)

    def test_staff_create_local_description_is_optional(self):
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        created_local = self._create_local_embarque(self.b.cidade_mongagua.id, description=None)

        local = LocalEmbarque.objects.get(id=created_local["id"])
        self.assertFalse(local.description)

    def test_staff_update_local_remove_description(self):
        client_staff = Client()
        client_staff.force_login(self.user_staff)
        created_local = self._create_local_embarque(self.b.cidade_sp.id)

        update_data = {"id": created_local["id"], "description": ""}

        self._update_local_embarque(update_data)
        updated_local = LocalEmbarque.objects.get(pk=created_local["id"])
        self.assertEqual("", updated_local.description)

    def _get_local(self, local_id, client):
        r = client.get(f"/api/staff/locais/{local_id}")
        self.assertEqual(200, r.status_code)
        return r.json()

    def _create_local_embarque(self, cidade_id, nickname=None, **kwargs):
        r = self._request_create_local_embarque(cidade_id=cidade_id, nickname=nickname, **kwargs)
        self.assertEqual(200, r.status_code)
        return r.json()

    def _request_create_local_embarque(self, cidade_id, nickname=None, modelos_venda=None, **kwargs):
        client = Client()
        client.force_login(self.user_infra)
        params_dict = {
            "nickname": nickname or "Estacionamento Tietê",
            "endereco": "Rua Voluntários da Pátria",
            "endereco_numero": "344",
            "endereco_bairro": "Santana",
            "endereco_logradouro": "Rua Voluntários da Pátria",
            "driver_endereco_logradouro": "logradouro",
            "driver_endereco_numero": 900,
            "endereco_cep": "02010-902",
            "cidade_id": cidade_id,
            "desembarque_rapido": False,
            "description": "alo alo rapaziada",
            "driver_description": "olo olo",
            "impede_bypass": False,
            "mapurl": "google.com",
            "features": [
                "cobertura",
                "bancos",
                "metro",
                "alimentacao",
                "banheiros",
                "iluminacao",
                "cameras",
                "centro",
                "rodoviaria",
            ],
            "latitude": -20.123231,
            "longitude": -21.128933,
        }
        params_dict.update(kwargs)
        params = json.dumps(params_dict)
        return client.post("/api/staff/createlocal", {"local": params})

    def _update_local_embarque(self, new_data_dict):
        client = Client()
        client.force_login(self.user_infra)
        r = client.post("/api/staff/updatelocal", new_data_dict, content_type="application/json")
        self.assertEqual(200, r.status_code)
        return r.json()


class TestStaffUpdateLocal(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user_infra = fixtures.user_infra()

    def get_client(self):
        client_infra = Client()
        client_infra.force_login(self.user_infra)
        return client_infra

    def request_update_local(self, new_data):
        return self.get_client().post("/api/staff/updatelocal", new_data, content_type="application/json")

    def update_local(self, new_data):
        response = self.request_update_local(new_data)
        self.assertEqual(200, response.status_code)

    def test_do_not_update_nickname_to_None(self):
        local = baker.make(LocalEmbarque)
        previous_nickname = local.nickname
        data = {"id": local.id, "nickname": None}

        self.update_local(data)

        local.refresh_from_db(fields=["nickname"])
        self.assertEqual(previous_nickname, local.nickname)

    def test_update_fields(self):
        local = baker.make(LocalEmbarque)
        updt_field_list = [
            ("endereco_logradouro", "novo log"),
            ("endereco_bairro", "novo bairro"),
            ("description", "nova descricao"),
            ("latitude", -23.9851),
            ("longitude", -40.2341),
            ("endereco_cep", "12324120"),
            ("nickname", "new_nick"),
            ("max_embarque_simultaneo", 5),
            ("max_minutos_permanencia", 30),
        ]

        for field_name, new_value in updt_field_list:
            data = {"id": local.id, field_name: new_value}

            self.update_local(data)

            local.refresh_from_db(fields=[field_name])
            self.assertEqual(new_value, getattr(local, field_name))

    def test_staff_update_local_short_url(self):
        local = baker.make(LocalEmbarque, mapurl="http://urlnaoatualizada.com")

        update_data = {"id": local.id, "mapurl": "http://nova.url/mapa"}
        self.update_local(update_data)

        local.refresh_from_db()
        self.assertEqual("https://short.url.com", local.mapurl)

    def test_raises_on_empty_data(self):
        with prevent_request_warnings():
            res = self.request_update_local({})

        self.assertEqual(400, res.status_code)

    def test_staff_update_local_ativo_false(self):
        local = baker.make(LocalEmbarque, ativo=True)

        self.update_local({"id": local.id, "ativo": False})

        local.refresh_from_db()
        self.assertIs(False, local.ativo)

    def test_staff_update_local_ativo_true(self):
        local = baker.make(LocalEmbarque, ativo=False)

        self.update_local({"id": local.id, "ativo": True})

        local.refresh_from_db()
        self.assertIs(True, local.ativo)

    def test_staff_update_local_ativo_to_none(self):
        local = baker.make(LocalEmbarque, ativo=True)

        self.update_local({"id": local.id, "ativo": None})

        local.refresh_from_db()
        self.assertIs(True, local.ativo)

    def test_staff_update_local_acesso_privado(self):
        local = baker.make(LocalEmbarque, tipo_de_acesso="publico")

        self.update_local({"id": local.id, "tipo_de_acesso": "privado"})

        local.refresh_from_db()
        self.assertEqual("privado", local.tipo_de_acesso)

    def test_staff_update_local_low_cost(self):
        local = baker.make(LocalEmbarque, categoria_do_ponto="premium")

        self.update_local({"id": local.id, "categoria_do_ponto": "low_cost"})

        local.refresh_from_db()
        self.assertEqual("low_cost", local.categoria_do_ponto)

    def test_staff_update_local_zona_maxima_de_restricao(self):
        local = baker.make(LocalEmbarque, zona_maxima_de_restricao=True)

        self.update_local({"id": local.id, "zona_maxima_de_restricao": False})

        local.refresh_from_db()
        self.assertFalse(local.zona_maxima_de_restricao)

    def test_staff_update_local_url_to_none(self):
        non_altered_url = "urlnaoalterada.com"
        local = baker.make(LocalEmbarque, mapurl=non_altered_url)

        self.update_local({"id": local.id, "mapurl": None})

        local.refresh_from_db()
        self.assertEqual(non_altered_url, local.mapurl)

    def test_staff_update_local_cidade(self):
        initial_city = baker.make(Cidade)
        altered_city = baker.make(Cidade)
        local = baker.make(LocalEmbarque, cidade=initial_city)

        self.update_local({"id": local.id, "cidade_id": altered_city.id})

        local.refresh_from_db()
        self.assertEqual(altered_city.id, local.cidade_id)

    def test_staff_update_local_weekdays_specified_and_always_working_must_raise(self):
        weekday = baker.make(Weekdays, start_time="10:00", end_time="18:00")
        local = baker.make(LocalEmbarque, weekdays=[weekday])

        with prevent_request_warnings():
            wh_data = [
                {"dia": 1, "start_time": "09:00", "end_time": "15:00"},
                {"dia": 2, "start_time": "09:00", "end_time": "15:00"},
                {"dia": 3, "start_time": "09:00", "end_time": "15:00"},
            ]
            res = self.request_update_local({"id": local.id, "working_hour_list": wh_data, "always_working": True})

        self.assertEqual(400, res.status_code)

    def test_staff_update_local_weekdays_not_specified_and_not_always_working_must_raise(self):
        weekday = baker.make(Weekdays, start_time="10:00", end_time="18:00")
        local = baker.make(LocalEmbarque, weekdays=[weekday])

        with prevent_request_warnings():
            res = self.request_update_local({"id": local.id, "always_working": False})

        self.assertEqual(400, res.status_code)

    def test_staff_update_local_remove_working_hour(self):
        weekday = baker.make(Weekdays, start_time="10:00", end_time="18:00")
        local = baker.make(LocalEmbarque, weekdays=[weekday])

        self.update_local({"id": local.id, "always_working": True})

        local.refresh_from_db()
        self.assertIsNone(local.start_time)
        self.assertIsNone(local.end_time)
        self.assertFalse(local.weekdays.all().exists())

    def test_staff_update_local_log(self):
        local = baker.make(LocalEmbarque, nickname="old_nick")

        self.update_local({"id": local.id, "nickname": "new_nick"})

        local.refresh_from_db()
        assert_has_expected_log_types_occurrences("update_local")

    def test_staff_update_local_same_mapurl(self):
        url = "https://short.url.com"
        local = baker.make(LocalEmbarque, mapurl=url)

        self.update_local({"id": local.id, "mapurl": url})

        local.refresh_from_db()
        self.assertEqual(url, local.mapurl)


@pytest.fixture
def locais_low_cost():
    cidade = baker.make("core.Cidade", name="São Paulo", uf="SP")
    locais = [
        baker.make(
            LocalEmbarque,
            cidade=cidade,
            categoria_do_ponto="low_cost",
            tipo_de_acesso="privado",
            zona_maxima_de_restricao=True,
        ),
        baker.make(
            LocalEmbarque,
            cidade=cidade,
            categoria_do_ponto="low_cost",
            tipo_de_acesso="publico",
            zona_maxima_de_restricao=False,
        ),
    ]
    return locais


@pytest.fixture
def locais_premium():
    cidade = baker.make("core.Cidade", name="São Paulo", uf="SP")
    locais = [
        baker.make(
            LocalEmbarque,
            cidade=cidade,
            categoria_do_ponto="premium",
            tipo_de_acesso="publico",
            zona_maxima_de_restricao=True,
        ),
        baker.make(
            LocalEmbarque,
            cidade=cidade,
            categoria_do_ponto="premium",
            tipo_de_acesso="privado",
            zona_maxima_de_restricao=False,
        ),
    ]
    return locais


@pytest.fixture
def pde_restricao_geral():
    return baker.make(LocalEmbarque, max_minutos_permanencia=30)


@pytest.fixture
def pde_com_restricoes_por_dia():
    local = baker.make(LocalEmbarque, max_minutos_permanencia=30)
    # Restrição para segunda-feira
    baker.make(Weekdays, local=local, dia=1, start_time=time(8, 0), end_time=time(18, 0), max_minutos_permanencia=15)
    # Restrição para terça-feira (manhã)
    baker.make(Weekdays, local=local, dia=2, start_time=time(6, 0), end_time=time(12, 0), max_minutos_permanencia=25)
    # Restrição para terça-feira (tarde)
    baker.make(Weekdays, local=local, dia=2, start_time=time(14, 0), end_time=time(22, 0), max_minutos_permanencia=30)
    return local


@pytest.fixture
def datas_restricao_pde():
    return {
        "chegada_na_segunda": datetime(2025, 7, 14, 10, 0, tzinfo=timezone.utc),
        "chegada_na_terca_de_manha": datetime(2025, 7, 15, 8, 0, tzinfo=timezone.utc),
        "chegada_na_terca_a_tarde": datetime(2025, 7, 15, 15, 0, tzinfo=timezone.utc),
        "chegada_na_quarta": datetime(2025, 7, 16, 10, 0, tzinfo=timezone.utc),
    }


def test_staff_list_locais_new_filters(rf, user_staff):
    cidade = baker.make("core.Cidade", name="São Paulo", uf="SP")
    # tudo no filtro
    return_city = baker.make(LocalEmbarque, cidade=cidade, classification="terminal_rodoviario")

    # cidade fora do filtro
    baker.make(LocalEmbarque, cidade=baker.make("core.Cidade"), classification="terminal_rodoviario")

    # classificacao fora do filtro
    baker.make(LocalEmbarque, cidade=cidade, classification="estacionamento")

    payload_params = {
        "filter_city": [cidade.id],
        "filter_classification": ["terminal_rodoviario", "shopping"],
        "filter_state": ["SP", "RJ"],
    }

    request = rf.get("/api/staff/locais", {"params": json.dumps(payload_params)})
    request.user = user_staff

    response = list_locais(request)
    content = json.loads(response.content)

    assert content["count"] == 1
    assert content["items"][0]["id"] == return_city.id


@pytest.mark.parametrize("tipo_acesso", ["publico", "privado"])
def test_staff_list_locais_tipo_acesso(rf, user_staff, locais_premium, locais_low_cost, tipo_acesso):
    request = rf.get("/api/staff/locais", {"params": json.dumps({"tipo_de_acesso": tipo_acesso})})
    request.user = user_staff
    response = list_locais(request)

    content = json.loads(response.content)
    assert content["count"] == 2
    for item in content["items"]:
        assert item["tipo_de_acesso"] == tipo_acesso


@pytest.mark.parametrize("categoria_do_ponto", ["premium", "low_cost"])
def test_staff_list_locais_tipo_ponto(rf, user_staff, locais_premium, locais_low_cost, categoria_do_ponto):
    expected_places = locais_premium if categoria_do_ponto == "premium" else locais_low_cost
    request = rf.get("/api/staff/locais", {"params": json.dumps({"categoria_do_ponto": categoria_do_ponto})})
    request.user = user_staff
    response = list_locais(request)

    content = json.loads(response.content)
    assert content["count"] == len(expected_places)
    for item in content["items"]:
        assert item["categoria_do_ponto"] == categoria_do_ponto


def test_list_locais_with_count_groups(rf, user_staff):
    local = baker.make(LocalEmbarque)
    newest_count = baker.make(GruposLocalEmbarque, count_date=today(), count=5, local_id=local.id)

    payload = {"include_group_count": True}
    request = rf.get("/api/staff/locais", {"params": json.dumps(payload)})
    request.user = user_staff

    response = list_locais(request)
    content = json.loads(response.content)

    assert content["count"] == 1
    assert content["items"][0]["id"] == local.id
    assert content["items"][0]["count_groups"] == newest_count.count


def test_list_locais_count_groups_default_value(rf, user_staff):
    local = baker.make(LocalEmbarque)

    payload = {"include_group_count": True}
    request = rf.get("/api/staff/locais", {"params": json.dumps(payload)})
    request.user = user_staff

    response = list_locais(request)
    content = json.loads(response.content)

    # como pedi para contar, mas não tem nada contado, deve retornar um zero
    assert content["count"] == 1
    assert content["items"][0]["id"] == local.id
    assert content["items"][0]["count_groups"] == 0


def test_list_locais_simple(rf, user_staff):
    exclude_local = baker.make(LocalEmbarque)
    baker.make(LocalEmbarque, _quantity=2)

    request = rf.get("/api/staff/locais_simple", {"exclude_self_id": json.dumps(exclude_local.id)})
    request.user = user_staff

    response = list_locais_simple(request)
    content = json.loads(response.content)
    assert len(content) == 2


def test_list_locais_simple_queries(rf, user_staff, django_assert_num_queries):
    exclude_local = baker.make(LocalEmbarque)
    baker.make(LocalEmbarque, _quantity=5)

    request = rf.get("/api/staff/locais_simple", {"exclude_self_id": json.dumps(exclude_local.id)})
    request.user = user_staff

    with django_assert_num_queries(2):
        response = list_locais_simple(request)
        content = json.loads(response.content)
        assert len(content) == 5


def test_staff_list_locais_with_internal_details(rf, user_staff):
    local = baker.make(LocalEmbarque)
    details = baker.make(
        LocalEmbarqueInternalDetails,
        local=local,
        status_flag="verde",
        reports="reports",
        observations="observações",
        contato_nome="Contato da Silva",
        contato_email="<EMAIL>",
        contato_telefone="11999999999",
    )

    request = rf.get("/api/staff/locais", {"params": json.dumps({})})
    request.user = user_staff

    response = list_locais(request)
    content = json.loads(response.content)
    assert len(content["items"]) == 1
    assert content["items"][0]["status_flag"] == details.status_flag
    assert content["items"][0]["reports"] == details.reports
    assert content["items"][0]["observations"] == details.observations
    assert content["items"][0]["contato_nome"] == details.contato_nome
    assert content["items"][0]["contato_email"] == details.contato_email
    assert content["items"][0]["contato_telefone"] == details.contato_telefone


def test_create_local_with_internal_details(rf, user_infra):
    cidade = baker.make(Cidade, name="city")
    params_dict = {
        "nickname": "Estacionamento",
        "cidade_id": cidade.id,
        "endereco": "Rua Voluntários da Pátria",
        "endereco_logradouro": "logradouro",
        "endereco_bairro": "bairro",
        "latitude": -20.123231,
        "longitude": -21.128933,
        "mapurl": "google.com",
        "status_flag": "verde",
        "reports": "reports",
        "observations": "observações",
        "contato_nome": "Contato da Silva",
        "contato_email": "<EMAIL>",
        "contato_telefone": "11999999999",
    }

    params = json.dumps(params_dict)
    request = rf.post("/api/staff/createlocal", {"local": params})
    request.user = user_infra

    response = create_local(request)
    assert response.status_code == 200
    assert LocalEmbarque.objects.count() == 1
    assert LocalEmbarqueInternalDetails.objects.count() == 1


def test_update_local_with_internal_details(rf, user_infra):
    local_embarque = baker.make(LocalEmbarque)
    params_dict = {
        "id": local_embarque.id,
        "status_flag": "verde",
        "reports": "reports",
        "observations": "observações",
        "contato_nome": "Contato da Silva",
        "contato_email": "<EMAIL>",
        "contato_telefone": "11999999999",
    }

    request = rf.post("/api/staff/updatelocal", params_dict, content_type="application/json")
    request.user = user_infra

    response = update_local(request)
    assert response.status_code == 200
    assert LocalEmbarque.objects.count() == 1
    assert LocalEmbarqueInternalDetails.objects.count() == 1


def test_serializer_local_queries(django_assert_num_queries):
    tomorrow = today() + timedelta(days=1)
    local_one = baker.make(LocalEmbarque)
    local_two = baker.make(LocalEmbarque)
    _create_local_count_grupos(local_one, count=1, count_date=tomorrow)
    _create_local_count_grupos(local_two)

    # antes estava fazendo 9 queries
    # matei a query extra dos grupos, mas ainda vou melhorar mais
    with django_assert_num_queries(4):
        queryset = LocalEmbarque.objects.to_serialize(LocalEmbarqueSerializer.with_groups()).all()
        assert len(queryset) == 2
        [local.serialize() for local in queryset]


def test_list_locais_search_filter_ignore_accent_and_case(rf, user_staff):
    rodo = baker.make(LocalEmbarque, nickname="Rodoviária")

    request = rf.get("/api/staff/locais_simple", {"search": "rodoviaria"})
    request.user = user_staff

    response = list_locais(request)
    content = json.loads(response.content)
    assert len(content["items"]) == 1
    assert content["items"][0]["id"] == rodo.id


def test_list_locais_search_filter_text_like(rf, user_staff):
    rodo = baker.make(LocalEmbarque, nickname="Rodoviária")

    request = rf.get("/api/staff/locais_simple", {"search": "rodo"})
    request.user = user_staff

    response = list_locais(request)
    content = json.loads(response.content)
    assert len(content["items"]) == 1
    assert content["items"][0]["id"] == rodo.id


def _create_local_count_grupos(local, count=None, count_date=None):
    count = count or 5
    count_date = count_date or today()
    baker.make(
        GruposLocalEmbarque,
        local=local,
        count=count,
        count_date=count_date,
    )


def test_staff_create_local_with_sales_model(rf, user_factory):
    cidade = baker.make("core.Cidade", name="city")
    usuario = user_factory(
        roles=["Infra"],
        username="Infra",
        first_name="Matheus",
        last_name="Baraduas",
        email="<EMAIL>",
        password="password",
    )
    modelos = ["Marketplace", "Freetamento", "Híbrido"]

    params_dict = {
        "nickname": "Estacionamento Tietê",
        "endereco": "Rua Voluntários da Pátria",
        "endereco_numero": "344",
        "endereco_bairro": "Santana",
        "endereco_logradouro": "Rua Voluntários da Pátria",
        "always_working": True,
        "endereco_cep": "02010-902",
        "cidade_id": cidade.id,
        "mapurl": "google.com",
        "latitude": -20.123231,
        "longitude": -21.128933,
        "modelos_venda": modelos,
    }
    params = json.dumps(params_dict)
    request = rf.post("/api/staff/createlocal", {"local": params})
    request.user = usuario
    response = create_local(request)
    local_embarque = LocalEmbarque.objects.all().values()

    assert response.status_code == 200
    assert LocalEmbarque.objects.count() == 1
    assert local_embarque[0]["modelos_venda"] == modelos


def test_staff_create_local_with_use_of_the_local(rf, user_factory):
    cidade = baker.make("core.Cidade", name="city")
    usuario = user_factory(
        roles=["Infra"],
        username="Infra",
        first_name="Matheus",
        last_name="Baraduas",
        email="<EMAIL>",
        password="password",
    )
    modelos = ["Marketplace", "Fretamento", "Híbrido"]
    uso_do_local = ["Lanche", "Embarque"]

    params_dict = {
        "nickname": "Estacionamento Tietê",
        "endereco": "Rua Voluntários da Pátria",
        "endereco_numero": "344",
        "endereco_bairro": "Santana",
        "endereco_logradouro": "Rua Voluntários da Pátria",
        "always_working": True,
        "endereco_cep": "02010-902",
        "cidade_id": cidade.id,
        "mapurl": "google.com",
        "latitude": -20.123231,
        "longitude": -21.128933,
        "modelos_venda": modelos,
        "uso_do_local": uso_do_local,
    }
    params = json.dumps(params_dict)
    request = rf.post("/api/staff/createlocal", {"local": params})
    request.user = usuario
    response = create_local(request)
    local_embarque = LocalEmbarque.objects.all().values()

    assert response.status_code == 200
    assert LocalEmbarque.objects.count() == 1
    assert local_embarque[0]["modelos_venda"] == modelos
    assert local_embarque[0]["uso_do_local"] == uso_do_local


def test_staff_update_local_sales_model_and_use_of_the_local(rf, user_factory):
    cidade = baker.make("core.Cidade")
    local = baker.make("core.LocalEmbarque", cidade=cidade)
    usuario = user_factory(
        roles=["Infra"],
        username="Infra",
        first_name="Matheus",
        last_name="Baraduas",
        email="<EMAIL>",
        password="password",
    )
    modelos = ["Marketplace"]
    uso_do_local = ["Lanche"]

    params_dict = {"id": local.id, "cidade_id": cidade.id, "modelos_venda": modelos, "uso_do_local": uso_do_local}

    request = rf.post("/api/staff/updatelocal", params_dict, content_type="application/json")
    request.user = usuario
    response = update_local(request)
    local_embarque = LocalEmbarque.objects.all().values()

    assert response.status_code == 200
    assert LocalEmbarque.objects.count() == 1
    assert local_embarque[0]["modelos_venda"] == modelos
    assert local_embarque[0]["uso_do_local"] == uso_do_local


def test_staff_list_locais_new_filters_model_local_id_filter(rf, user_staff):
    cidade = baker.make("core.Cidade")
    local = baker.make("core.LocalEmbarque", cidade=cidade)

    payload_params = {"local_id": local.id}

    request = rf.get("/api/staff/locais", {"params": json.dumps(payload_params)})
    request.user = user_staff

    response = list_locais(request)
    content = json.loads(response.content)

    assert len(content["items"]) == 1
    assert content["items"][0]["id"] == local.id


def test_staff_list_locais_new_filters_model_modelos_venda_filter(rf, user_staff):
    cidade = baker.make("core.Cidade")
    modelos_venda = ["Híbrido", "Fretamento", "Marketplace"]
    local = baker.make("core.LocalEmbarque", cidade=cidade, modelos_venda=modelos_venda)

    payload_params = {"modelos_venda": local.modelos_venda}

    request = rf.get("/api/staff/locais", {"params": json.dumps(payload_params)})
    request.user = user_staff

    response = list_locais(request)
    content = json.loads(response.content)

    assert len(content["items"]) == 1
    assert content["items"][0]["modelos_venda"] == modelos_venda


def test_staff_list_locais_new_filters_model_uso_do_local_filter(rf, user_staff):
    cidade = baker.make("core.Cidade")
    uso_do_local = ["Embarque", "Local"]
    local = baker.make("core.LocalEmbarque", cidade=cidade, uso_do_local=uso_do_local)

    payload_params = {"uso_do_local": local.uso_do_local}

    request = rf.get("/api/staff/locais", {"params": json.dumps(payload_params)})
    request.user = user_staff

    response = list_locais(request)
    content = json.loads(response.content)

    assert len(content["items"]) == 1
    assert content["items"][0]["uso_do_local"] == uso_do_local


def test_staff_list_locais_new_filters_model_status_flag_filter(rf, user_staff):
    cidade = baker.make("core.Cidade")
    flag_color = "verde"
    local = baker.make("core.LocalEmbarque", cidade=cidade)
    baker.make("core.LocalEmbarqueInternalDetails", status_flag=flag_color, local=local)

    payload_params = {"status_flag": local.detalhes_internos.status_flag}

    request = rf.get("/api/staff/locais", {"params": json.dumps(payload_params)})
    request.user = user_staff

    response = list_locais(request)
    content = json.loads(response.content)

    assert len(content["items"]) == 1
    assert content["items"][0]["status_flag"] == flag_color
    assert content["items"][0]["id"] == local.id


def test_staff_list_locais_new_filters_model_onibus_suportados_filter(rf, user_staff):
    cidade = baker.make("core.Cidade")

    local = baker.make("core.LocalEmbarque", cidade=cidade)
    baker.make("core.OnibusSuportado", tipo="toco", local=local)
    baker.make("core.OnibusSuportado", tipo="trucado", local=local)

    payload_params = {"onibus_suportados": ["toco", "trucado"]}

    request = rf.get("/api/staff/locais", {"params": json.dumps(payload_params)})
    request.user = user_staff

    response = list_locais(request)
    content = json.loads(response.content)

    assert len(content["items"]) == 1
    assert content["items"][0]["onibus_suportados"] == ["toco", "trucado"]


def test_update_local_dias_proibidos():
    local = baker.make(LocalEmbarque)
    form = UpdateLocalEmbarqueForm.parse_obj(
        dict(
            id=local.id,
            local=local,
            dias_proibidos="01/03/2024,02/03/2024",
        )
    )
    localadm_svc.update_local(form)

    local.refresh_from_db()
    assert local.dias_proibidos == [date(2024, 3, 1), date(2024, 3, 2)]


def test_update_local_insert_restrictions():
    local = baker.make(LocalEmbarque)
    form = UpdateLocalEmbarqueForm.parse_obj(
        dict(
            id=local.id,
            local=local,
            always_working=False,
            max_embarque_simultaneo=4,
            max_minutos_permanencia=15,
            working_hour_list=[
                {
                    "start_time": "08:00",
                    "end_time": "18:00",
                    "max_embarque_simultaneo": 4,
                    "max_minutos_permanencia": 15,
                    "dia": 1,
                },
                {
                    "start_time": "08:00",
                    "end_time": "18:00",
                    "max_embarque_simultaneo": 2,
                    "max_minutos_permanencia": 10,
                    "dia": 2,
                },
            ],
        )
    )
    localadm_svc.update_local(form)

    local.refresh_from_db()
    assert local.weekdays.count() == 2
    assert local.weekdays.get(dia=1).max_embarque_simultaneo == 4
    assert local.weekdays.get(dia=1).max_minutos_permanencia == 15
    assert local.weekdays.get(dia=2).max_embarque_simultaneo == 2
    assert local.weekdays.get(dia=2).max_minutos_permanencia == 10
    assert local.max_embarque_simultaneo == 4
    assert local.max_minutos_permanencia == 15


def test_update_local_remove_restrictions():
    local = baker.make(
        LocalEmbarque,
        max_embarque_simultaneo=4,
        max_minutos_permanencia=15,
    )
    baker.make(Weekdays, local=local, dia=1, start_time="08:00", end_time="18:00")
    baker.make(Weekdays, local=local, dia=2, start_time="08:00", end_time="18:00")

    assert Weekdays.objects.count() == 2

    form = UpdateLocalEmbarqueForm.parse_obj(
        dict(
            id=local.id,
            local=local,
            max_embarque_simultaneo=4,
            max_minutos_permanencia=15,
            working_hour_list=[],
            always_working=True,
        )
    )
    localadm_svc.update_local(form)

    local.refresh_from_db()

    assert Weekdays.objects.filter(local=local).count() == 0


def test_calcula_horario_chegada_com_diferentes_restricoes(
    pde_com_restricoes_por_dia, pde_restricao_geral, datas_restricao_pde
):
    """Testa o cálculo com diferentes restrições de horário e dias da semana"""
    # Segunda feira 10:00, deve retornar 15 minutos antes
    result_chegada_segunda = calculate_arrival_for_first_pde(
        pde_com_restricoes_por_dia, datas_restricao_pde["chegada_na_segunda"]
    )
    assert result_chegada_segunda == datas_restricao_pde["chegada_na_segunda"] - timedelta(minutes=15)

    # Terça-feira primeiro período deve retornar 25 minutos antes
    result_chegada_terca_manha = calculate_arrival_for_first_pde(
        pde_com_restricoes_por_dia, datas_restricao_pde["chegada_na_terca_de_manha"]
    )
    assert result_chegada_terca_manha == datas_restricao_pde["chegada_na_terca_de_manha"] - timedelta(minutes=25)

    # terça-feira segundo período deve retornar 30 minutos antes
    result_chegada_terca_tarde = calculate_arrival_for_first_pde(
        pde_com_restricoes_por_dia, datas_restricao_pde["chegada_na_terca_a_tarde"]
    )
    assert result_chegada_terca_tarde == datas_restricao_pde["chegada_na_terca_a_tarde"] - timedelta(minutes=30)

    # Teste pde sem restrição de horário mas com restrição geral por funcionar 24 horas
    result_chegada_na_quarta = calculate_arrival_for_first_pde(
        pde_restricao_geral, datas_restricao_pde["chegada_na_quarta"]
    )
    assert result_chegada_na_quarta == datas_restricao_pde["chegada_na_quarta"] - timedelta(minutes=30)


def test_retorna_none_quando_parametros_invalidos(pde_restricao_geral, datas_restricao_pde):
    """Testa que retorna None quando o datetime é inválido"""
    assert calculate_arrival_for_first_pde(pde_restricao_geral, None) is None  # type: ignore[arg-type]
    assert calculate_arrival_for_first_pde(None, datas_restricao_pde["chegada_na_segunda"]) is None  # type: ignore[arg-type]


def test_usa_fallback_quando_sem_restricao_para_dia(datas_restricao_pde):
    """Testa que usa o tempo do local quando não há restrição para o dia específico
    Esse cenário pode ocorrer quando foi criado um grupo, havia restrições, mas depois foram removidas
    """
    local = baker.make(LocalEmbarque, max_minutos_permanencia=30)
    baker.make(
        Weekdays,
        local=local,
        dia=1,
        start_time=time(8, 0),
        end_time=time(18, 0),
        max_minutos_permanencia=15,
    )

    # Testa numa terça-feira (deve usar o tempo padrão do local)
    result_chegada_terca = calculate_arrival_for_first_pde(local, datas_restricao_pde["chegada_na_terca_de_manha"])
    assert result_chegada_terca == datas_restricao_pde["chegada_na_terca_de_manha"] - timedelta(minutes=30)
