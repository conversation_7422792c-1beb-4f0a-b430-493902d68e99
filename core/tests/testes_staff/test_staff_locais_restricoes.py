import json

from django.test import TestCase
from django.test.client import Client

from core.models_rota import LocalEmbarque
from core.tests import fixtures
from core.tests.prevent_useless_logs import prevent_request_warnings


class TestStaffLocaisRestricoes(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user_infra = fixtures.user_infra()
        cls.b = fixtures.cidades()

    def test_staff_criar_local_com_restricao_de_horario_funcionamento(self):
        working_hours_dict = {
            "working_hour_list": [
                {
                    "dia": 2,
                    "start_time": "10:00",
                    "end_time": "13:00",
                    "max_minutos_permanencia": 15,
                    "max_embarque_simultaneo": 4,
                },
                {
                    "dia": 3,
                    "start_time": "10:00",
                    "end_time": "13:00",
                    "max_minutos_permanencia": 15,
                    "max_embarque_simultaneo": 4,
                },
                {
                    "dia": 4,
                    "start_time": "10:00",
                    "end_time": "13:00",
                    "max_minutos_permanencia": 15,
                    "max_embarque_simultaneo": 4,
                },
                {
                    "dia": 5,
                    "start_time": "10:00",
                    "end_time": "13:00",
                    "max_minutos_permanencia": 30,
                    "max_embarque_simultaneo": 2,
                },
                {
                    "dia": 6,
                    "start_time": "10:00",
                    "end_time": "13:00",
                    "max_minutos_permanencia": 30,
                    "max_embarque_simultaneo": 2,
                },
            ],
            "always_working": False,
        }

        local = self._create_local_embarque(working_hours_dict)

        self.assertCountEqual(local["working_hour_list"], working_hours_dict["working_hour_list"])

    def test_staff_criar_local_sem_restricao_de_horario_com_weekdays(self):
        working_hours_dict = {
            "working_hour_list": [
                {
                    "dia": 2,
                    "start_time": "10:00",
                    "end_time": "13:00",
                    "max_minutos_permanencia": 15,
                    "max_embarque_simultaneo": 4,
                },
                {
                    "dia": 3,
                    "start_time": "10:00",
                    "end_time": "13:00",
                    "max_minutos_permanencia": 15,
                    "max_embarque_simultaneo": 4,
                },
                {
                    "dia": 4,
                    "start_time": "10:00",
                    "end_time": "13:00",
                    "max_minutos_permanencia": 15,
                    "max_embarque_simultaneo": 4,
                },
                {
                    "dia": 5,
                    "start_time": "10:00",
                    "end_time": "13:00",
                    "max_minutos_permanencia": 30,
                    "max_embarque_simultaneo": 2,
                },
                {
                    "dia": 6,
                    "start_time": "10:00",
                    "end_time": "13:00",
                    "max_minutos_permanencia": 30,
                    "max_embarque_simultaneo": 2,
                },
            ]
        }

        with prevent_request_warnings():
            create_local_res = self._request_create_local_embarque(working_hours_dict)

        self.assertEqual(400, create_local_res.status_code)

    def test_staff_criar_local_restricao_de_horario_com_start_time_invalido(self):
        working_hours_dict = {
            "working_hour_list": [
                {"dia": 2, "start_time": "26:23", "end_time": "13:00"},
                {"dia": 3, "start_time": "10:00", "end_time": "13:00"},
                {"dia": 4, "start_time": "10:00", "end_time": "13:00"},
                {"dia": 5, "start_time": "10:00", "end_time": "13:00"},
                {"dia": 6, "start_time": "10:00", "end_time": "13:00"},
            ],
            "always_working": False,
        }

        with prevent_request_warnings():
            create_local_res = self._request_create_local_embarque(working_hours_dict)

        self.assertEqual(400, create_local_res.status_code)

    def test_staff_criar_local_restricao_de_horario_com_end_time_invalido(self):
        working_hours_dict = {
            "working_hour_list": [
                {"dia": 2, "start_time": "10:00", "end_time": "28:00"},
                {"dia": 3, "start_time": "10:00", "end_time": "13:00"},
                {"dia": 4, "start_time": "10:00", "end_time": "13:00"},
                {"dia": 5, "start_time": "10:00", "end_time": "13:00"},
                {"dia": 6, "start_time": "10:00", "end_time": "13:00"},
            ],
            "always_working": False,
        }

        with prevent_request_warnings():
            create_local_res = self._request_create_local_embarque(working_hours_dict)

        self.assertEqual(400, create_local_res.status_code)

    def test_staff_criar_local_restricao_de_horario_com_day_de_tipo_invalido(self):
        working_hours_dict = {
            "working_hour_list": [
                {"dia": "Batata", "start_time": "10:00", "end_time": "13:00"},
                {"dia": 3, "start_time": "10:00", "end_time": "13:00"},
                {"dia": 4, "start_time": "10:00", "end_time": "13:00"},
                {"dia": 5, "start_time": "10:00", "end_time": "13:00"},
                {"dia": 6, "start_time": "10:00", "end_time": "13:00"},
            ],
            "always_working": False,
        }

        with prevent_request_warnings():
            create_local_res = self._request_create_local_embarque(working_hours_dict)

        self.assertEqual(400, create_local_res.status_code)

    def test_staff_criar_local_restricao_de_horario_com_day_invalido(self):
        working_hours_dict = {
            "working_hour_list": [
                {"dia": 64, "start_time": "10:00", "end_time": "13:00"},
                {"dia": 3, "start_time": "10:00", "end_time": "13:00"},
                {"dia": 4, "start_time": "10:00", "end_time": "13:00"},
                {"dia": 5, "start_time": "10:00", "end_time": "13:00"},
                {"dia": 6, "start_time": "10:00", "end_time": "13:00"},
            ],
            "always_working": False,
        }

        with prevent_request_warnings():
            create_local_res = self._request_create_local_embarque(working_hours_dict)

        self.assertEqual(400, create_local_res.status_code)

    def test_staff_criar_local_restricao_de_tipo_de_onibus(self):
        # onibus_suportados = ['toco', 'trucado', 'ld', 'dd', 'micro-onibus', 'van', 'carro']
        onibus_suportados = ["toco", "trucado"]

        local = self._create_local_embarque({"onibus_suportados": onibus_suportados})
        local_from_db = LocalEmbarque.objects.get(id=local["id"])

        onibus_suportados_from_db = [suporte.tipo for suporte in local_from_db.onibus_suportados.all()]
        self.assertCountEqual(onibus_suportados, onibus_suportados_from_db)
        self.assertCountEqual(onibus_suportados, local["onibus_suportados"])

    def test_staff_criar_local_restricao_de_tipo_de_onibus_tipo_micro_onibus(self):
        onibus_suportados = ["micro-onibus"]

        local = self._create_local_embarque({"onibus_suportados": onibus_suportados})
        local_from_db = LocalEmbarque.objects.get(id=local["id"])

        onibus_suportados_from_db = [suporte.tipo for suporte in local_from_db.onibus_suportados.all()]
        self.assertCountEqual(onibus_suportados, onibus_suportados_from_db)

    def test_staff_criar_local_restricao_de_tipo_de_onibus_tipo_invalido(self):
        onibus_suportados = ["toquinho"]

        with prevent_request_warnings():
            create_local_res = self._request_create_local_embarque({"onibus_suportados": onibus_suportados})

        self.assertEqual(400, create_local_res.status_code)

    def test_staff_criar_local_restricao_de_tipo_de_onibus_tipo_lista_vazia(self):
        onibus_suportados = []

        local = self._create_local_embarque({"onibus_suportados": onibus_suportados})
        local_from_db = LocalEmbarque.objects.get(id=local["id"])

        onibus_suportados_from_db = [suporte.tipo for suporte in local_from_db.onibus_suportados.all()]
        self.assertCountEqual(onibus_suportados, onibus_suportados_from_db)
        self.assertCountEqual([], local["onibus_suportados"])

    def test_staff_criar_local_embarque_aceita_embarque_ao(self):
        local = self._create_local_embarque({"aceita_embarque_ao": True})
        local_from_db = LocalEmbarque.objects.get(id=local["id"])

        self.assertEqual(True, local_from_db.aceita_embarque_ao)

    def test_staff_criar_local_restricao_de_embarque_simultaneo(self):
        max_embarque_simultaneo = 4

        local = self._create_local_embarque({"max_embarque_simultaneo": max_embarque_simultaneo})
        local_from_db = LocalEmbarque.objects.get(id=local["id"])

        self.assertEqual(max_embarque_simultaneo, local_from_db.max_embarque_simultaneo)
        self.assertEqual(max_embarque_simultaneo, local["max_embarque_simultaneo"])

    def test_staff_criar_local_restricao_de_embarque_simultaneo_max_zero(self):
        max_embarque_simultaneo = 0

        local = self._create_local_embarque({"max_embarque_simultaneo": max_embarque_simultaneo})
        local_from_db = LocalEmbarque.objects.get(id=local["id"])

        self.assertIsNone(local_from_db.max_embarque_simultaneo)

    def test_staff_criar_local_restricao_de_embarque_simultaneo_max_negative(self):
        max_embarque_simultaneo = -2

        with prevent_request_warnings():
            create_local_res = self._request_create_local_embarque({"max_embarque_simultaneo": max_embarque_simultaneo})

        self.assertEqual(400, create_local_res.status_code)

    def test_staff_criar_local_restricao_de_embarque_simultaneo_not_integer(self):
        max_embarque_simultaneo = "3 batata e uma coxinha"

        with prevent_request_warnings():
            create_local_res = self._request_create_local_embarque({"max_embarque_simultaneo": max_embarque_simultaneo})

        self.assertEqual(400, create_local_res.status_code)

    def _request_create_local_embarque(self, custom_props):
        client_infra = Client()
        client_infra.force_login(self.user_infra)
        params_dict = default_local_dict.copy()
        params_dict["cidade_id"] = self.b.cidade_bh.id
        params_dict.update(custom_props)

        params = json.dumps(params_dict)
        return client_infra.post("/api/staff/createlocal", {"local": params})

    def _create_local_embarque(self, custom_props):
        r = self._request_create_local_embarque(custom_props)
        return json.loads(r.content.decode("utf-8"))


default_local_dict = {
    "nickname": "Estacionamento Tietê",
    "endereco": "Rua Voluntários da Pátria",
    "endereco_numero": "344",
    "endereco_bairro": "Santana",
    "endereco_logradouro": "Rua Voluntários da Pátria",
    "endereco_cep": "02010-902",
    "description": "alo alo rapaziada",
    "mapurl": "google.com",
    "latitude": -20.123231,
    "longitude": -21.128933,
}
