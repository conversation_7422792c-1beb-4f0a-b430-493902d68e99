# coding: utf-8
import json
from datetime import <PERSON><PERSON><PERSON>

from django.contrib import admin, messages
from django.contrib.admin import ModelAdmin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from django.core.checks import Error, register
from django.core.exceptions import MultipleObjectsReturned, PermissionDenied
from django.db import models
from django.db.models import J<PERSON><PERSON><PERSON>
from django.forms import CheckboxSelectMultiple, ModelForm
from django.http.request import HttpRequest
from django_json_widget.widgets import JSONEditorWidget
from social_django import admin as social_admin  # noqa
from social_django.models import Association, Nonce
from taggit import admin as taggit_admin

from commons import guard
from commons.dateutils import now
from core.forms.buckets_forms import BucketForm
from core.models_commons import (
    AliasPlace,
    Cidade,
    CidadeInfo,
    CidadeMetrica,
    GlobalSetting,
    HelpQuestion,
    Imagem,
    NotificationBatch,
    PassagemG<PERSON><PERSON><PERSON><PERSON><PERSON>st,
    PersonaNonGrata,
    PontoDeVenda,
    Profile,
    Survey,
    SurveyQuestion,
    Tag,
    TextoEstatico,
)
from core.models_company import (
    Company,
    CompanyLending,
    MotivoMulta,
    NotaFiscal,
    RepasseMarketplace,
    TaxaServicoCheckout,
)
from core.models_grupo import Grupo, TrechoClasse
from core.models_rota import LocalEmbarque, LocalRetiradaMarketplace, RotaPrincipal
from core.models_travel import Buseiro, MotivoAlteracao, TaxaCancelamento, Travel, TravelSurvey
from core.service.reserva import reserva_svc


class DeleteNotAllowedModelAdmin(admin.ModelAdmin):
    """Admin do django para desabilitar a ação de excluir
    mesmo para usuários que tem essa permissão.
    """

    def get_actions(self, request):
        actions = super(DeleteNotAllowedModelAdmin, self).get_actions(request)
        actions.pop("delete_selected", None)
        return actions

    def has_delete_permission(self, request: HttpRequest, obj=None):
        return False

    def delete_model(self, request: HttpRequest, obj) -> None:
        raise PermissionDenied("Não pode excluir registro pela admin.")


class BucketsJSONWidget(JSONEditorWidget):
    def format_value(self, value):
        if value == "null":
            placeholder_bucket = {"tamanho": "insira inteiro", "max_split_value": "insira string"}
            value = json.dumps([placeholder_bucket, placeholder_bucket])
        return super(JSONEditorWidget, self).format_value(value)


class GlobalSettingAdmin(admin.ModelAdmin):
    search_fields = ("key",)
    fields = ("key", "value")
    list_display = ("key", "value")


class AliasPlaceAdmin(DeleteNotAllowedModelAdmin):
    search_fields = ("alias", "cidade__name")
    fields = ("alias", "cidade")
    list_editable = ("cidade", "alias")
    list_display = ("id", "alias", "cidade", "slug")
    readonly_fields = ("slug",)
    autocomplete_fields = ("cidade",)


class CidadeAdmin(DeleteNotAllowedModelAdmin):
    fields = (
        "name",
        "uf",
        "sigla",
        "slug",
        "ativo",
        "timezone",
        "img_cc",
        "image",
        "city_code_ibge",
        "uf_code_ibge",
    )
    search_fields = ["name"]
    list_display = ["id", "name", "uf", "slug", "sigla"]
    list_editable = ["name", "uf", "sigla"]
    readonly_fields = ["slug"]


class TaxaServicoCheckoutAdmin(DeleteNotAllowedModelAdmin):
    fields = ("company", "ativa", "percentual_referencia_marketplace")
    search_fields = ["company__name"]
    list_display = ["company", "ativa", "percentual_referencia_marketplace"]
    list_editable = ["ativa"]
    readonly_fields = ["company"]


class RepasseMarketplaceAdmin(DeleteNotAllowedModelAdmin):
    fields = ("company", "percentual_repasse")
    search_fields = ["company__name"]
    list_display = ["company", "percentual_repasse"]
    list_editable = ["percentual_repasse"]
    readonly_fields = ["company"]


class MotivoMultaAdmin(DeleteNotAllowedModelAdmin):
    fields = ("codigo", "descricao", "percentual_padrao", "valor_fixo", "base_calculo", "is_active")
    search_fields = ["id", "codigo", "descricao"]
    list_display = ["id", "codigo", "descricao", "percentual_padrao", "valor_fixo", "base_calculo", "is_active"]


@admin.register(Company)
class CompanyAdmin(DeleteNotAllowedModelAdmin):
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "name",
                    "bank_account",
                    "gestor",
                    "reputation",
                    "is_simples_nacional",
                    "tipo_icms",
                    "nibo_id",
                    "emissao_nf_enabled",
                    "saque_enabled",
                    "is_enabled",
                    "vinculo",
                    "internal_note",
                )
            },
        ),
        (
            "Opções Avançadas",
            {
                "classes": ("collapse",),
                "fields": ("cnpj", "cnpj_saque_alternativo", "parent_company_hibrido"),
                "description": '<div style="color:red"><p style="font-size: large;"><strong>Consulte alguém mais experiente antes de alterar as informações abaixo.<strong></p><p><strong>CNPJ</strong>: Não alterar sem autorização do Financeiro.</p><p><strong>CNPJ Saque Alternativo</strong>: Só pode usar com procuração validada pelo Jurídico.</p></div>',
            },
        ),
    )
    search_fields = ["name", "cnpj"]
    list_display = ["name", "cnpj", "vinculo"]
    readonly_fields = ["bank_account", "gestor", "reputation"]
    formfield_overrides = {models.ManyToManyField: {"widget": CheckboxSelectMultiple}}

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = list(super().get_readonly_fields(request, obj))
        if not guard.can_manage_company_critical_data(request.user):
            readonly_fields += ["cnpj", "cnpj_saque_alternativo", "parent_company_hibrido"]
        return readonly_fields


class LocalEmbarqueAdmin(DeleteNotAllowedModelAdmin):
    def has_add_permission(self, request, obj=None):
        return False

    fields = (
        "cidade",
        "nickname",
        "nickname_slug",
        "endereco",
        "endereco_logradouro",
        "endereco_numero",
        "endereco_bairro",
        "bairro_slug",
        "endereco_cep",
        "description",
        "mapurl",
        "ativo",
        "original_mapurl",
        "latitude",
        "longitude",
        "dias_proibidos",
    )
    list_display = ["cidade", "endereco", "nickname", "description", "latitude", "longitude"]
    list_editable = ["endereco", "nickname", "latitude", "longitude"]
    readonly_fields = ["bairro_slug", "nickname_slug"]


class MotivoAlteracaoAdmin(DeleteNotAllowedModelAdmin):
    search_fields = ["descricao"]
    fields = ("descricao",)
    list_display = ["descricao"]


class LocalRetiradaMarketplaceAdmin(DeleteNotAllowedModelAdmin):
    search_fields = ["local_embarque", "company"]
    fields = ("local_embarque", "company", "tipo", "descricao")
    list_display = ["local_embarque", "company", "tipo", "descricao"]
    list_select_related = ["local_embarque", "company"]


class RotaPrincipalAdmin(DeleteNotAllowedModelAdmin):
    fields = ("eixo",)
    search_fields = ["id", "eixo"]
    list_display = ["id", "eixo"]
    list_editable = ["eixo"]


class UserProfileInline(admin.StackedInline):
    model = Profile
    fk_name = "user"
    can_delete = False
    verbose_name_plural = "profile"
    formfield_overrides = {models.ManyToManyField: {"widget": CheckboxSelectMultiple}}
    fields = (
        "fullname",
        "birth_date",
        "cep",
        "cell_phone",
        "cpf",
        "annotation",
        "verification_status",
        "cell_phone_confirmed",
        "verification_rejection_reason",
        "inactivation_reason",
        "reactivation_reason",
    )
    readonly_fields = ["inviter", "reputation"]


@admin.action(description="Desativar @buser.com.br")
def inactivate_buser_user(modeladmin, request, queryset):
    """Desativa, retirando todas as permissões de um usuário @buser.com.br e cancelando as viagens promocionais
    futuras.
    """

    try:
        user = queryset.get()
    except MultipleObjectsReturned:
        messages.error(request, "Selecione apenas um usuário por vez para essa ação.")
        return

    if any(not user.email.endswith("@buser.com.br") for user in queryset):
        raise RuntimeError("Selecione apenas @buser.")

    travels = list(
        Travel.objects.filter(
            user_id=user.id,
            status="pending",
            grupo__datetime_ida__gte=now() + timedelta(hours=24),
        )
    )
    tem_viagem_paga = any(t.promocao != "VIAGEM_GRATIS" for t in travels)
    if tem_viagem_paga:
        messages.error(request, f"Usuário {user.email} tem viagens sem ser promocionais! Cancele essas viagens antes.")
        return

    for travel in travels:
        reserva_svc.admin_cancel_travel(travel.id, reason="Conta inativa")

    user.is_active = False
    user.is_staff = False
    user.is_superuser = False
    user.groups.clear()
    user.user_permissions.clear()
    user.save()
    messages.success(request, f"Usuário '{user.email}' foi desativado, com todas as viagens promocionais canceladas.")


class UserAdmin(BaseUserAdmin):
    inlines = (UserProfileInline,)
    list_display = ["username", "first_name", "last_name", "email", "date_joined"]
    actions = [inactivate_buser_user]

    def has_delete_permission(self, request, obj=None):
        return False


class NotaFiscalAdmin(DeleteNotAllowedModelAdmin):
    search_fields = ["id"]
    fields = (
        "company",
        "valor",
        "discriminacao",
        "data_competencia",
        "data_emissao",
        "removed",
        "status",
        "status_sefaz",
        "s3key",
        "xml_s3key",
        "acao_sefaz",
        "provider",
        "fix_instructions",
    )
    list_display = ("company", "valor", "provider", "discriminacao", "data_competencia", "data_emissao", "removed")
    readonly_fields = ("company", "vendor")


class HelpQuestionAdmin(DeleteNotAllowedModelAdmin):
    fields = ("title", "slug", "description")
    list_display = ["title", "slug", "description"]
    readonly_fields = ["title", "slug", "description"]


class TravelSurveyAdmin(DeleteNotAllowedModelAdmin):
    fields = ("question", "choices", "active")
    list_display = ["question", "choices", "active"]


class PersonaNonGrataAdmin(DeleteNotAllowedModelAdmin):
    def get_queryset(self, request):
        return PersonaNonGrata.objects.all()

    fields = ["cpf", "reason", "updated_by", "restricao_ativa", "tipo"]
    list_display = ["cpf", "reason", "restricao_ativa", "tipo"]
    search_fields = ["cpf"]
    raw_id_fields = ["updated_by"]


class GrupoAdmin(DeleteNotAllowedModelAdmin):
    search_fields = ("id",)
    fields = (
        "notafiscal",
        "status",
        "checkin_status",
        "checkpoint_idx",
        "modelo_venda",
        "one_driver_exception",
        "active_driver",
        "percentual_repasse",
        "hidden_for_pax",
        "percentual_taxa_servico",
    )
    raw_id_fields = ("notafiscal",)
    readonly_fields = ["valor_frete"]


class PontoDeVendaAdmin(DeleteNotAllowedModelAdmin):
    def get_endereco(self, obj):
        endereco = ""
        if obj.endereco_logradouro:
            endereco += obj.endereco_logradouro
        if obj.endereco_numero:
            endereco += f", {obj.endereco_numero}"
        if obj.endereco_bairro:
            endereco += f" - {obj.endereco_bairro}"
        return endereco

    get_endereco.short_description = "Endereço"

    list_display = ("nickname", "get_endereco", "cidade", "ativo")
    search_fields = ("nickname", "endereco_cep", "endereco_bairro", "endereco_logradouro")
    fields = (
        "cidade",
        "mapurl",
        "ativo",
        "phone",
        "latitude",
        "longitude",
        "nickname",
        "endereco_referencia",
        "endereco_numero",
        "endereco_bairro",
        "endereco_cep",
        "endereco_logradouro",
    )


class ImagemAdmin(DeleteNotAllowedModelAdmin):
    search_fields = ["descricao", "image_file", "tipo"]
    fields = (
        "tipo",
        "descricao",
        "origin_url",
        "updated_at",
        "created_at",
        "cidade_info",
        "termo",
        "post",
        "ponto_turistico",
        "image_file",
    )
    readonly_fields = ["origin_url", "updated_at", "created_at"]
    list_display = ["tipo", "descricao", "image_file", "cidade_info_id", "updated_at", "created_at"]
    raw_id_fields = ("cidade_info", "termo", "post", "ponto_turistico")


class CidadeInfoAdmin(DeleteNotAllowedModelAdmin):
    search_fields = ["cidade__name", "descricao"]
    fields = ("descricao", "pontos_turisticos", "hospedagens", "restaurantes", "eventos", "cidade", "updated_at")
    readonly_fields = ["cidade", "updated_at"]
    list_display = ["cidade", "descricao", "updated_at"]


class CidadeMetricaAdmin(DeleteNotAllowedModelAdmin):
    search_fields = ["cidade__name"]
    fields = ["cidade", "viagens_count", "page_views"]
    list_display = ["cidade", "viagens_count", "page_views"]

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = ["viagens_count", "page_views"]
        if obj:
            return readonly_fields + ["cidade"]
        return readonly_fields


@admin.register(Buseiro)
class BuseiroAdmin(DeleteNotAllowedModelAdmin):
    search_fields = ("id", "cpf", "name", "email")
    list_display = ("id", "cpf", "name", "email")
    fields = ("name", "email", "cpf", "active", "user")
    readonly_fields = ("user",)


class CompanyLendingAdmin(DeleteNotAllowedModelAdmin):
    fields = ("refinancing", "status", "accepted")
    raw_id_fields = ["refinancing"]


@admin.register(NotificationBatch)
class NotificationBatchAdmin(DeleteNotAllowedModelAdmin):
    list_display = ("pk", "process_at", "processed")
    fields = ("notifications", "created_by", "process_at", "processed")
    readonly_fields = ("created_by", "processed")


class TagAdmin(taggit_admin.TagAdmin):
    fields = ("name", "slug")


class TaggedItemInline(taggit_admin.TaggedItemInline):
    fields = ("name", "slug")


class TextoEstaticoAdmin(DeleteNotAllowedModelAdmin):
    list_display = ("slug", "titulo", "atualizado_em", "updated_by")
    fields = ("slug", "titulo", "html", "updated_by", "atualizado_em", "created_at")
    readonly_fields = ("atualizado_em", "created_at")
    raw_id_fields = ("updated_by",)


class TrechoClasseForm(ModelForm):
    class Meta:
        model = TrechoClasse
        fields = ("buckets",)

    def clean(self):
        buckets = self.cleaned_data.get("buckets")
        if buckets:
            buckets = [BucketForm(**bucket).dict() for bucket in buckets]
        return self.cleaned_data


class TrechoClasseAdmin(DeleteNotAllowedModelAdmin):
    def has_add_permission(self, request, obj=None):
        return False

    search_fields = ("pk",)
    list_display = ("pk", "pessoas", "buckets", "closed", "closed_reason")
    fields = ("pessoas", "buckets", "closed", "closed_reason")
    readonly_fields = ("pessoas",)
    form = TrechoClasseForm
    formfield_overrides = {
        JSONField: {"widget": BucketsJSONWidget},
    }


class PassagemGratisModelAdmin(ModelAdmin):
    search_fields = ("pk", "cpf")
    list_display = ("pk", "cpf", "campanha", "due_date")
    fields = ("cpf", "campanha", "due_date", "user")
    raw_id_fields = ("user",)


# class SurveyInline(StackedInline):
#     model = Survey
#     filter_horizontal = ('questions',)


@admin.register(Survey)
class SurveyModelAdmin(ModelAdmin):
    search_fields = ("pk", "area", "theme", "category")
    list_display = ("pk", "area", "theme", "subtheme", "category", "idx", "is_active", "created_at", "survey_slug")
    fields = ("created_by", "area", "theme", "subtheme", "category", "idx", "is_active", "questions")
    raw_id_fields = ("created_by",)
    filter_horizontal = ["questions"]
    readonly_fields = ["survey_slug"]


@admin.register(SurveyQuestion)
class SurveyQuestionModelAdmin(ModelAdmin):
    search_fields = ("pk", "title", "subtitle", "options", "category")
    list_display = ("pk", "title", "subtitle", "options", "category", "created_at", "parent_question_id")
    fields = ("created_by", "title", "subtitle", "options", "category", "parent_question")
    raw_id_fields = (
        "created_by",
        "parent_question",
    )


@admin.register(Tag)
class TagModelAdmin(ModelAdmin):
    search_fields = ("id", "nome")
    list_display = ("id", "nome", "descricao", "hex_color", "hex_color_background")
    fields = ("nome", "descricao", "hex_color", "hex_color_background")


class TaxaCancelamentoAdmin(DeleteNotAllowedModelAdmin):
    fields = ("modelo_venda", "horas_limite", "taxa", "active")
    search_fields = ["modelo_venda"]
    list_display = ("id", "modelo_venda", "horas_limite", "taxa", "active")
    list_editable = ["active"]


# Core
admin.site.register(GlobalSetting, GlobalSettingAdmin)
admin.site.register(Cidade, CidadeAdmin)
admin.site.register(LocalEmbarque, LocalEmbarqueAdmin)
admin.site.register(NotaFiscal, NotaFiscalAdmin)
admin.site.register(HelpQuestion, HelpQuestionAdmin)
admin.site.register(TravelSurvey, TravelSurveyAdmin)
admin.site.register(MotivoMulta, MotivoMultaAdmin)
admin.site.register(PersonaNonGrata, PersonaNonGrataAdmin)
admin.site.register(Grupo, GrupoAdmin)
admin.site.register(PontoDeVenda, PontoDeVendaAdmin)
admin.site.register(LocalRetiradaMarketplace, LocalRetiradaMarketplaceAdmin)
admin.site.register(MotivoAlteracao, MotivoAlteracaoAdmin)
admin.site.register(RotaPrincipal, RotaPrincipalAdmin)
admin.site.register(Imagem, ImagemAdmin)
admin.site.register(CidadeInfo, CidadeInfoAdmin)
admin.site.register(CidadeMetrica, CidadeMetricaAdmin)
admin.site.register(CompanyLending, CompanyLendingAdmin)
admin.site.register(TextoEstatico, TextoEstaticoAdmin)
admin.site.register(PassagemGratisSafelist, PassagemGratisModelAdmin)
admin.site.register(AliasPlace, AliasPlaceAdmin)

admin.site.unregister(User)
admin.site.register(User, UserAdmin)
# admin.site.unregister(UserSocialAuth)
admin.site.unregister(Association)
admin.site.unregister(Nonce)
admin.site.register(TrechoClasse, TrechoClasseAdmin)
admin.site.register(TaxaServicoCheckout, TaxaServicoCheckoutAdmin)
admin.site.register(RepasseMarketplace, RepasseMarketplaceAdmin)
admin.site.register(TaxaCancelamento, TaxaCancelamentoAdmin)


@register()
def check_admin_fields(app_configs, **kwargs):
    """
    Valida se todos os `ModelAdmin.fields`.

    Isso é feito porque quando os `fields` não estão definidos, o Django faz
    autodiscover deles.

    No caso de FKs, o Django transforma em um select, renderizando todos os
    dados da FK. Temos tabelas gigantes em produção, o Django tenta carregar
    eles em memória (explode memória) e depois tenta renderizar eles no HTML
    (explode CPU).

    Defina os fields e coloque FKs para tabelas grandes (`User`, `Profile` e
    quase todas) nos `raw_id_fields` ou `readonly_fields`.
    """
    # Alguns ModelAdmin que não temos controle, mas nois_confia™.
    ALLOWED_LIST = {"auth.GroupAdmin", "auth.UserAdmin", "social_django.UserSocialAuthOption", "taggit.TagAdmin"}
    # Models gigantes que não devemos usar como field, porque o Django explode
    # em produção carregando tudo em memória.
    BIG_MODELS = {
        "auth.User",
        "core.ActivityLog",
        "core.TransactionEmail",
        "core.Lead",
        "core.Profile",
    }
    errors = []
    for cls in admin.site._registry.values():
        if str(cls) in ALLOWED_LIST:
            continue

        sane_fields = set(cls.raw_id_fields).union(cls.readonly_fields)

        fields = []
        if cls.fields:
            fields.extend(cls.fields)

        if cls.fieldsets:
            for _, fieldset in cls.fieldsets:
                fields.extend(fieldset["fields"])

        if not fields:
            errors.append(
                Error(
                    'Defina o atributo "fields" no ModelAdmin.',
                    hint="Sempre que alguém esquece disso, morre um coala e/ou trava em produção.",
                    obj=cls,
                    id="buserdjango.E001",
                )
            )

        for field_name in fields:
            model_field = getattr(cls.model, field_name)

            related_model = model_field.field.related_model
            if related_model:
                model_label = related_model._meta.label
                if model_label in BIG_MODELS and field_name not in sane_fields:
                    admin_name = cls.__class__.__name__
                    errors.append(
                        Error(
                            f"O campo {field_name} ({model_label}) no {admin_name} não deve ser usado como field.",
                            hint=f"Defina o campo {field_name} no {admin_name} "
                            "em raw_id_fields ou readonly_fields para não travar produção.",
                            obj=cls,
                        )
                    )

    return errors
