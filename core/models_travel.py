import json
import logging
import uuid
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from decimal import Decimal as D
from functools import cached_property
from json import JSONDecoder
from typing import TYPE_CHECKING, Any, ClassVar, TypedDict

import dateutil.parser
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import User
from django.contrib.postgres.fields import ArrayField
from django.core.exceptions import ValidationError
from django.core.validators import MaxV<PERSON>ueValidator, MinValueValidator
from django.db import IntegrityError, models, transaction
from django.db.models import Q
from django.db.models.functions import Length
from django.utils import timezone
from django_fsm import FSMField, transition
from django_fsm_log.decorators import fsm_log_by, fsm_log_description
from django_qserializer.serialization import SerializableManager, SerializableQuerySet

from accounting.service.accounting_utils import (
    LIMITE_DIAS_ESTORNO_CARTAO,
    LIMITE_DIAS_ESTORNO_PIX,
)
from commons import dateutils, signer, storage, ufs_brasil
from commons.dateutils import frombrdate, now, to_default_tz, to_default_tz_required, to_tz, tobrdate
from commons.django_model_utils import get_or_none
from commons.django_utils import error_str
from commons.enum import ModeloVenda
from commons.storage import private_media_storage
from commons.utils import hashint, only_alphanum, only_numbers, random_code
from core.constants import TIPOS_ASSENTO_CHOICES, TIPOS_ASSENTO_PESO
from core.enums import CategoriaEspecial
from core.forms.rodoviaria_forms import DadosBeneficioForm
from core.models_commons import CanalAtendimento, Cidade, Lead, PontoDeVenda
from core.models_company import Company, Onibus
from core.models_grupo import Grupo, GrupoClasse, TrechoClasse
from core.models_rota import Checkpoint, LocalEmbarque, Rota, TrechoVendido
from core.models_utils import ModelSimpleReprMixin
from core.serializers import serializer_trecho_classe
from core.service import globalsettings_svc

if TYPE_CHECKING:
    from django.db.models.manager import RelatedManager

    from core.models_contabil import AccountingOperation


class Buseiro(ModelSimpleReprMixin, models.Model):
    class LinkedStatus(models.TextChoices):
        CONFIRMED = "confirmed"
        PENDING = "pending"
        REFUSED = "refused"

    class TiposDeficiencias(models.TextChoices):
        FISICA = "fisica", "Deficiência física/motora"
        INTELECTUAL = "intelectual", "Deficiência intelectual"
        VISUAL = "visual", "Deficiência visual"
        AUDITIVA = "auditiva", "Deficiência auditiva"

    objects: ClassVar[SerializableManager] = SerializableManager()
    id: int
    user_id: int
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    is_mainpassenger = models.BooleanField(default=False)  # não faz mais o menor sentido, faz?
    name = models.CharField(max_length=512)
    social_name = models.CharField(max_length=512, null=True, blank=True)
    email = models.CharField(max_length=256, null=True, blank=True)
    cpf = models.CharField(max_length=11, null=True, blank=True)
    rg_number = models.CharField(max_length=64, null=True, blank=True)
    rg_orgao = models.CharField(max_length=64, null=True, blank=True)
    tipo_documento = models.CharField(max_length=64, null=True, blank=True)
    birthday = models.DateField(null=True, blank=True)
    phone = models.CharField(max_length=16, null=True, blank=True)
    dados_receita = models.TextField(null=True, blank=True, default="{}")
    created_on = models.DateTimeField(auto_now_add=True)
    active = models.BooleanField(default=True)
    cep = models.CharField(max_length=8, null=True, blank=True)
    linked_user_id: int
    linked_user = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True, related_name="buseiros_linkados"
    )
    linked_status = models.CharField(max_length=32, null=True, blank=True, choices=LinkedStatus.choices)
    linked_user_confirmation_token = models.CharField(max_length=6, null=True, blank=True)
    tipos_deficiencia = ArrayField(
        models.CharField(max_length=64, choices=TiposDeficiencias.choices), blank=True, default=list
    )

    class Meta:
        indexes = [
            models.Index(fields=["cpf"]),
            models.Index(fields=["linked_user_confirmation_token"]),
        ]

    CRITICAL_ATTRS = ["name", "cpf", "rg_number", "rg_orgao", "birthday", "tipo_documento"]
    ALL_ATTRS = CRITICAL_ATTRS + [
        "email",
        "social_name",
        "phone",
        "cep",
        "tipos_deficiencia",
        "linked_status",
        "linked_user_id",
    ]

    def update_from_dict(self, _d):
        d = dict(**_d)
        d["birthday"] = frombrdate(d.get("birthday"))
        d["cpf"] = only_numbers(d.get("cpf"))
        d["phone"] = only_numbers(d.get("phone", ""))
        d["rg_number"] = only_alphanum(d.get("rg_number", ""))
        d["rg_orgao"] = d.get("rg_orgao")
        d["cep"] = only_numbers(d.get("cep", ""))
        d["tipos_deficiencia"] = d.get("tipos_deficiencia")
        d["linked_status"] = d.get("linked_status")
        d["linked_user_id"] = d.get("linked_user")
        for attr in Buseiro.ALL_ATTRS:
            setattr(self, attr, d.get(attr))
        self.save()
        _d["id"] = self.id
        return self

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "name": self.name,
            "social_name": self.social_name,
            "email": self.email,
            "cpf": self.cpf,
            "rg_number": self.rg_number,
            "rg_orgao": self.rg_orgao,
            "tipo_documento": self.tipo_documento,
            "rg": "%s / %s" % (self.rg_number or "", self.rg_orgao or ""),
            "birthday": tobrdate(self.birthday),
            "phone": self.phone,
            "active": self.active,
            "cep": self.cep,
            "linked_status": self.linked_status,
            "linked_user": self.linked_user.id if self.linked_user else None,
            "tipos_deficiencia": self.tipos_deficiencia,
        }

    def __str__(self):
        return self.name


CUPOM_TYPE_CHOICES = [(c, c) for c in ["PROMO", "CREDIT"]]


class Cupom(models.Model):
    class TipoOrigemDestino(models.TextChoices):
        CIDADES = "cidades"
        ESTADOS = "estados"

    id: int

    code = models.CharField(max_length=16, unique=True)
    is_voucher = models.BooleanField(default=False)
    is_parceria = models.BooleanField(default=False)
    fixed_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    discount = models.DecimalField(max_digits=12, decimal_places=4, null=True, blank=True)
    type = models.CharField(
        max_length=32, null=True, blank=True, choices=CUPOM_TYPE_CHOICES, default="CREDIT"
    )  # todo: deprecated. Agora é sempre 'PROMO'
    is_volta = models.BooleanField(default=False)
    is_nunca_viajou = models.BooleanField(default=False)
    is_apenas_com_convidados = models.BooleanField(default=False)
    is_apenas_reserva = models.BooleanField(default=False)
    start_date = models.DateTimeField()
    due_date = models.DateTimeField()
    title = models.TextField(null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    datetime_ida_start = models.DateTimeField(null=True, blank=True)
    datetime_ida_end = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    count_usages = models.IntegerField(default=0)
    rotas = models.ManyToManyField(Rota, blank=True)
    created_by = models.ForeignKey(User, null=True, on_delete=models.SET_NULL)
    companies = models.ManyToManyField(Company, blank=True)
    cidades_origem = models.ManyToManyField(Cidade, related_name="cidades_de_origem", blank=True)
    cidades_destino = models.ManyToManyField(Cidade, blank=True)
    estados_origem = ArrayField(models.CharField(max_length=2, choices=ufs_brasil.UFS), blank=True, default=list)
    estados_destino = ArrayField(models.CharField(max_length=2, choices=ufs_brasil.UFS), blank=True, default=list)
    tipo_origem = models.CharField(max_length=16, choices=TipoOrigemDestino.choices, null=True, blank=True)
    tipo_destino = models.CharField(max_length=16, choices=TipoOrigemDestino.choices, null=True, blank=True)
    image_s3key = models.TextField(null=True, blank=True)
    regras_extras = ArrayField(models.TextField(null=True, blank=True), null=True, blank=True)
    tipos_assento = ArrayField(models.CharField(max_length=64, choices=TIPOS_ASSENTO_CHOICES), blank=True, null=True)
    max_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    min_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    max_discount = models.DecimalField(max_digits=12, decimal_places=4, null=True, blank=True)
    qtde_max_uso = models.IntegerField(null=True, blank=True)
    is_destaque_home = models.BooleanField(null=True, blank=True, default=False)
    exclusivo_app = models.BooleanField(null=True, blank=True, default=False)
    categorias = ArrayField(models.CharField(max_length=32, null=True, blank=True), blank=True, default=list)
    destaque_search_result = models.BooleanField(null=False, blank=True, default=False)

    class Meta:
        indexes = [
            models.Index(fields=["updated_at"]),
        ]

    objects: ClassVar[SerializableManager] = SerializableManager()

    @property
    def valor_maximo_da_viagem(self):
        if not self.fixed_value or not self.max_value:
            return None
        return self.fixed_value + self.max_value

    @property
    def time_to_expire(self):
        if self.due_date < now() + timedelta(hours=24):
            return (self.due_date - now()).total_seconds()
        else:
            return None

    @property
    def valor_minimo_da_viagem(self):
        if not self.min_value:
            if not self.value or not self.max_discount or self.max_discount <= 0:
                return None
        return self.min_value or self.value / self.max_discount

    def clean(self):
        super().clean()
        choices = {c[0] for c in TIPOS_ASSENTO_CHOICES}
        for tipo in self.tipos_assento:
            if tipo not in choices:
                raise ValidationError(f"Tipo de assento inválido: {tipo}")

    def get_type(self):
        if self.discount and self.discount > 0:
            return "DESCONTO_PERCENTUAL"
        elif self.fixed_value and self.fixed_value > 0:
            return "VALOR_FIXO"
        return "DESCONTO_ABSOLUTO"

    @property
    def valor_limite(self):
        if self.discount:
            return self.max_value
        if self.fixed_value and self.max_value:
            return self.max_value + self.fixed_value
        if self.value and self.max_discount:
            return self.value / self.max_discount
        return None

    def get_discount_limit(self):
        limit = ""
        if self.valor_maximo_da_viagem:
            limit = f" para viagens de até R$ {self.valor_maximo_da_viagem:.2f}"
        elif self.valor_minimo_da_viagem:
            limit = f" para viagens à partir de R$ {self.valor_minimo_da_viagem:.2f}"
        elif self.max_value:
            limit = f" limitado a um desconto de até R$ {self.max_value:.2f}"
        return limit

    @property
    def regra_description(self):
        if self.is_parceria:
            return "Promoção via parceria"
        limit = self.get_discount_limit()

        if self.discount:
            if self.is_volta:
                return f"Desconto de {self.discount * 100:.0f}% na ida" + limit
            return f"Desconto de {self.discount * 100:.0f}%" + limit
        if self.fixed_value:
            if self.is_volta:
                return f"Ida por R$ {self.fixed_value:.2f}" + limit
            return f"Viagem por R$ {self.fixed_value:.2f}" + limit
        if self.value:
            if self.is_volta:
                return f"Desconto de R$ {self.value:.2f} na ida" + limit
            return f"Desconto de R$ {self.value:.2f}" + limit
        return ""

    def trecho_is_valid_for_cupom(self, origem_slug, destino_slug):
        restricoes_de_trecho = list(self.trecho_restriction.all())

        if not restricoes_de_trecho:
            return True

        trechos_map = {(trecho.origem.slug, trecho.destino.slug, trecho.ida_e_volta) for trecho in restricoes_de_trecho}

        valid_trechos_combinations = [
            (origem_slug, destino_slug, True),
            (origem_slug, destino_slug, False),
            (destino_slug, origem_slug, True),
        ]

        return any(valid_combination in trechos_map for valid_combination in valid_trechos_combinations)

    def get_discount_value(self, split_value, travel_price=None):
        if self.discount and self.discount > D(0):
            if travel_price:
                if self.code == "REMARCANSMANUAL":
                    desconto = travel_price * self.discount
                else:
                    desconto = min(split_value, travel_price) * self.discount
                value = min(desconto, split_value)
            else:
                value = min(split_value * self.discount, split_value)
                if self.max_value and self.max_value > D(0):
                    value = min(value, self.max_value)
            return round(value, 2)
        elif self.fixed_value and self.fixed_value > D(0):
            return max(split_value - self.fixed_value, D(0))
        elif self.value and self.value > D(0):
            return min(self.value, split_value)
        return D(0)

    @property
    def assentos_description(self):
        if not self.tipos_assento:
            return ""

        tipos_assento = self.tipos_assento[:]
        if not tipos_assento:
            return None
        last = tipos_assento.pop()
        if not tipos_assento:
            return last
        return ", ".join(tipos_assento) + " e " + last

    def save(self, *args, **kwargs):
        self.code = self.code.upper()
        if not self.start_date:
            self.start_date = self.created_at or to_default_tz(now())
        super(Cupom, self).save(*args, **kwargs)

    def image_url(self):
        if not self.image_s3key:
            return None
        return storage.public_url(self.image_s3key)

    def validate_datetime_ida_for_restrictions(self, datetime_ida):
        date_restrictions = self.date_restrictions.all()
        for d in date_restrictions:
            valid = datetime_ida < d.date_start or datetime_ida > d.date_end
            if valid is False:
                return False
        return True

    def delete_date_restrictions(self, date_restrictions_updated=None):
        if date_restrictions_updated is None:
            self.date_restrictions.all().delete()
            return
        date_restrictions_updated_ids = [restriction.get("id", None) for restriction in date_restrictions_updated]
        self.date_restrictions.exclude(id__in=date_restrictions_updated_ids).delete()

    def create_or_update_date_restrictions(self, date_restrictions_updated):
        for restriction in date_restrictions_updated:
            restriction["cupom_id"] = self.id
            restriction["date_start"] = datetime.fromisoformat(restriction["date_start"]).replace(
                hour=0, minute=0, second=0
            )
            restriction["date_end"] = datetime.fromisoformat(restriction["date_end"]).replace(
                hour=23, minute=59, second=59
            )
            CupomDateRestrictions(**restriction).save()

    @property
    def atingiu_uso_maximo(self):
        return self.qtde_max_uso and self.count_usages >= self.qtde_max_uso

    @property
    def tipo(self):
        if self.is_parceria:
            return "parceria"
        if self.discount:
            return "percentual"
        if self.fixed_value:
            return "fixo"
        else:
            return "dinheiro"

    @property
    def label_desconto(self):
        return (self.discount * 100 if self.discount else None) or self.fixed_value or self.value

    def __str__(self):
        return f"{self.code}: {self.regra_description}"

    def to_dict_json(self) -> dict:
        rotas = [t.id for t in self.rotas.all()]
        companies = [company.id for company in self.companies.all()]
        cidades_origem = list(self.cidades_origem.all())
        cidades_destino = list(self.cidades_destino.all())

        return {
            "id": self.id,
            "code": self.code,
            "value": self.value,
            "discount": self.discount,
            "fixed_value": self.fixed_value,
            "is_volta": self.is_volta,
            "is_voucher": self.is_voucher,
            "is_parceria": self.is_parceria,
            "is_nunca_viajou": self.is_nunca_viajou,
            "exclusivo_app": self.exclusivo_app,
            "is_apenas_com_convidados": self.is_apenas_com_convidados,
            "is_apenas_reserva": self.is_apenas_reserva,
            "specific_groups": bool(rotas),
            "rotas": rotas,
            "companies": companies,
            "cidades_origem": cidades_origem,
            "cidades_destino": cidades_destino,
            "title": self.title,
            "regra": self.regra_description,
            "description": self.description,
            "created_at": to_default_tz(self.created_at).isoformat(),
            "start_date": to_default_tz(self.start_date).isoformat(),
            "due_date": to_default_tz(self.due_date).isoformat(),
            "datetime_ida_start": (
                to_default_tz(self.datetime_ida_start).isoformat() if self.datetime_ida_start else None
            ),
            "datetime_ida_end": to_default_tz(self.datetime_ida_end).isoformat() if self.datetime_ida_end else None,
            "background_image": self.image_url(),
            "regras_extras": self.regras_extras,
            "tipos_assento": self.assentos_description,
            "tipos_assento_list": self.tipos_assento,
            "qtde_max_uso": self.qtde_max_uso,
            "atingiu_uso_maximo": self.atingiu_uso_maximo,
            "count_usages": self.count_usages,
            "is_destaque_home": self.is_destaque_home,
            "promo_image": self.image_url(),
            "categorias": self.categorias,
        }


class CupomTrechoRestriction(models.Model):
    id: int
    cupom_id: int
    cupom = models.ForeignKey(Cupom, on_delete=models.CASCADE, related_name="trecho_restriction")
    origem_id: int
    origem = models.ForeignKey(Cidade, on_delete=models.CASCADE, related_name="cupom_origem_restriction")
    destino_id: int
    destino = models.ForeignKey(Cidade, on_delete=models.CASCADE, related_name="cupom_destino_restriction")
    ida_e_volta = models.BooleanField(default=False)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=["cupom", "origem", "destino"], name="unique_origem_destino_pair_per_cupom")
        ]


class CupomDateRestrictions(models.Model):
    id: int
    cupom_id: int
    cupom = models.ForeignKey(Cupom, on_delete=models.CASCADE, related_name="date_restrictions")
    date_start = models.DateTimeField(null=False, blank=False)
    date_end = models.DateTimeField(null=False, blank=False)


class VoucherManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(active=True)


class Voucher(models.Model):
    id: int

    cupom_id: int
    cupom = models.ForeignKey(Cupom, on_delete=models.CASCADE)
    code = models.CharField(max_length=256, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    due_date = models.DateTimeField(null=True, blank=True)
    fromuser_id: int
    fromuser = models.ForeignKey(User, blank=True, null=True, related_name="voucher_fromuser", on_delete=models.CASCADE)
    reason = models.CharField(max_length=256, null=True, blank=True)
    reason_key = models.CharField(max_length=256, null=True, blank=True)
    origin_reason_key = models.CharField(max_length=256, null=True, blank=True)
    reason_description = models.TextField(null=True, blank=True)
    active = models.BooleanField(default=True)
    canal_atendimento_id: int
    canal_atendimento = models.ForeignKey(CanalAtendimento, on_delete=models.DO_NOTHING, blank=True, null=True)
    link_jira = models.TextField(null=True, blank=True)
    link_slack = models.TextField(null=True, blank=True)
    protocolo_atendimento = models.TextField(null=True, blank=True)
    travel_price = models.DecimalField(max_digits=12, decimal_places=4, null=True, blank=True)

    objects = VoucherManager()
    objects_base = models.Manager()

    @classmethod
    def generate_code(cls):
        code = random_code(6)
        return code


class CupomLeadManager(SerializableManager):
    pass


class TravelQuerySet(SerializableQuerySet):
    def delete(self):
        return self.update(status="canceled", canceled_reason="Called delete on queryset")


class TravelManager(SerializableManager):
    def get_queryset(self):
        return TravelQuerySet(self.model, using=self._db)


PROMOCOES_PRICING = ("PROMO_PRICING", "PROMO_PRICING")


class Travel(ModelSimpleReprMixin, models.Model):
    class Status(models.TextChoices):
        PENDING = "pending"
        CANCELED = "canceled"

    class Seller(models.TextChoices):
        BUSER = "buser", "Buser"  # vendas feitas pela Buser
        PROMOTOR = "promotor", "Promotor de Vendas"  # vendas feitas pelo vendas.buser.com.br
        AGENCIA = "agencia", "Agência física"
        API = "api", "Api de Terceiros"  # vendas feitas via api de integração com terceiros

    objects = TravelManager(
        select_related=[
            "user__profile",
            "pagamento",
            "grupo_classe__grupo",
            "trecho_classe__trecho_vendido__rota",
            "trecho_classe__trecho_vendido__origem__cidade",
            "trecho_classe__trecho_vendido__destino__cidade",
            "trecho_classe__grupo",
            "trecho_classe__grupo_classe__grupo__rota",
            "trecho_classe__grupo_classe__closed_by",
            "trecho_vendido__rota",
            "trecho_vendido__origem__cidade",
            "trecho_vendido__destino__cidade",
        ],
        prefetch_related=[
            models.Prefetch(
                "grupo__rota__itinerario",
                queryset=Checkpoint.objects.to_serialize(),
            ),
            "trecho_classe__grupo_classe__trechoclasse_set",
        ],
    )

    id: int
    grupo_id: int
    grupo = models.ForeignKey(Grupo, on_delete=models.CASCADE)
    grupo_classe_id: int
    grupo_classe = models.ForeignKey(GrupoClasse, on_delete=models.CASCADE)
    trecho_classe_id: int
    trecho_classe = models.ForeignKey(TrechoClasse, on_delete=models.CASCADE)
    trecho_vendido_id: int
    trecho_vendido = models.ForeignKey(TrechoVendido, on_delete=models.CASCADE)
    user_id: int
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    contact_phone = models.CharField(max_length=16, null=True, blank=True)
    pagamento_id: int
    pagamento = models.ForeignKey(
        "Pagamento", null=True, blank=True, related_name="travels", db_column="pagamento_id", on_delete=models.CASCADE
    )
    status = models.CharField(max_length=32, default="pending", choices=Status.choices)
    source = models.CharField(max_length=128, null=True, blank=True)
    source_id = models.CharField(max_length=256, null=True, blank=True)
    canceled_reason = models.CharField(max_length=256, null=True, blank=True)
    user_cancel_reason = models.TextField(null=True, blank=True)
    canceled_on = models.DateTimeField(null=True, blank=True)
    count_seats = models.IntegerField()
    # value = models.DecimalField(null=True, max_digits=12, decimal_places=2)  # dah pra nao ter?
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    reservation_code = models.CharField(max_length=6, null=True, blank=True)
    max_split_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    ref_split_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    split_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    probability_on_reservation = models.CharField(max_length=32, default="very_low")
    promocao = models.CharField(max_length=64, null=True, blank=True)
    cupom_id: int
    cupom = models.ForeignKey(Cupom, null=True, blank=True, on_delete=models.CASCADE)
    voucher_id: int
    voucher = models.ForeignKey(Voucher, null=True, blank=True, on_delete=models.CASCADE)
    travel_instructions_sent = models.BooleanField(default=False)
    agasalho_reminder_sent = models.BooleanField(default=False)
    travel_ida_id: int
    travel_ida = models.ForeignKey("self", null=True, blank=True, on_delete=models.CASCADE)
    revendedor_user_id: int
    revendedor_user = models.ForeignKey(
        User, blank=True, null=True, related_name="travel_revendedor", on_delete=models.CASCADE
    )
    movido_em = models.DateTimeField(null=True)
    movido_de_id: int
    movido_de = models.ForeignKey(Grupo, on_delete=models.SET_NULL, null=True, related_name="travel_grupo_anterior")
    is_retencao = models.BooleanField(null=True, default=False)
    reserva_id: int
    reserva = models.ForeignKey("Reserva", on_delete=models.SET_NULL, null=True, blank=True)
    is_reembolsavel = models.BooleanField(default=True)
    seller = models.CharField(max_length=255, choices=Seller.choices, default=Seller.BUSER)
    pax_can_edit = models.BooleanField(null=True, default=True)
    tem_checkin: bool

    if TYPE_CHECKING:
        passageiro_set = RelatedManager["Passageiro"]()
        taxacancelamentotravel_set = RelatedManager["TaxaCancelamentoTravel"]()
        accountingoperation_set = RelatedManager[AccountingOperation]()

    # travel_antes_remanejamento aponta para a travel que existia antes de um remanejamento pelo staff

    # google gera um id pra o usuário que serve pra identificar as conversões feitas
    google_tracking_id = models.TextField(blank=True, null=True)

    # Quando enviamos uma transação de reserva para a rakuten, retornam pra gente o id
    # do documento salvo por eles. Se estiver em branco, ou essa travel não foi enviada
    # para a rakuten ainda, ou ocorreu um erro durante envio.
    rakuten_doc_id = models.UUIDField(null=True)

    travel_conexao_id: int
    travel_conexao = models.ForeignKey("core.TravelConexao", null=True, blank=True, on_delete=models.PROTECT)
    trecho_conexao_id: int
    trecho_conexao = models.ForeignKey("core.TrechoConexao", null=True, blank=True, on_delete=models.PROTECT)
    # protocolo relacionado ao atendimento que transborda e é convertido em venda pelos promotores
    protocolo_atendimento = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ["grupo__datetime_ida"]
        indexes = [
            models.Index(fields=["reservation_code"]),
            models.Index(fields=["updated_on"]),
            models.Index(fields=["-created_on"]),
        ]

    @classmethod
    def list(cls, grupo, *, serializer=None, status="pending"):
        q = (
            Travel.objects.to_serialize(serializer)
            .select_related("grupo", "grupo__rota")
            .filter(grupo=grupo, status=status)
        )
        return list(q)

    @classmethod
    def generate_reservation_code(cls):
        code, keepon = None, True
        while keepon:
            code = random_code(6)
            if not Travel.objects.filter(reservation_code=code).exists():
                keepon = False
        return code

    @property
    def gmv(self) -> Decimal:
        return self.max_split_value * self.count_seats

    @property
    def is_pagamento_ok(self):
        return not self.pagamento or self.pagamento.status == Pagamento.Status.PAID

    @property
    def is_pagamento_waiting(self):
        return self.pagamento and self.pagamento.status == Pagamento.Status.WAITING_PAYMENT

    @property
    def is_pagamento_processing(self):
        return self.pagamento and self.pagamento.status == Pagamento.Status.PROCESSING

    @property
    def is_grupo_ok(self):
        return self.grupo.status == Grupo.Status.TRAVEL_CONFIRMED or self.grupo.confirming_probability == "high"

    @property
    def status_ptbr(self):
        status_dict = {
            "pending": "ativa",
            "canceled": "cancelada",
            "done": "concluída",
            "noshow": "não compareceu",
            "travel_confirmed": "confirmada",
        }

        return status_dict.get(self.get_status(), self.get_status())

    @property
    def is_parceria(self):
        return self.cupom and self.cupom.is_parceria

    @property
    def has_connection(self):
        return self.travel_conexao_id is not None

    def get_travel_pairs(self):
        if not self.pagamento:
            return None
        _filter = ~Q(pk=self.id) & Q(pagamento=self.pagamento)
        if self.travel_conexao_id:
            _filter &= ~Q(travel_conexao_id=self.travel_conexao_id)
        return Travel.objects.filter(_filter)

    def get_promo_passengers(self, promocao=None):
        promocao = promocao or self.promocao
        passageiro_qs = self.passageiro_set.filter(removed=False)
        if promocao in PROMOCOES_PRICING:
            return passageiro_qs
        return passageiro_qs.filter(promocao=promocao)

    def remove_promocao(self):
        self.get_promo_passengers().update(promocao=None, cupom=None)
        self.promocao = None
        self.cupom = None
        self.save(
            update_fields=[
                "promocao",
                "cupom",
                "updated_on",
            ]
        )

    def apply_promocao(self, pax_ids, promocao: str, cupom: Cupom | None, voucher: Voucher | None):
        self.passageiro_set.filter(removed=False, id__in=pax_ids).update(promocao=promocao, cupom=cupom)
        self.cupom = cupom
        self.promocao = promocao
        self.voucher = voucher
        self.save(
            update_fields=[
                "cupom",
                "voucher",
                "promocao",
                "updated_on",
            ]
        )

    def cancel(self, reason, user_reason=""):
        self.status = "canceled"
        self.canceled_reason = reason
        self.canceled_on = dateutils.now()
        if user_reason:
            self.user_cancel_reason = user_reason
        self.save(
            update_fields=[
                "status",
                "canceled_reason",
                "canceled_on",
                "user_cancel_reason",
                "updated_on",
            ]
        )

    def delete(self, using=None, keep_parents=False):
        return self.cancel("Called delete on model")

    def uncancel(self):
        self.status = "pending"
        self.canceled_reason = None
        self.save(
            update_fields=[
                "status",
                "canceled_reason",
                "updated_on",
            ]
        )

    def get_status(self):
        is_pending_status = self.status == "pending"
        is_done_status = self.grupo.status == Grupo.Status.DONE

        if is_pending_status and is_done_status and self.grupo.checkin_ok():
            if any(x.checkin for x in self.passageiro_set.all()):
                return "done"
            return "noshow"

        return self.grupo.status if is_pending_status else self.status

    def update_count_seats(self):
        self.count_seats = self.passageiro_set.filter(removed=False).count()
        self.save(
            update_fields=[
                "count_seats",
                "updated_on",
            ]
        )

    @property
    def is_volta(self):
        return self.travel_ida_id

    def get_travels_ida_e_volta(self) -> tuple[list, list]:
        def _get_queryset(obj):
            _filter = Q(id=obj.id)
            if obj.travel_conexao_id:
                _filter |= Q(travel_conexao_id=obj.travel_conexao_id)
            return list(Travel.objects.filter(_filter).order_by("trecho_conexao__idx"))

        if self.is_volta:
            return _get_queryset(self.travel_ida), _get_queryset(self)
        else:
            travels_ida = _get_queryset(self)
            return travels_ida, list(Travel.objects.filter(travel_ida__in=travels_ida).order_by("trecho_conexao__idx"))

    def get_travels_belong_to_connection(self):
        if not self.has_connection:
            return Travel.objects.none()
        return Travel.objects.filter(travel_conexao_id=self.travel_conexao_id).order_by("trecho_conexao__idx")

    def get_original_historico_remarcacao(self):
        last_remarcacao = HistoricoRemarcacao.objects.filter(new_travel_id=self.id).first()
        if last_remarcacao:
            return last_remarcacao.old_travel.get_original_historico_remarcacao()
        return HistoricoRemarcacao.objects.filter(old_travel=self).first()

    def get_primeiro_historico_remanejamento(self, with_info_travel_antiga=False):
        """
        Pega o primeiro histórico de remanejamento da uma série de remanejamentos que culminou na criação desta travel.
        Caso não tenham havido remanejamentos envolvendo esta travel, retorna None.
        Ou seja, no seguinte cenário:
            travel_original -> remanejou 1x -> remanejou denovo -> ... -> travel_final
        a ideia é que esse método sempre retorne o histórico em que a travel_original é a travel_antiga
        Ignora os remanejamentos que foram feitos por remarcação gratuita por decisão do usuário para não haver erros.
        """
        remanejamento_para_esta_travel = (
            HistoricoRemanejamento.objects.filter(travel_nova=self).exclude(motivo="REMARCACAO_GRATUITA").first()
        )
        if remanejamento_para_esta_travel:
            return remanejamento_para_esta_travel.travel_antiga.get_primeiro_historico_remanejamento()
        else:
            if with_info_travel_antiga:
                historico_remanejamento = HistoricoRemanejamento.objects.select_related(
                    "travel_antiga__trecho_vendido__origem",
                    "travel_antiga__trecho_vendido__destino",
                    "travel_antiga__trecho_classe",
                    "travel_antiga__grupo_classe",
                )
            else:
                historico_remanejamento = HistoricoRemanejamento.objects.select_related(
                    "cupom_antigo", "voucher_antigo", "passageiro_promocao"
                )
            return historico_remanejamento.filter(travel_antiga=self).first()

    def _get_canceled_reason(self):
        if self.status == "pending" and self.grupo.status == Grupo.Status.CANCELED:
            canceled_reason = "GROUP_CANCELED"
            if self.grupo.canceled_reason:
                canceled_reason += "_" + self.grupo.canceled_reason
            return canceled_reason

        return self.canceled_reason

    def get_cancellation_deadline(self):
        cancellation_deadline = 3 if self.grupo.is_marketplace or self.grupo.is_hibrido else 1
        return self.trecho_classe.datetime_ida - timedelta(hours=cancellation_deadline)

    def to_dict_json(  # noqa: C901
        self,
        withpayment=False,
        withuser=False,
        withtrechoclasse=True,
        withcompanyinfo=False,
        withitinerario=False,
        withsource=False,
        with_embarque_status=False,
        withcompanylogo=False,
        marketplace_info=False,
        withveiculoinfo=False,
        signed=False,
        bo_info=False,
        with_rateio=False,
        taxa_servico_info=False,
        with_connection_info=False,
        with_addon_payments=False,
        with_revendedor=False,
        withValuePayment=False,
        include_deleted_grupo_classe=False,
        with_marcacao_assento=False,
    ):
        d = {
            "id": self.id,
            "travel_ida": self.is_volta,
            "user_id": self.user_id,
            "contact_phone": self.contact_phone,
            "status": self.get_status(),
            "canceled_reason": self._get_canceled_reason(),
            "user_cancel_reason": self.user_cancel_reason,
            "tipo_assento": self.grupo_classe.tipo_assento,
            "count_seats": self.count_seats,
            "reservation_code": self.reservation_code,
            "probability_on_reservation": self.probability_on_reservation,
            "promocao": self.promocao,
            "trecho_vendido_id": self.trecho_vendido_id,
            "created_on": self.created_on.isoformat(),
            "max_split_value": self.max_split_value,
            "cancellation_deadline": self.get_cancellation_deadline().isoformat(),
            "grupo_classe_id": self.grupo_classe_id,
            "grupo_classe_id_hashed": hashint(self.grupo_classe_id),
            "has_connection": self.has_connection,
            "travels_relacionadas_conexao": [],
            "origem": self.trecho_vendido.origem.cidade.name,
            "destino": self.trecho_vendido.destino.cidade.name,
            "has_marcacao_assento": self.trecho_classe.get_has_marcacao_assento() if with_marcacao_assento else None,
        }
        if withtrechoclasse:
            grupo = serializer_trecho_classe.serialize(
                self.trecho_classe,
                companyinfo=withcompanyinfo,
                withitinerario=withitinerario,
                with_embarque_status=with_embarque_status,
                companylogo=withcompanylogo,
                marketplace_info=marketplace_info,
                bo_info=bo_info,
                with_rateio=with_rateio,
                taxa_servico_info=taxa_servico_info,
                include_deleted_grupo_classe=include_deleted_grupo_classe,
            )
            if isinstance(grupo, list):
                grupo = grupo[0]
            grupo["checkin_status"] = self.grupo.checkin_status
            grupo["checkin_status_updated_at"] = self.grupo.checkin_status_updated_at
            grupo["modelo_operacao"] = self.grupo.modelo_operacao
            d["grupo"] = grupo
            d["grupo"]["hashed_id"] = hashint(self.trecho_classe_id)

        if self.has_connection and with_connection_info:
            travels_infos = list(
                self.get_travels_belong_to_connection().exclude(pk=self.pk).values_list("id", "reservation_code")
            )
            d["travels_relacionadas_conexao"] = travels_infos
        if withpayment:
            payment = self.pagamento
            d["payment"] = payment.to_dict_json() if payment else None
        if withValuePayment:
            payment = self.pagamento
            d["payment"] = payment and payment.value and {"value": payment.value}
        if with_addon_payments:
            payments = [
                addon.pagamento.to_dict_json(withtransactionid=True, with_pix_details=True)
                for addon in ItemAdicional.objects.filter(travel=self, is_upsell=True)
                .exclude(pagamento=None)
                .distinct("pagamento")
            ]
            d["addon_payments"] = payments
        if withuser:
            d["user"] = {
                "email": self.user.email,
                "name": self.user.get_full_name(),
                "id": self.user.id,
                "photo_url": self.user.profile.photo_url,
                "facebook_url": self.user.profile.get_facebook_url(),
            }
        if with_revendedor:
            d["revendedor"] = {
                "id": self.revendedor_user_id,
                "name": self.revendedor_user.get_full_name() if self.revendedor_user else None,
            }
        if withsource:
            d["source"] = self.source
            d["source_id"] = self.source_id
        if withveiculoinfo and self.grupo.onibus:
            d["tipo_veiculo"] = self.grupo.onibus.tipo
            d["onibus_plotado"] = self.grupo.onibus.is_plotado
            d["tem_banheiro"] = self.grupo.onibus.tem_banheiro
        else:
            d["tipo_veiculo"] = None
            d["onibus_plotado"] = None
            d["tem_banheiro"] = True
        if signed:
            d["signed"] = signer.sign_hash(str(self.reservation_code))

        return d

    def __str__(self):
        tc = self.trecho_classe
        tv = self.trecho_vendido
        cidade_origem = tv.origem.cidade
        s = "(%s) %s => %s, %s" % (
            self.reservation_code,
            tv.origem.cidade.name,
            tv.destino.cidade.name,
            to_tz(tc.datetime_ida, cidade_origem.timezone).strftime("%d/%m/%Y %H:%M"),
        )
        return s


class CupomLead(models.Model):
    id: int
    lead_id: int
    lead = models.ForeignKey(Lead, on_delete=models.CASCADE)
    cupom_id: int
    cupom = models.ForeignKey(Cupom, null=True, on_delete=models.CASCADE)
    voucher_id: int
    voucher = models.ForeignKey(Voucher, null=True, on_delete=models.CASCADE)
    travel_id: int
    travel = models.ForeignKey(Travel, on_delete=models.DO_NOTHING, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    objects: CupomLeadManager = CupomLeadManager()

    @classmethod
    def add(cls, lead, cupom, voucher, travel=None):
        if lead and (cupom is not None or voucher is not None):
            try:
                with transaction.atomic():
                    cls.objects.create(lead=lead, cupom=cupom, voucher=voucher, travel=travel)
            except IntegrityError:
                pass  # Já foi salvo esse par pra essa pessoa recentemente

    class Meta:
        unique_together = [("lead", "cupom"), ("lead", "voucher")]
        indexes = [
            models.Index(fields=["lead_id", "cupom"]),
            models.Index(fields=["lead_id", "voucher"]),
        ]


class DecimalJsonDecoder(JSONDecoder):
    def __init__(self) -> None:
        super().__init__(parse_float=Decimal)


class Reserva(models.Model):
    objects: ClassVar[SerializableManager] = SerializableManager()

    class Status(models.TextChoices):
        CONCLUIDA = "concluida"
        CRIADA = "criada"
        ENVIADO_PARA_PAGAMENTO = "enviado_para_pagamento"
        PAGAMENTO_PROCESSSADO = "pagamento_processado"
        INICIO_DE_PROCESSAMENTO = "inicio_de_processamento"
        TIMEOUT = "timeout"
        ERRO_NO_PROCESSAMENTO = "erro_no_processamento"

    id: int
    status = models.CharField(max_length=32, choices=Status.choices)
    is_async = models.BooleanField()
    input_payload = models.JSONField(decoder=DecimalJsonDecoder, null=True)
    is_authenticated = models.BooleanField(default=True, null=True, blank=True)
    #: mais detalhes sobre o erro se status == ERRO_NO_PROCESSAMENTO
    error_data = models.JSONField(decoder=DecimalJsonDecoder, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    user_id: int
    user = models.ForeignKey(get_user_model(), null=True, on_delete=models.SET_NULL)
    evento_contabil = models.JSONField(decoder=DecimalJsonDecoder, null=True)
    evento_credito = models.JSONField(decoder=DecimalJsonDecoder, null=True)

    if TYPE_CHECKING:
        travel_set = RelatedManager[Travel]()

    class Meta:
        indexes = [
            models.Index(fields=["is_async", "-created_at"]),
        ]

    def travels(self) -> list[Travel]:
        travel_list = list(self.travel_set.all())
        if travel_list:
            return travel_list

        payment = self.pagamento()
        if payment:
            return list(payment.travels.all())

        return []

    def __str__(self):
        return f"Reserva de id {self.id}"

    def error(
        self,
        error_message: str | None = "",
        help: list[str] | None = [],
        extra: dict | None = None,
        error_code: str | None = None,
    ):
        self.status = Reserva.Status.ERRO_NO_PROCESSAMENTO
        self.error_data = {
            "error": error_message,
            "error_code": error_code,
            "help": help,
        }
        if extra:
            self.error_data.update(extra)
        self.save(
            update_fields=[
                "status",
                "error_data",
                "updated_at",
            ]
        )

    def concluida(self, evento_contabil: list[dict[Any, Any]], evento_credito: list[dict[Any, Any]]):
        self.evento_contabil = evento_contabil
        self.evento_credito = evento_credito
        self.status = Reserva.Status.CONCLUIDA
        self.save(
            update_fields=[
                "status",
                "updated_at",
                "evento_contabil",
                "evento_credito",
            ]
        )

    def timeout(self):
        self.status = Reserva.Status.TIMEOUT
        self.save(
            update_fields=[
                "status",
                "updated_at",
            ]
        )

    def init_processamento(self):
        self.status = Reserva.Status.INICIO_DE_PROCESSAMENTO
        self.save(
            update_fields=[
                "status",
                "updated_at",
            ]
        )

    def enviado_para_pagamento(self):
        self.status = Reserva.Status.ENVIADO_PARA_PAGAMENTO
        self.save(
            update_fields=[
                "status",
                "updated_at",
            ]
        )

    def pagamento_processado(self):
        self.status = Reserva.Status.PAGAMENTO_PROCESSSADO
        self.save(update_fields=["status", "updated_at"])

    def set_evento_contabil(self, evento_contabil: list[dict[Any, Any]]):
        self.evento_contabil = evento_contabil
        self.save(
            update_fields=[
                "evento_contabil",
                "updated_at",
            ]
        )

    def set_evento_credito(self, evento_credito: list[dict[Any, Any]]):
        self.evento_credito = evento_credito
        self.save(
            update_fields=[
                "evento_credito",
                "updated_at",
            ]
        )

    def timed_out(self) -> bool:
        """Retorna se a reserva já passou do limite de espera como criada."""
        if self.status == Reserva.Status.CRIADA:
            age_in_seconds = (now() - self.created_at).total_seconds()
            return age_in_seconds > settings.ASYNC_PAYMENT_TIMEOUT
        return False

    def pagamento(self):
        return get_or_none(Pagamento, reserva_id=self.id)

    def categoria_especial_info(self):
        # TODO fazer isso dentro de core_passageiro
        payload = self.input_payload or {}
        categoria_especial = payload.get("categoria_especial") or CategoriaEspecial.NORMAL
        passengers = payload.get("passengers")

        if not passengers or categoria_especial == CategoriaEspecial.NORMAL:
            return categoria_especial, None
        passenger = passengers[0]
        dados_beneficio = passenger.get("dados_beneficio")
        dados_beneficio = DadosBeneficioForm.parse_obj(dados_beneficio) if dados_beneficio else None
        return categoria_especial, dados_beneficio

    def set_emissao_revendedor(self):
        input_payload = self.input_payload or {}
        input_payload["set_emissao_revendedor"] = True
        self.input_payload = input_payload
        self.save(update_fields=["input_payload"])


CADEIRINHA_STATUSES = [(c, c) for c in ["cadeirinha_buser", "cadeirinha_propria", "sem_cadeirinha"]]  # todo: deprecated


class PassageiroExtra(TypedDict):
    class EmissaoMarketplace(TypedDict):
        sucesso: bool
        saved_at: datetime
        detalhes_emissao: dict
        erro_emissao: str

    class BlockedSeatDict(TypedDict):
        poltrona: dict  # Assento
        tempo_limite_bloqueio: datetime | None
        external_payload: Any | None

    emissao: EmissaoMarketplace
    bloqueio_poltrona: BlockedSeatDict


class Passageiro(models.Model):
    id: int
    travel_id: int
    travel = models.ForeignKey(Travel, on_delete=models.CASCADE)
    buseiro_id: int
    buseiro = models.ForeignKey(Buseiro, on_delete=models.PROTECT)
    promocao = models.CharField(max_length=64, null=True, blank=True)
    cupom_id: int
    cupom = models.ForeignKey(Cupom, null=True, blank=True, on_delete=models.CASCADE)
    voucher_id: int
    voucher = models.ForeignKey(Voucher, null=True, blank=True, on_delete=models.CASCADE)
    removed = models.BooleanField(default=False)
    checkin = models.BooleanField(default=False)
    checkin_updated_at = models.DateTimeField(null=True, blank=True)
    noshow = models.BooleanField(default=False)  # TODO: deprecated
    seat_label = models.CharField(max_length=16, null=True, blank=True)
    seat_position = models.CharField(max_length=32, null=True, blank=True)
    cadeirinha_status = models.CharField(
        max_length=32, default="sem_cadeirinha", choices=CADEIRINHA_STATUSES
    )  # todo: deprecated
    needs_cadeirinha = models.BooleanField(default=False)
    emergency_contact = models.TextField(null=True, blank=True)  # TODO: deprecated
    rodoviaria_bpe_qrcode = models.URLField(null=True, blank=True)
    rodoviaria_monitriip_code = models.CharField(null=True, blank=True, max_length=254)
    poltrona = models.IntegerField(null=True, blank=True, default=None)
    removed_at = models.DateTimeField(null=True, blank=True)
    categoria_especial = models.TextField(choices=CategoriaEspecial.choices, null=True, blank=True)
    quantidade_bagagem = models.PositiveIntegerField(null=True, blank=True, default=0)
    extra = models.JSONField(default=dict)

    class Meta:
        ordering = ("id",)
        # TODO botar uma constraint unique aqui

    @classmethod
    def seat_key(cls, dpos):
        return "%s_%s_%s" % (dpos["x"], dpos["y"], dpos["z"])

    @classmethod
    def create(
        cls,
        travel,
        _d,
        buseiro_id,
        cupom=None,
        voucher=None,
        categoria_especial=CategoriaEspecial.NORMAL,
        commit=True,
        poltrona=None,
        quantidade_bagagem=0,
        extra={},
    ):
        d = {
            "travel": travel,
            "buseiro_id": buseiro_id,
            "removed": _d.get("removed", False),
            "promocao": _d.get("promocao"),
            "cupom": cupom,
            "voucher": voucher,
            "quantidade_bagagem": quantidade_bagagem,
            "extra": extra,
        }
        seat = _d.get("seat")
        if seat:
            pos = seat["position"]
            x, y, z = pos["x"], pos["y"], pos["z"]
            d["seat_label"] = seat["label"]
            d["seat_position"] = json.dumps([x, y, z])
        if categoria_especial:
            d["categoria_especial"] = categoria_especial
        if poltrona is not None:
            d["poltrona"] = poltrona
        if quantidade_bagagem is not None:
            d["quantidade_bagagem"] = quantidade_bagagem
        if commit:
            return cls.objects.create(**d)
        instance = cls(**d)
        instance.set_cadeirinha()
        return instance

    def set_cadeirinha(self):
        birthday = self.buseiro.birthday
        if birthday:
            date_ida = self.travel.grupo.datetime_ida.date()
            diff_months = (
                (date_ida.year - birthday.year) * 12
                + date_ida.month
                - birthday.month
                - (1 if birthday.day > date_ida.day else 0)
            )
            self.needs_cadeirinha = diff_months < 16

    def save(self, *args, **kwargs):
        self.set_cadeirinha()
        super(Passageiro, self).save(*args, **kwargs)

    def set_removed(self):
        self.removed = True
        self.removed_at = dateutils.now()
        self.save(
            update_fields=[
                "removed",
                "removed_at",
            ]
        )

    def toggle_checkin(self):  # TODO: vai morrer junto com o front novo
        self.checkin = not self.checkin
        self.checkin_updated_at = dateutils.now()
        self.save(
            update_fields=[
                "checkin",
                "checkin_updated_at",
            ]
        )

    def set_poltrona(self, poltrona):
        self.poltrona = poltrona
        self.save(
            update_fields=[
                "poltrona",
            ]
        )

    def set_bagagem(self, bagagem):
        self.quantidade_bagagem = bagagem
        self.save(
            update_fields=[
                "quantidade_bagagem",
            ]
        )

    def remove_poltrona(self):
        poltrona = self.poltrona
        self.poltrona = None
        self.save(
            update_fields=[
                "poltrona",
            ]
        )
        return poltrona

    @property
    def typed_extra(self) -> PassageiroExtra:
        return PassageiroExtra(**self.extra)

    @property
    def foi_emitido(self):
        emissao = self.extra.get("emissao", {})
        return emissao.get("sucesso", False) or not emissao.get("cancelado", False)

    @property
    def emissao_v2(self) -> bool:
        return self.typed_extra and "bloqueio_poltrona" in self.typed_extra

    def set_emissao_staff(self):
        extra = self.extra or {}
        extra["emissao_staff"] = True
        self.extra = extra
        self.save(update_fields=["extra"])

    def set_cancelamento_emissao_marketplace(self):
        emissao = self.extra.get("emissao", {})
        emissao["cancelado"] = True
        emissao["cancelado_at"] = timezone.now()
        self.extra["emissao"] = emissao
        self.save()

    def set_emissao_marketplace(self, detalhes_emissao: dict):
        self.extra["emissao"] = {"sucesso": True, "saved_at": timezone.now(), "detalhes_emissao": detalhes_emissao}
        self.save()

    def set_erro_emissao_marketplace(self, erro_emissao: Exception):
        self.extra["emissao"] = {"sucesso": False, "saved_at": timezone.now(), "erro_emissao": error_str(erro_emissao)}
        self.save()

    def to_dict_json(self) -> dict:
        d = {
            "pid": self.id,
            "removed": self.removed,
            "checkin": self.checkin,
            "promocao": self.promocao,
            "needs_cadeirinha": self.needs_cadeirinha,
            "poltrona": self.poltrona,
            "removed_at": self.removed_at,
            "quantidade_bagagem": self.quantidade_bagagem,
        }
        if self.seat_position:
            x, y, z = json.loads(self.seat_position)
            d["seat"] = {
                "label": self.seat_label,
                "position": {
                    "x": x,
                    "y": y,
                    "z": z,
                },
            }
        d.update(self.buseiro.to_dict_json())
        return d

    def get_seat_key(self):
        return "%s_%s_%s" % tuple(json.loads(self.seat_position))

    def __str__(self):
        return "%s / %s" % (self.travel.reservation_code, self.buseiro)


class ScoreCompra(models.Model):
    class Provider(models.TextChoices):
        CLEARSALE = "clearsale", "ClearSale"

    class Status(models.TextChoices):
        # (Aprovação Automática)
        # Pedido foi aprovado Automaticamente segundo parâmetros definidos na regra de aprovação automática
        APA = "APA"
        # (Aprovação Por Política)
        # Pedido aprovado por política estabelecida pelo cliente ou Clearsale
        APP = "APP"
        # (Reprovação Automática)
        # Pedido Reprovado Automaticamente por algum tipo de Regra de Negócio que necessite aplicá-la
        RPA = "RPA"
        # (Reprovação Por Política)
        # Pedido reprovado por política estabelecida pelo cliente ou Clearsale
        RPP = "RPP"

    id: int
    pagamento_id: int
    pagamento = models.ForeignKey("Pagamento", on_delete=models.DO_NOTHING)
    code = models.TextField(default="")
    provider = models.TextField(choices=Provider.choices)
    score = models.FloatField()
    status = models.TextField(choices=Status.choices)
    created_at = models.DateTimeField(null=True, auto_now_add=True)
    updated_at = models.DateTimeField(null=True, auto_now=True)


class Pagamento(models.Model):
    class Provider(models.TextChoices):
        PAGARME = "pagarme", "Pagar.me"
        STARK = "stark", "Stark"
        MERCADOPAGO = "mercadopago", "Mercado Pago"
        NUBANK = "nubank", "Nubank"
        MERCADOPAGO_SA = "mercadopago_sa", "Mercado Pago Sa"
        SPLITED = "splited", "Dividido"
        BARTE = "barte", "Barte"

    class Method(models.TextChoices):
        BOLETO = "boleto", "Boleto bancário"
        CREDIT_CARD = "credit_card", "Cartão de crédito"
        PIX = "pix", "Pix"
        DINHEIRO = "dinheiro", "Dinheiro"
        GIFT_CARD = "gift_card", "Gift card"
        SPLITED = "splited", "Dividido"
        NUPAY = "nupay", "NuPay"

    class Status(models.TextChoices):
        PAID = "paid", "Pago"
        PROCESSING = "processing", "Aguardando aprovação"
        PENDING_CHALLENGE = "pending_challenge", "Desafio pendente"
        WAITING_PAYMENT = "waiting_payment", "Aguardando pagamento"
        REFUNDED = "refunded", "Estornado"
        REFUSED = "refused", "Recusado"
        CANCELED = "canceled", "Cancelado"
        CHARGEDBACK = "chargedback", "Reclamado"
        CHARGEBACK_REFUND = "chargeback_refund", "Reclamado estornado"
        LATE = "late", "Pelo menos uma cobrança em atraso"
        PARTIALLY_PAID = "partially_paid", "Ainda há parcelas a serem pagas"
        ABANDONED = "abandoned", "Mais de 60 dias de inadimplência"
        INVALID = "invalid", "Inválido"

    class ChainType(models.TextChoices):
        DEFAULT = "default", "Default"
        MARKETPLACE = "marketplace", "Marketplace"

    id: int
    user_id: int
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    status = FSMField(choices=Status.choices, default=Status.WAITING_PAYMENT, max_length=64)
    status_detail = models.TextField(blank=True, null=True)
    value = models.DecimalField(max_digits=12, decimal_places=2)
    paid_value = models.DecimalField(max_digits=12, decimal_places=2, default=D(0), null=True, blank=True)
    refunded_value = models.DecimalField(max_digits=12, decimal_places=2, default=D(0))
    pending_refund_value = models.DecimalField(
        max_digits=12, decimal_places=2, default=D(0)
    )  # TODO: remover - nao tem mais reembolso de boleto
    jsondata = models.TextField()
    method = models.CharField(max_length=12, choices=Method.choices)
    provider = models.TextField(null=True, blank=True, choices=Provider.choices)

    cpf = models.CharField(max_length=32, null=True, blank=True)
    transaction_reprocessada_id = models.CharField(max_length=32, null=True, blank=True)
    transaction_id = models.CharField(max_length=128, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    confirmed_at = models.DateTimeField(null=True)
    net_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    refunded_net_value = models.DecimalField(max_digits=12, decimal_places=2, default=D(0), null=True, blank=True)
    parcela_count = models.IntegerField(default=1, null=True, blank=True)
    parcela_value = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    juros_factor = models.DecimalField(max_digits=12, decimal_places=4, default=D("1.0000"), null=True, blank=True)
    ponto_de_venda_id: int
    ponto_de_venda = models.ForeignKey(PontoDeVenda, null=True, blank=True, on_delete=models.SET_NULL)
    meio_pagamento = models.ForeignKey("core.MeioPagamento", null=True, blank=True, on_delete=models.SET_NULL)
    #: Pagamentos os quais temos confiança de que provavelmente não é fraude
    nois_confia = models.BooleanField(default=False)
    split_pagamento = models.ForeignKey("self", null=True, on_delete=models.CASCADE, related_name="split_pagamento_set")
    reserva_id: int
    reserva = models.ForeignKey("Reserva", on_delete=models.SET_NULL, null=True)
    chain_type = models.CharField(
        max_length=30,
        choices=ChainType.choices,
        null=True,
        blank=True,
        help_text="The chain used to process the payment, if it's null means that we didn't use the payment chain "
        "(feature flag) on this payment.",
    )

    CREDIT_CARD_PROVIDERS = {Provider.MERCADOPAGO, Provider.MERCADOPAGO_SA, Provider.BARTE}
    MERCADOPAGO_PROVIDERS = {Provider.MERCADOPAGO, Provider.MERCADOPAGO_SA}
    STATUSES_OK = {Status.PAID}
    STATUSES_WAIT = {Status.PROCESSING, Status.WAITING_PAYMENT, Status.PENDING_CHALLENGE}
    STATUSES_FAIL = {Status.REFUNDED, Status.REFUSED, Status.CANCELED, Status.CHARGEDBACK, Status.CHARGEBACK_REFUND}
    STATUS_CHARGEBACKED = {Status.CHARGEDBACK, Status.CHARGEBACK_REFUND}

    if TYPE_CHECKING:
        travels = RelatedManager[Travel]()

    class Meta:
        indexes = [
            models.Index(fields=["cpf"]),
            models.Index(fields=["transaction_id"]),
            models.Index(fields=["updated_at"]),
            models.Index(fields=["created_at"]),
            models.Index(fields=["provider", "method", "status"]),
        ]

    def __str__(self):
        return "%s: %s, %s no %s - %s" % (self.id, self.value, self.status, self.method, self.provider)

    @fsm_log_by
    @fsm_log_description
    @transition(field=status, source={*STATUSES_WAIT}, target=Status.CANCELED)
    def canceled(self):
        pass

    @fsm_log_by
    @fsm_log_description
    @transition(field=status, source={*STATUSES_WAIT, Status.REFUSED}, target=Status.PAID)
    def paid(self):
        """pode vir do status refused em caso de reprocessarmento"""
        pass

    @fsm_log_by
    @fsm_log_description
    @transition(field=status, source={*STATUSES_WAIT}, target=Status.REFUSED)
    def refused(self):
        pass

    @fsm_log_by
    @fsm_log_description
    @transition(field=status, source={*STATUSES_OK, Status.REFUSED}, target=Status.REFUNDED)
    def refunded(self):
        pass

    @fsm_log_by
    @fsm_log_description
    @transition(field=status, source={*STATUSES_OK, Status.CHARGEBACK_REFUND}, target=Status.CHARGEDBACK)
    def chargedback(self):
        pass

    @fsm_log_by
    @fsm_log_description
    @transition(
        field=status,
        source={*STATUSES_OK, Status.CHARGEDBACK},
        target=Status.CHARGEBACK_REFUND,
    )
    def chargeback_refund(self):
        pass

    @fsm_log_by
    @fsm_log_description
    @transition(field=status, source={Status.WAITING_PAYMENT, Status.REFUSED}, target=Status.PROCESSING)
    def processing(self):
        """pode vir do status refused em caso de reprocessarmento"""
        pass

    @fsm_log_by
    @fsm_log_description
    @transition(field=status, source={Status.REFUSED, Status.WAITING_PAYMENT}, target=Status.WAITING_PAYMENT)
    def waiting(self):
        """pode vir do status refused em caso de reprocessarmento"""
        pass

    @fsm_log_by
    @fsm_log_description
    @transition(
        field=status,
        source=Status.WAITING_PAYMENT,
        target=Status.PENDING_CHALLENGE,
    )
    def pending_challenge(self):
        pass

    def set_status(self, status: Status):
        if self.status == status:
            return
        change_status = {
            self.Status.PAID: self.paid,
            self.Status.PROCESSING: self.processing,
            self.Status.WAITING_PAYMENT: self.waiting,
            self.Status.PENDING_CHALLENGE: self.pending_challenge,
            self.Status.CANCELED: self.canceled,
            self.Status.REFUNDED: self.refunded,
            self.Status.REFUSED: self.refused,
            self.Status.CHARGEDBACK: self.chargedback,
            self.Status.CHARGEBACK_REFUND: self.chargeback_refund,
        }
        if status in change_status:
            change_status[status]()
        else:
            raise ValidationError(f"Status desconhecido {status}")

    def set_nois_confia(self, nois_confia_rule):
        self.nois_confia = True
        jsondata = self._jsondata
        jsondata["nois_confia_rule"] = nois_confia_rule
        self.jsondata = json.dumps(jsondata)
        self.invalidar_cache_jsondata()
        self.save(
            update_fields=[
                "jsondata",
                "nois_confia",
                "updated_at",
            ]
        )

    def set_transaction(self, transaction: dict, is_reprocessamento: bool = False):
        if self.transaction_id and not is_reprocessamento:
            buserlogger.error(
                "ERRO: pagamento %s ja possui transacao associada. Impossivel atribuir nova transacao %s",
                self.id,
                transaction,
            )
            raise Exception("Erro: pagamento jah possui transacao associada")
        self._update_confirmed_at(transaction)
        self.set_status(transaction["status"])
        self.status_detail = transaction.get("status_detail")
        self.provider = transaction["provider"]
        self.transaction_id = transaction["id"]
        self.transaction_reprocessada_id = transaction.get("transaction_reprocessada_id")
        self.paydata = transaction

        if self.provider in Pagamento.CREDIT_CARD_PROVIDERS:
            self.paid_value = round(transaction["paid_amount"] / D("100.00") * self.juros_factor, 2)
        else:
            self.paid_value = round(transaction["paid_amount"] / D("100.00"), 2)
        self.save()

    def update_status(self, transaction, net_value_estornar=D("0")):
        self._update_confirmed_at(transaction)
        self.set_status(transaction["status"])
        self.status_detail = transaction.get("status_detail")
        self.paydata = transaction

        if self.provider in Pagamento.CREDIT_CARD_PROVIDERS:
            self.paid_value = round(transaction["paid_amount"] / D("100.00") * self.juros_factor, 2)
            self.refunded_net_value = round(transaction["refunded_amount"] / D("100.00"), 2)
            self.refunded_value = round(self.refunded_net_value * self.juros_factor, 2)

            if self.provider == Pagamento.Provider.BARTE:
                # Barte trabalha com valores com juros, precisamos não aplicar novamente e
                # remover no net_value
                self.refunded_value = self.refunded_net_value
                self.refunded_net_value = round(self.refunded_value / self.juros_factor, 2)

        else:
            self.paid_value = round(transaction["paid_amount"] / D("100.00"), 2)
            self.refunded_value = round(transaction["refunded_amount"] / D("100.00"), 2)
            self.refunded_net_value = round(self.refunded_value / self.juros_factor, 2)

        if self.status not in self.STATUSES_WAIT | {self.Status.REFUSED, self.Status.CANCELED}:
            if net_value_estornar and (
                abs(net_value_estornar - self.refunded_net_value) <= D("0.01") or self.net_value == net_value_estornar
            ):
                self.refunded_net_value = net_value_estornar
            elif abs(self.net_value - self.refunded_net_value) <= D("0.01") or (
                self.paid_value > D("0") and abs(self.paid_value - self.refunded_value) <= D("0.01")
            ):
                self.refunded_net_value = self.net_value

        self.save()

    def travels_canceled(self) -> bool:
        travels = list(self.travels.select_related("grupo").all())
        if not (travels or self.is_divida or self.is_divida_pdv):
            return True

        for ptravel in travels:
            if ptravel.status == "canceled" or ptravel.grupo.status == Grupo.Status.CANCELED:
                return True
        return False

    def _update_confirmed_at(self, transaction):
        if self.status in self.STATUSES_WAIT and transaction["status"] in self.STATUSES_OK:
            self.confirmed_at = dateutils.now()

    def update_detalhes_pix(self, payment):
        self.payment = payment
        self.save(
            update_fields=[
                "jsondata",
                "updated_at",
            ]
        )

    def invalidar_cache_jsondata(self):
        self.__dict__.pop("_jsondata", None)

    @cached_property
    def _jsondata(self) -> dict:
        return json.loads(self.jsondata)

    @property
    def paydata(self):
        return self._jsondata.get("paydata") or {}

    @paydata.setter
    def paydata(self, value):
        paydata = self.paydata
        paydata.update(value)
        jsondata = self._jsondata
        jsondata["paydata"] = paydata
        self.jsondata = json.dumps(jsondata)
        self.invalidar_cache_jsondata()

    @property
    def payment(self):
        if self.provider in Pagamento.CREDIT_CARD_PROVIDERS:
            account_number = (
                (
                    (((self.paydata.get("point_of_interaction") or {}).get("transaction_data") or {}) or {}).get(
                        "bank_info"
                    )
                    or {}
                ).get("payer")
                or {}
            ).get("long_name", "")

            if account_number:
                return {
                    "account_number": account_number,
                }
            return {}
        return self._jsondata.get("payment") or {}

    @payment.setter
    def payment(self, value):
        jsondata = self._jsondata
        jsondata["payment"] = value
        self.jsondata = json.dumps(jsondata)
        self.invalidar_cache_jsondata()

    @property
    def boleto_url(self):
        if self.method != self.Method.BOLETO:
            return
        return self.paydata.get("boleto_url", "")

    @property
    def nupay_url(self):
        if self.method != self.Method.NUPAY:
            return
        return self.paydata.get("paymentUrl", "")

    @property
    def linha_digitavel(self):
        if self.method != self.Method.BOLETO:
            return
        return self.paydata.get("line", "")

    @property
    def boleto_expiration_date(self) -> str | None:
        if self.method not in (self.Method.BOLETO, self.Method.DINHEIRO):
            return
        return self.paydata.get("boleto_expiration_date", "")

    @property
    def pix_url(self):
        if self.method != self.Method.PIX:
            return
        return f"{settings.SITE_BASE_URL}/perfil/carteira"

    @property
    def pix_expiration_date(self) -> str | None:
        if self.method != self.Method.PIX:
            return
        return self.paydata.get("pix_expiration_date") or self.paydata.get("expiration_date", "")

    @property
    def nupay_expiration_date(self) -> str | None:
        if self.method != self.Method.NUPAY:
            return
        return self.paydata.get("expiration_date", "")

    @property
    def is_expired(self) -> bool:
        if not self.expiration_date:
            return True
        expiration_date = dateutil.parser.parse(self.expiration_date)
        return dateutils.now() > expiration_date

    @property
    def pix_brcode(self) -> str | None:
        if self.method != self.Method.PIX:
            return
        if self.provider in Pagamento.MERCADOPAGO_PROVIDERS:
            return ((self.paydata.get("point_of_interaction") or {}).get("transaction_data") or {}).get("qr_code", "")
        if self.provider == self.Provider.STARK:
            return self.paydata.get("brcode", "")

    @property
    def account_number(self) -> str | None:
        if self.method != self.Method.PIX:
            return
        return self.payment.get("account_number", "")

    @property
    def url(self) -> str | None:
        if self.method == self.Method.PIX:
            return self.pix_url
        if self.method == self.Method.BOLETO:
            return self.boleto_url
        if self.method == self.Method.NUPAY:
            return self.nupay_url

    @property
    def expiration_date(self) -> str | None:
        if self.method == self.Method.PIX:
            return self.pix_expiration_date
        if self.method == self.Method.BOLETO:
            return self.boleto_expiration_date
        if self.method == self.Method.NUPAY:
            return self.nupay_expiration_date

    @property
    def card_brand(self) -> str | None:
        if self.provider == self.Provider.PAGARME:
            return self.paydata.get("card_brand", "")
        if self.method == Pagamento.Method.CREDIT_CARD:
            return self._jsondata.get("card_brand")
        return ""

    @property
    def card_name(self) -> str | None:
        if self.method == Pagamento.Method.CREDIT_CARD:
            return self._jsondata.get("card_name")
        return ""

    @property
    def card_cpf(self) -> str | None:
        if self.method == Pagamento.Method.CREDIT_CARD:
            return self._jsondata.get("cpf")
        return ""

    @property
    def card_first_digits(self):
        if self.method != self.Method.CREDIT_CARD:
            return
        if self.provider in Pagamento.CREDIT_CARD_PROVIDERS:
            return (self.paydata.get("card") or {}).get("first_six_digits", "")

    @property
    def card_last_digits(self):
        if self.method != self.Method.CREDIT_CARD:
            return
        if self.provider in Pagamento.CREDIT_CARD_PROVIDERS:
            return (self.paydata.get("card") or {}).get("last_four_digits", "")
        if self.provider == Pagamento.Provider.PAGARME:
            return (self.paydata.get("card") or {}).get("last_digits", "")

    @property
    def credit_card_payment_expiration_date(self):
        if self.method != self.Method.CREDIT_CARD:
            return
        return self._jsondata.get("credit_card_payment_expiration_date", "")

    @property
    def money_release_date(self):
        if self.provider not in Pagamento.MERCADOPAGO_PROVIDERS:
            return
        money_release_date = self.paydata.get("money_release_date", "")
        if not money_release_date:
            return
        return to_default_tz(dateutil.parser.parse(money_release_date))

    @property
    def is_divida(self):
        return self._jsondata.get("is_divida")

    @property
    def is_divida_pdv(self):
        return self._jsondata.get("is_divida_pdv")

    @property
    def boleto_pdv_modificado(self):
        return self._jsondata.get("boleto_pdv_modificado")

    @property
    def old_pagamento_id(self):
        """se um boleto de pdv foi parcelado, essa property indica qual foi o boleto 'pai' que foi parcelado"""
        return self._jsondata.get("old_pagamento_id")

    @property
    def is_item_adicional(self):
        return not any([self.is_divida, self.is_divida_pdv]) and self._jsondata.get("is_item_adicional")

    @property
    def is_pagamento_reserva(self):
        return not any([self.is_divida, self.is_divida_pdv, self.is_item_adicional])

    @property
    def gift_card_serial(self):
        return self._jsondata.get("gift_card_serial")

    @property
    def is_parcela_divida_pdv(self):
        return self.is_divida_pdv and self.boleto_pdv_modificado and self.old_pagamento_id

    @property
    def method_is_boleto(self):
        return self.method == Pagamento.Method.BOLETO

    @property
    def method_is_pix(self):
        return self.method == Pagamento.Method.PIX

    @property
    def waiting_payment(self):
        return self.status == self.Status.WAITING_PAYMENT

    @property
    def status_interno(self):
        if not self._should_check_internal_status():
            return self.status

        travels = self._get_travels()

        if self._is_implicitly_canceled(travels):
            return "canceled"

        if self._has_any_canceled_travel(travels):
            return "canceled"

        if self._is_expired_boleto():
            return "canceled"

        if self._is_expired_pix():
            return "canceled"

        return self.status

    def _should_check_internal_status(self):
        return (
            self.method in {Pagamento.Method.BOLETO, Pagamento.Method.PIX}
            and self.status == self.Status.WAITING_PAYMENT
        )

    def _get_travels(self):
        if self.id is not None:
            return list(self.travels.select_related("grupo").all())
        return []

    def _is_implicitly_canceled(self, travels):
        return not travels and not self.is_divida and not self.is_divida_pdv

    def _has_any_canceled_travel(self, travels):
        return any(t.status == "canceled" or t.grupo.status == Grupo.Status.CANCELED for t in travels)

    def _is_expired_boleto(self):
        if self.method != Pagamento.Method.BOLETO:
            return False
        if not self.boleto_expiration_date:
            return False
        expiration = dateutil.parser.parse(self.boleto_expiration_date).date()
        return to_default_tz_required(now()).date() > expiration

    def _is_expired_pix(self):
        if self.method != Pagamento.Method.PIX:
            return False
        if not self.pix_expiration_date:
            return False
        expiration = dateutil.parser.parse(self.pix_expiration_date)
        return now() > expiration

    @property
    def str_via(self):
        return {
            Pagamento.Method.CREDIT_CARD: "via cartão",
            Pagamento.Method.PIX: "via pix",
            Pagamento.Method.BOLETO: "via boleto",
            Pagamento.Method.DINHEIRO: "via dinheiro",
            Pagamento.Method.GIFT_CARD: "via gift card",
        }.get(self.method, "")

    @property
    def payment_value(self):
        """
        Valor inteiro em centavos
        """
        return int(round(self.value * 100, 2))

    @property
    def mp_payment_value(self):
        """
        Valor decimal e sem juros/ Usado no Mercado Pago
        """
        return self.net_value

    @property
    def payment_reference(self):
        """
        Valor de referência pelo qual uma transação pode ser buscada no pagarme. Está com esse "b" na frente porque era
        assim que já funcionava antes da implementação do novo pagamento assíncrno.
        Para entender, procure os decorators _get_payment_reference_fallback
        """
        return f"b{self.id}"

    @property
    def device_id(self):
        return self._jsondata.get("device_id")

    @property
    def estornavel(self) -> bool:
        metodos_e_prazo = {
            "pix": LIMITE_DIAS_ESTORNO_PIX,
            "credit_card": LIMITE_DIAS_ESTORNO_CARTAO,
        }

        if self.status != Pagamento.Status.PAID:
            return False

        if self.method == Pagamento.Method.NUPAY:
            return True

        if self.method not in list(metodos_e_prazo.keys()) + [Pagamento.Method.NUPAY]:
            return False

        return now() - self.created_at <= timedelta(days=metodos_e_prazo.get(self.method, 0))

    @property
    def card_hash(self):
        # MERCADOPAGO_SA é sempre reprocessamento e sempre utiliza o hash gerado pela acc principal
        if self.provider == Pagamento.Provider.MERCADOPAGO_SA:
            return self._jsondata.get("card_hash_reprocess")
        return self._jsondata.get("card_hash")

    @property
    def save_card(self):
        # TODO (tech-debt): Só salvar no mercadopago por enquanto
        return self._jsondata.get("save_card") and self.provider != Pagamento.Provider.MERCADOPAGO_SA

    @property
    def cartao_novo(self):
        return not self._jsondata.get("card_id")

    @property
    def deve_reprocessar_sem_antifraude(self):
        if self.method == self.Method.CREDIT_CARD:
            return self.status == Pagamento.Status.REFUSED and self.status_detail in {
                "cc_rejected_high_risk",
                "cc_rejected_blacklist",
                "cc_rejected_3ds_challenge",
                "cc_rejected_3ds_mandatory",
            }

        elif self.method == self.Method.PIX:
            return (
                self.provider == Pagamento.Provider.MERCADOPAGO
                and self.status == Pagamento.Status.REFUSED
                and self.status_detail == "rejected_high_risk"
                and globalsettings_svc.get("reprocessar_antifraude_pix", False)
            )
        return False

    @property
    def using_chain(self):
        return self.method == Pagamento.Method.CREDIT_CARD and self.chain_type is not None

    @property
    def three_ds_url(self):
        return self.paydata.get("three_ds_info", {}).get("external_resource_url")

    @property
    def three_ds_creq(self):
        return self.paydata.get("three_ds_info", {}).get("creq")

    def to_dict_json(self, withtransactionid=False, with_pix_details=False):
        d = {
            "id": self.id,
            "value": self.value,
            "net_value": self.net_value,
            "paid_value": self.paid_value,
            "refunded_value": self.refunded_value,
            "parcela_count": self.parcela_count,
            "parcela_value": self.parcela_value,
            "refunded_net_value": self.refunded_net_value,
            "status": self.status_interno,
            "method": self.method,
            "provider": self.provider,
            "provider_name": self.get_provider_display(),
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "confirmed_at": self.confirmed_at.isoformat() if self.confirmed_at else None,
            "card_brand": self.card_brand,
            "card_last_digits": self.card_last_digits,
            "credit_card_payment_expiration_date": self.credit_card_payment_expiration_date,
            "boleto_expiration_date": self.boleto_expiration_date,
            "boleto_url": self.boleto_url,
            "boleto_linha_digitavel": self.linha_digitavel,
            "old_pagamento_id": self.old_pagamento_id if self.old_pagamento_id else None,
            "pix_expiration_date": self.pix_expiration_date,
            "nupay_expiration_date": self.nupay_expiration_date,
            "pix_url": self.pix_url,
            "brcode": self.pix_brcode,
            "nupay_url": self.nupay_url,
        }
        if withtransactionid:
            d["transaction_id"] = self.transaction_id
        if with_pix_details:
            d["pix_details"] = {"account_number": self.account_number} if self.payment else None
        return d


class Estorno(models.Model):
    id: int

    class Meta:
        indexes = [
            models.Index(
                fields=[
                    "idempotency_key",
                ]
            ),
        ]

    class Status(models.TextChoices):
        PROCESSANDO = "processando"
        SUCESSO = "sucesso"
        ERRO = "erro"

    pagamento_id: int
    pagamento = models.ForeignKey(Pagamento, on_delete=models.PROTECT)
    value = models.DecimalField(max_digits=12, decimal_places=2)
    net_value = models.DecimalField(max_digits=12, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    status_interno = FSMField(choices=Status.choices, default=Status.PROCESSANDO)
    erro = models.TextField(null=True)
    external_id = models.CharField(max_length=128, null=True, blank=True)
    status_externo = models.CharField(max_length=128, null=True, blank=True)
    idempotency_key = models.CharField(max_length=128, null=True, blank=True, unique=True)

    @fsm_log_by
    @fsm_log_description
    @transition(field=status_interno, source=Status.PROCESSANDO, target=Status.ERRO)
    def _set_erro(self, erro, status_externo: str | None = None):
        self.status_externo = status_externo
        self.erro = erro

    @fsm_log_by
    @fsm_log_description
    @transition(field=status_interno, source=Status.PROCESSANDO, target=Status.SUCESSO)
    def _set_success(self, status_externo: str | None = None):
        self.status_externo = status_externo

    def set_erro(self, erro, status_externo: str | None = None):
        self._set_erro(erro, status_externo)
        self.save(
            update_fields=[
                "status_externo",
                "erro",
                "status_interno",
                "updated_at",
            ]
        )

    def set_success(self, status_externo: str | None = None):
        self._set_success(status_externo)
        self.save(
            update_fields=[
                "status_externo",
                "status_interno",
                "updated_at",
            ]
        )

    def set_external_id(self, external_id: str | None):
        if external_id:
            self.external_id = external_id
            self.save(
                update_fields=[
                    "external_id",
                    "updated_at",
                ]
            )

    def set_idempotency_key(self):
        if not self.idempotency_key:
            self.idempotency_key = f"{uuid.uuid4()}-{self.id}"
            self.save(
                update_fields=[
                    "idempotency_key",
                    "updated_at",
                ]
            )


class ProviderCustomer(models.Model):
    id: int
    external_customer_id = models.TextField()
    customer_document_number = models.TextField()
    customerjson = models.JSONField()

    class Meta:
        indexes = [
            models.Index(fields=["customer_document_number"]),
        ]


class MeioPagamento(models.Model):
    class Provider(models.TextChoices):
        PAGARME = "pagarme", "Pagar.me"
        MERCADOPAGO = "mercadopago", "Mercado Pago"
        BARTE = "barte", "Barte"

    id: int
    user_id: int
    user = models.ForeignKey(User, on_delete=models.PROTECT)
    provider_customer_id: int
    provider_customer = models.ForeignKey(ProviderCustomer, null=True, on_delete=models.PROTECT)
    method = models.CharField(max_length=32)
    cardjson = models.TextField(null=True)
    billingjson = models.TextField(null=True)
    customerjson = models.TextField(null=True)
    first_digits = models.CharField(max_length=10)
    last_digits = models.CharField(max_length=10)
    expiration_date = models.CharField(max_length=10)
    provider = models.CharField(max_length=12, default=Provider.MERCADOPAGO, choices=Provider.choices)
    external_card_id = models.TextField(null=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=["external_card_id"]),
        ]

    def to_dict_json(self) -> dict:
        return {
            "id": self.card_id,
            "first_digits": self.first_digits,
            "last_digits": self.last_digits,
            "card_brand": self.card_brand,
        }

    @cached_property
    def _cardjson(self) -> dict:
        if not self.cardjson:
            return {}
        return json.loads(self.cardjson)

    @cached_property
    def _customerjson(self) -> dict:
        if not self.customerjson:
            return {}
        return json.loads(self.customerjson)

    @property
    def card_brand(self):
        if self.provider in Pagamento.CREDIT_CARD_PROVIDERS:
            return (self._cardjson.get("payment_method") or {}).get("id")
        if self.provider == self.Provider.PAGARME:
            return self.paydata.get("card_brand", "")
        return ""

    @property
    def card_id(self):
        if self.provider not in Pagamento.CREDIT_CARD_PROVIDERS:
            return
        return self.external_card_id

    @property
    def customer_id(self):
        if self.provider not in Pagamento.CREDIT_CARD_PROVIDERS:
            return
        if self.provider_customer is None:
            raise ValueError("Provider customer não encontrado")
        return self.provider_customer.external_customer_id

    @property
    def customer_cpf(self):
        if self.provider not in Pagamento.CREDIT_CARD_PROVIDERS:
            return
        if self.provider_customer is None:
            raise ValueError("Provider customer não encontrado")
        return self.provider_customer.customer_document_number

    @cached_property
    def address(self) -> dict:
        if self.provider not in Pagamento.CREDIT_CARD_PROVIDERS:
            return {}
        zip_code = self._customerjson.get("address", {}).get("zip_code")
        if not zip_code:
            return {}
        for addr in self._customerjson["addresses"]:
            if addr.get("zip_code") == zip_code:
                return addr
        return {}

    @property
    def street(self):
        return self.address.get("street_name")

    @property
    def number(self):
        return self.address.get("street_number")

    @property
    def neighborhood(self):
        return self.address.get("neighborhood", {}).get("name")

    @property
    def city(self):
        return self.address.get("city", {}).get("name")

    @property
    def state(self):
        state = self.address.get("state", {}).get("id", "")
        if state:
            return state.split("-")[1]

    @property
    def zipcode(self):
        return self.address.get("zip_code")

    @property
    def card_name(self):
        return f"{self._customerjson.get('first_name', '')} {self._customerjson.get('last_name', '')}"

    @property
    def expiration_month(self):
        return self.expiration_date[:2]

    @property
    def expiration_year(self):
        return self.expiration_date[2:4]


class LinkPagamento(models.Model):
    objects: ClassVar[SerializableManager] = SerializableManager()

    id: int

    jsondata = models.TextField(null=True)
    expiration_date = models.DateTimeField(null=False)
    pagamento_id: int
    pagamento = models.ForeignKey(Pagamento, null=True, on_delete=models.CASCADE)

    EXPIRY_TIME = 10

    class Meta:
        indexes = [
            models.Index(fields=["expiration_date"]),
        ]

    @classmethod
    def create(cls, jsondata):
        expiration_date = to_default_tz(now() + timedelta(minutes=LinkPagamento.EXPIRY_TIME))
        d = {"jsondata": jsondata, "expiration_date": expiration_date}
        return cls.objects.create(**d)


class TravelFeedbackQuerySet(SerializableQuerySet):
    def with_valid_comment(self):
        return self.annotate(comment_len=Length("comment")).filter(comment__isnull=False, comment_len__gt=3)


class TravelSurvey(models.Model):
    id: int

    question = models.TextField()
    choices = models.TextField()  # Separadas por vírugla
    active = models.BooleanField(default=True)

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "question": self.question,
            "choices": [c.strip() for c in self.choices.split(",")],
        }


class TravelFeedback(models.Model):
    id: int

    objects: ClassVar[SerializableManager] = SerializableManager.from_queryset(TravelFeedbackQuerySet)(
        select_related=[
            "travel__grupo__company",
            "travel__trecho_vendido__origem__cidade",
            "travel__trecho_vendido__destino__cidade",
            "travel__user__profile",
        ]
    )
    travel_id: int
    travel = models.ForeignKey(Travel, on_delete=models.CASCADE)
    rating = models.IntegerField()
    comment = models.TextField(blank=True, null=True)
    categoria = models.TextField(blank=True, null=True)
    sub_categoria = models.TextField(blank=True, null=True)
    email_sent = models.BooleanField(default=False)
    anonimo = models.BooleanField(default=False)  # todo deprecated
    jsontags = models.TextField(blank=True, null=True)  # todo deprecated
    survey_id: int
    survey = models.ForeignKey(TravelSurvey, null=True, blank=True, on_delete=models.CASCADE)
    survey_answer = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=["created_at"]),
        ]

    def to_dict_json(self, anonimo=True):
        grupo = self.travel.grupo
        trecho_vendido = self.travel.trecho_vendido
        user = self.travel.user
        return {
            "id": self.id,
            "nota": self.rating,
            "comentario": self.comment,
            "categoria": self.categoria,
            "sub_categoria": self.sub_categoria,
            "grupo_id": grupo.id,
            "grupo_description": "%s - %s %s - %s"
            % (
                trecho_vendido.origem.cidade.sigla,
                trecho_vendido.destino.cidade.sigla,
                to_default_tz(grupo.datetime_ida).strftime("%d/%m/%Y - %H:%M"),
                grupo.company.name if grupo.company else "",
            ),
            "user": {
                "name": user.get_full_name() if not anonimo else "Buseiro",
                "photo_url": user.profile.photo_url if not anonimo else "",
            },
            "created_at": self.created_at,
        }


class UserTravelEmailSent(models.Model):
    id: int

    class EmailType(models.TextChoices):
        TRAVEL_CONFIRMED = "travel_confirmed"

    user_id: int
    user = models.ForeignKey(User, unique=False, on_delete=models.CASCADE, null=False)
    travel_id: int
    travel = models.ForeignKey(Travel, unique=False, on_delete=models.PROTECT, null=False)
    sent_at = models.DateTimeField(auto_now=True, null=False)
    type = models.CharField(max_length=50, choices=EmailType.choices, null=False)


class Ressarcimento(models.Model):
    id: int
    objects: ClassVar[SerializableManager] = SerializableManager()

    user_id: int
    user = models.ForeignKey(User, related_name="ressarcimento", on_delete=models.CASCADE)
    fromuser_id: int
    fromuser = models.ForeignKey(User, related_name="ressarcimento_fromuser", on_delete=models.PROTECT)
    passenger_id: int
    passenger = models.ForeignKey(Passageiro, on_delete=models.CASCADE)
    value = models.DecimalField(max_digits=12, decimal_places=2, default=D(0))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    status = models.CharField(default="pendente", max_length=10, null=False, blank=False)
    reason_key = models.CharField(max_length=256, null=True, blank=True)
    reason_description = models.TextField(null=True, blank=True)
    travel_id: int
    travel = models.ForeignKey(Travel, on_delete=models.DO_NOTHING, blank=True, null=True)
    origin_reason_key = models.CharField(max_length=256, null=True, blank=False)
    canal_atendimento_id: int
    canal_atendimento = models.ForeignKey(CanalAtendimento, on_delete=models.DO_NOTHING, blank=True, null=True)
    comprovante_url = models.CharField(max_length=1024, null=True, blank=True)
    accounting_operation_id: int
    accounting_operation = models.ForeignKey(
        "core.AccountingOperation", on_delete=models.DO_NOTHING, blank=True, null=True
    )
    link_jira = models.TextField(null=True, blank=True)
    link_slack = models.TextField(null=True, blank=True)
    protocolo_atendimento = models.TextField(null=True, blank=True)


class HistoricoRemarcacao(models.Model):
    id: int

    objects: ClassVar[SerializableManager] = SerializableManager()

    created_at = models.DateTimeField(auto_now_add=True)

    old_travel_id: int
    old_travel = models.ForeignKey(Travel, on_delete=models.CASCADE, related_name="old_remarcacao")
    new_travel_id: int
    new_travel = models.ForeignKey(Travel, on_delete=models.CASCADE, related_name="new_remarcacao")


class HistoricoRemanejamento(models.Model):
    id: int

    class Status(models.TextChoices):
        CRIADO = "criado"
        CRIANDO_REGISTROS_CONTABEIS = "criando_registros_contabeis_remanejamento"
        CONCLUIDO = "concluido"
        ERRO_REGISTROS_CONTABEIS = "erro_criacao_registros_contabeis"

    objects: ClassVar[SerializableManager] = SerializableManager()

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    travel_antiga_id: int
    travel_antiga = models.ForeignKey(Travel, related_name="travel_antiga", on_delete=models.CASCADE)
    travel_nova_id: int
    travel_nova = models.ForeignKey(Travel, related_name="travel_nova", on_delete=models.CASCADE)
    # Esses campos são cancelados quando acontece um remanejamento, por isso precisam ser salvos aqui
    promocao_antiga = models.TextField(blank=True)
    cupom_antigo_id: int
    cupom_antigo = models.ForeignKey(Cupom, on_delete=models.DO_NOTHING, blank=True, null=True)
    voucher_antigo_id: int
    voucher_antigo = models.ForeignKey(Voucher, on_delete=models.DO_NOTHING, blank=True, null=True)
    # Passageiro que tinha a promoção aplicada. Precisamos saber para entender para quem aplicamos a promoção
    # em um possível caso de remarcação
    passageiro_promocao_id: int
    passageiro_promocao = models.ForeignKey(Passageiro, on_delete=models.DO_NOTHING, blank=True, null=True)
    status = models.CharField(max_length=64, choices=Status.choices, default=Status.CONCLUIDO)

    # Campos para calcular ressarcimento
    #: Esses campos de assentos estão sendo salvos incorretamente em alguns casos, por isso não devem ser
    # usados para queries
    # https://docs.google.com/document/d/15ZnCt2xMQ2-JRFQjN9TDebKT9YHk9AIHrdlB2-N1bAM/edit?pli=1#
    antigo_tipo_assento = models.CharField(max_length=64, blank=True, null=True, choices=TIPOS_ASSENTO_CHOICES)
    novo_tipo_assento = models.CharField(max_length=64, blank=True, null=True, choices=TIPOS_ASSENTO_CHOICES)

    custo_alteracao = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True, default=Decimal("0"))

    # motivo da alteração
    motivo = models.TextField(null=True, blank=True)
    reason = models.TextField(null=True, blank=True)

    # gambi pra o listener do remanejamento_signal funcinar com histórico remanejamento
    @property
    def travel(self):
        return self.travel_antiga

    @property
    def travel_remanejada(self):
        return self.travel_nova

    @property
    def downgrade(self):
        return TIPOS_ASSENTO_PESO[self.novo_tipo_assento] < TIPOS_ASSENTO_PESO[self.antigo_tipo_assento]

    @property
    def custo_compensacao(self):
        from core.service.ressarcimento_downgrade_svc import get_downgrade_value

        valor_que_o_pax_vai_receber_pela_diferenca_de_ticket = self.custo_alteracao
        valor_que_o_pax_deveria_receber_pela_mudanca_de_categoria = get_downgrade_value(
            self.travel_antiga, self.antigo_tipo_assento, self.novo_tipo_assento
        )
        valor_que_o_pax_vai_receber_como_compensacao = (
            valor_que_o_pax_deveria_receber_pela_mudanca_de_categoria
            - valor_que_o_pax_vai_receber_pela_diferenca_de_ticket
        )
        return max(valor_que_o_pax_vai_receber_como_compensacao, D("0"))


class AlteracaoTravel(models.Model):
    """
    A cada alteração que a travel sofrer um registro vai ser criado
    Isso não inclui remanejos para outros grupos, pois a travel é recriada
    """

    class TipoAlteracao(models.TextChoices):
        MUDANCA_DE_CLASSE = "change_class"
        MUDANCA_DE_HORARIO = "change_departure_hour"

    class CausadaPor(models.TextChoices):
        PARCEIRO = "parceiro", "usuário parceiro"
        ANALISTA = "analista", "analista"
        AUTOMATICO = "automatico", "automático"

    travel = models.ForeignKey(Travel, on_delete=models.DO_NOTHING)
    created_at = models.DateTimeField(auto_now_add=True)
    antigo_tipo_assento = models.CharField(max_length=64, blank=True, null=True, choices=TIPOS_ASSENTO_CHOICES)
    novo_tipo_assento = models.CharField(max_length=64, blank=True, null=True, choices=TIPOS_ASSENTO_CHOICES)
    antigo_horario = models.DateTimeField(null=True, blank=True)
    novo_horario = models.DateTimeField(null=True, blank=True)
    tipo = models.CharField(max_length=24, choices=TipoAlteracao.choices)

    motivo = models.ForeignKey("MotivoAlteracao", null=True, on_delete=models.SET_NULL)
    causada_por = models.TextField(choices=CausadaPor.choices, null=True)
    fluxo = models.TextField(null=True)


class MotivoAlteracao(models.Model):
    descricao = models.TextField()

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects: ClassVar[SerializableManager] = SerializableManager()


class RessarcimentoDowngrade(models.Model):
    class ReasonsDowngrade(models.TextChoices):
        MOVE_TRAVEL = "move_travel"
        TROCA_ONIBUS = "troca_onibus"
        RESSARCIMENTO_UNICO = "ressarc_unico"

    objects: ClassVar[SerializableManager] = SerializableManager()

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    passenger = models.ForeignKey(Passageiro, on_delete=models.CASCADE)
    value = models.DecimalField(max_digits=12, decimal_places=2)
    reason_key = models.CharField(max_length=16, choices=ReasonsDowngrade.choices, null=True)


class PromocaoParceria(models.Model):
    travel = models.ForeignKey(Travel, on_delete=models.CASCADE)
    cpf = models.CharField(max_length=11)


class TaxaCancelamento(models.Model):
    modelo_venda = models.CharField(max_length=15, choices=ModeloVenda.choices)
    horas_limite = models.IntegerField()
    taxa = models.DecimalField(max_digits=12, decimal_places=2)
    active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.modelo_venda}: ({self.horas_limite}h | {self.taxa}%)"

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["modelo_venda", "active"],
                name="unique_modelo_venda_active",
                condition=models.Q(active=True),
            )
        ]


class TaxaCancelamentoTravel(models.Model):
    taxa_cancelamento = models.ForeignKey(TaxaCancelamento, on_delete=models.CASCADE)
    travel = models.ForeignKey(Travel, on_delete=models.CASCADE)
    remarcacao_expirada = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["travel"],
                name="unique_travel",
            )
        ]


buserlogger = logging.getLogger("buserlogger")


class TravelComunicada(models.Model):
    class Periodo(models.TextChoices):
        MENOS_DE_24H = "menos_de_24h"
        DE_24H_A_48H = "de_24h_a_48h"
        DE_48H_A_72H = "de_48h_a_72h"
        MAIS_DE_72H = "mais_de_72h"

        @classmethod
        def from_datetime(cls, datetime_ida: datetime):
            if timezone.is_naive(datetime_ida):
                raise ValueError("datetime_ida precisa ter timezone")
            _now = dateutils.now()
            _24_HORAS = _now + timedelta(hours=24)
            _48_HORAS = _now + timedelta(hours=48)
            _72_HORAS = _now + timedelta(hours=72)
            if _now < datetime_ida <= _24_HORAS:
                return cls.MENOS_DE_24H
            elif _24_HORAS < datetime_ida <= _48_HORAS:
                return cls.DE_24H_A_48H
            elif _48_HORAS < datetime_ida <= _72_HORAS:
                return cls.DE_48H_A_72H
            elif datetime_ida >= _72_HORAS:
                return cls.MAIS_DE_72H
            else:
                # esse caso ocorre quando alguém do staff move um buseiro de grupo ou faz alguma alteração
                # depois do horário de início da viagem
                return cls.MENOS_DE_24H

    id: int
    travel = models.ForeignKey(Travel, on_delete=models.CASCADE)
    travel_id: int
    datetime_ida_mais_proximo = models.DateTimeField(null=True, blank=True)
    tipo_assento = models.CharField(max_length=64, null=True, blank=True, choices=TIPOS_ASSENTO_CHOICES)
    datetime_ida = models.DateTimeField(null=True, blank=True)
    duracao_ida = models.DurationField(null=True, blank=True)
    origem = models.ForeignKey(
        LocalEmbarque, null=True, blank=True, related_name="origem_travel_comunicada", on_delete=models.PROTECT
    )
    destino = models.ForeignKey(
        LocalEmbarque, null=True, blank=True, related_name="destino_travel_comunicada", on_delete=models.PROTECT
    )
    onibus = models.ForeignKey(Onibus, null=True, blank=True, on_delete=models.PROTECT)
    ultimo_periodo_avaliado = models.CharField(choices=Periodo.choices, max_length=16)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["travel"],
                name="unique_travel_comunicada",
            )
        ]
        indexes = [
            models.Index(fields=["-datetime_ida_mais_proximo", "ultimo_periodo_avaliado"]),
        ]


class TravelConexao(models.Model):
    conexao_id: int
    conexao = models.ForeignKey("core.Conexao", on_delete=models.PROTECT)
    travel_conexao_volta = models.ForeignKey(
        "self", related_name="travel_conexao_ida", null=True, blank=True, on_delete=models.SET_NULL
    )


class PaginaFeriado(models.Model):
    id: int

    # Basic
    code = models.SlugField(
        max_length=100, unique=True, help_text="Code que será usado para a URL do feriado, ex: corpus-christi"
    )
    name = models.CharField(max_length=100, help_text="Nome do feriado")
    day = models.PositiveSmallIntegerField(
        help_text="Dia do feriado entre 01 e 31", validators=[MinValueValidator(1), MaxValueValidator(31)]
    )
    month = models.PositiveSmallIntegerField(
        help_text="Mês do feriado entre 01 e 12", validators=[MinValueValidator(1), MaxValueValidator(12)]
    )

    # SEO
    page_title = models.TextField(
        null=True, blank=True, help_text="(MetaTag) Titulo do feriado que será usado pelo navegador/SEO"
    )
    page_description = models.TextField(
        null=True, blank=True, help_text="(MetaTag) Descrição do feriado que será usado para SEO"
    )

    # Search Box
    search_box_title = models.TextField(
        null=True, blank=True, help_text="Título que será exibido acima do caixa de search box"
    )
    search_box_text = models.TextField(
        null=True, blank=True, help_text="Texto e em baixo título que será exibido acima do caixa de search box"
    )
    background_image_s3key = models.TextField(
        null=True, blank=True, help_text="Imagem de fundo que será utilizada na página"
    )
    background_image_description = models.TextField(
        null=True, blank=True, help_text="Descrição em texto da imagem que foi utilizada"
    )

    # Additional sections
    first_section_title = models.TextField(null=True, blank=True, help_text="Título da primeira seção adicional")
    first_section_image_s3key = models.TextField(
        null=True, blank=True, help_text="Imagem utilizada na primeira additional section"
    )
    first_section_image_description = models.TextField(
        null=True, blank=True, help_text="Descrição em texto da imagem que foi utilizada na primeira additional section"
    )
    additional_sections = models.TextField(
        null=True,
        blank=True,
        help_text="Texto que vai nas seções adicionais, ex: Viaje com a Buser no feriado. Esse field é um JSON com a "
        "seguinte estrutura: {'section_id': [{'title': 'text', 'title': 'text'}]}",
    )

    objects: ClassVar[SerializableManager] = SerializableManager()

    class Meta:
        indexes = [
            models.Index(fields=["code"]),
            models.Index(fields=["day", "month"]),
        ]

    @property
    def background_image_url(self):
        if not self.background_image_s3key:
            return None
        return storage.public_url(self.background_image_s3key)

    @property
    def first_section_image_url(self):
        if not self.first_section_image_s3key:
            return None
        return storage.public_url(self.first_section_image_s3key)

    def __str__(self):
        return f"{self.code} ({self.day}/{self.month})"


class ItemAdicional(models.Model):
    class TipoItemAdicional(models.TextChoices):
        BAGAGEM_ADICIONAL = "BAGAGEM_ADICIONAL"
        MARCACAO_ASSENTO = "MARCACAO_ASSENTO"
        SEGURO_EXTRA = "SEGURO_EXTRA"

    class StatusItemAdicional(models.TextChoices):
        PENDENTE = "pendente"
        CONCLUIDO = "concluido"
        CANCELADO = "cancelado"

    id: int
    pagamento_id: int
    pagamento = models.ForeignKey(Pagamento, related_name="+", null=True, blank=True, on_delete=models.PROTECT)
    pax_id: int
    pax = models.ForeignKey(Passageiro, related_name="+", on_delete=models.PROTECT)
    travel_id: int
    travel = models.ForeignKey(Travel, related_name="+", on_delete=models.PROTECT)
    tipo = models.CharField(max_length=100, null=False, blank=False, choices=TipoItemAdicional.choices)
    status = models.CharField(max_length=100, null=False, blank=False, choices=StatusItemAdicional.choices)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    valor = models.DecimalField(max_digits=12, decimal_places=2, null=False, blank=False)
    quantidade = models.IntegerField(null=False, blank=False)
    is_upsell = models.BooleanField(default=True)
    poltrona = models.IntegerField(null=True, blank=True, default=None)

    objects: ClassVar[SerializableManager] = SerializableManager()

    class Meta:
        indexes = [
            models.Index(fields=["pax_id", "travel_id"]),
        ]

    def __str__(self):
        return f"{self.tipo} - {self.pax} - {self.travel.reservation_code}"

    def __repr__(self) -> str:
        return f"ItemAdicional(pax_id={self.pax_id}, travel_id={self.travel_id}, tipo={self.tipo})"

    def update_status(self, status: StatusItemAdicional) -> None:
        self.status = status
        self.save(update_fields=["status", "updated_at"])

    @classmethod
    def get_tipo_item_adicional_dict(cls) -> dict[TipoItemAdicional, str]:
        return {
            ItemAdicional.TipoItemAdicional.BAGAGEM_ADICIONAL: "Bagagem extra",
            ItemAdicional.TipoItemAdicional.SEGURO_EXTRA: "Seguro viagem",
            ItemAdicional.TipoItemAdicional.MARCACAO_ASSENTO: "Marcação de assento",
        }


class SolicitacaoRessarcimento(models.Model):
    class Status(models.TextChoices):
        PENDENTE = "pendente"
        PRONTO_PARA_PAGAR = "pronto_para_pagar"
        PAGO = "pago"
        NEGADO = "negado"

    class Categoria(models.TextChoices):
        DESLOCAMENTO = "deslocamento"

    valor = models.DecimalField(max_digits=12, decimal_places=2, null=False, blank=False)
    travel = models.ForeignKey(Travel, on_delete=models.PROTECT)
    categoria = models.TextField(null=False, blank=False, choices=Categoria.choices)
    status = FSMField(default=Status.PENDENTE, choices=Status.choices)
    created_at = models.DateTimeField(auto_now_add=True)
    data_conclusao = models.DateTimeField(null=True, blank=True)
    resposta = models.TextField(null=True, blank=True)
    observacao_interna = models.TextField(null=True, blank=True)

    @transition(field=status, source=Status.PRONTO_PARA_PAGAR, target=Status.PAGO)
    def pagar(self):
        """Transição de pendente para PAGO"""
        pass

    @transition(field=status, source=Status.PENDENTE, target=Status.NEGADO)
    def negar(self):
        """Transição de pendente para negado"""
        pass

    @transition(field=status, source=Status.PENDENTE, target=Status.PRONTO_PARA_PAGAR)
    def pronto_para_pagar(self):
        """Transição de pendente para pronto para pagar"""
        pass


class SolicitacaoRessarcimentoComprovante(models.Model):
    solicitacao = models.ForeignKey(SolicitacaoRessarcimento, on_delete=models.CASCADE)
    file = models.FileField(upload_to="private/pax_comprovante", storage=private_media_storage)
