from datetime import datetime, timedelta, timezone
from decimal import Decimal as D
from typing import Any

from pydantic import BaseModel, field_validator

from commons.dateutils import now
from core.enums import CategoriaEspecial
from core.models_company import PoltronaOnibus


class Assento(BaseModel):
    livre: bool | None = None
    x: int
    y: int
    andar: int = 1
    numero: int
    tipo_assento: str
    categoria_especial: CategoriaEspecial = CategoriaEspecial.NORMAL
    id: int | None = None
    ativo: bool | None = None
    preco: D | None = None

    def __eq__(self, other):
        if not isinstance(other, Assento):
            return False
        return self.numero == other.numero and self.x == other.x and self.y == other.y

    def __hash__(self):
        return hash((self.numero, self.x, self.y))

    @classmethod
    def from_numero_poltrona(cls, numero_poltrona: int) -> "Assento":
        """
        Cria um Assento a partir do número da poltrona
        """
        return cls(numero=numero_poltrona, x=0, y=0, tipo_assento="any")

    @staticmethod
    def from_poltrona_onibus(
        poltrona: PoltronaOnibus,
        livre: bool = True,
        categoria_especial: CategoriaEspecial = CategoriaEspecial.NORMAL,
    ) -> "Assento":
        """
        Args:
            poltrona: Objeto PoltronaOnibus a ser convertido
            livre: Indica se o assento está livre ou ocupado
            categoria_especial: Categoria especial do assento

        Returns:
            Um objeto Assento correspondente à poltrona do ônibus
        """
        return Assento(
            livre=livre,
            x=poltrona.coluna,
            y=poltrona.linha,
            andar=poltrona.andar,
            numero=poltrona.poltrona,
            tipo_assento=poltrona.tipo,
            categoria_especial=categoria_especial,
        )

    def to_poltrona_onibus(self, onibus_id: int, andar: int = 1) -> PoltronaOnibus:
        """
        Args:
            onibus_id: ID do ônibus ao qual a poltrona pertence
            andar: Andar do ônibus onde a poltrona está localizada

        Returns:
            Um objeto PoltronaOnibus correspondente a este assento
        """
        return PoltronaOnibus(
            onibus_id=onibus_id,
            poltrona=self.numero,
            coluna=self.x,
            linha=self.y,
            tipo=self.tipo_assento,
            ativo=True,
            andar=andar,
        )

    def disponivel(self, tipo_assento, categoria_especial, preco_maximo_assento: D | None) -> bool:
        return (
            bool(self.livre)
            and self.tipo_assento == tipo_assento
            and (categoria_especial is None or self.categoria_especial == categoria_especial)
            and (self.preco is None or preco_maximo_assento is None or self.preco <= preco_maximo_assento)
        )


class Deck(BaseModel):
    andar: int
    assentos: list[Assento]

    def assentos_disponiveis(self, tipo_assento, categoria_especial, preco_maximo_assento: D | None) -> list[Assento]:
        """
        Filtra os assentos disponíveis com base no tipo e na categoria especial.
        """
        return [
            assento
            for assento in self.assentos
            if assento.disponivel(tipo_assento, categoria_especial, preco_maximo_assento)
        ]


class MapaPoltronasOnibus(BaseModel):
    layout: list[Deck]


THRESHOLD_BLOQUEIO = timedelta(minutes=1)


class BlockedSeat(BaseModel):
    # TODO - BlockedSeat é um Assento, usar herança

    poltrona: Assento
    tempo_limite_bloqueio: datetime | None = None
    external_payload: Any | None = None
    # O tempo limite bloqueio é válido, porém recebemos o erro de que o bloqueio não existe mais
    force_expired: bool = False

    @field_validator("tempo_limite_bloqueio", mode="before")
    @classmethod
    def validate_tempo_limite_bloqueio(cls, v):
        if not v:
            return v
        if isinstance(v, str):
            v = datetime.fromisoformat(v)
        if v.tzinfo is None:
            return v.replace(tzinfo=timezone.utc)
        return v

    @classmethod
    def from_numero_poltrona(cls, numero_poltrona: int) -> "BlockedSeat":
        """
        Cria um BlockedSeat a partir do número da poltrona e do tempo limite de bloqueio.
        """
        return cls(
            poltrona=Assento.from_numero_poltrona(numero_poltrona),
        )

    @property
    def expired(self):
        return (
            self.force_expired
            or not self.tempo_limite_bloqueio
            or now() > self.tempo_limite_bloqueio - THRESHOLD_BLOQUEIO
        )

    def set_expired(self):
        self.force_expired = True
