import logging

from beeline import traced
from django.contrib.auth.models import User
from tenacity import retry, retry_if_exception_type, stop_after_attempt

from core.forms.rodoviaria_forms import BuseiroForm, ComprarForm
from core.models_travel import (
    Buseiro,
    Passageiro,
    Reserva,
    Travel,
)
from core.service import rodoviaria_svc
from core.service.selecao_assento import SeatNotBlocked, SeatSelectionConnectionError
from core.service.selecao_assento import marketplace as marketplace_selecao_assento
from core.service.selecao_assento.models import Assento, BlockedSeat
from integrations import get_client
from integrations.rodoviaria_client import RodoviariaClient
from integrations.rodoviaria_client.exceptions import (
    PoltronaExpiradaException,
    RodoviariaException,
    RodoviariaOverbooking,
    RodoviariaViagemBloqueada,
    RodoviariaViagemIndisponivel,
)

buserlogger = logging.getLogger("buserlogger")


def passagens_emitidas(travels: list[Travel]) -> bool:
    travels_rodoviaria = [t for t in travels if rodoviaria_svc._is_integrado(t.trecho_classe_id)]

    if not travels_rodoviaria:
        return True

    travels_ids = [t.id for t in travels_rodoviaria]
    buseiros_ids = list(Buseiro.objects.filter(passageiro__travel_id=travels_ids[0]).values_list("id", flat=True))
    passagens_emitidas = rodoviaria_svc.get_passagens_rodoviaria(travels_ids, buseiros_ids, "confirmada")

    # casos de conexão podem gerar mais de uma passagem por buseiro, por isso checa a quantidade minima, não exata
    todas_emitidas = bool(passagens_emitidas and len(passagens_emitidas) >= len(travels_ids) * len(buseiros_ids))

    buserlogger.info(
        "[rodoviaria_reserva_apos_pgto] _existe_passagens_emitidas_rodoviaria",
        extra={
            "existe_passagens_emitidas": todas_emitidas,
            "passagens_emitidas": passagens_emitidas,
            "travels_rodoviaria": travels_rodoviaria,
        },
    )

    return todas_emitidas


@traced("reserva_rodoviaria_svc.emitir_passagens_rodoviaria_passagem_unica")
def emitir_passagens_rodoviaria_passagem_unica(user: User | None, reserva: Reserva, travels: list[Travel], timeout=180):
    travels_rodoviaria = [t for t in travels if rodoviaria_svc._is_integrado(t.trecho_classe_id, only_marketplace=True)]

    if not travels_rodoviaria:
        return

    passageiros = list(Passageiro.objects.filter(travel__in=travels_rodoviaria))

    buyer_cpf = _get_buyer_cpf(user, passageiros)
    for pax in passageiros:
        _emitir_pax(pax, user, reserva, timeout, buyer_cpf)


def _emitir_pax(pax: Passageiro, user: User | None, reserva: Reserva, timeout: int, buyer_cpf: str):
    if pax.foi_emitido:
        return

    try:
        seat = BlockedSeat.model_validate(pax.typed_extra["bloqueio_poltrona"])
        compra = _emitir_passagem_pax(user, reserva, timeout, buyer_cpf, pax, seat)
        pax.set_emissao_marketplace(compra)
    except Exception as ex:
        pax.set_erro_emissao_marketplace(ex)
        raise ex


@retry(retry=retry_if_exception_type(PoltronaExpiradaException), stop=stop_after_attempt(2), reraise=True)
def _emitir_passagem_pax(user, reserva, timeout, buyer_cpf, pax: Passageiro, blocked_seat: BlockedSeat):
    if blocked_seat.expired:
        controller = marketplace_selecao_assento.MarketplaceSeatsController(pax.travel.trecho_classe, None)
        controller.desbloquear_poltrona(blocked_seat)  # garante que ela estará disponivel para bloqueio
        blocked_seat = controller.bloquear_poltrona(poltrona=blocked_seat.poltrona)

    try:
        comprar_form = form_emissao_pax_v2(reserva, user, buyer_cpf, pax, blocked_seat)
        return rodoviaria_svc.efetua_compra_unica(comprar_form, timeout=timeout)
    except PoltronaExpiradaException as ex:
        blocked_seat.set_expired()
        raise ex


def _get_buyer_cpf(user, passageiros):
    return (
        user.profile.cpf
        if user and user.profile.cpf
        else next((pax.buseiro.cpf for pax in passageiros if pax.buseiro and pax.buseiro.cpf), None)
    )


def form_emissao_pax_v2(
    reserva: Reserva, user: User | None, buyer_cpf: str, passageiro: Passageiro, poltrona_bloqueada: BlockedSeat
) -> ComprarForm:
    categoria_especial, dados_beneficio = reserva.categoria_especial_info()

    default_phone = user.profile.cell_phone if user else ""
    buseiro = passageiro.buseiro
    travel = passageiro.travel
    form = ComprarForm(
        trechoclasse_id=travel.trecho_classe_id,
        travel_id=travel.id,
        valor_cheio=travel.max_split_value,
        poltronas=[passageiro.poltrona],
        extra_poltronas=poltrona_bloqueada.external_payload,
        buseiros=[
            BuseiroForm(
                id=buseiro.id,
                name=buseiro.name,
                cpf=buseiro.cpf,
                buyer_cpf=buyer_cpf,
                rg_number=buseiro.rg_number,
                rg_orgao=buseiro.rg_orgao,
                tipo_documento=buseiro.tipo_documento,
                phone=default_phone,
                birthday=buseiro.birthday,
                dados_beneficio=dados_beneficio,
            )
        ],
        categoria_especial=categoria_especial,
    )
    return form


def get_vagas_por_categoria_especial(trecho_classe_id, company_id):
    empresa_info = rodoviaria_svc.get_empresa_info(company_id)

    if not (empresa_info and empresa_info.get("features") and "active" in empresa_info["features"]):
        return {}

    client: RodoviariaClient = get_client("rodoviaria")
    try:
        return client.get_vagas_por_categoria_especial(trecho_classe_id)
    except client.exceptions.HTTPErrorNotFound as ex:
        raise RodoviariaViagemIndisponivel from ex
    except client.exceptions.ClientError:
        raise RodoviariaException("Não foi possível buscar categoria especiais disponíveis")


def remanejamento_async(trecho_classe_origem, trecho_classe_destino, remanejamentos):
    is_trechoclasse_origem_integrado = rodoviaria_svc._is_integrado(trecho_classe_origem.id)
    is_trechoclasse_destino_integrado = rodoviaria_svc._is_integrado(trecho_classe_destino.id)
    if not (is_trechoclasse_origem_integrado or is_trechoclasse_destino_integrado):
        return
    remanejamentos_rodoviaria = []
    for remanejamento in remanejamentos:
        buseiros = [p.buseiro for p in remanejamento.travel.passageiro_set.all()]
        params = _remaneja_passageiros_params(remanejamento.travel, remanejamento.travel_remanejada, None, buseiros)
        remanejamentos_rodoviaria.append(params)
    return rodoviaria_svc._client.remanejamento_async(remanejamentos_rodoviaria)


def remaneja_passageiro(travel: Travel, travel_destino: Travel):
    """
    travel.reservation_code: código gerado aleatoriamente
    travel_destino.reservation_code: código de reserva original
    """
    reservation_code = travel_destino.reservation_code
    is_trechoclasse_origem_integrado = rodoviaria_svc.is_integrado_and_marketplace(travel.trecho_classe)
    is_trechoclasse_destino_integrado = rodoviaria_svc.is_integrado_and_marketplace(travel_destino.trecho_classe)
    buseiros = [p.buseiro for p in travel.passageiro_set.all()]
    if not is_trechoclasse_origem_integrado and not is_trechoclasse_destino_integrado:
        return
    elif is_trechoclasse_destino_integrado:
        try:
            controller = marketplace_selecao_assento.MarketplaceSeatsController(travel_destino.trecho_classe)
            new_seats = controller.escolhe_e_bloqueia_poltronas(
                quantidade_poltronas=len(buseiros),
                categoria_especial=None,
                timeout=20,
            )
            poltronas_destino = [seat.poltrona.numero for seat in new_seats]
        except RodoviariaOverbooking:
            error_msg = f"Reserva {reservation_code} não movida, por overbooking"
            resp = _remaneja_passageiro_base_response_error(
                reservation_code, error_msg, is_trechoclasse_origem_integrado, is_trechoclasse_destino_integrado
            )
            resp["overbooking_on_purchase"] = True
            buserlogger.info(resp)
            return resp
        except (SeatSelectionConnectionError, SeatNotBlocked):
            error_msg = f"Reserva {reservation_code} não movida, por perda de conexão com a rodoviária"
            resp = _remaneja_passageiro_base_response_error(
                reservation_code, error_msg, is_trechoclasse_origem_integrado, is_trechoclasse_destino_integrado
            )
            resp["connection_error_on_purchase"] = True
            buserlogger.info(resp)
            return resp
        except (RodoviariaViagemBloqueada, RodoviariaViagemIndisponivel):
            error_msg = f"Reserva {reservation_code} não movida, por bloqueio de trecho pelo parceiro"
            resp = _remaneja_passageiro_base_response_error(
                reservation_code, error_msg, is_trechoclasse_origem_integrado, is_trechoclasse_destino_integrado
            )
            resp["blocked_travel_on_purchase"] = True
            buserlogger.info(resp)
            return resp
    else:
        poltronas_destino = []
    remaneja_params = _remaneja_passageiros_params(travel, travel_destino, poltronas_destino, buseiros)
    return rodoviaria_svc._client.remaneja_passageiro(remaneja_params)


def _remaneja_passageiro_base_response_error(
    reservation_code, error_msg, is_trechoclasse_origem_integrado, is_trechoclasse_destino_integrado
):
    return {
        "reservation_code": reservation_code,
        "external_purchase_success": False,
        "external_cancelation_success": False,
        "external_error_reason": error_msg,
        "is_trechoclasse_origem_integrado": is_trechoclasse_origem_integrado,
        "is_trechoclasse_destino_integrado": is_trechoclasse_destino_integrado,
        "internal_relocation_permission": False,
        "overbooking_on_purchase": False,
        "connection_error_on_purchase": False,
        "blocked_travel_on_purchase": False,
    }


def _remaneja_passageiros_params(travel, travel_destino, poltronas_destino, buseiros):
    passageiros = [
        {
            "id": buseiro.id,
            "name": buseiro.name,
            "rg_number": buseiro.rg_number,
            "cpf": buseiro.cpf,
            "phone": buseiro.phone,
            "tipo_documento": "RG",
        }
        for buseiro in buseiros
    ]
    return {
        "travel_id": travel.id,
        "travel_destino_id": travel_destino.id,
        "reservation_code": travel.reservation_code,
        "travel_max_split_value": travel_destino.max_split_value,
        "trechoclasse_origem_id": travel.trecho_classe.id,
        "trechoclasse_destino_id": travel_destino.trecho_classe.id,
        "grupo_destino_id": travel_destino.grupo_id,
        "company_origem_id": travel.trecho_classe.grupo.company_id,
        "modelo_venda_origem": travel.trecho_classe.grupo.modelo_venda,
        "company_destino_id": travel_destino.trecho_classe.grupo.company_id,
        "modelo_venda_destino": travel_destino.trecho_classe.grupo.modelo_venda,
        "passengers": passageiros,
        "poltronas_destino": poltronas_destino,
    }


def _bloquear_poltrona_para_passageiro(pax: Passageiro, numero_poltrona: int | None = None) -> None:
    """Bloqueia uma poltrona para o passageiro, seja específica ou automática."""
    controller = marketplace_selecao_assento.MarketplaceSeatsController(pax.travel.trecho_classe)

    if numero_poltrona:
        blocked_seat = controller.bloquear_poltrona(Assento.from_numero_poltrona(numero_poltrona))
    else:
        categoria_especial, _ = pax.travel.reserva.categoria_especial_info()  # type: ignore
        blocked_seat = controller.escolhe_e_bloqueia_poltronas(1, categoria_especial)[0]

    pax.poltrona = blocked_seat.poltrona.numero
    pax.extra = {"bloqueio_poltrona": blocked_seat.model_dump()}
    pax.save()


def emitir_passagem_staff(travel_id, buseiro_id, buyer_cpf, numero_poltrona=None):
    pax = Passageiro.objects.select_related(
        "travel", "buseiro", "travel__trecho_classe", "travel__reserva", "buseiro__user"
    ).get(travel_id=travel_id, buseiro_id=buseiro_id)

    if not pax.travel.reserva:
        raise Exception("Reserva não encontrada")

    if pax.foi_emitido and not passagens_emitidas([pax.travel]):
        # TODO remover
        # setando cancelamento aqui apenas pra manter compatibilidade com o fluxo antigo
        pax.set_cancelamento_emissao_marketplace()

    # Bloqueia poltrona se necessário (número específico ou automático se não tem poltrona)
    if numero_poltrona or not pax.poltrona:
        _bloquear_poltrona_para_passageiro(pax, numero_poltrona)

    _emitir_pax(pax, pax.buseiro.user, pax.travel.reserva, 180, buyer_cpf)
    pax.set_emissao_staff()
