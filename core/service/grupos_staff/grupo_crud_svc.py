import re
from collections import defaultdict
from datetime import date, datetime, time, timedelta
from decimal import Decimal as D
from functools import partial
from typing import Any

from beeline import traced
from celery import group
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>ists,
    ExpressionWrapper,
    F,
    OuterRef,
    Prefetch,
    Q,
    QuerySet,
    Sum,
    Value,
)
from django.db.models.functions import Coalesce, Concat
from django.utils.dateparse import parse_time
from django.utils.timezone import now

from accounting.models import CompanyAccountingOperation
from accounting.service import account_operation_svc
from adapters import new_incidents_adapter
from commons import dateutils
from commons.async_task import TaskProgressTracker
from commons.dateutils import to_default_tz, to_default_tz_required, to_tz
from commons.django_celery_utils import my_shared_task
from commons.django_model_utils import get_or_none
from commons.exceptions import CustomValidationError
from core import tasks
from core.constants import TIPOS_ASSENTO_PESO
from core.forms.buckets_forms import BucketForm
from core.forms.staff_forms import EscalarEmpresaOnibusRotinaForm
from core.models_commons import AsyncTask
from core.models_company import NotaFiscal, Onibus
from core.models_grupo import AutorizacaoGrupo, Grupo, GrupoClasse, RotinaOnibus, TrechoClasse
from core.models_rota import Checkpoint, Rota, TrechoVendido
from core.models_travel import AlteracaoTravel, Travel, TravelFeedback
from core.serializers import serializer_grupo, serializer_travel
from core.serializers.serializer_grupo import (
    StaffListGrupoSerializer,
    grupo_rota_dict,
)
from core.serializers.serializer_grupo_pagamentos import SerializerGrupoPagamento
from core.serializers.serializer_travel import RevendedorExtra, TravelDetailedSerializer
from core.service import (
    driver_svc,
    grupos_svc,
    gruposempresa_svc,
    log_svc,
    multitrecho_svc,
    preco_svc,
    processamento_nf_svc,
    rodoviaria_svc,
    rotas_svc,
    rotina_svc,
)
from core.service.grupos_staff import escalar_onibus_svc, ressarcimento_svc
from core.service.notifications import user_notification_svc
from core.service.reserva import reserva_extrato_visualizacao_svc
from core.service.rodoviaria_alterar_grupos_svc import map_operacoes_alterar_grupo_rodoviaria
from core.service.timezone_svc import rota_origem_tz, to_tz_trecho
from integrations import get_client
from integrations.motorista_client import MotoristaClient
from pagamento_parceiro.service import dia_parado_svc
from pricing import cenarios_precificacao_svc

REGIONAL_NULL_FLAG = -1  # filtro sem regional manda -1


def list_grupos(sortby, descending, filters):
    sortby = sortby or "datetime_ida"
    descending = descending or False
    ida_after = filters.get("departureAfter")
    ida_before = filters.get("departureBefore")
    chegada_after = filters.get("arrivalAfter")
    chegada_before = filters.get("arrivalBefore")

    queryset = Grupo.objects.to_serialize(SerializerGrupoPagamento)
    queryset = queryset.exclude(modelo_venda=Grupo.ModeloVenda.MARKETPLACE, valor_frete=0)
    queryset = queryset.exclude(
        modelo_venda=Grupo.ModeloVenda.MARKETPLACE, percentual_repasse__isnull=True
    )  # Pagos via buserfin
    queryset = queryset.exclude(status="canceled", contar_dia_parado=False)
    queryset = queryset.exclude(contar_dia_parado=True, valor_frete=D(0), notafiscal__isnull=True)
    modelo_venda = filters.get("modelo_venda")
    if modelo_venda:
        if modelo_venda == Grupo.ModeloVenda.BUSER:
            queryset = queryset.filter(modelo_venda__in=Grupo.ModeloVenda.FRETAMENTO_MODELO_VENDA)
            if filters.get("exclude_slot_extras"):
                queryset = queryset.exclude(rotina_onibus__is_extra=True)
        elif modelo_venda == "extra":
            queryset = queryset.filter(modelo_venda__in=Grupo.ModeloVenda.FRETAMENTO_MODELO_VENDA)
            queryset = queryset.filter(rotina_onibus__is_extra=True)
        else:
            queryset = queryset.filter(modelo_venda=modelo_venda)
    if filters.get("nao_teve_frete"):
        queryset = queryset.filter(nao_teve_frete=True)
    if filters.get("status"):
        queryset = queryset.filter(status__in=filters.get("status"))
    if filters.get("somente_com_licenca"):
        queryset = queryset.exclude(
            autorizacao_s3key__isnull=True, contar_dia_parado=False, modelo_venda=Grupo.ModeloVenda.BUSER
        )
    if filters.get("only_aprovacao_pendente"):
        queryset = queryset.filter(notafiscal__status="waiting_approval", notafiscal__numero__isnull=False)
    if filters.get("only_disapproved"):
        queryset = queryset.filter(notafiscal__status__in=["disapproved"])
    if filters.get("only_pending"):
        queryset = queryset.annotate(
            total_accounting_value=Coalesce(Sum("companyaccountingoperation__value"), D(0)),
            total_accounting_value_sem_repasse=Coalesce(Sum("companyaccountingoperation__value"), D(0))
            - Coalesce(
                Sum(
                    "companyaccountingoperation__value",
                    filter=Q(companyaccountingoperation__reason_key="MODELO_HIBRIDO"),
                ),
                D(0),
            ),
        )

        _buser_pending_filter = Q(modelo_venda=Grupo.ModeloVenda.BUSER) & (
            Q(notafiscal__isnull=True)
            | Q(
                notafiscal__status__in=[
                    NotaFiscal.Status.WAITING_PAYMENT,
                    NotaFiscal.Status.WAITING_APPROVAL,
                    NotaFiscal.Status.DISAPPROVED,
                ]
            )
        )
        _marketplace_pending_filter = (
            Q(modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
            & Q(total_accounting_value__lte=0)
            & (
                Q(companyaccountingoperation__isnull=True)
                | Q(
                    companyaccountingoperation__source__in=[
                        CompanyAccountingOperation.Source.PAGAMENTO_VIAGEM,
                        CompanyAccountingOperation.Source.PAGAMENTO_VIAGEM_CANCELADO,
                        CompanyAccountingOperation.Source.REPASSE_VIAGEM_SUBTRAIDO_CANCELADO,
                        CompanyAccountingOperation.Source.REPASSE_VIAGEM_SUBTRAIDO,
                    ]
                )
            )
        )
        _hibrido_pending_filter = Q(modelo_venda=Grupo.ModeloVenda.HIBRIDO) & Q(total_accounting_value_sem_repasse=0)
        queryset = queryset.filter(_buser_pending_filter | _marketplace_pending_filter | _hibrido_pending_filter)
    if filters.get("company"):
        queryset = queryset.filter(company__id=filters.get("company"))
    if filters.get("id"):
        grupo_ids = filters["id"]
        if isinstance(grupo_ids, int):
            list_group_ids = [grupo_ids]
        else:
            list_group_ids = [int(_id.strip()) for _id in grupo_ids.split(",") if _id.strip().isnumeric()]
        queryset = queryset.filter(id__in=list_group_ids)
    if filters.get("gestor"):
        if filters.get("gestor") == -1:
            queryset = queryset.filter(company__gestor_id__isnull=True)
        else:
            queryset = queryset.filter(company__gestor_id=filters.get("gestor"))
    if filters.get("gestorQualidade"):
        if filters.get("gestorQualidade") == -1:
            queryset = queryset.filter(company__gestor_qualidade_id__isnull=True)
        else:
            queryset = queryset.filter(company__gestor_qualidade_id=filters.get("gestorQualidade"))
    if filters.get("rotas"):
        queryset = queryset.filter(rota__id__in=filters.get("rotas"))
    if filters.get("autorizacao_status"):
        queryset = queryset.filter(autorizacao_grupo__status__in=filters.get("autorizacao_status"))
    if filters.get("autorizacao_status_e_hibrido"):
        queryset = queryset.filter(
            Q(autorizacao_grupo__isnull=True)
            | Q(autorizacao_grupo__status__in=filters.get("autorizacao_status_e_hibrido"))
        )
    if filters.get("fretes_dif_nfs"):
        queryset = queryset.exclude(notafiscal__valor=F("valor_frete"))
    if filters.get("riscoPaysGroup"):
        if filters.get("riscoPaysGroup") == "risco":
            queryset = queryset.filter(area_pays_group="Risco")
        else:
            queryset = queryset.exclude(area_pays_group="Risco")
    multas = filters.get("multas", [])
    if multas:
        queryset = queryset.filter(multa__status__in=multas)
    if filters.get("omitir_multa_1_1"):
        queryset = queryset.exclude(multa__motivo__codigo="1.1")
    obs_probs = filters.get("observacao_problema", [])
    if obs_probs:
        _filter = Q()
        if "sem_obs_prob" in obs_probs:
            _filter = (
                Q(observation__isnull=True)
                & Q(problem_description__isnull=True)
                & Q(autorizacao_grupo__comentario__isnull=True)
                & (Q(autorizacao_grupo__isnull=True) | Q(autorizacao_grupo__reprovacoes__len=0))
            )
        else:
            if "observacao" in obs_probs:
                _filter |= Q(observation__isnull=False) | Q(autorizacao_grupo__comentario__isnull=False)
            if "problema" in obs_probs:
                _filter |= Q(problem_description__isnull=False) | Q(autorizacao_grupo__reprovacoes__len__gte=1)
        queryset = queryset.filter(_filter)
    if ida_after:
        ida_after_with_timezone = to_default_tz(ida_after)
        queryset = queryset.filter(datetime_ida__gte=ida_after_with_timezone)
    if ida_before:
        ida_before_with_timezone = to_default_tz(ida_before)
        ida_before_datetime = dateutils.end_of_day(ida_before_with_timezone)
        ida_before_with_timezone = to_default_tz(ida_before_datetime)
        queryset = queryset.filter(datetime_ida__lte=ida_before_with_timezone)
    if chegada_after:
        chegada_after_with_timezone = to_default_tz(chegada_after)
        queryset = queryset.annotate(
            datetime_chegada=ExpressionWrapper(
                F("datetime_ida") + F("rota__duracao_total"), output_field=DateTimeField()
            )
        ).filter(datetime_chegada__gte=chegada_after_with_timezone)
    if chegada_before:
        chegada_before_with_timezone = to_default_tz(chegada_before)
        end_datetime = dateutils.end_of_day(chegada_before_with_timezone)
        chegada_before_with_timezone = to_default_tz(end_datetime)
        queryset = queryset.annotate(
            datetime_chegada=ExpressionWrapper(
                F("datetime_ida") + F("rota__duracao_total"), output_field=DateTimeField()
            )
        ).filter(datetime_chegada__lte=chegada_before_with_timezone)
    if sortby:
        sortby = sortby.replace(".", "__")
    if descending:
        sortby = "-%s" % sortby
    queryset = queryset.order_by(sortby)
    queryset = queryset.distinct()
    return queryset


def get_departure_map(departure):
    departure_map = defaultdict(list)
    departures = departure.split()

    for d in departures:
        if not re.match(r"^[0-9]{2}:[0-9]{2}([+|-]?[0-9]+)?$", d):
            continue
        parts = re.split(r"(?=[\+|-])", d)
        if len(parts) == 1:
            departure_map[0] += parts
            continue
        [hour, delta_dias] = parts
        departure_map[int(delta_dias)].append(hour)
    return departure_map


def get_datetime_ida_list(dates: list[date], times: str) -> list[datetime]:
    datetime_ida_list = []
    departure_map = get_departure_map(times)
    for date_ida in dates:
        for delta_dias, hour_list in departure_map.items():
            for hour in hour_list:
                datetime_ida_list.append(
                    datetime.combine(date_ida, datetime.strptime(hour, "%H:%M").time()) + timedelta(days=delta_dias)
                )
    return datetime_ida_list


def get_related_dates_from_datetime(dt_tz, departure_map):
    related_dates = set()
    for delta_dias, departure_times in departure_map.items():
        if dt_tz.strftime("%H:%M") in departure_times:
            related_dates.add((dt_tz - timedelta(days=delta_dias)).strftime("%Y-%m-%d"))
    return related_dates


def list_group_dates_related_to_route(rota_id, departure):
    tz = rota_origem_tz(rota_id)
    grupos_qs = Grupo.objects.filter(rota__id=rota_id, datetime_ida__gte=now())
    grupos_confirming_status = grupos_qs.filter(status="pending", confirming_probability__in=["high", "medium"])
    grupos_status = grupos_qs.filter(status="travel_confirmed")
    grupos_qs = grupos_confirming_status | grupos_status
    datetimes_ida = grupos_qs.values_list("datetime_ida", flat=True)
    departure_map = get_departure_map(departure)
    related_dates_set = set()
    for dt in datetimes_ida:
        dt_tz = to_tz(dt, tz)
        related_dates_set = related_dates_set.union(get_related_dates_from_datetime(dt_tz, departure_map))
    return list(related_dates_set)


def creategroup(
    rota_id,
    status: Grupo.Status,
    confirming_probability: str,
    modelo_venda: Grupo.ModeloVenda,
    company_id: int | None,
    onibus_id: int | None,
    rotina_onibus_id: int | None,
    valor_frete,
    is_extra: bool,
    evento_extra_id: int | None,
    departure_dates,
    departure_times,
    percentual_repasse,
    percentual_taxa_servico,
    trechos_classes,
    classes,
    autorizacao_hibrido,
    user: User,
    dgroup,
    permite_mesmo_horario_grupo_existente_na_rota=False,
    modelo_operacao: Grupo.ModeloOperacao = Grupo.ModeloOperacao.DEFAULT,
):
    rota = Rota.objects.prefetch_related(
        Prefetch("itinerario", queryset=Checkpoint.objects.to_serialize()),
        Prefetch("trechos_vendidos", queryset=TrechoVendido.objects.to_serialize()),
    ).get(pk=rota_id)

    valida_trecho_vendido_duplicado([rota_id])
    valida_local_embarque_duplicado([rota_id])

    is_transbrasil_bus = Onibus.objects.filter(id=onibus_id, jsondata__transbrasil_agreement=True).exists()
    if is_transbrasil_bus:
        # Regra de negócio: ônibus transbrasil só pode ser escalado em grupos híbridos
        # ou grupos de fretamento, desde que não sejam interestaduais
        if is_transbrasil_bus and modelo_venda == Grupo.ModeloVenda.BUSER and rota.interestadual():
            raise ValidationError("Não é possível escalar ônibus TransBrasil em viagens interestaduais de fretamento")

    trechos_vendidos_ids = [int(pk) for pk in trechos_classes.keys()]
    trechos_vendidos_map = {tv.id: tv for tv in TrechoVendido.objects.filter(pk__in=trechos_vendidos_ids)}
    departure_datetimes = get_datetime_ida_list(departure_dates, departure_times)

    is_rotina_extra = RotinaOnibus.objects.get(id=rotina_onibus_id).is_extra if rotina_onibus_id else False

    grupos_criados = grupos_svc.creategroups(
        classes,
        trechos_classes,
        departure_datetimes,
        user,
        rota,
        trechos_vendidos_map,
        autorizacao_hibrido,
        permite_mesmo_horario_grupo_existente_na_rota,
        grupo_kwargs={
            "company_id": company_id,
            "onibus_id": onibus_id,
            "rotina_onibus_id": rotina_onibus_id,
            "status": status,
            "is_extra": is_extra or is_rotina_extra,
            "evento_extra_id": evento_extra_id,
            "valor_frete": valor_frete,
            "confirming_probability": confirming_probability,
            "modelo_venda": modelo_venda,
            "modelo_operacao": modelo_operacao,
            "percentual_repasse": percentual_repasse,
            "percentual_taxa_servico": percentual_taxa_servico,
        },
    )
    for grupo in grupos_criados:
        log_svc.log_admin_create_group(grupo, data=dgroup)

    rodoviaria_svc.verifica_criacao_grupos_hibrido(grupos_criados)

    cenarios_precificacao_svc.reprice_grupos(grupos_criados)

    return grupos_criados


def get_group_details(group_id, is_hashed=False, simple=False):
    grupo_qs = Grupo.objects.to_staff_serialize()
    if is_hashed:
        grupo = grupo_qs.get(trechoclasse=group_id)
    else:
        grupo = grupo_qs.get(pk=group_id)

    dgroup = serializer_grupo.to_staff(grupo, with_paradas=True)
    if simple:
        return dgroup

    dgroup["classes"] = [gc.to_dict_json(staff=True) for gc in grupo.grupoclasse_set.all()]
    dgroup["travels"] = _travels_detailed_dict(grupo)

    dgroup["has_vip_users"] = any(filter(lambda u: u["user"]["is_vip"], dgroup["travels"]))

    dgroup["feedbacks"] = grupo.feedbacks()
    dgroup["pay_group_area"] = grupo.area_pays_group
    dgroup["has_incident"] = _has_incident(grupo)
    dgroup["total_ressarcimentos"] = _get_total_value_ressarcimentos(grupo)
    if dgroup["has_incident"]:
        dgroup["incident_grade_change"] = _incident_grade_change(grupo)

    return dgroup


def get_passengers_and_boarding_locations(group_id: int):
    grupo = Grupo.objects.get(id=group_id)
    grupo = grupo_rota_dict(grupo)

    locais_itinerario = [
        f"{itinerario['local']['name']} ({itinerario['local']['nickname']})" for itinerario in grupo["itinerario"]
    ]

    origem = locais_itinerario[0]
    destino = locais_itinerario[-1]

    possiveis_embarques = [local for local in locais_itinerario if local != destino]
    possiveis_desembarques = [local for local in locais_itinerario if local != origem]

    travels = _travels_detailed_dict(group_id)

    all_passengers = []
    for travel in travels:
        for idx, passenger in enumerate(travel["passengers"]):
            passenger["index"] = idx + 1 if travel["count_seats"] >= 2 else None
            passenger["travel_id"] = travel["id"]
            passenger["grupo"] = travel["grupo"]
            passenger["trechoclasse_id"] = travel["trechoclasse_id"]
            passenger["tipo_assento"] = travel["tipo_assento"]
            passenger["user"] = travel["user"]
            passenger["reservation_code"] = travel["reservation_code"]
            passenger["travel_status"] = travel["status"]
            all_passengers.append(passenger)

    return all_passengers, possiveis_embarques, possiveis_desembarques


def get_driver_feedback(grupo_id):
    motorista: MotoristaClient = get_client("motorista")
    return motorista.get_motorista_feedback(grupo_id)


def update_groups_departure_hour(grupo_ids, horario, notify):
    """
    Altera horário de grupos de uma mesma rota.
    """
    notify_push_inbox_sms_email = notify.get("pushInboxSmsEmail", False)
    notify_zap = notify.get("zap", False)
    notify_grupos_done = notify.get("gruposDone", False)

    # Precisa converter para list para suportar o index negativo depois.
    grupos = list(
        Grupo.objects.filter(pk__in=grupo_ids)
        .select_related("rota")
        # Ordena para facilitar pegar o mínimo e o máximo depois.
        .order_by("datetime_ida")
    )

    if not grupos:
        return

    if len({grupo.rota_id for grupo in grupos}) != 1:
        raise ValidationError("Atualização de horário deve ser feita apenas com grupos da mesma rota.")

    tz_origem = rota_origem_tz(grupos[0].rota_id)
    datetime_ida_range = (
        to_tz(grupos[0].datetime_ida, tz_origem),
        to_tz(grupos[-1].datetime_ida, tz_origem),
    )
    horarios_existentes = grupos_svc._find_horarios_proximos(datetime_ida_range, grupos[0].rota_id)

    data = []

    map_trecho_datetime_ida_anterior = {
        tc.id: to_tz_trecho(tc.datetime_ida, tc) for tc in TrechoClasse.objects.filter(grupo_id__in=grupo_ids)
    }

    tasks_rodoviaria = []
    map_grupo_datetime_ida = {}
    for grupo in grupos:
        datetime_ida_anterior = to_tz(grupo.datetime_ida, tz_origem)
        map_grupo_datetime_ida.update({grupo.id: datetime_ida_anterior})
        horario_anterior = datetime.strftime(datetime_ida_anterior, "%H:%M")

        datetime_ida = datetime.strptime(f"{datetime_ida_anterior:%Y-%m-%d} {horario}", "%Y-%m-%d %H:%M")
        datetime_ida = to_tz(datetime_ida, tz_origem)

        if datetime_ida == datetime_ida_anterior:
            # Horario não mudou.
            continue

        grupo.datetime_ida = grupos_svc.get_next_horario_livre(datetime_ida, horarios_existentes)

        data.append((grupo, horario_anterior))

        if grupo.modelo_venda == Grupo.ModeloVenda.HIBRIDO and grupo.onibus:
            tasks_rodoviaria.append(rodoviaria_svc.alterar_horario_grupo_hibrido(grupo))

    Grupo.objects.bulk_update(grupos, ("datetime_ida", "updated_on"))
    rotas_svc.update_grupo_trechos_classes_horarios(grupos)
    group(tasks_rodoviaria).apply_async()
    _update_alteracao_travel(grupo_ids, map_trecho_datetime_ida_anterior)

    if notify_push_inbox_sms_email or notify_zap:
        notificar_grupos = [grupo for grupo, _ in data]
        if not notify_grupos_done:
            notificar_grupos = [grupo for grupo, _ in data if grupo.datetime_ida > dateutils.now()]
            map_grupo_datetime_ida = {grupo.id: map_grupo_datetime_ida[grupo.id] for grupo in notificar_grupos}
        user_notification_svc.grupo_departure_changed(
            notificar_grupos,
            notify_push_inbox_sms_email,
            notify_zap,
            map_trecho_datetime_ida_anterior,
            map_grupo_datetime_ida,
        )
    for grupo, horario_anterior in data:
        params = {"horario_anterior": horario_anterior, "horario_novo": horario, "notify": notify}
        log_svc.log_update_group(grupo, params)
        tasks.cria_atualiza_grupo_cenario_task.delay(grupo.id)


def _update_alteracao_travel(grupo_ids, map_trecho_datetime_ida_anterior):
    map_trecho_datetime_ida_novo = {
        tc.id: to_tz_trecho(tc.datetime_ida, tc) for tc in TrechoClasse.objects.filter(grupo_id__in=grupo_ids)
    }
    travels = Travel.objects.filter(grupo_id__in=grupo_ids, status="pending").select_related("trecho_classe")
    alteracoes = []

    for travel in travels:
        trecho_id = travel.trecho_classe_id
        horario_anterior = map_trecho_datetime_ida_anterior.get(trecho_id)
        novo_horario = map_trecho_datetime_ida_novo.get(trecho_id)

        if horario_anterior != novo_horario:
            alteracoes.append(
                AlteracaoTravel(
                    travel=travel,
                    antigo_horario=horario_anterior,
                    novo_horario=novo_horario,
                    tipo=AlteracaoTravel.TipoAlteracao.MUDANCA_DE_HORARIO,
                )
            )
    AlteracaoTravel.objects.bulk_create(alteracoes)


def _update_group_confirming_probability(grupo, params):
    if "confirming_probability" not in params:
        return
    grupo.confirming_probability = params["confirming_probability"]


def _update_group_empresa(grupo, params):
    if "empresa" not in params:
        return
    atualizar_empresa(grupo, params.get("empresa"))


def _update_group_driver_one(grupo, params):
    if "driver_one" not in params:
        return
    driver_one_id = params["driver_one"]
    driver_two_id = params.get("driver_two", None)
    motorista_um = User.objects.select_related("profile").get(pk=driver_one_id) if driver_one_id else None
    motorista_dois = User.objects.select_related("profile").get(pk=driver_two_id) if driver_two_id else None

    for motora in [motorista_um, motorista_dois]:
        if motora and motora.profile.is_blocked:
            raise CustomValidationError(
                f"O motorista {motora.first_name} está bloqueado por conta de {motora.profile.block_reason}"
            )

        if motora and not gruposempresa_svc._can_escalar_motorista(motora, grupo.datetime_ida):
            raise CustomValidationError(
                f"O motorista {motora.first_name} não esta com a situação regular para dirigir neste grupo."
            )

    grupo.driver_one = motorista_um if driver_one_id else None
    grupo.driver_two = motorista_dois if driver_two_id else None
    motorista_descansado = params.get("motorista_descansado", {})
    datetime_ida = grupo.datetime_ida
    expected_travel_duration = grupo.duracao_total
    two_drivers = grupo.driver_two is not None

    if grupo.driver_one:
        motorista_descansado["driver_one"] = driver_svc.driver_has_enough_rest(
            driver=grupo.driver_one,
            ref_travel_start_dt=datetime_ida,
            two_drivers=two_drivers,
            expected_travel_duration=expected_travel_duration,
        )
    if grupo.driver_two:
        motorista_descansado["driver_two"] = driver_svc.driver_has_enough_rest(
            grupo.driver_two,
            ref_travel_start_dt=datetime_ida,
            two_drivers=two_drivers,
            expected_travel_duration=expected_travel_duration,
        )

    grupo.motorista_descansado = motorista_descansado
    rodoviaria_svc.escala_motoristas(grupo)


def _update_group_classes(grupo, params, updated_by):
    has_new_trechos_classe = False
    if "classes" not in params:
        return has_new_trechos_classe
    if grupo.status == Grupo.Status.DONE:
        return has_new_trechos_classe

    capacity_manager = multitrecho_svc.prepare_capacity_manager_from_grupo(grupo)
    gc_map = {gc.id: gc for gc in grupo.grupoclasse_set.all()}
    for dclasse in params["classes"]:
        if _is_new_classe(dclasse["id"]):
            trechos_classes = []
            for trecho_vendido_id, dtrecho_vendido in params["trechos_classes"].items():
                dtrecho_classe = dtrecho_vendido[dclasse["id"]]
                dtrecho_classe["id"] = trecho_vendido_id
                trechos_classes.append(dtrecho_classe)
            create_grupo_e_trecho_classe(grupo, dclasse, trechos_classes, closed_by_id=updated_by.id)
            has_new_trechos_classe = True
            continue

        gc_obj = gc_map[int(dclasse["id"])]
        if "max_capacity" in dclasse:
            max_capacity = int(dclasse.get("max_capacity"))
            if any(capacity_manager.ocupacao(tc) > max_capacity for tc in gc_obj.trechoclasse_set.all()):
                raise ValidationError(
                    "Tentativa de definir capacidade do grupo menor que número de pessoas atual. "
                    + f"Verifique a quantidade de vagas da classe {gc_obj.tipo_assento}."
                )
            gc_obj.capacidade = max_capacity

        if dclasse.get("closed") and not gc_obj.closed:
            gc_obj.closed_reason = dclasse.get("closed_reason")
            gc_obj.closed_by = updated_by
            gc_obj.closed_at = to_default_tz(dateutils.now())
        elif not dclasse.get("closed"):
            gc_obj.closed_reason = None
            gc_obj.closed_by = None
            gc_obj.closed_at = None
        gc_obj.closed = dclasse.get("closed", gc_obj.closed)
        gc_obj.save(
            update_fields=[
                "capacidade",
                "closed_reason",
                "closed_by",
                "closed_at",
                "closed",
            ]
        )
    return has_new_trechos_classe


def _update_group_trechos_classes(grupo, params, updated_by):
    if "trechos_classes" not in params or grupo.status == Grupo.Status.DONE:
        return

    price_logs, price_buckets = [], []
    grupo_classes_ids = _get_grupo_classes_ids(params)

    trechos_classe = _fetch_trechos_classe(grupo, grupo_classes_ids)
    trecho_classe_map = {(tc.grupo_classe_id, tc.trecho_vendido_id): tc for tc in trechos_classe}
    trechos_classe_atualizados_ids = set()

    for trecho_vendido_id, dtrecho_vendido in params["trechos_classes"].items():
        for classe_id, dgrupo_classe in dtrecho_vendido.items():
            if _is_new_classe(classe_id):
                continue
            _validate_split_values(dgrupo_classe)
            trecho_classe = trecho_classe_map.get((int(classe_id), int(trecho_vendido_id)))
            if trecho_classe:
                updated = _update_trecho_classe(
                    trecho_classe, dgrupo_classe, updated_by, grupo, price_logs, price_buckets
                )
                if updated:
                    trechos_classe_atualizados_ids.add(trecho_classe.id)

    preco_svc.bulk_update_price_objects([], price_buckets, price_logs)


def _get_grupo_classes_ids(params):
    grupo_classes_ids = defaultdict(list)
    for trecho_vendido_id, dtrecho_vendido in params["trechos_classes"].items():
        for classe_id, dgrupo_classe in dtrecho_vendido.items():
            if not _is_new_classe(classe_id):
                grupo_classes_ids[int(trecho_vendido_id)].append(int(classe_id))
    return grupo_classes_ids


def _fetch_trechos_classe(grupo, grupo_classes_ids):
    q = Q()
    for trecho_vendido_id, grupo_classes_ids_list in grupo_classes_ids.items():
        q |= Q(trecho_vendido__id=trecho_vendido_id, grupo_classe__id__in=grupo_classes_ids_list)

    return (
        TrechoClasse.objects.filter(q, grupo=grupo)
        .select_related(
            "grupo",
            "grupo_classe",
            "trecho_vendido__origem__cidade",
            "trecho_vendido__destino__cidade",
            "price_manager",
        )
        .prefetch_related(
            "price_manager__buckets",
        )
    )


def _validate_split_values(dgrupo_classe):
    max_split_value = round(dgrupo_classe["max_split_value"], 2)
    ref_split_value = round(dgrupo_classe.get("ref_split_value", max_split_value), 2)
    if max_split_value >= 10000 or ref_split_value >= 10000:
        raise ValidationError("ref_split_value e max_split_value devem ser menor que 10000")
    if not (abs(ref_split_value - max_split_value) < 100):
        raise ValidationError("A diferença entre ref_split_value e max_split_value deve ser menor do que 100")


def _update_trecho_classe(trecho_classe, dgrupo_classe, updated_by, grupo, price_logs, price_buckets) -> bool:
    updated = False
    is_to_close = bool(dgrupo_classe.get("closed"))
    closed_reason = dgrupo_classe.get("closed_reason")
    promo_value = dgrupo_classe.get("promo_value")
    closed_by = updated_by
    closed_at = to_default_tz(dateutils.now())
    max_split_value = round(dgrupo_classe["max_split_value"], 2)
    ref_split_value = round(dgrupo_classe.get("ref_split_value", max_split_value), 2)
    buckets = dgrupo_classe.get("buckets")
    cleaned_buckets = [BucketForm(**bucket).dict() for bucket in buckets] if buckets else None

    if (is_to_close, closed_reason) != (trecho_classe.closed, trecho_classe.closed_reason):
        trecho_classe.closed = is_to_close
        if is_to_close:
            trecho_classe.closed_by = closed_by
            trecho_classe.closed_at = closed_at
            trecho_classe.closed_reason = closed_reason
            log_svc.log_fechamento_trechos([trecho_classe], closed_reason, updated_by)
        else:
            trecho_classe.closed_by = None
            trecho_classe.closed_at = None
            trecho_classe.closed_reason = None
            log_svc.log_abertura_trechos([trecho_classe], updated_by)

    buckets_old = preco_svc.get_active_buckets(trecho_classe)
    if not trecho_classe.price_manager.is_locked and (max_split_value, ref_split_value, cleaned_buckets) != (
        trecho_classe.max_split_value,
        trecho_classe.ref_split_value,
        buckets_old,
    ):
        log_svc.log_alteracao_preco(
            grupo,
            trecho_classe.id,
            "update_group",
            trecho_classe.max_split_value,
            max_split_value,
            trecho_classe.ref_split_value,
            ref_split_value,
            buckets_old,
            cleaned_buckets,
        )
        trecho_classe.max_split_value = max_split_value
        trecho_classe.ref_split_value = ref_split_value
        price_manager, price_bucket, price_log = preco_svc.determine_bucket_update(
            trecho_classe, cleaned_buckets, promo_value, validate_buckets=False
        )
        price_buckets += price_bucket
        price_logs += price_log
        price_manager.save()
        trecho_classe.price_manager = price_manager

        updated = True

    trecho_classe.save(
        update_fields=[
            "closed",
            "closed_at",
            "closed_by",
            "closed_reason",
            "max_split_value",
            "price_manager",
            "ref_split_value",
            "updated_on",
        ]
    )

    return updated


def _update_group_onibus(grupo, params, origem_alteracao_placa):
    if "onibus" not in params:
        return
    onibus = Onibus.objects.get(pk=params["onibus"]) if params["onibus"] is not None else None

    if not onibus:
        return

    # Regra de negócio: ônibus transbrasil só pode ser escalado em grupos híbridos
    # ou grupos de fretamento, desde que não sejam interestaduais
    is_transbrasil_bus = "transbrasil_agreement" in onibus.jsondata and onibus.jsondata["transbrasil_agreement"]
    if is_transbrasil_bus and grupo.modelo_venda == Grupo.ModeloVenda.BUSER and grupo.rota.interestadual():
        raise ValidationError("Não é possível escalar ônibus TransBrasil em viagens interestaduais de fretamento")

    if onibus == grupo.onibus:
        return

    alterar_layout = params.get("update_group_layout", False)

    escalar_onibus_svc._create_alteracaoclasserotina([grupo], onibus)
    log_svc.log_troca_onibus(grupo, old_bus_id=grupo.onibus_id, new_bus_id=onibus.id)
    escalar_onibus_svc.alterar_onibus_grupo(
        onibus,
        grupo,
        "OUTROS",
        "grupo_crud_svc.update_group",
        alterar_layout,
        staff=True,
        origem_alteracao_placa=origem_alteracao_placa,
        causada_por=AlteracaoTravel.CausadaPor.ANALISTA,
    )


def update_group(grupo_id, params, origem_alteracao_placa, updated_by=None):
    grupo = Grupo.objects.prefetch_related(
        Prefetch(
            "grupoclasse_set",
            queryset=GrupoClasse.objects.prefetch_related(
                Prefetch("trechoclasse_set", queryset=TrechoClasse.objects.select_related("trecho_vendido")),
            ),
        )
    ).get(pk=grupo_id)

    _update_group_confirming_probability(grupo, params)
    _update_group_empresa(grupo, params)
    _update_group_driver_one(grupo, params)
    has_new_trechos_classe = _update_group_classes(grupo, params, updated_by)
    _update_group_trechos_classes(grupo, params, updated_by)
    _update_group_onibus(grupo, params, origem_alteracao_placa)

    grupo.problem_description = params.get("problem", grupo.problem_description)
    grupo.observation = params.get("observation", grupo.observation)
    grupo.valor_encomenda = params.get("valor_encomenda", grupo.valor_encomenda)
    grupo.save()

    rotas_svc.update_grupo_trechos_classes_horarios(grupo)
    if has_new_trechos_classe:
        # inclui update_buckets após atualização dos horario pois no caso de
        # criação (create_grupo_e_trecho_classe) o datetime_ida está nulo
        # e só atualiza em casos de novos trechos criados
        grupos_svc.cria_bulk_price_manager([grupo])
    log_svc.log_update_group(grupo, params)
    tasks.cria_atualiza_grupo_cenario_task.delay(grupo.id, update_load_factor=False)
    tasks.atualiza_vagas_trechos_classe.delay(grupo)

    return grupo


@transaction.atomic
def closed_or_open_trechos_e_classes(grupos_ids: list[int], is_closed: bool, company_id: int, updated_by: User):
    closed_reason = "Fechamento pelo parceiro(Ação em massa)" if is_closed else "Abertura pelo parceiro(Ação em massa)"
    grupos = (
        Grupo.objects.filter(pk__in=grupos_ids, company_id=company_id, modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
        .exclude(status=Grupo.Status.DONE)
        .prefetch_related(
            "grupoclasse_set",
            Prefetch(
                "trechoclasse_set",
                queryset=TrechoClasse.objects.select_related(
                    "trecho_vendido__origem__cidade", "trecho_vendido__destino__cidade"
                ),
            ),
        )
    )

    for grupo in grupos:
        atualizar_status_itens(grupo.grupoclasse_set.all(), updated_by, is_closed, closed_reason)

        trechos = grupo.trechoclasse_set.all()
        atualizar_status_itens(trechos, updated_by, is_closed, closed_reason)

        log_svc.log_update_group(grupo, closed_reason)
        if is_closed:
            log_svc.log_fechamento_trechos(trechos, closed_reason, updated_by)

    tasks.bulk_atualiza_vagas_trechos_classe.delay(list(grupos))


def atualizar_status_itens(itens, updated_by, is_closed: bool, closed_reason: str):
    now = to_default_tz(dateutils.now()) if is_closed else None

    update_kwargs = {
        "closed": is_closed,
        "closed_reason": closed_reason if is_closed else None,
        "closed_by": updated_by if is_closed else None,
        "closed_at": now,
    }
    itens.update(**update_kwargs)


def alterar_ocasiao(grupo_id, params, user, fluxo: str):
    grupo = Grupo.objects.get(pk=grupo_id)
    if "is_extra" in params:
        grupo.is_extra = params["is_extra"]
        grupo.save(
            update_fields=[
                "is_extra",
                "updated_on",
            ]
        )
    if "dia_parado" in params:
        dia_dict = dict(**params["dia_parado"], grupo={"id": grupo_id})
        dia_parado_svc.altera_dia_parado(dia_dict, user, fluxo)
    log_svc.log_update_group(grupo, params)
    return grupo


def alterar_probabilidade(groups_ids, params):
    prob = params.get("probabilidade")
    if prob not in ["high", "medium", "very_low"]:
        raise ValidationError("Chance de confirmação inválido")
    groups = Grupo.objects.filter(id__in=groups_ids)
    groups.update(confirming_probability=prob)


def alterar_valor_encomenda(groups_ids, val_encomenda):
    groups = Grupo.objects.filter(id__in=groups_ids)
    groups.update(valor_encomenda=val_encomenda)


def validate_grupo_classe(dclasse):
    if "max_capacity" not in dclasse:
        raise ValidationError("Faltando tipo capacidade da classe.")
    if int(dclasse.get("max_capacity")) < 0:
        raise ValidationError("Capacidade do grupo não pode ser negativa")
    if "tipo_assento" not in dclasse:
        raise ValidationError("Faltando tipo do assento.")


def create_grupo_e_trecho_classe(
    grupo, dclasse, trechos_classes, save=True, closed_by_id=None, trechos_vendidos_map=None
):
    if trechos_vendidos_map is None:
        trecho_vendido_ids = {int(tv["id"]) for tv in trechos_classes}
        trechos_vendidos_map = TrechoVendido.objects.in_bulk(trecho_vendido_ids)

    validate_grupo_classe(dclasse)
    tipo_assento = str(dclasse.get("tipo_assento", "executivo"))
    max_capacity = int(dclasse["max_capacity"])
    closed = bool(dclasse.get("closed"))
    closed_reason = dclasse.get("closed_reason") if closed else None
    closed_by_id = closed_by_id if closed else None
    closed_at = to_default_tz(dateutils.now()) if closed else None

    oclasse = GrupoClasse.create(
        grupo,
        capacidade=max_capacity,
        tipo_assento=tipo_assento,
        closed=closed,
        closed_reason=closed_reason,
        closed_by_id=closed_by_id,
        closed_at=closed_at,
        save=save,
    )
    otrechos_classes_buckets = {}
    trechos_classe, price_managers, price_buckets, price_loggers = [], [], [], []
    for trecho_classe in trechos_classes:
        tv_id = int(trecho_classe["id"])
        otrecho_vendido = trechos_vendidos_map.get(tv_id)
        max_split_value = trecho_classe["max_split_value"]
        ref_split_value = trecho_classe.get("ref_split_value", max_split_value)
        buckets = trecho_classe.get("buckets")
        trecho_classe_closed = bool(trecho_classe.get("closed"))
        trecho_classe_closed_at = None
        trecho_classe_closed_reason = None
        trecho_classe_closed_by_user_id = None
        if trecho_classe_closed:
            trecho_classe_closed_reason = trecho_classe.get("closed_reason")
            trecho_classe_closed_by_user_id = trecho_classe.get("closed_by_user_id")
            trecho_classe_closed_at = to_default_tz(datetime.fromisoformat(trecho_classe.get("closed_at")))
        pm = None
        if buckets:
            pm, pbs, pls = preco_svc.determine_price_manager_e_price_buckets_base(
                max_split_value, ref_split_value, buckets
            )
            price_managers.append(pm)
            price_buckets += pbs
            price_loggers += pls
        tc = TrechoClasse.create(
            otrecho_vendido,
            oclasse,
            max_split_value=max_split_value,
            ref_split_value=ref_split_value,
            price_manager=pm,
            save=False,
            closed=trecho_classe_closed,
            closed_reason=trecho_classe_closed_reason,
            closed_by_id=trecho_classe_closed_by_user_id,
            closed_at=trecho_classe_closed_at,
        )
        trechos_classe.append(tc)
        otrechos_classes_buckets[otrecho_vendido.id] = (tc, buckets)

    if save:
        preco_svc.bulk_update_price_objects(price_managers, price_buckets, price_loggers)
        TrechoClasse.objects.bulk_create(trechos_classe)
        tasks.cria_atualiza_grupo_cenario_task.delay(grupo.id)

    return oclasse, otrechos_classes_buckets


def get_travel(travel_id):
    otravel = get_or_none(Travel, id=travel_id)
    travel = None
    if otravel:
        travel = serializer_travel.serialize_adm_get_travel(otravel)
        travel["extrato"] = reserva_extrato_visualizacao_svc.get_extrato_visualizacao_travel_existente(otravel)
        feedback = TravelFeedback.objects.to_serialize().filter(travel=otravel).first()
        travel["feedback"] = feedback.to_dict_json(anonimo=False) if feedback else None
        revendedor = RevendedorExtra().serialize_object(otravel)
        travel.update(revendedor)
        ressarcimento_ops = account_operation_svc.get_travel_accops_ressarcimento(otravel)
        travel["ressarcimentos"] = [
            dict(**op.to_dict_json(), fromuser=(op.fromuser and op.fromuser.get_full_name()))
            for op in ressarcimento_ops
        ]
        idas, voltas = otravel.get_travels_ida_e_volta()
        travel["ida_volta"] = serializer_travel.serialize_travel_ida_volta(idas, voltas)
    return travel


def list_feedbacks(params):
    page = params.get("page", 1)
    empresa_id = params.get("company")
    driver_id = params.get("driver")
    bus_id = params.get("onibus")
    feedbacks = (
        TravelFeedback.objects.to_serialize().filter(comment__isnull=False).order_by("-travel__grupo__datetime_ida")
    )

    if empresa_id:
        feedbacks = feedbacks.filter(travel__grupo__company__id=int(empresa_id))
    if driver_id:
        feedbacks = feedbacks.filter(
            Q(travel__grupo__driver_one__id=int(driver_id)) | Q(travel__grupo__driver_two__id=int(driver_id))
        )
    if bus_id:
        feedbacks = feedbacks.filter(travel__grupo__onibus__id=int(bus_id), rating__lt=5)

    paginator = Paginator(feedbacks, 10).page(page)

    return {"comments": [f.to_dict_json() for f in paginator.object_list if f.comment], "total_length": len(feedbacks)}


def _is_new_classe(classe_id):
    return "new-" in str(classe_id)


def atualizar_empresa(grupo, empresa_id, force=False, force_reason="", *, save=True):
    if isinstance(grupo, int):
        grupo = Grupo.objects.get(pk=grupo)

    continua_mesma_empresa = grupo.company_id and grupo.company_id == empresa_id
    continua_sem_empresa = empresa_id is None and grupo.company_id is None
    if continua_mesma_empresa or continua_sem_empresa:
        return None

    if grupo.notafiscal and grupo.notafiscal.status == "paid":
        if not force:
            raise ValidationError(
                f"Grupo ID {grupo.id} [{grupo.rota}] possui nota fiscal paga: alterar a empresa pode gerar imprevistos financeiros."
            )

        log_svc.log_alteracao_empresa_grupo_pago(grupo=grupo, nova_empresa_id=empresa_id, motivo=force_reason)

    if grupo.notafiscal:
        log_svc.log_solicitacao_cacelamento_cteos(grupo.notafiscal, "atualizar_empresa_grupo", grupo.id)
        processamento_nf_svc.cancelar_async(notafiscal_id=grupo.notafiscal.id)
    grupo.notafiscal = None
    grupo.driver_one = None
    grupo.driver_two = None
    grupo.company_id = empresa_id
    if save:
        grupo.save(
            update_fields=[
                "notafiscal",
                "driver_one",
                "driver_two",
                "company_id",
                "updated_on",
            ]
        )
    return grupo


@traced("grupo_crud_svc._bulk_atualizar_empresa")
def _bulk_atualizar_empresa(grupos, empresa_id, force=False):
    for grupo in grupos:
        atualizar_empresa(grupo, empresa_id, force, "escalar_empresa_onibus_rotina", save=False)
    Grupo.objects.bulk_update(
        grupos,
        [
            "notafiscal",
            "driver_one",
            "driver_two",
            "company_id",
        ],
    )


def search(filters: dict[str, Any] | None = None, include_solver_data: bool = False) -> QuerySet:
    if not filters:
        raise ValidationError("Filtros não informados")

    if not (filters.get("ida_after") and filters.get("ida_before")):
        raise ValidationError("Intervalo de datas (De... Até...) é obrigatório")

    q = _search_base_queryset(include_solver_data)
    q = _apply_company_filters(q, filters)
    q = _apply_datetime_filters(q, filters)
    q = _apply_grupo_filters(q, filters)
    q = _apply_grupoclasse_filters(q, filters)
    q = _apply_onibus_filters(q, filters)
    q = _apply_rota_filters(q, filters)
    q = _apply_rotina_filters(q, filters)
    q = _apply_status_filters(q, filters)
    q = _apply_trechoclasse_filters(q, filters)

    q = q.distinct()

    return q


def _apply_rotina_filters(q: QuerySet, filters: dict[str, Any]) -> QuerySet:
    if filters.get("tem_rotina"):
        q = q.filter(rotina_onibus_id__isnull=False)

    if len(filters.get("rotas_principais_in", [])) > 0:
        q = q.filter(rotina_onibus__rota_principal__in=filters["rotas_principais_in"])

    if len(filters.get("rotina_onibus_in", [])) > 0:
        rotina_query = Q(rotina_onibus__in=filters["rotina_onibus_in"])
        if None in filters["rotina_onibus_in"]:
            rotina_query |= Q(rotina_onibus__isnull=True)
        q = q.filter(rotina_query)

    if filters.get("regional"):
        if filters["regional"] == REGIONAL_NULL_FLAG:
            q = q.filter(rotina_onibus__regional__isnull=True)
        else:
            q = q.filter(rotina_onibus__regional=filters["regional"])

    if filters.get("confirmation_with_forecast"):
        """
        confirmation_with_forecast (None): Filtro desativado
        confirmation_with_forecast (True): Buscar confirmações com forecast
        confirmation_with_forecast (False): Buscar confirmações sem forecast
        """
        q = q.filter(rotina_onibus__minimo_reservas_prev__isnull=False, rotina_onibus__minimo_reservas_prev__gte=1)

    if filters.get("confirmation_with_forecast") is False:
        q = q.filter(rotina_onibus__minimo_reservas__isnull=False, rotina_onibus__minimo_reservas__gte=1)
    return q


def _apply_grupoclasse_filters(q: QuerySet, filters: dict[str, Any]) -> QuerySet:
    if filters.get("grupo_fechado"):
        sq = GrupoClasse.objects.filter(grupo=OuterRef("pk"), closed=False)
        q = q.annotate(possui_grupo_fechado=~Exists(sq)).filter(possui_grupo_fechado=True)

    if filters.get("classe_in"):
        q = q.filter(grupoclasse__tipo_assento__in=filters["classe_in"])
    return q


def _apply_rota_filters(q: QuerySet, filters: dict[str, Any]) -> QuerySet:
    if len(filters.get("rota_in", [])) > 0:
        q = q.filter(rota__in=filters["rota_in"])

    if local_filter := filters.get("local_filter"):
        if filters.get("local_filter_is_and"):
            for local_id in local_filter:
                q = q.filter(rota__itinerario__local__id=local_id)
        else:
            q = q.filter(rota__itinerario__local__id__in=local_filter)

    if cidades_no_itinerario := filters.get("cidades"):
        for cidade in cidades_no_itinerario:
            q = q.filter(rota__itinerario__local__cidade__id=cidade)
    return q


def _apply_company_filters(q: QuerySet, filters: dict[str, Any]) -> QuerySet:
    if company_in := filters.get("company_in"):
        company_query = Q(company__in=company_in)
        if None in company_in:
            company_query |= Q(company__isnull=True)
        q = q.filter(company_query)

    if filters.get("gestor"):
        q = q.filter(company__gestor__id=filters["gestor"])
    return q


def _apply_onibus_filters(q: QuerySet, filters: dict[str, Any]) -> QuerySet:
    if filters.get("all_onibus"):
        q = q.filter(onibus__isnull=False)

    if onibus_in := filters.get("onibus_in"):
        onibus_query = Q(onibus__in=onibus_in)
        if None in onibus_in:
            onibus_query = onibus_query | Q(onibus__isnull=True)

        q = q.filter(onibus_query)

    if filters.get("locadora"):
        q = q.filter(onibus__locadora=True)

    if filters.get("availability") is not None:
        q = q.filter(onibus__available=filters["availability"])
    return q


def _apply_trechoclasse_filters(q: QuerySet, filters: dict[str, Any]) -> QuerySet:
    """
    Ler para entender a confusão
    https://docs.djangoproject.com/en/4.2/topics/db/queries/#spanning-multi-valued-relationships
    """
    date_range = _datetime_range_from_filters(filters)

    tc_subq = TrechoClasse.objects.filter(grupo_id=OuterRef("pk"), datetime_ida__range=date_range)

    if cidades_origem := filters.get("cidades_origem"):
        if filters.get("cid_origem_filter_and"):
            # Todas as cidades da lista
            for cidade in cidades_origem:
                q = q.filter(Exists(tc_subq.filter(trecho_vendido__origem__cidade_id=cidade)))
        else:
            # Trechos com quaisquer cidades da lista
            q = q.filter(Exists(tc_subq.filter(trecho_vendido__origem__cidade_id__in=cidades_origem)))

    if cidades_destino := filters.get("cidades_destino"):
        if filters.get("cid_destino_filter_and"):
            for cidade in cidades_destino:
                q = q.filter(Exists(tc_subq.filter(trecho_vendido__destino__cidade_id=cidade)))
        else:
            q = q.filter(Exists(tc_subq.filter(trecho_vendido__destino__cidade_id__in=cidades_destino)))
    return q


def _apply_grupo_filters(q: QuerySet, filters: dict[str, Any]) -> QuerySet:
    if filters.get("grupo_id") and all(g_id.isnumeric() for g_id in filters["grupo_id"]):
        q = q.filter(id__in=filters["grupo_id"])

    if filters.get("nao_teve_frete"):
        q = q.filter(nao_teve_frete=True)

    if filters.get("modelo_venda_in"):
        q = q.filter(modelo_venda__in=filters["modelo_venda_in"])

    if filters.get("confirming_probability_in"):
        q = q.filter(confirming_probability__in=filters["confirming_probability_in"])

    if filters.get("contar_dia_parado"):
        q = q.filter(contar_dia_parado=True)

    if filters.get("motoristas"):
        q = q.filter(Q(driver_one_id__in=filters["motoristas"]) | Q(driver_two_id__in=filters["motoristas"]))

    return q


def _apply_datetime_filters(q: QuerySet, filters: dict[str, Any]) -> QuerySet:
    date_range = _datetime_range_from_filters(filters)
    q = q.filter(datetime_ida__range=date_range)

    if filters.get("departure_hour"):
        time_range = _normalize_departure_hour(filters["departure_hour"])
        q = q.filter(datetime_ida__time__range=time_range)

    if len(filters.get("dia_semana_in", [])) > 0:
        dias_semana = filters["dia_semana_in"]
        q = q.filter(datetime_ida__week_day__in=dias_semana)

    return q


def _datetime_range_from_filters(filters) -> tuple[datetime, datetime]:
    return to_default_tz_required(filters["ida_after"]), to_default_tz_required(filters["ida_before"])


def _apply_status_filters(q: QuerySet, filters: dict[str, Any]) -> QuerySet:
    if filters.get("status_in"):
        q = q.filter(status__in=filters["status_in"])

    if filters.get("checkin_status_in"):
        q = q.filter(checkin_status__in=filters["checkin_status_in"])

    if filters.get("ocasiao_status"):
        q = q.filter(is_extra=filters["ocasiao_status"] == "is_extra")

    return q


def _search_base_queryset(include_solver_data: bool = False) -> QuerySet:
    extras = []
    if include_solver_data:
        extras.append("solver_suggestions")
    return Grupo.objects.to_serialize(StaffListGrupoSerializer(extra=extras)).order_by("datetime_ida")


def _normalize_departure_hour(departure_hour):
    if len(departure_hour) != 2:
        departure_hour.append("23:59")

    inicio = None
    fim = None

    try:
        inicio = parse_time(str(departure_hour[0]))
    except ValueError:
        pass

    try:
        fim = parse_time(str(departure_hour[1]))
    except ValueError:
        pass

    if not inicio:
        inicio = time(00, 00)

    if not fim:
        fim = time(23, 59)

    return inicio, fim


def _travels_detailed_dict(grupo):
    travels = Travel.objects.to_serialize(TravelDetailedSerializer.detailed()).filter(grupo=grupo)
    dtravels = []
    for travel in travels:
        dtravel = travel.serialize()
        dtravel["status"] = travel.status
        dtravel["trechoclasse_id"] = travel.trecho_classe_id
        dtravels.append(dtravel)
    return dtravels


def _has_incident(group):
    group_with_incidents = new_incidents_adapter.groups_have_incidents([group.id])
    return group_with_incidents.get(group.id, False)


def _incident_grade_change(group):
    group_seat_types = new_incidents_adapter.group_seat_types(group.id)

    downgrades = []
    upgrades = []
    group_seat_type = group_seat_types.get("group_seat_type")

    if group_seat_type:
        for seat_type, passengers_qtt in group_seat_types.get("incidents_seat_types", []):
            if TIPOS_ASSENTO_PESO.get(seat_type, 0) < TIPOS_ASSENTO_PESO.get(group_seat_type, 0):
                downgrades.append(f"Downgrade de {passengers_qtt} passageiros de {group_seat_type} para {seat_type}")
            elif TIPOS_ASSENTO_PESO.get(seat_type, 0) > TIPOS_ASSENTO_PESO.get(group_seat_type, 0):
                upgrades.append(f"Upgrade de {passengers_qtt} passageiros de {group_seat_type} para {seat_type}")

    return {"downgrades": downgrades, "upgrades": upgrades}


def _get_total_value_ressarcimentos(group):
    return ressarcimento_svc.get_group_total_ressarcimento(group.id)


@my_shared_task(queue="async_task")
@transaction.atomic
def escalar_empresa_onibus_rotina_task(async_task_id: int) -> None:
    async_task = AsyncTask.objects.get(id=async_task_id)
    with TaskProgressTracker(async_task):
        form = EscalarEmpresaOnibusRotinaForm.construct(**async_task.input_data)
        grupo_ids = form.grupo_ids

        grupos = (
            Grupo.objects.select_related("company", "notafiscal")
            .select_related("onibus")
            .prefetch_related("grupoclasse_set", "grupoclasse_set__closed_by")
            .filter(id__in=grupo_ids)
        )

        tasks_rodoviaria = None
        operacoes_rodoviaria = map_operacoes_alterar_grupo_rodoviaria(grupos, form)

        if "company_id" in form.__fields_set__:
            _bulk_atualizar_empresa(grupos, form.company_id, form.force_company)

        if "onibus_id" in form.__fields_set__:
            is_transbrasil_bus = Onibus.objects.filter(id=form.onibus_id, jsondata__transbrasil_agreement=True).exists()
            have_fretamento_interestadual_grupos = (
                grupos.filter(
                    modelo_venda=Grupo.ModeloVenda.BUSER,
                )
                .exclude(
                    rota__ufs_intermediarios="",
                    rota__ufs_intermediarios__isnull=True,
                )
                .exists()
            )

            # Regra de negócio: ônibus transbrasil só pode ser escalado em grupos híbridos
            # ou grupos de fretamento, desde que não sejam interestaduais
            if is_transbrasil_bus and have_fretamento_interestadual_grupos:
                raise ValidationError(
                    "Não é possível escalar ônibus TransBrasil em viagens interestaduais de fretamento"
                )

            tasks_rodoviaria = escalar_onibus_svc.bulk_escalar_onibus(
                form, operacoes_rodoviaria=operacoes_rodoviaria, origem_alteracao_placa=async_task.endpoint
            )

        if "company_id" in form.__fields_set__ and "onibus_id" in form.__fields_set__:
            rodoviaria_svc.verifica_criacao_grupos_hibrido_n_rotas(grupos)

        if "rotina_id" in form.__fields_set__:
            rotina_id = form.rotina_id
            if rotina_id is not None:
                rotina_svc.alterar_rotina_dos_grupos(grupo_ids, rotina_id)
            else:
                rotina_svc.remover_rotinas_dos_grupos(grupos)

    transaction.on_commit(partial(escalar_empresa_onibus_rotina_callback, grupos, form, tasks_rodoviaria))

    def cria_atualiza_grupo_cenario():
        for grupo in grupos:
            tasks.cria_atualiza_grupo_cenario_task.delay(grupo.id)

    transaction.on_commit(cria_atualiza_grupo_cenario)


def escalar_empresa_onibus_rotina_callback(grupos, form, tasks_rodoviaria=None):
    log_svc.log_altera_empresa_onibus_rotina_de_grupos(grupos, form.dict(exclude_unset=True, by_alias=True))
    if tasks_rodoviaria:
        tasks_rodoviaria.apply_async()


def update_autorizacao_grupo_model(grupo_id, autorizacoes_form, user):
    autorizacaoGrupo = AutorizacaoGrupo.objects.get(grupo_id=grupo_id)
    for field, value in autorizacoes_form.dict().items():
        setattr(autorizacaoGrupo, field, value)
    autorizacaoGrupo.avaliado_by = user
    autorizacaoGrupo.avaliacao_updated_at = now()
    autorizacaoGrupo.save()

    grupo = Grupo.objects.get(id=grupo_id)
    log_svc.log_edit_autorizacao_grupo(grupo, user, autorizacaoGrupo)


def update_hidden_from_pax(grupo_id, hidden_for_pax_status, user):
    try:
        grupo = Grupo.objects.get(pk=grupo_id)
    except Grupo.DoesNotExist:
        raise ValidationError("Grupo não existe")
    grupo.hidden_for_pax = hidden_for_pax_status
    grupo.save(
        update_fields=[
            "hidden_for_pax",
            "updated_on",
        ]
    )
    log_svc.log_update_group_hidden_for_pax_status(grupo, user, hidden_for_pax_status)


def get_hidden_from_pax(grupo_id):
    try:
        return Grupo.objects.get(id=grupo_id).hidden_for_pax
    except Grupo.DoesNotExist:
        return False


def valida_local_embarque_duplicado(rotas_id: list[int]):
    ckpt_qs = Checkpoint.objects.filter(rota__in=rotas_id).annotate(
        ckpt_duplicado_id=(Concat(F("rota_id"), Value("_"), F("local_id"), output_field=CharField()))
    )
    if (
        ckpt_qs.values("ckpt_duplicado_id")
        .annotate(ckpt_dup_count=Count("ckpt_duplicado_id"))
        .filter(ckpt_dup_count__gt=1)
        .exists()
    ):
        raise ValidationError("Local de embarque duplicado na rota(s) selecionada(s).")


def valida_trecho_vendido_duplicado(rotas_id: list[int]):
    # apesar de não existirem fluxos que duplicam ckpt ou tvs, existem registros antigos com essas inconsistências.
    # removê-los do banco é crítico por conta do delete cascade. Assim, estamos tratando para que uma vez inativada,
    # uma rota inconsistente não volte a ser ativa.
    # tv_duplicados_id = origem_id + destino_id + rota_id
    # ckpt_duplicados_id = rota_id + local_id
    tvs_qs = TrechoVendido.objects.filter(rota__in=rotas_id).annotate(
        tv_duplicados_id=Concat(
            F("origem_id"), Value("_"), F("destino_id"), Value("_"), F("rota_id"), output_field=CharField()
        )
    )

    if (
        tvs_qs.values("tv_duplicados_id")
        .annotate(tv_dup_count=Count("tv_duplicados_id"))
        .filter(tv_dup_count__gt=1)
        .exists()
    ):
        raise ValidationError("Trecho Vendido duplicado na rota(s) selecionada(s).")
