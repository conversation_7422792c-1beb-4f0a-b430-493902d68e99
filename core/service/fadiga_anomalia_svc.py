from datetime import datetime, timedelta

from django.conf import settings

from commons.dateutils import now, to_default_tz
from core import tasks
from core.models_commons import LicensePlateValidation
from core.models_driving import (
    AlertaSeguranca,
    EnumDesatencaoMotorista,
    EventoDesatencaoMotorista,
    IndiceFadigaMotorista,
    SmartCamAlarm,
)
from core.service import fadiga_svc
from core.service.notifications import staff_notification_svc
from core.service.notifications.notification_svc import NotificationBuilder
from telemetria.models import ViagemTelemetria


def _get_alert_context(alert, group):
    moto_strs = []
    for driver in [d for d in [group.driver_one, group.driver_two] if d]:
        if driver.id == group.active_driver:
            moto_strs.append(f"{driver.get_full_name()}: {driver.profile.cell_phone} (dirigindo)")
        else:
            moto_strs.append(f"{driver.get_full_name()}: {driver.profile.cell_phone} (co-piloto)")
    context = {
        "group_str": str(group),
        "alert_datetime": alert.alert_datetime,
        "alert_message": alert.message,
        "moto_strs": moto_strs,
        "resolve_url": fadiga_svc.get_resolve_link(alert.id),
        "group_url": f"{settings.STAFF_BASE_URL}/staff/grupos/{group.id}",
    }
    return context


def send_alert_notifications(alert_id):
    alert = AlertaSeguranca.objects.select_related("grupo__driver_one__profile", "grupo__driver_two__profile").get(
        pk=alert_id
    )
    context = _get_alert_context(alert, alert.grupo)
    # tasks.send_fatigue_alert_to_freshchat.delay(**context) # não manda mais para o freshchat
    for email in staff_notification_svc.select_staff_users_to_notify("Operacoes", permission="fatigue_alerts"):
        template = "notificacao/staff/alerta_fadiga"
        NotificationBuilder(toemail=email, template=template, ctx=context).send(channels={"email"})


def _warn(
    group,
    alert_type,
    message,
    ref_datetime,
    desatencoes=None,
    fadigas=None,
    license_plate_validation=None,
    smartcam_alarms=None,
    origin_id=0,
    origin="bus_telemetry",
):
    if ref_datetime > to_default_tz(now()):
        return
    """ ref_datetime diz o horário que o alerta deveria ter sido criado.
    Útil para o caso em que os dados de telemetria chegam atrasados"""
    desatencoes = desatencoes or []
    fadigas = fadigas or []
    smartcam_alarms = smartcam_alarms or []
    obj = AlertaSeguranca.objects.create(
        grupo=group,
        alert_type=alert_type,
        message=message,
        alert_datetime=ref_datetime,
        origin_id=origin_id,
        origin=origin,
    )
    EventoDesatencaoMotorista.objects.filter(id__in=desatencoes).update(event=obj)
    IndiceFadigaMotorista.objects.filter(id__in=fadigas).update(event=obj)
    LicensePlateValidation.objects.filter(id=license_plate_validation).update(event=obj)
    SmartCamAlarm.objects.filter(id__in=smartcam_alarms).update(event=obj)
    tasks.send_fatigue_alert_notifications.delay(obj.id)


def _existe_alerta_no_intervalo(alert_type, group, interval_start_dt):
    ja_existe_alerta_no_intervalo = AlertaSeguranca.objects.filter(
        grupo=group, alert_type=alert_type, alert_datetime__gte=interval_start_dt
    ).exists()
    return ja_existe_alerta_no_intervalo


def verifica_alerta_nmr_3(group, indicator_event):
    alert_type = AlertaSeguranca.Type.DESATENCAO_GRAVE
    msg = f"""{alert_type}: 5 eventos desatenção no intervalo de 10 minutos com velocidade > 60km/h"""
    intervalo = timedelta(minutes=10)
    end_dt = indicator_event["gps_datetime"]
    start_dt = end_dt - intervalo
    vel_alerta = 60
    eventos_desatencao = EventoDesatencaoMotorista.objects.filter(
        onibus_placa=group.onibus.placa,
        gps_datetime__gte=start_dt,
        gps_datetime__lte=end_dt,
        velocidade__gt=vel_alerta,
    ).exclude(
        tipo_desatencao__in=(EnumDesatencaoMotorista.BAIXO.value, EnumDesatencaoMotorista.OLHO_FECHADO.value),
    )
    desatencoes_id = fadigas_id = []
    desatencoes_id = [evento["id"] for evento in eventos_desatencao.values()]
    ja_existe_alerta_no_intervalo = _existe_alerta_no_intervalo(alert_type, group, start_dt)
    num_eventos_gera_alerta = 5
    gerar_alerta = not ja_existe_alerta_no_intervalo and (eventos_desatencao.count() >= num_eventos_gera_alerta)
    return gerar_alerta, alert_type, msg, desatencoes_id, fadigas_id


def verifica_alerta_nmr_4(group, indicator_event):
    alert_type = AlertaSeguranca.Type.CELULAR
    msg = f"""{alert_type}: 3 eventos (olho fechado/ baixo) no intervalo de 10 min com velocidade > 60km/h"""
    intervalo = timedelta(minutes=10)
    end_dt = indicator_event["gps_datetime"]
    start_dt = end_dt - intervalo
    vel_alerta = 60
    eventos_desatencao = EventoDesatencaoMotorista.objects.filter(
        onibus_placa=group.onibus.placa,
        gps_datetime__gte=start_dt,
        gps_datetime__lte=end_dt,
        velocidade__gt=vel_alerta,
        tipo_desatencao__in=(EnumDesatencaoMotorista.BAIXO.value, EnumDesatencaoMotorista.OLHO_FECHADO.value),
    )
    desatencoes_id = fadigas_id = []
    desatencoes_id = [evento["id"] for evento in eventos_desatencao.values()]
    ja_existe_alerta_no_intervalo = _existe_alerta_no_intervalo(alert_type, group, start_dt)
    num_eventos_gera_alerta = 3
    gerar_alerta = not ja_existe_alerta_no_intervalo and (eventos_desatencao.count() >= num_eventos_gera_alerta)
    return gerar_alerta, alert_type, msg, desatencoes_id, fadigas_id


def verifica_alerta_nmr_5(group, indicator_event):
    alert_type = AlertaSeguranca.Type.CANSACO_GRAVISSIMO

    msg = f"""{alert_type}: 5 picos > 8,5 (vermelho) + 3 olho fechado/baixo/bocejo no intervalo de 10 min com velocidade > 60km/h"""
    intervalo = timedelta(minutes=10)
    end_dt = indicator_event["gps_datetime"]
    start_dt = end_dt - intervalo
    vel_alerta = 60
    fadigas_no_periodo = IndiceFadigaMotorista.objects.filter(
        onibus_placa=group.onibus.placa,
        gps_datetime__gte=start_dt,
        gps_datetime__lte=end_dt,
        percentual_perculos__gt=8.5,
        velocidade__gt=vel_alerta,
    )
    eventos_desatencao = EventoDesatencaoMotorista.objects.filter(
        onibus_placa=group.onibus.placa,
        gps_datetime__gte=start_dt,
        gps_datetime__lte=end_dt,
        velocidade__gt=vel_alerta,
        tipo_desatencao__in=(
            EnumDesatencaoMotorista.BAIXO.value,
            EnumDesatencaoMotorista.OLHO_FECHADO.value,
            EnumDesatencaoMotorista.BOCEJO.value,
        ),
    )
    desatencoes_id = [evento["id"] for evento in eventos_desatencao.values()]
    fadigas_id = [evento["id"] for evento in fadigas_no_periodo.values()]
    ja_existe_alerta_no_intervalo = _existe_alerta_no_intervalo(alert_type, group, start_dt)
    num_eventos_fadiga_gera_alerta, num_eventos_desatencao_gera_alerta = 5, 3
    gerar_alerta = not ja_existe_alerta_no_intervalo and (
        fadigas_no_periodo.count() >= num_eventos_fadiga_gera_alerta
        and eventos_desatencao.count() >= num_eventos_desatencao_gera_alerta
    )
    return gerar_alerta, alert_type, msg, desatencoes_id, fadigas_id


def verifica_alerta_nmr_6(group, indicator_event):
    alert_type = AlertaSeguranca.Type.CANSACO_INTENSO

    msg = f"""{alert_type}: 10 picos > 8,5 (vermelho) no intervalo de 30 min com velocidade > 60km/h"""
    intervalo = timedelta(minutes=30)
    end_dt = indicator_event["gps_datetime"]
    start_dt = end_dt - intervalo
    vel_alerta = 60
    fadigas_no_periodo = IndiceFadigaMotorista.objects.filter(
        onibus_placa=group.onibus.placa,
        gps_datetime__gte=start_dt,
        gps_datetime__lte=end_dt,
        percentual_perculos__gt=8.5,
        velocidade__gt=vel_alerta,
    )
    desatencoes_id = fadigas_id = []
    fadigas_id = [fadiga["id"] for fadiga in fadigas_no_periodo.values()]
    ja_existe_alerta_no_intervalo = _existe_alerta_no_intervalo(alert_type, group, start_dt)
    num_eventos_gera_alerta = 10
    gerar_alerta = not ja_existe_alerta_no_intervalo and (fadigas_no_periodo.count() >= num_eventos_gera_alerta)
    return gerar_alerta, alert_type, msg, desatencoes_id, fadigas_id


def verifica_alerta_nmr_7(group, indicator_event):
    alert_type = AlertaSeguranca.Type.BOCEJO

    msg = f"""{alert_type}: 2 eventos (bocejo) no intervalo de 5 min com velocidade > 60km/h"""
    intervalo = timedelta(minutes=5)
    end_dt = indicator_event["gps_datetime"]
    start_dt = end_dt - intervalo
    vel_alerta = 60
    eventos_desatencao = EventoDesatencaoMotorista.objects.filter(
        onibus_placa=group.onibus.placa,
        gps_datetime__gte=start_dt,
        gps_datetime__lte=end_dt,
        velocidade__gt=vel_alerta,
        tipo_desatencao=EnumDesatencaoMotorista.BOCEJO.value,
    )
    desatencoes_id = fadigas_id = []
    desatencoes_id = [evento["id"] for evento in eventos_desatencao.values()]
    ja_existe_alerta_no_intervalo = _existe_alerta_no_intervalo(alert_type, group, start_dt)
    num_eventos_gera_alerta = 2
    gerar_alerta = not ja_existe_alerta_no_intervalo and (eventos_desatencao.count() >= num_eventos_gera_alerta)
    return gerar_alerta, alert_type, msg, desatencoes_id, fadigas_id


def verifica_alerta_nmr_8(group, tracking_event):
    alert_type_velocidade_alta = AlertaSeguranca.Type.VELOCIDADE_ALTA

    alert_type_velocidade_muito_alta = AlertaSeguranca.Type.VELOCIDADE_MUITO_ALTA

    msg = f"""{alert_type_velocidade_alta}: ônibus com velocidade entre 96km/h e 100km/h"""
    intervalo = timedelta(minutes=3)
    end_dt = tracking_event.datetime_medicao
    start_dt = end_dt - intervalo
    default_return_value_for_not_creating_alert = (False, alert_type_velocidade_alta, msg, None)

    # acima de 100 km/h é gerado o alerta 9
    vel_alerta_minimo = settings.SECURITY_ALERTS_MIN_VELOCITY
    vel_alerta_maximo = 100
    telemetria_condizente_com_alerta = ViagemTelemetria.objects.filter(
        onibus_placa=group.onibus.placa,
        datetime_medicao__gte=start_dt,
        datetime_medicao__lte=end_dt,
        speed__gte=vel_alerta_minimo,
        speed__lt=vel_alerta_maximo,
        provider=ViagemTelemetria.Provider.INFLEET,
    ).first()

    if not telemetria_condizente_com_alerta:
        return default_return_value_for_not_creating_alert
    previous_speeds = (
        ViagemTelemetria.objects.filter(
            onibus_placa=group.onibus.placa,
            datetime_medicao__gte=start_dt,
            datetime_medicao__lte=end_dt,
            provider=ViagemTelemetria.Provider.INFLEET,
        )
        .exclude(pk=telemetria_condizente_com_alerta.pk)
        .values_list("speed", flat=True)
        .order_by("-created_at")[:3]
    )
    if len(previous_speeds) < 3:
        return default_return_value_for_not_creating_alert

    pelo_menos_1_anterior_maior_que_vel_min = False
    for speed in previous_speeds:
        if speed < 90:
            # não usar o orm, precisamos exatamente dos 3 anteriores
            return default_return_value_for_not_creating_alert
        if speed >= vel_alerta_minimo:
            pelo_menos_1_anterior_maior_que_vel_min = True

    if not pelo_menos_1_anterior_maior_que_vel_min:
        return default_return_value_for_not_creating_alert
    # gera alerta apenas se não gerou algum nos últimos 3 minutos
    ja_existe_alerta_no_intervalo_8 = _existe_alerta_no_intervalo(alert_type_velocidade_alta, group, start_dt)
    ja_existe_alerta_no_intervalo_9 = _existe_alerta_no_intervalo(alert_type_velocidade_muito_alta, group, start_dt)
    ja_existe_alerta_no_intervalo = ja_existe_alerta_no_intervalo_8 or ja_existe_alerta_no_intervalo_9
    return (
        not ja_existe_alerta_no_intervalo,
        alert_type_velocidade_alta,
        msg,
        telemetria_condizente_com_alerta,
    )


def verifica_alerta_nmr_9(group, tracking_event):
    alert_type = AlertaSeguranca.Type.VELOCIDADE_MUITO_ALTA
    msg = f"""{alert_type}: ônibus com velocidade maior ou igual a 100km/h"""
    intervalo = timedelta(minutes=3)
    end_dt = tracking_event.datetime_medicao
    start_dt = end_dt - intervalo
    default_return_value_for_not_creating_alert = (False, alert_type, msg, None)

    vel_alerta_minimo = 100
    telemetria_condizente_com_alerta = ViagemTelemetria.objects.filter(
        onibus_placa=group.onibus.placa,
        datetime_medicao__gte=start_dt,
        datetime_medicao__lte=end_dt,
        speed__gte=vel_alerta_minimo,
        provider=ViagemTelemetria.Provider.INFLEET,
    ).first()

    if not telemetria_condizente_com_alerta:
        return default_return_value_for_not_creating_alert

    previous_speeds = (
        ViagemTelemetria.objects.filter(
            onibus_placa=group.onibus.placa,
            datetime_medicao__gte=start_dt,
            datetime_medicao__lte=end_dt,
            provider=ViagemTelemetria.Provider.INFLEET,
        )
        .exclude(pk=telemetria_condizente_com_alerta.pk)
        .values_list("speed", flat=True)
        .order_by("-created_at")[:3]
    )

    if len(previous_speeds) < 3:
        return default_return_value_for_not_creating_alert

    pelo_menos_2_anterior_maior_que_95 = 0
    for speed in previous_speeds:
        if speed < 90:
            # não usar o orm, precisamos exatamente dos 3 anteriores
            return default_return_value_for_not_creating_alert
        if speed > 95:
            pelo_menos_2_anterior_maior_que_95 += 1

    if pelo_menos_2_anterior_maior_que_95 < 2:
        return default_return_value_for_not_creating_alert
    # gera alerta apenas se não gerou algum nos últimos 3 minutos
    ja_existe_alerta_no_intervalo = _existe_alerta_no_intervalo(alert_type, group, start_dt)
    return not ja_existe_alerta_no_intervalo, alert_type, msg, telemetria_condizente_com_alerta


def verifica_alerta_nmr_9_motora_app(group, telemetry, other_telemetry):
    alert_type = AlertaSeguranca.Type.VELOCIDADE_MUITO_ALTA
    msg = f"""{alert_type}: ônibus com velocidade maior ou igual a 100km/h"""
    vel_alerta_minimo = 100

    default_return_value_for_not_creating_alert = False, alert_type, None, None, None
    pelo_menos_2_anterior_maior_que_95 = 0
    for ot in other_telemetry:
        if ot["speed"] < 90:
            return default_return_value_for_not_creating_alert
        if ot["speed"] >= 95:
            pelo_menos_2_anterior_maior_que_95 += 1

    if pelo_menos_2_anterior_maior_que_95 < 2:
        return default_return_value_for_not_creating_alert

    if telemetry["licensePlate"] == group.onibus.placa and telemetry["speed"] >= vel_alerta_minimo:
        telemetry_dt = datetime.fromisoformat(telemetry["eventDatetime"])
        start_dt = telemetry_dt - timedelta(minutes=3)
        ja_existe_alerta_no_intervalo = _existe_alerta_no_intervalo(alert_type, group, start_dt)
        return not ja_existe_alerta_no_intervalo, alert_type, msg, telemetry_dt, telemetry["id"]

    return False, alert_type, None, None, None


def verifica_alerta_nmr_8_motora_app(group, telemetry, other_telemetry):
    alert_type = AlertaSeguranca.Type.VELOCIDADE_ALTA

    alert_type_9 = AlertaSeguranca.Type.VELOCIDADE_MUITO_ALTA
    msg = f"""{alert_type}: ônibus com velocidade entre 96km/h e 100km/h"""
    vel_alerta_minimo = settings.SECURITY_ALERTS_MIN_VELOCITY
    vel_alerta_maximo = 100

    default_return_value_for_not_creating_alert = False, alert_type, None, None, None
    pelo_menos_1_anterior_maior_que_vel_min = False
    for ot in other_telemetry:
        if ot["speed"] < 90:
            return default_return_value_for_not_creating_alert
        if ot["speed"] >= vel_alerta_minimo:
            pelo_menos_1_anterior_maior_que_vel_min = True

    if not pelo_menos_1_anterior_maior_que_vel_min:
        return default_return_value_for_not_creating_alert

    if telemetry["licensePlate"] == group.onibus.placa and vel_alerta_minimo <= telemetry["speed"] < vel_alerta_maximo:
        telemetry_dt = datetime.fromisoformat(telemetry["eventDatetime"])
        start_dt = telemetry_dt - timedelta(minutes=3)
        ja_existe_alerta_no_intervalo = _existe_alerta_no_intervalo(
            alert_type, group, start_dt
        ) or _existe_alerta_no_intervalo(alert_type_9, group, start_dt)
        return not ja_existe_alerta_no_intervalo, alert_type, msg, telemetry_dt, telemetry["id"]

    return default_return_value_for_not_creating_alert


def verifica_alerta_nmr_10(group):
    alert_type = AlertaSeguranca.Type.PLACA_NAO_IDENTIFICADA
    msg = alert_type
    validation = LicensePlateValidation.objects.filter(group_id=group.id).order_by("pk").first()
    existe_alerta = False
    if validation.event:
        existe_alerta = True
    validation_id = validation.id
    return alert_type, msg, validation_id, existe_alerta


def verifica_alerta_nmr_11(group, alarm):
    alert_type = AlertaSeguranca.Type.CINTO_DE_SEGURANCA
    msg = f"{alert_type}: 1 evento de cinto desapertado com velocidade  > 10km/h"
    existe_alerta = AlertaSeguranca.objects.filter(alert_type=AlertaSeguranca.Type.CINTO_DE_SEGURANCA, grupo=group)
    deve_criar = not existe_alerta and alarm.speed > 10 * 10
    return deve_criar, alert_type, msg, [alarm.id], alarm.gps_time


def verifica_alerta_nmr_12(group, alarm):
    alert_type = AlertaSeguranca.Type.USO_DE_CELULAR
    msg = f"{alert_type}: 1 evento de uso de celular com velocidade > 10km/h"
    existe_alerta = AlertaSeguranca.objects.filter(alert_type=AlertaSeguranca.Type.USO_DE_CELULAR, grupo=group)
    deve_criar = not existe_alerta and alarm.speed > 10 * 10
    return deve_criar, alert_type, msg, [alarm.id], alarm.gps_time


def verifica_alerta_nmr_13(group, alarm):
    alert_type = AlertaSeguranca.Type.DESATENCAO_MEDIA
    msg = f"{alert_type}: 2 eventos de Distração genérica no intervalo de 10 minutos com velocidade > 30 km/h"
    intervalo = timedelta(minutes=10)
    end_dt = alarm.gps_time
    start_dt = end_dt - intervalo
    vel_alerta = 30 * 10
    eventos_desatencao = SmartCamAlarm.objects.filter(
        plate=group.onibus.placa,
        gps_time__gte=start_dt,
        gps_time__lte=end_dt,
        speed__gt=vel_alerta,
        type=SmartCamAlarm.Types.DISTRACAO,
        event__isnull=True,
    )
    desatencao_ids = [desatencao.id for desatencao in eventos_desatencao]
    deve_criar = eventos_desatencao.count() >= 2 and not _existe_alerta_no_intervalo(alert_type, group, start_dt)
    return deve_criar, alert_type, msg, desatencao_ids, alarm.gps_time


def verifica_alerta_nmr_14(group, alarm):
    alert_type = AlertaSeguranca.Type.DESATENCAO_MEDIA
    msg = f"{alert_type}: 3 eventos de Distração genérica no intervalo de 20 minutos com velocidade > 30 km/h"
    intervalo = timedelta(minutes=20)
    end_dt = alarm.gps_time
    start_dt = end_dt - intervalo
    vel_alerta = 30 * 10

    eventos_desatencao = SmartCamAlarm.objects.filter(
        plate=group.onibus.placa,
        gps_time__gte=start_dt,
        gps_time__lte=end_dt,
        speed__gt=vel_alerta,
        type=SmartCamAlarm.Types.DISTRACAO,
        event__isnull=True,
    )
    desatencao_ids = [desatencao.id for desatencao in eventos_desatencao]
    deve_criar = eventos_desatencao.count() >= 3 and not _existe_alerta_no_intervalo(alert_type, group, start_dt)

    return deve_criar, alert_type, msg, desatencao_ids, alarm.gps_time


def verifica_alerta_nmr_15(group, alarm):
    alert_type = AlertaSeguranca.Type.CANSACO_LEVE
    msg = f"{alert_type}: 2 eventos de Olho fechado e ou Bocejo no intervalo de 1 hora, considerando velocidades superiores e inferiores a 30 km/h."
    intervalo = timedelta(minutes=60)
    end_dt = alarm.gps_time
    start_dt = end_dt - intervalo
    eventos_cansaco = SmartCamAlarm.objects.filter(
        plate=group.onibus.placa,
        gps_time__gte=start_dt,
        gps_time__lte=end_dt,
        type__in=[SmartCamAlarm.Types.BOCEJO, SmartCamAlarm.Types.OLHO_FECHADO],
        event__isnull=True,
    )
    cansaco_ids = [cansaco.id for cansaco in eventos_cansaco]
    deve_criar = eventos_cansaco.count() >= 2
    return deve_criar, alert_type, msg, cansaco_ids, alarm.gps_time


def verifica_alerta_nmr_16(group, alarm):
    alert_type = AlertaSeguranca.Type.CANSACO_MEDIO
    msg = f"{alert_type}: 2 eventos de Olho fechado e ou Bocejo no intervalo de 20 minutos, considerando velocidades superiores e inferiores a 30 km/h."
    intervalo = timedelta(minutes=20)
    end_dt = alarm.gps_time
    start_dt = end_dt - intervalo
    eventos_cansaco = SmartCamAlarm.objects.filter(
        plate=group.onibus.placa,
        gps_time__gte=start_dt,
        gps_time__lte=end_dt,
        type__in=[SmartCamAlarm.Types.BOCEJO, SmartCamAlarm.Types.OLHO_FECHADO],
        event__isnull=True,
    )
    cansaco_ids = [cansaco.id for cansaco in eventos_cansaco]
    deve_criar = eventos_cansaco.count() >= 2
    return deve_criar, alert_type, msg, cansaco_ids, alarm.gps_time


def verifica_alerta_nmr_18(group, alarm):
    alert_type = AlertaSeguranca.Type.RISCO_DE_COLISAO
    msg = f"{alert_type}: 1 evento de Colisão frontal > 30 km/h no intervalo de 10 min"
    intervalo = timedelta(minutes=10)
    end_dt = alarm.gps_time
    start_dt = end_dt - intervalo
    vel_alerta = 30 * 10
    existe_alerta = SmartCamAlarm.objects.filter(
        plate=group.onibus.placa,
        gps_time__gte=start_dt,
        gps_time__lte=end_dt,
        speed__gt=vel_alerta,
        type=SmartCamAlarm.Types.COLISAO_FRONTAL,
        event__isnull=False,
    )
    deve_criar = not existe_alerta and alarm.speed > vel_alerta
    return deve_criar, alert_type, msg, [alarm.id], alarm.gps_time


def verifica_alerta_nmr_19(group, alarm):
    alert_type = AlertaSeguranca.Type.COLISAO_COM_PEDESTRE
    msg = f"{alert_type}: 1 evento de Colisão com pedestre com velocidade > 10km/h"
    existe_alerta = AlertaSeguranca.objects.filter(alert_type=AlertaSeguranca.Type.COLISAO_COM_PEDESTRE, grupo=group)
    deve_criar = not existe_alerta and alarm.speed > 10 * 10
    return deve_criar, alert_type, msg, [alarm.id], alarm.gps_time


def verifica_alerta_nmr_20(group, alarm):
    alert_type = AlertaSeguranca.Type.ALERTA_DE_VIOLACAO
    msg = f"{alert_type}: 1 evento de violação da Câmera de Fadiga > 30 km/h"
    intervalo = timedelta(minutes=10)
    end_dt = alarm.gps_time
    start_dt = end_dt - intervalo
    vel_alerta = 30 * 10
    existe_alerta = SmartCamAlarm.objects.filter(
        plate=group.onibus.placa,
        gps_time__gte=start_dt,
        gps_time__lte=end_dt,
        speed__gt=vel_alerta,
        type__in=[SmartCamAlarm.Types.PERDA_VIDEO, SmartCamAlarm.Types.LENTE_COBERTA],
        event__isnull=False,
    )
    deve_criar = not existe_alerta and alarm.speed > vel_alerta
    return deve_criar, alert_type, msg, [alarm.id], alarm.gps_time


def verifica_alerta_nmr_21(group, alarm):
    alert_type = AlertaSeguranca.Type.DISTANCIA_INSEGURA
    msg = f"{alert_type}: 3 eventos de Colisão frontal > 30 km/h no intervalo de 10 min"
    intervalo = timedelta(minutes=10)
    end_dt = alarm.gps_time
    start_dt = end_dt - intervalo
    vel_alerta = 30 * 10
    eventos_colisao = SmartCamAlarm.objects.filter(
        plate=group.onibus.placa,
        gps_time__gte=start_dt,
        gps_time__lte=end_dt,
        speed__gt=vel_alerta,
        type=SmartCamAlarm.Types.COLISAO_FRONTAL,
        event__isnull=True,
    )
    colisao_ids = [colisao.id for colisao in eventos_colisao]
    deve_criar = eventos_colisao.count() >= 3
    return deve_criar, alert_type, msg, colisao_ids, alarm.gps_time


def check(group, indicator_event, ref_datetime=None):
    ref_datetime = ref_datetime or now()
    verificacoes = [
        verifica_alerta_nmr_3,
        verifica_alerta_nmr_4,
        verifica_alerta_nmr_5,
        verifica_alerta_nmr_6,
        verifica_alerta_nmr_7,
    ]
    for verifica in verificacoes:
        warn, alert_type, msg, desatencoes_id, fadigas_id = verifica(group=group, indicator_event=indicator_event)
        if not warn:
            continue
        _warn(
            group=group,
            alert_type=alert_type,
            message=msg,
            ref_datetime=ref_datetime,
            desatencoes=desatencoes_id,
            fadigas=fadigas_id,
        )


def check_telemetria(group, tracking_event, ref_datetime=None):
    ref_datetime = ref_datetime or now()
    verificacoes = [
        verifica_alerta_nmr_9,
        verifica_alerta_nmr_8,
    ]
    for verifica in verificacoes:
        warn, alert_type, msg, telemetry = verifica(group=group, tracking_event=tracking_event)
        if not warn:
            continue
        _warn(
            group=group,
            alert_type=alert_type,
            message=msg,
            ref_datetime=ref_datetime,
            origin_id=telemetry.id,
            origin="viagem_telemetria__infleet",
        )


def check_telemetria_motora_app(group, telemetry_list=None):
    verificacoes = [
        verifica_alerta_nmr_9_motora_app,
        verifica_alerta_nmr_8_motora_app,
    ]
    telemetry_list = telemetry_list or []
    for i in range(0, len(telemetry_list), 4):
        for verifica in verificacoes:
            chunk = telemetry_list[i : i + 4]
            if len(chunk) < 4:
                continue
            telemetry = chunk[0]
            other_telemetry = chunk[1:]
            warn, alert_type, msg, ref_datetime, telemetry_id = verifica(
                group=group, telemetry=telemetry, other_telemetry=other_telemetry
            )
            if not warn:
                continue
            _warn(
                group=group,
                alert_type=alert_type,
                message=msg,
                ref_datetime=ref_datetime,
                origin_id=telemetry_id,
                origin="driver_app",
            )


def check_ocr(group, ref_datetime=None):
    ref_datetime = ref_datetime or now()

    alert_type, msg, validation_id, existe_alerta = verifica_alerta_nmr_10(group=group)
    if not existe_alerta:
        _warn(
            group=group,
            alert_type=alert_type,
            message=msg,
            ref_datetime=ref_datetime,
            license_plate_validation=validation_id,
        )


def check_smartcam(group, alarm):
    VERIFICACOES_MAP = {
        SmartCamAlarm.Types.PERDA_VIDEO: [verifica_alerta_nmr_20],
        SmartCamAlarm.Types.LENTE_COBERTA: [verifica_alerta_nmr_20],
        SmartCamAlarm.Types.CELULAR: [verifica_alerta_nmr_12],
        SmartCamAlarm.Types.DISTRACAO: [verifica_alerta_nmr_14, verifica_alerta_nmr_13],
        SmartCamAlarm.Types.COLISAO_FRONTAL: [verifica_alerta_nmr_21, verifica_alerta_nmr_18],
        SmartCamAlarm.Types.BOCEJO: [verifica_alerta_nmr_16, verifica_alerta_nmr_15],
        SmartCamAlarm.Types.OLHO_FECHADO: [verifica_alerta_nmr_16, verifica_alerta_nmr_15],
        SmartCamAlarm.Types.COLISAO_PEDESTRE: [verifica_alerta_nmr_19],
        SmartCamAlarm.Types.CINTO: [verifica_alerta_nmr_11],
        # SASCAR TABLET EVENTS
        SmartCamAlarm.Types.EXCESSO_VELOCIDADE_PISTA_SECA: [verifica_alerta_velocidade_tablet_sascar],
        SmartCamAlarm.Types.EXCESSO_VELOCIDADE_PISTA_MOLHADA: [verifica_alerta_velocidade_tablet_sascar],
        SmartCamAlarm.Types.EXCESSO_VELOCIDADE_ROTOGRAMA_PISTA_SECA: [verifica_alerta_velocidade_tablet_sascar],
        SmartCamAlarm.Types.FREADA_BRUSCA: [verifica_alerta_tablet_sascar],
        SmartCamAlarm.Types.FORCA_G_LATERAL_FORTE: [verifica_alerta_tablet_sascar],
        SmartCamAlarm.Types.FORCA_G_LATERAL_MEDIA: [verifica_alerta_tablet_sascar],
        SmartCamAlarm.Types.ACELERACAO_BRUSCA: [verifica_alerta_tablet_sascar],
        SmartCamAlarm.Types.EXCESSO_VELOCIDADE_ROTOGRAMA_PISTA_MOLHADA: [verifica_alerta_velocidade_tablet_sascar],
    }
    verificacoes = VERIFICACOES_MAP[alarm.type]
    for verifica in verificacoes:
        warn, alert_type, msg, smartcam_alarms, ref_datetime = verifica(group=group, alarm=alarm)
        if not warn:
            continue
        _warn(
            group=group,
            alert_type=alert_type,
            message=msg,
            smartcam_alarms=smartcam_alarms,
            ref_datetime=ref_datetime,
            origin_id=alarm.id,
            origin=f"smartcam_{alarm.provider}",
        )


def create_alertas_from_motorista_back(group, alert_datetime, alert_type, message):
    _warn(
        group=group,
        alert_type=alert_type,
        ref_datetime=alert_datetime,
        message=message,
        origin="driver_app",
    )


def verifica_alerta_velocidade_tablet_sascar(group, alarm):
    OITENTA_KM_H = 80 * 10
    alert_type = AlertaSeguranca.Type.VELOCIDADE_ALTA
    velocidade = alarm.speed / 10
    msg = f"{alert_type}: evento de Excesso de velocidade. Velocidade: {velocidade}km/h"
    existe_alerta = AlertaSeguranca.objects.filter(
        alert_type=AlertaSeguranca.Type.VELOCIDADE_ALTA, grupo=group, created_at__gte=now() - timedelta(minutes=20)
    )
    deve_criar = not existe_alerta and alarm.speed > OITENTA_KM_H
    return deve_criar, alert_type, msg, [alarm.id], alarm.gps_time


def verifica_alerta_tablet_sascar(group, alarm):
    alarm_type = alarm.type
    velocidade = alarm.speed / 10
    msg = f"{alarm_type}: evento de {alarm_type} com Velocidade: {velocidade}km/h"

    existe_alerta = AlertaSeguranca.objects.filter(
        alert_type=alarm_type, grupo=group, created_at__gte=now() - timedelta(minutes=10)
    )
    deve_criar = not existe_alerta
    return deve_criar, alarm_type, msg, [alarm.id], alarm.gps_time
