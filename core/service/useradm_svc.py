# coding: utf-8
import logging
import re
from dataclasses import asdict
from datetime import datetime
from itertools import chain

from django.contrib.auth.models import User
from django.core.exceptions import SuspiciousOperation, ValidationError
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Count, F, Max, Q
from django.db.models import Value as V
from django.db.models.functions import Concat, Lower, Trim
from django.utils.timezone import localtime

from accounting.service import accounting_svc
from adapters.doc_check_adapter.exceptions import CPFCheckException
from commons import guard, storage
from commons.dateutils import now
from commons.guard import UserGuard
from commons.utils import is_valid_cpf, only_numbers, random_code
from core.forms.staff_forms import ListDriversForm
from core.models_commons import (
    ActivityLog,
    DocValidationProvider,
    MotoristaAprovacao,
    Revendedor,
    TransactionEmail,
    TravelCommunication,
    WhatsappMessage,
)
from core.models_company import Company, DriverRate
from core.models_contabil import AccountingTransaction
from core.models_grupo import Grupo
from core.models_travel import <PERSON>om<PERSON>ead, Passageiro, Ressarcimento, Travel
from core.serializers import serializer_travel, serializer_user
from core.serializers.serializer_ponto_de_venda import PontoDeVendaSerializer
from core.serializers.serializer_transaction_email import TransactionEmailSerializer
from core.serializers.serializer_whatsapp_message import WhatsappMessageSerializer
from core.service import cupomadm_svc, doc_check_svc, doc_validation_svc, log_svc, profile_svc, user_svc
from core.service.driver_svc import create_or_update_company_driver
from core.service.notifications import user_notification_svc
from core.service.pagamento import pagamento_svc
from core.service.reserva import reserva_extrato_svc
from core.service.tags_svc import get_tags_by_cpf
from core.service.user_svc import validate_user_dic
from core.signals import remove_reais_do_pax_signal
from integrations import get_client
from integrations.motorista_client import MotoristaClient
from payments.service import antifraude_svc

buserlogger = logging.getLogger("buserlogger")


def get_driver_details(driver_id: int) -> dict:
    """
    Atualmente a função retorna:
        # Data cadastro do motorista na Buser
        # Numero de viagens concluídas (mesmo que é mostrado no app do motora)
        # Data da última viagem concluida
        # Link CNH, data de expiração, UF, cidade
        # Link Curso de condutor, data de expiração
        # Treinamento Buser (ead_progress)
        # Regime de contratação (nem todos tem essa info)
        # Feedbacks do próprio Motorista (joinha pra cima X joinha pra baixo)
        # Empresa vinculada (Presente)
    """
    try:
        q = User.objects.select_related("profile", "profile__reputation")
        user = q.get(pk=driver_id)
    except User.DoesNotExist:
        return None

    d = serializer_user.serialize_adm_driver(user)

    recent_feedbacks_error = False
    try:
        motorista: MotoristaClient = get_client("motorista")
        recent_feedbacks = motorista.get_motorista_recent_feedback(driver_id)
    except Exception:
        recent_feedbacks = None
        recent_feedbacks_error = True

    passengers = (
        user.buseiro_set.all()
        .annotate(last_reservation_date=Max("passageiro__travel__created_on"))
        .order_by("-last_reservation_date")[:10]
        .to_serialize(serializer_travel.BuseiroSerializer)
    )

    count_travels = (
        DriverRate.objects.filter(motorista_id=driver_id).order_by("-ref_date").values("viagens_totais").first()
    )

    registration_date = ActivityLog.objects.filter(touser_id=driver_id, type="created_new_user")

    last_done_travel = (
        Grupo.objects.filter(Q(driver_one__id=driver_id) | Q(driver_two__id=driver_id), status=Grupo.Status.DONE)
        .order_by("-datetime_ida")
        .first()
    )

    d.update(
        {
            "cached_passengers": [passenger.serialize() for passenger in passengers],
            "count_travels": count_travels["viagens_totais"] if count_travels else 0,
            "feedbacks": [asdict(feedback) for feedback in recent_feedbacks] if recent_feedbacks else None,
            "feedbacks_error": recent_feedbacks_error,
            "last_done_travel_date": last_done_travel.datetime_ida.date() if last_done_travel else None,
            "rating": round(user.profile.reputation.rating if user.profile.reputation else 5, 2),
            "registration_date": registration_date[0].created_at.date() if registration_date else None,
        }
    )

    return d


def _get_buseiro_details(buseiro_id):
    user = (
        User.objects.select_related(
            "revendedor__team_leader_revendedor",
            "revendedor__ponto_de_venda",
            "profile",
        )
        .filter(pk=buseiro_id)
        .first()
    )

    if user is None:
        return None

    details = serializer_user.serialize_buseiro_details(user)

    if hasattr(user, "revendedor") and user.revendedor.team_leader_revendedor:
        details.update({"team_leader_user_id": user.revendedor.team_leader_revendedor.user_id})
    if hasattr(user, "revendedor") and user.revendedor.ponto_de_venda:
        details.update({"ponto_de_venda": PontoDeVendaSerializer().serialize_object(user.revendedor.ponto_de_venda)})

    persona_non_grata = antifraude_svc.get_persona_non_grata_details(cpf=user.profile.cpf)
    details.update({"persona_non_grata": persona_non_grata if persona_non_grata else False})

    serializer = serializer_travel.BuseiroSerializer
    passengers = (
        user.buseiro_set.annotate(last_reservation_date=Max("passageiro__travel__created_on"))
        .order_by("-active", "-last_reservation_date")
        .to_serialize(serializer)
    )
    passengers = passengers[:20]
    details.update(
        {
            "cached_passengers": [p.serialize() for p in passengers],
        }
    )
    return {"user": user, "buseiro_details": details}


def get_buseiro_details(buseiro_id):
    data = _get_buseiro_details(buseiro_id)
    if data is None:
        return None

    return data["buseiro_details"]


def get_buseiro_details_with_tags(buseiro_id):
    data = _get_buseiro_details(buseiro_id)

    if data is None:
        return None

    user = data["user"]
    details = data["buseiro_details"]

    tags = list(
        get_tags_by_cpf(user.profile.cpf).values(
            "atribuicoes__id", "nome", "atribuicoes__referencia", "hex_color", "hex_color_background"
        )
    )
    vip_tag = next((tag for tag in tags if tag["nome"] == "vip"), None)
    details["tags"] = tags
    details["is_vip"] = vip_tag is not None
    details["social_networks"] = vip_tag["atribuicoes__referencia"] if vip_tag else None

    return details


def cancelar_ressarcimento(loggeduser, pax_id_list, amount, reason_key, reason, ressarcimento_id):
    removereais(loggeduser, pax_id_list, amount, reason_key, reason)
    return Ressarcimento.objects.filter(id=ressarcimento_id).update(status="cancelado", updated_at=now())


def removereais(loggeduser, pax_id_list, amount, reason_key, reason):
    if amount > 0:
        raise ValidationError("Valor a cancelar precisa ser negativo")
    passageiros = Passageiro.objects.select_related("travel__user").filter(pk__in=pax_id_list)
    with transaction.atomic():
        for pax in passageiros:
            accounting_svc.remover_reais(pax, loggeduser, value=amount, reason_key=reason_key, reason=reason)
            remove_reais_do_pax_signal.send(None, pax=pax, travel=pax.travel)

    for pax in passageiros:
        log_svc.log_admin_give_reais(pax.travel.user, amount, reason, pax.travel)


def give_voucher(
    giver,
    targets,
    cupom,
    due_date,
    reason,
    send_mail=False,
    reason_key=None,
    reason_description=None,
    canal_atendimento_id=None,
    link_jira=None,
    link_slack=None,
    protocolo_atendimento=None,
):
    travels_ressarcidas = []
    user_vouchers = {}

    user_ids = [target["user_id"] for target in targets]
    travel_ids = [target["travel_id"] for target in targets if target["travel_id"]]

    travel_qs = (
        Travel.objects.select_related(
            "trecho_vendido",
            "trecho_classe__trecho_vendido__origem__cidade",
            "trecho_classe__trecho_vendido__destino__cidade",
        )
        .prefetch_related("passageiro_set")
        .annotate(
            passengers_count=Count("passageiro", filter=Q(passageiro__removed=False)),
            refundable_passengers_count=Count(
                "passageiro", filter=Q(passageiro__removed=False) | Q(canceled_reason__in=["BY_ADMIN", "BY_RISCO"])
            ),
        )
        .filter(pk__in=travel_ids)
    )
    users_qs = User.objects.select_related("lead").filter(pk__in=user_ids)
    map_travels = {t.id: t for t in travel_qs}
    map_users = {u.id: u for u in users_qs}
    _now = now()
    cupom_leads_created = []
    for target in targets:
        ressarcir = True
        user_id = target["user_id"]
        travel_id = target["travel_id"]
        travel = None
        travel_price = None
        user = map_users[user_id]
        if travel_id is not None:
            if travel_id in travels_ressarcidas:
                ressarcir = False
            else:
                travel = map_travels[travel_id]
                passengers_count = travel.passengers_count
                valor_pago_travel = reserva_extrato_svc.calcula_valor_pago_travel(travel)
                if cupom.code == "REMARCANSMANUAL":
                    travel_price = valor_pago_travel / passengers_count
                else:
                    travel_price = valor_pago_travel
                vouchers_ja_dados = CupomLead.objects.filter(
                    lead=user.lead, travel_id=travel_id, voucher__reason_key=reason_key
                ).count()
                if reason_key is not None and vouchers_ja_dados >= travel.refundable_passengers_count:
                    ressarcir = False
                else:
                    travels_ressarcidas.append(travel_id)
        if ressarcir:
            voucher = cupomadm_svc.create_vouchers(
                cupom=cupom,
                due_date=due_date,
                reason=reason,
                fromuser=giver,
                reason_key=reason_key,
                reason_description=reason_description,
                canal_atendimento_id=canal_atendimento_id,
                link_jira=link_jira,
                link_slack=link_slack,
                protocolo_atendimento=protocolo_atendimento,
                travel_price=travel_price,
            )[0]
            cupom_lead = CupomLead(lead=user.lead, voucher=voucher, travel=travel, created_at=_now)
            cupom_leads_created.append(cupom_lead)
            user_vouchers[user.id] = voucher
            log_svc.log_admin_give_voucher(user, cupom, reason)
            if send_mail:
                user_notification_svc.user_recebeu_voucher.delay(
                    user, cupom, due_date, travel, travel_price, evento="useradm_svc.give_voucher"
                )
    CupomLead.objects.bulk_create(cupom_leads_created)
    return user_vouchers


def list_drivers(params: ListDriversForm):
    drivers = list_unordered_drivers(params)
    if not (params.paginator and params.paginator.sort_by):
        return drivers.order_by("first_name")
    return drivers.order_by(("-" if params.paginator.descending else "") + params.paginator.sort_by)


def list_unordered_drivers(params: ListDriversForm):
    drivers = UserGuard.drivers(company=params.company).prefetch_related("motoristaaprovacao_set")

    if params.company:
        drivers = drivers.filter(Q(profile__company__id=params.company))

    if params.gestor_qualidade:
        drivers = drivers.filter(
            Q(profile__company__gestor_qualidade__isnull=False)
            & Q(profile__company__gestor_qualidade__id=params.gestor_qualidade)
        )

    if params.gestor_comercial:
        drivers = drivers.filter(
            Q(profile__company__gestor__isnull=False) & Q(profile__company__gestor__id=params.gestor_comercial)
        )

    if params.docs:
        drivers = drivers.filter(Q(profile__isnull=False) & Q(profile__driver_docs_status__in=params.docs))

    if params.driver:
        query = Q()
        if "@" in params.driver:
            query |= Q(email=params.driver.lower())
        elif is_valid_cpf(only_numbers(params.driver)):
            query |= Q(profile__cpf=only_numbers(params.driver))
        elif any(c.isdigit() for c in params.driver):
            query |= Q(profile__cell_phone=params.driver)
        else:
            drivers = drivers.annotate(full_name=Concat(F("first_name"), V(" "), F("last_name"))).filter(
                full_name__icontains=params.driver
            )
        drivers = drivers.filter(query)

    return drivers


def update_user(user_id, params):
    user = User.objects.get(pk=user_id)
    is_exception_update = "is_blocked" in params or "annotation" in params

    if user:
        if not user.is_active and not is_exception_update:
            raise ValidationError("Usuário está inativo e não pode ser atualizado")
        permissions = params.get("permissions", {})
        can_accept_partner_terms = permissions.get("can_accept_partner_terms", False)

        if can_accept_partner_terms and guard.is_buser(user):
            raise SuspiciousOperation("Usuários Buser não podem ter essa permissao")

        profile_update_dict = {}
        if "is_company" in params:
            guard.set_role(user, "company", _bool=params["is_company"])
        if "is_driver" in params:
            guard.set_role(user, "driver", _bool=params["is_driver"])
        if "is_revendedor" in params:
            guard.set_role(user, "Revendedor", _bool=params["is_revendedor"])
            user_revendedor, _ = Revendedor.objects.get_or_create(user=user)
            user_revendedor.active = params["is_revendedor"]
            user_revendedor.save(update_fields=["active"])
        if "company" in params:
            if params["company"] is not None:
                company_id = int(params["company"])
                company = Company.objects.get(pk=company_id)
                company.partners.add(user)
            else:
                company_id = None
            profile_update_dict.update(company_id=company_id)
        if "cpf" in params:
            profile_update_dict.update(cpf=only_numbers(params["cpf"]))
        if "is_blocked" in params:
            if params["is_blocked"] is True:
                user.is_active = False
                profile_update_dict.update(
                    is_blocked=True,
                    block_reason=params["block_reason"],
                    verification_status="unverified",
                    cell_phone_confirmed=False,
                )
            elif params["is_blocked"] is False:
                user.is_active = True
                annotation = f"{user.profile.annotation}\n\n" if user.profile.annotation else ""
                block_reason = f"Usuário desbloqueado em {localtime():%d/%m/%Y às %H:%M}"
                block_reason += (
                    f"\nMotivo do desbloqueio: {params['unblock_reason']}" if "unblock_reason" in params else ""
                )
                block_reason += (
                    f"\nMotivo do bloqueio anterior: {user.profile.block_reason}" if user.profile.block_reason else ""
                )
                profile_update_dict.update(is_blocked=False, annotation=annotation + block_reason)
        if "annotation" in params:
            profile_update_dict.update(annotation=params["annotation"])
        if profile_update_dict:
            profile_svc.update_profile(user.id, **profile_update_dict)
            profile_svc.refresh_profile_if_needed(user, list(profile_update_dict.keys()))
        user.save()

        for permission, enabled in permissions.items():
            guard.set_permission(user, permission, _bool=enabled)

        log_svc.log_staff_change_user_permissions(user, params)
    return serializer_user.serialize_adm_drivers(user)


def edit_user_phone(user_id, phone):
    user = User.objects.get(id=user_id)
    existing_user_with_phone = (
        User.objects.filter(is_active=True, profile__cell_phone=phone, profile__cell_phone_confirmed=True)
        .exclude(pk=user_id)
        .first()
    )
    if existing_user_with_phone:
        us = serializer_user.serialize_user(existing_user_with_phone)
        us.update({"saldo": accounting_svc.get_saldo(existing_user_with_phone)})
        res = {"error": "Esse número já está sendo usado em outra conta", "error_type": "FATAL", "user": us}
        return res
    else:
        profile_svc.update_profile(user_id, cell_phone_to_confirm=phone, cell_phone_confirmation_date=None)
        user.profile.refresh_from_db(fields=["cell_phone_to_confirm", "cell_phone_confirmation_date"])
        if not guard.is_driver(user):
            profile_svc.unverify_user(user_id, phone_or_email_edited=True)
        pagamento_svc.remove_all_user_cards(user)
        user_notification_svc.confirmar_telefone(user, staff=True, evento="useradm_svc.edit_user_phone")
        log_svc.log_staff_edited_phone_and_sent_sms_to_user(user, phone)
        return {}


def edit_user_email(user_id, email):
    user = User.objects.get(id=user_id)
    email = user_svc._validate_email(email, user)
    existing_user_with_email = (
        User.objects.alias(email_lower=Lower(Trim("email"))).filter(email_lower=email).exclude(pk=user_id).first()
    )

    if existing_user_with_email:
        if existing_user_with_email.is_active:
            user_data = serializer_user.serialize_user(existing_user_with_email)
            user_data.update({"saldo": accounting_svc.get_saldo(existing_user_with_email)})
            return {"error": "Esse email já está sendo usado em outra conta", "error_type": "FATAL", "user": user_data}

        rnd_email = f"{random_code(12)}@invalid.email"
        existing_user_with_email.email = rnd_email
        existing_user_with_email.username = rnd_email
        existing_user_with_email.save()
    old_email = user.email
    user.email = email
    user.username = email
    user.save(update_fields=["email", "username"])

    user_notification_svc.notifica_old_email(user, old_email)
    log_svc.log_staff_edited_user_email(old_email, email, user)
    pagamento_svc.remove_all_user_cards(user)

    if not guard.is_driver(user):
        profile_svc.unverify_user(user_id, phone_or_email_edited=True)

    return {}


def edit_user_name(user_id, name):
    def _build_last_name(last_name):
        result = last_name[0]
        for i, name in enumerate(last_name):
            if i > 0:
                result += f" {name}"
        return result

    user = User.objects.get(pk=user_id)
    old_fullname = user.get_full_name()
    fullname = name.split(" ")
    first_name = fullname[0]
    last_name = fullname[1:]
    user.first_name = first_name
    user.last_name = _build_last_name(last_name)
    user.save()
    new_fullname = user.get_full_name()
    log_svc.log_staff_edited_user_name(user, old_fullname, new_fullname)

    return {}


def get_dados_receita(cpf):
    dados_receita = doc_check_svc.get_dados_receita(cpf)
    if not dados_receita:
        try:
            dados_receita = doc_check_svc.check_cpf(cpf)
        except CPFCheckException as e:
            dados_receita = {"erro": str(e)}

        doc_check_svc.update_dados_receita(cpf, dados_receita)
    return dados_receita


def verifica_dados_receita(cpf):
    dados_receita = get_dados_receita(cpf)
    return {"receita": dados_receita}


def get_user_verification_data(
    user_id,
):
    user = User.objects.select_related("profile").get(pk=user_id)
    cpf = user.profile.cpf
    if not cpf:
        raise Exception("Usuário %s não tem cpf para ser verificado" % user_id)

    dados_receita = get_dados_receita(cpf)

    saques_pendentes = list(
        AccountingTransaction.objects.filter(user=user, pending_account_verification=True, confirmed=True, failed=False)
    )
    count_saques_pendentes = len(saques_pendentes)
    valor_saques_pendentes = sum([s.value for s in saques_pendentes])
    return {
        "receita": dados_receita,
        "count_saques_pendentes": count_saques_pendentes,
        "valor_saques_pendentes": valor_saques_pendentes,
    }


def create_new_driver(user_dic: dict, company_id: int, files=None):
    user = user_svc.create_user_from_dict(user_dic)
    cpf = only_numbers(user_dic["cpf"])
    company = Company.objects.get(pk=company_id)
    user_dic.update({"company": company.name})
    profile_update_dict = dict(company_id=company_id, cpf=cpf)
    if files:
        for f in files:
            key = _upload_driver_docs(files[f], user.id, f)
            profile_update_dict.update({f: key})
        profile_update_dict.update(driver_docs_status="pendente")

    profile_update_dict.update(
        cnh_expire_date=user_dic.get("cnh_expire_date"),
        cnh_uf=user_dic.get("cnh_uf"),
        cnh_city=user_dic.get("cnh_city"),
        curso_expire_date=user_dic.get("curso_expire_date"),
        birth_date=user_dic.get("birth_date"),
        driver_gender=user_dic.get("driver_gender"),
    )

    profile_svc.update_profile(user.id, **profile_update_dict)
    profile_svc.refresh_profile_if_needed(user, list(profile_update_dict.keys()))
    guard.assign_role(user, "driver")
    log_svc.log_created_new_user(user, user_dic)

    # em migração: Relação de motorista vai morar na empresa
    create_or_update_company_driver(driver=user, company=company, contract_type=user_dic.get("contract_type"))

    if user.profile.cnh_foto:
        doc_validation_svc.initiate_driver_cnh_validation(user.email)

    return serializer_user.serialize_adm_drivers(user)


def _upload_driver_docs(file, driver_id, field):
    if not bool(re.search(r"application\/pdf|image\/.+", file.content_type)):
        raise Exception("São permitidos apenas pdf ou imagens.")
    user = User.objects.get(pk=driver_id)
    return storage.upload_file(file, user, field)


def update_driver(user_dic, user_id, files=None, update_type="update", confirm_phone=False):
    validate_user_dic(user_dic)
    name = user_dic.get("name", False)
    phone, email, cpf = only_numbers(user_dic["phone"]), user_dic["email"].lower(), only_numbers(user_dic["cpf"])
    namesplit = name.split()
    first_name = namesplit[0]
    last_name = " ".join(namesplit[1:])
    user = User.objects.get(pk=user_id)
    user.first_name = first_name
    user.last_name = last_name
    user.email = email
    user.save()
    profile_svc.set_phone(user, phone)
    profile_svc.refresh_profile_if_needed(
        user,
        [
            "cell_phone_to_confirm",
            "cell_phone",
            "cell_phone_confirmation_code",
            "cell_phone_confirmation_attempts",
            "cell_phone_confirmed",
        ],
    )
    company = user_dic.pop("empresa")
    company_name = company["name"] if "name" in company else None
    user_dic.update({"empresa": company_name})
    company = Company.objects.get(pk=company["id"])
    profile_update_dict = dict(
        company_id=company.id,
        cpf=cpf,
        birth_date=user_dic.get("birth_date"),
        driver_gender=user_dic.get("driver_gender"),
    )

    # em migração: Relação de motorista vai morar na empresa
    create_or_update_company_driver(user, company, contract_type=user_dic.get("contract_type"))

    if not (user_dic.get("driver_docs_status") == "rejeitado" and update_type == "update_avaliacao"):
        profile_update_dict.update(
            cnh_expire_date=user_dic.get("cnh_expire_date"),
            curso_expire_date=user_dic.get("curso_expire_date"),
            cnh_uf=user_dic.get("cnh_uf"),
            cnh_city=user_dic.get("cnh_city"),
        )

    aprovacao = user.motoristaaprovacao_set.order_by("-updated_at").first()
    if update_type == "update_avaliacao" and aprovacao:
        cnh = aprovacao.cnh

        new_approval = MotoristaAprovacao.objects.create(
            user=user,
            cnh=cnh,
            status=MotoristaAprovacao.Status.REPROVADO_MANUALMENTE,
            provider=DocValidationProvider.MANUAL,
        )

        if user_dic.get("driver_docs_status") == "aprovado":
            new_approval.status = MotoristaAprovacao.Status.APROVADO_MANUALMENTE
        new_approval.save()
    is_cnh_updated = False
    if files:
        for f in files:
            key = _upload_driver_docs(files[f], user.id, f)
            profile_update_dict.update({f: key})
            if f == "cnh_foto":
                is_cnh_updated = True
        profile_update_dict.update(driver_docs_status="pendente")

    _handle_status_aprovacao(
        profile_update_dict,
        user_dic.get("driver_docs_status", "não enviado"),
        user_dic.get("driver_avaliado_em"),
        user_dic.get("mensagem_status"),
    )

    user.save()
    profile_svc.update_profile(user.id, **profile_update_dict)
    if confirm_phone:
        user_svc.confirm_phone(user, user.profile.cell_phone_confirmation_code)
    guard.set_role(user, "driver", _bool=user_dic.get("is_driver", True))
    log_svc.log_updated_driver(user, user_dic)
    if is_cnh_updated:
        doc_validation_svc.initiate_driver_cnh_validation(user.email)
    return serializer_user.serialize_adm_drivers(user)


def _handle_status_aprovacao(update_dict: dict, status: str, avaliado_em: datetime, mensagem_status: str):
    if status not in ("pendente", "não enviado"):
        update_dict.update(
            driver_docs_status=status, driver_avaliado_em=avaliado_em, mensagem_status_docs=mensagem_status
        )


def create_new_admin(user_dic, company_id, permissions=None):
    user = user_svc.create_user_from_dict(user_dic)
    company = Company.objects.get(pk=company_id)
    profile_svc.update_profile(user.id, company_id=company_id)
    guard.assign_role(user, "company")

    # em migração: Relação de parceiro vai morar na empresa
    company.partners.add(user)

    if permissions:
        for permission, enabled in permissions.items():
            guard.set_permission(user, permission, _bool=enabled)

    log_svc.log_created_new_user(user, user_dic)
    return serializer_user.serialize_admins(user)


def list_users_by_role(role):
    serialzed_users = []
    users = guard.UserGuard.role(role)
    for user in users:
        serialzed_users.append({"id": user.id, "name": user.get_full_name()})

    return serialzed_users


def list_user_communication_logs(user, search=None):
    whatsapp_messages = (
        WhatsappMessage.objects.to_serialize(WhatsappMessageSerializer).filter(user=user).select_related("travel")
    )
    transaction_emails = TransactionEmail.objects.to_serialize(TransactionEmailSerializer).filter(to_user=user)

    email_ids = []
    for transaction_email in transaction_emails:
        email_ids.append(transaction_email.id)

    travel_communications_email = {
        c.communication_id: c.travel
        for c in TravelCommunication.objects.filter(
            communication_id__in=transaction_emails, type="email"
        ).select_related("travel")
    }

    communication_logs = sorted(
        chain(whatsapp_messages, transaction_emails), key=lambda msg: msg.created_at, reverse=True
    )
    dlogs = []

    for log in communication_logs:
        serialized_log = log.serialize()
        current_type = "email" if serialized_log.get("to_mail") else "whatsapp"
        if current_type == "whatsapp":
            if log.travel:
                serialized_log["travel"] = {"id": log.travel.id, "reservation_code": log.travel.reservation_code}
        else:
            travel = travel_communications_email.get(log.id)
            if travel:
                serialized_log["travel"] = {"id": travel.id, "reservation_code": travel.reservation_code}

        dlogs.append(serialized_log)

    if search:
        dlogs = _search_log_comms_by_string(dlogs, search)

    return dlogs


def _search_log_comms_by_string(logs, search_str):
    result = []
    for log in logs:
        log_values = log.values()
        log_string = " ".join([str(log) for log in log_values])
        if log_string.find(search_str) > -1:
            result.append(log)
    return result


def get_buseiro_travels(buseiro, paginator, search):
    travels = (
        buseiro.travel_set.to_serialize()
        .select_related("revendedor_user", "pagamento", "grupo")
        .order_by("-grupo__datetime_ida")
    )
    if search:
        travels = travels.filter(reservation_code=search.upper())
    items = Paginator(travels, paginator.rows_per_page).page(paginator.page)
    return {
        "total_count": items.paginator.count,
        "travels": serializer_travel.serialize_buseiro_travels(items.object_list),
    }
