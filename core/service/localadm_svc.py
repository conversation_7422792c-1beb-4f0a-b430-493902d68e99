import logging
from _operator import itemgetter
from collections import defaultdict
from datetime import datetime, timedelta

from django.db import transaction
from django.db.models import Exists, OuterRef, Q
from django.utils.text import slugify

from adapters import url_shortener_adapter
from adapters.url_shortener_adapter.exceptions import URLShortenerException
from commons.dateutils import now, to_tz
from core.forms.staff_forms import (
    ListBusUnsupportedLocalForm,
    ListLocaisForm,
    ListLocaisSelectOptionsForm,
    ListLociasSimpleForm,
    ListRestrictionsForm,
    LocalEmbarqueForm,
    UpdateLocalEmbarqueForm,
)
from core.models_company import Onibus
from core.models_grupo import Grupo, RestrictionTypes
from core.models_rota import (
    HUMANIZE_WEEKDAY,
    Checkpoint,
    LocalEmbarque,
    LocalEmbarqueInternalDetails,
    OnibusSuportado,
    Weekdays,
)
from core.service.grupos_staff.grupo_crud_svc import get_datetime_ida_list
from core.service.locais_embarque_svc import filter_weekdays_by_usage_time
from core.service.timezone_svc import rota_origem_tz

logger = logging.getLogger("buserlogger")


@transaction.atomic
def update_local(update_form: UpdateLocalEmbarqueForm):
    local = update_form.local

    local.cidade = update_form.cidade or local.cidade
    local.description = update_form.description if update_form.description is not None else local.description
    local.description_ao = (
        update_form.description_ao if update_form.description_ao is not None else local.description_ao
    )
    local.driver_description = update_form.driver_description

    if update_form.mapurl is not None and update_form.mapurl != local.mapurl:
        try:
            local.mapurl = url_shortener_adapter.shorten(update_form.mapurl)
        except URLShortenerException:
            logger.exception("Erro ao encurtar url: %s", update_form.mapurl)
            local.mapurl = update_form.mapurl

    if update_form.street_view_mapurl is None:
        local.street_view_mapurl = None
    elif update_form.street_view_mapurl != local.street_view_mapurl:
        try:
            local.street_view_mapurl = url_shortener_adapter.shorten(update_form.street_view_mapurl)
        except URLShortenerException:
            logger.exception("Erro ao encurtar url: %s", update_form.street_view_mapurl)
            local.street_view_mapurl = update_form.street_view_mapurl

    if update_form.ativo is not None:
        local.ativo = update_form.ativo

    local.latitude = update_form.latitude or local.latitude
    local.longitude = update_form.longitude or local.longitude
    local.nickname = update_form.nickname or local.nickname
    local.endereco_numero = update_form.endereco_numero or local.endereco_numero
    local.endereco_bairro = update_form.endereco_bairro or local.endereco_bairro
    local.endereco_cep = update_form.endereco_cep or local.endereco_cep
    local.endereco_logradouro = update_form.endereco_logradouro or local.endereco_logradouro
    local.driver_endereco_numero = update_form.driver_endereco_numero
    local.driver_endereco_logradouro = update_form.driver_endereco_logradouro
    local.modelos_venda = update_form.modelos_venda or local.modelos_venda
    local.uso_do_local = update_form.uso_do_local or local.uso_do_local
    local.categoria_do_ponto = update_form.categoria_do_ponto or local.categoria_do_ponto
    local.tipo_de_acesso = update_form.tipo_de_acesso or local.tipo_de_acesso
    local.dias_proibidos = update_form.dias_proibidos
    local.desembarque_rapido = update_form.desembarque_rapido

    if update_form.impede_bypass is not None:
        local.impede_bypass = update_form.impede_bypass

    if update_form.zona_maxima_de_restricao is not None:
        local.zona_maxima_de_restricao = update_form.zona_maxima_de_restricao

    if update_form.max_embarque_simultaneo is not None:
        local.max_embarque_simultaneo = update_form.max_embarque_simultaneo

    if update_form.max_minutos_permanencia is not None:
        local.max_minutos_permanencia = update_form.max_minutos_permanencia

    if update_form.aceita_embarque_ao is not None:
        local.aceita_embarque_ao = update_form.aceita_embarque_ao

    local.classification = update_form.classification or local.classification
    local.features = update_form.features

    if update_form.working_hour_list:
        local.weekdays.all().delete()
        new_weekday_list = []
        for weekday in update_form.working_hour_list:
            new_weekday = Weekdays(
                local=local,
                dia=weekday.dia,
                start_time=weekday.start_time,
                end_time=weekday.end_time,
                max_minutos_permanencia=weekday.max_minutos_permanencia,
                max_embarque_simultaneo=weekday.max_embarque_simultaneo,
            )
            new_weekday_list.append(new_weekday)

        Weekdays.objects.bulk_create(new_weekday_list)

    if update_form.onibus_suportados:
        local.onibus_suportados.all().delete()
        new_onibus_suportado_list = []
        for tipo_onibus in update_form.onibus_suportados:
            new_onibus_suportado = OnibusSuportado(local=local, tipo=tipo_onibus)
            new_onibus_suportado_list.append(new_onibus_suportado)

        OnibusSuportado.objects.bulk_create(new_onibus_suportado_list)

    local.endereco = update_form.endereco or local.endereco

    if update_form.always_working is True:
        local.start_time = None
        local.end_time = None
        local.weekdays.all().delete()

    LocalEmbarqueInternalDetails.objects.update_or_create(
        local=local,
        defaults={
            "status_flag": update_form.status_flag,
            "reports": update_form.reports,
            "observations": update_form.observations,
            "contato_nome": update_form.contato_nome,
            "contato_email": update_form.contato_email,
            "contato_telefone": update_form.contato_telefone,
        },
    )

    local.save()

    return local


def create_local(form: LocalEmbarqueForm) -> LocalEmbarque:
    try:
        short_map_url = url_shortener_adapter.shorten(form.map_url)
    except URLShortenerException:
        logger.exception("Erro ao encurtar url: %s", form.map_url)
        short_map_url = form.map_url

    street_view_mapurl = form.street_view_mapurl or None
    if street_view_mapurl:
        try:
            street_view_mapurl = url_shortener_adapter.shorten(street_view_mapurl)
        except URLShortenerException:
            logger.exception("Erro ao encurtar url: %s", street_view_mapurl)

    nickname_slug = slugify(form.nickname)

    local = LocalEmbarque.objects.create(
        cidade=form.cidade,
        nickname=form.nickname,
        nickname_slug=nickname_slug,
        endereco=form.endereco,
        endereco_logradouro=form.endereco_logradouro,
        endereco_slug=slugify(form.endereco),
        endereco_numero=form.endereco_numero,
        endereco_bairro=form.endereco_bairro,
        bairro_slug=slugify(form.endereco_bairro),
        endereco_cep=form.endereco_cep,
        driver_endereco_numero=form.driver_endereco_numero,
        driver_endereco_logradouro=form.driver_endereco_logradouro,
        description=form.description,
        description_ao=form.description_ao,
        driver_description=form.driver_description,
        mapurl=short_map_url,
        original_mapurl=form.map_url,
        street_view_mapurl=street_view_mapurl,
        latitude=form.latitude,
        longitude=form.longitude,
        max_embarque_simultaneo=form.max_embarque_simultaneo,
        classification=form.classification,
        features=form.features,
        slug=slugify(f"{nickname_slug}-{form.cidade.slug}"),
        modelos_venda=form.modelos_venda,
        uso_do_local=form.uso_do_local,
        categoria_do_ponto=form.categoria_do_ponto,
        tipo_de_acesso=form.tipo_de_acesso,
        zona_maxima_de_restricao=bool(form.zona_maxima_de_restricao),
        aceita_embarque_ao=form.aceita_embarque_ao,
        desembarque_rapido=form.desembarque_rapido,
    )

    if form.onibus_suportados:
        OnibusSuportado.objects.bulk_create(OnibusSuportado(local=local, tipo=tipo) for tipo in form.onibus_suportados)

    if form.working_hour_list:
        weekday_list = [
            Weekdays(
                local=local,
                dia=wh.dia,
                start_time=wh.start_time,
                end_time=wh.end_time,
                max_minutos_permanencia=wh.max_minutos_permanencia,
                max_embarque_simultaneo=wh.max_embarque_simultaneo,
            )
            for wh in form.working_hour_list
        ]
        Weekdays.objects.bulk_create(weekday_list)

    if not form.cidade.ativo:
        form.cidade.ativo = True
        form.cidade.save()

    LocalEmbarqueInternalDetails.objects.update_or_create(
        local=local,
        defaults={
            "status_flag": form.status_flag,
            "reports": form.reports,
            "observations": form.observations,
            "contato_nome": form.contato_nome,
            "contato_email": form.contato_email,
            "contato_telefone": form.contato_telefone,
        },
    )

    return local


def list_locais(params: ListLocaisForm):
    sortby = params.paginator.sort_by if params.paginator else "grupos_futuros__count"
    if params.paginator and params.paginator.descending:
        sortby = f"-{sortby}"

    locais_list_query = LocalEmbarque.objects.select_related("cidade").order_by(sortby)

    if params.search:
        locais_list_query = _search_locais_queryset(locais_list_query, params.search)

    if params.status_filter is not None:
        locais_list_query = locais_list_query.filter(ativo=params.status_filter)

    if params.filter_city:
        locais_list_query = locais_list_query.filter(cidade__in=params.filter_city)

    if params.filter_classification:
        locais_list_query = locais_list_query.filter(classification__in=params.filter_classification)

    if params.filter_state:
        if isinstance(params.filter_state, str):
            params.filter_state = [params.filter_state]
        locais_list_query = locais_list_query.filter(cidade__uf__in=params.filter_state)

    if params.local_id:
        locais_list_query = locais_list_query.filter(id=params.local_id)

    if params.modelos_venda:
        locais_list_query = locais_list_query.filter(modelos_venda__contains=params.modelos_venda)

    if params.uso_do_local:
        locais_list_query = locais_list_query.filter(uso_do_local__contains=params.uso_do_local)

    if params.categoria_do_ponto:
        locais_list_query = locais_list_query.filter(categoria_do_ponto__contains=params.categoria_do_ponto)

    if params.tipo_de_acesso:
        locais_list_query = locais_list_query.filter(tipo_de_acesso__contains=params.tipo_de_acesso)

    if params.zona_maxima_de_restricao is not None:
        locais_list_query = locais_list_query.filter(zona_maxima_de_restricao=params.zona_maxima_de_restricao)

    if params.gestor:
        if params.gestor["id"] == -1:
            locais_list_query = locais_list_query.filter(gestor_id__isnull=True)
        else:
            locais_list_query = locais_list_query.filter(gestor_id=params.gestor["id"])

    if params.status_flag:
        locais_list_query = locais_list_query.filter(detalhes_internos__status_flag=params.status_flag)

    if params.onibus_suportados:
        locais_list_query = locais_list_query.filter(onibus_suportados__tipo__in=params.onibus_suportados)

    return locais_list_query.distinct()


def list_locais_simple(params: ListLociasSimpleForm):
    locais_list_query = (
        LocalEmbarque.objects.select_related("cidade").values("id", "nickname").exclude(id=params.exclude_self_id)
    )

    return locais_list_query


def list_locais_by_city_id(city_id: int):
    return LocalEmbarque.objects.filter(cidade_id=city_id, ativo=True)


def _search_locais_queryset(local_qs, search_str):
    search_query = Q()
    fields = [
        "nickname",
        "endereco_numero",
        "endereco_bairro",
        "endereco_cep",
        "endereco_logradouro",
        "cidade__name",
    ]
    for field in fields:
        search_query |= Q(**{f"{field}__unaccent__icontains": search_str})

    return local_qs.filter(search_query)


def list_locais_select_options(params: ListLocaisSelectOptionsForm):
    locais_list_query = LocalEmbarque.objects.all()

    if params.search:
        locais_list_query = _search_locais_queryset(locais_list_query, params.search)

    return locais_list_query


def list_unsupported_local_for_bus(form: ListBusUnsupportedLocalForm):
    return [
        {"type": RestrictionTypes.ONIBUS_SUPORTADOS.name, "label": label, "local": local_id}
        for label, local_id in _unsupported_bus_in_routes(form.rota_id_list, form.bus_id)
    ]


def _unsupported_bus_in_routes(rota_id_list, bus_id):
    bus = Onibus.objects.filter(pk=bus_id, tipo__isnull=False).first()
    if not bus:
        return []
    unsupported_local_qs = LocalEmbarque.objects.filter(
        ~Exists(OnibusSuportado.objects.filter(local=OuterRef("pk"), tipo=bus.tipo.lower())),
        onibus_suportados__isnull=False,
        checkpoint__rota_id__in=rota_id_list,
    ).distinct()

    for unsupported_local in unsupported_local_qs:
        yield (
            f'O Local de embarque "{unsupported_local.nickname}" não suporta onibus do tipo "{bus.tipo}"',
            unsupported_local.id,
        )


MAP_LOCAL_MODELO_VENDA_GRUPO_MODELO_VENDA = {
    "Híbrido": "hibrido",
    "Fretamento": "buser",
    "Corporativo": "buser",
    "Marketplace": "marketplace",
}


def local_restrictions(form: ListRestrictionsForm, itinerario: list[Checkpoint] | None = None):
    restricoes = []

    if form.bus_id:
        for unsupported_bus_label, unsupported_local_id in _unsupported_bus_in_routes([form.rota_id], form.bus_id):
            restricoes.append(
                {"type": "onibus_suportados", "label": unsupported_bus_label, "local": unsupported_local_id}
            )

    datetime_ida_list = get_datetime_ida_list(form.date_ida_list, form.time_ida)
    if not datetime_ida_list:
        return restricoes

    if not itinerario:
        itinerario: list[Checkpoint] = list(
            Checkpoint.objects.filter(rota_id=form.rota_id).prefetch_related("local__weekdays").all()
        )
    tz = rota_origem_tz(form.rota_id)

    for checkpoint in itinerario:
        local_restricoes = []
        local: LocalEmbarque = checkpoint.local

        if (
            form.modelo_venda is not None
            and local.modelos_venda is not None
            and form.modelo_venda not in [MAP_LOCAL_MODELO_VENDA_GRUPO_MODELO_VENDA[m] for m in local.modelos_venda]
        ):
            local_restricoes.append(
                {
                    "type": "modelo_venda_nao_suportado_no_local",
                    "label": f'O Local de embarque "{local.nickname}" não pode ser para grupos do modelo de venda {form.modelo_venda}',
                    "local": local.id,
                }
            )

        new_use_time_list = []
        for dtida in datetime_ida_list:
            dtidatz = to_tz(dtida, tz)
            inicio, fim = calculate_use_time(dtidatz, itinerario, local)
            new_use_time_list.append((inicio, fim, dtidatz))

        if dias_proibidos := _local_dias_proibidos(local, datetime_ida_list):
            local_restricoes.extend(dias_proibidos)

        if overbookings := _local_overbooking(local, new_use_time_list):
            local_restricoes.extend(overbookings)

        if work_hours := _local_out_of_working_hours(local, new_use_time_list):
            local_restricoes.extend(work_hours)

        if local.impede_bypass and local_restricoes:
            # essa restrição só deve ser aplicada se houver outras restrições, pois ela é quem impede o bypass pra um local especifico
            local_restricoes.append(
                {
                    "type": "local_embarque_impede_bypass",
                    "label": f'O Local de embarque "{local.nickname}" não pode ser usado com bypass nas restrições que foram quebradas',
                    "local": local.id,
                }
            )

        restricoes.extend(local_restricoes)

    return restricoes


def _local_dias_proibidos(local: LocalEmbarque, datetime_ida_list: list[datetime]):
    if local.dias_proibidos is None:
        return

    restricoes = []
    for dt in datetime_ida_list:
        if dt.date() in local.dias_proibidos:
            restricoes.append(
                {
                    "type": "dias_proibidos",
                    "label": f'O Local de embarque "{local.nickname}" não pode ser usado nos dias {local.dias_proibidos}',
                    "local": local.id,
                }
            )
    return restricoes


def _local_overbooking(local, requested_use_time_list):
    weekdays_restrictions = list(local.weekdays.all())  # for cache reasons
    if not local.max_embarque_simultaneo and not weekdays_restrictions:
        return []

    requested_use_time_sorted = sorted(requested_use_time_list, key=itemgetter(0))
    lastest_use_time = requested_use_time_sorted[-1][1]
    max_datetime_ida = lastest_use_time + timedelta(minutes=31)  # groups departing after this datetime will not overlap
    existing_use_time_list = generate_use_time_range(local, max_datetime_ida=max_datetime_ida)

    if not existing_use_time_list:  # if there's no other group using this boarding point we can skip
        return []

    existing_use_time_sorted = sorted(existing_use_time_list, key=itemgetter(0))

    restricoes = []
    count_overlapping_use_time = 1  # count overlapping use times within existing groups for each requested use time
    existing_iter = iter(existing_use_time_sorted)
    requested_iter = iter(requested_use_time_sorted)
    existing_use_time_range = next(existing_iter)
    requested_use_time_range = next(requested_iter)
    while existing_use_time_range and requested_use_time_range:
        start_existing_use_time, end_existing_use_time = existing_use_time_range
        start_requested_use_time, end_requested_use_time, datetime_ida = requested_use_time_range

        if end_existing_use_time < start_requested_use_time:
            # if current existing use time ends before the requested use time begins, check the next existing use time
            existing_use_time_range = next(existing_iter, False)
            continue

        if end_requested_use_time < start_existing_use_time:
            # if current requested use time ends before the existing use time begins, check the next requested use time
            count_overlapping_use_time = 1
            requested_use_time_range = next(requested_iter, False)
            continue

        # if both are false, then these use times overlaps - add to count
        count_overlapping_use_time += 1
        existing_use_time_range = next(existing_iter, False)

        max_simultaneous_boarding = _get_max_simultaneous_boarding(local, weekdays_restrictions, datetime_ida)

        if max_simultaneous_boarding and count_overlapping_use_time > max_simultaneous_boarding:
            count_overlapping_use_time = 1
            requested_use_time_range = next(requested_iter, False)
            restricoes.append(
                {
                    "type": "max_embarque_simultaneo",
                    "label": f'O Local de embarque "{local.nickname}" ficará sobrecarregado partindo às '
                    f"{datetime_ida.strftime('%H:%M')} em {datetime_ida.strftime('%d/%m/%Y')}",
                    "local": local.id,
                }
            )

    return restricoes


def _get_max_simultaneous_boarding(local: LocalEmbarque, weekdays: list[Weekdays], datetime_ida: datetime):
    max_simultaneos_boarding = local.max_embarque_simultaneo
    if not weekdays:
        return max_simultaneos_boarding or 0

    if restriction := filter_weekdays_by_usage_time(weekdays, datetime_ida):
        max_simultaneos_boarding = restriction.max_embarque_simultaneo

    return max_simultaneos_boarding or 0


def _local_out_of_working_hours(local, new_use_time_list):
    local_working_hour = local.weekdays.all()
    if not local_working_hour:
        return []

    working_hour_by_day = {}
    for working_hour in local_working_hour:
        if not working_hour.start_time or not working_hour.end_time:
            continue
        if working_hour.dia in working_hour_by_day:
            working_hour_by_day[working_hour.dia].append(working_hour)
        else:
            working_hour_by_day[working_hour.dia] = [working_hour]

    restricoes = []
    for requested_use_time_range in new_use_time_list:
        start_use_datetime, end_use_datetime, _ = requested_use_time_range
        weekday_start_use = start_use_datetime.isoweekday()
        weekday_end_use = end_use_datetime.isoweekday()

        if weekday_start_use not in working_hour_by_day or weekday_end_use not in working_hour_by_day:
            restricoes.append(_create_weekdays_violation(local, start_use_datetime, end_use_datetime))
            continue

        working_hour_list = working_hour_by_day[weekday_start_use]
        if weekday_start_use != weekday_end_use:
            working_hour_list.extend(working_hour_by_day[weekday_end_use])

        start_use_time, end_use_time = start_use_datetime.time(), end_use_datetime.time()
        for wh in working_hour_list:
            if _use_time_within_working_hour(wh, start_use_time, end_use_time):
                break
        else:
            wh_str_gen = (
                f"{HUMANIZE_WEEKDAY[wh.dia]} das {wh.start_time.strftime('%H:%M')} às {wh.end_time.strftime('%H:%M')}"
                for wh in working_hour_list
            )
            working_hour_str = " e ".join(wh_str_gen)
            str_weekdays = HUMANIZE_WEEKDAY[weekday_start_use]
            if weekday_start_use != weekday_end_use:
                str_weekdays += "/" + HUMANIZE_WEEKDAY[weekday_end_use]
            restricoes.append(
                {
                    "type": "working_hours",
                    "label": f'O Local de embarque "{local.nickname}" não funciona {str_weekdays} das '
                    f"{start_use_time.strftime('%H:%M')} às {end_use_time.strftime('%H:%M')} "
                    f"(funciona {working_hour_str})",
                    "local": local.id,
                }
            )

    return restricoes


def _local_impede_bypass(local: LocalEmbarque):
    if not local.impede_bypass:
        return

    yield {
        "type": "local_embarque_impede_bypass",
        "label": f'O Local de embarque "{local.nickname}" não pode ser usado com bypass nas restrições que foram quebradas',
        "local": local.id,
    }


def _create_weekdays_violation(local, start_use_datetime, end_use_datetime):
    strf_pattern = "%d/%m/%Y %H:%M"
    return {
        "type": "weekdays",
        "label": f'O Local de embarque "{local.nickname}" não funciona '
        f"{HUMANIZE_WEEKDAY[start_use_datetime.isoweekday()]} nesta configuração o ônibus chegará no local em "
        f"{start_use_datetime.strftime(strf_pattern)} e partirá as {end_use_datetime.strftime(strf_pattern)}",
        "local": local.id,
    }


def generate_use_time_range(local, max_datetime_ida):
    groups = (
        Grupo.objects.filter(
            rota__itinerario__local_id=local.id,
            datetime_ida__range=(now(), max_datetime_ida),
            status__in=["pending", "travel_confirmed"],
        )
        .exclude(status="pending", confirming_probability="very_low")
        .values("datetime_ida", "rota_id")
    )

    distinct_rota_id = {g["rota_id"] for g in groups}
    checkpoints = Checkpoint.objects.filter(rota_id__in=distinct_rota_id).order_by("rota_id")
    itinerario = _checkpoints_group_by_rota_id(checkpoints)

    return [calculate_use_time(group["datetime_ida"], itinerario[group["rota_id"]], local) for group in groups]


def _checkpoints_group_by_rota_id(checkpoints):
    buckets = defaultdict(list)
    for c in checkpoints:
        buckets[c.rota_id].append(c)

    return buckets


def calculate_use_time(datetime_ida, itinerario, local):
    return calculate_use_times(datetime_ida, itinerario)[local.id]


def calculate_use_times(datetime_ida, itinerario: list[Checkpoint]):
    use_times = {}
    duration = timedelta()

    max_idx = max(it.idx for it in itinerario)

    for checkpoint in sorted(itinerario, key=lambda it: it.idx):
        duration += checkpoint.duracao or timedelta(0)

        if checkpoint.idx == 0:
            arrival = duration - timedelta(minutes=30)
            departure = duration
        elif checkpoint.idx == max_idx:
            arrival = duration
            departure = duration + timedelta(minutes=20)
        else:
            arrival = duration
            departure = duration + checkpoint.tempo_embarque

        duration += checkpoint.tempo_embarque

        use_times[checkpoint.local_id] = (
            datetime_ida + arrival,
            datetime_ida + departure,
        )

    return use_times


def _use_time_within_working_hour(local_wh, start_use_time, end_use_time):
    return (
        local_wh.start_time <= start_use_time <= local_wh.end_time
        and local_wh.start_time <= end_use_time <= local_wh.end_time
    )
