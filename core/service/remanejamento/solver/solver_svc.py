import logging
from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal
from importlib.metadata import version
from typing import Iterable

from beeline import traced
from django.contrib.auth.models import User
from django.db import transaction
from django.db.models import F, OuterRef, Prefetch, Q, QuerySet, Subquery, Sum
from django.db.models.functions import Coalesce

from accounting.service import accounting_svc
from commons import redis
from commons.dateutils import now, to_tz, to_tz_required
from core.forms.remanejamento_forms import (
    FormRemanejamentoConstraints,
    RemanejamentoForm,
    SimulaRemanejamentoAutomatico,
)
from core.forms.staff_forms import AcaoContabil
from core.models_grupo import ClosedReasons, Grupo, GrupoClasse, TrechoClasse
from core.models_travel import Travel
from core.service.grupos_staff.grupo_status_svc import cancel_groups, close_groups, confirm_groups
from core.service.ml_models_svc import (
    TrechoForecastItem,
    get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento,
    round_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento,
)
from core.service.multitrecho_svc import CMRemanejamento, DummyManager, DummyTravel, bulk_prepare_capacity_manager
from core.service.remanejamento.commons.cost_calculator import (
    DOWNGRADES_PROIBITIVOS,
    CustosRemanejamento,
    calcula_custos,
)
from core.service.remanejamento.commons.remanejamento_base import (
    RemanejamentoBase,
    RemanejamentoBuseiro,
    dist_destino,
    dist_origem,
    get_score_params,
)
from core.service.remanejamento.commons.utils import get_groups_data
from core.service.remanejamento.remanejamento_svc import remanejamento_automatico
from core.service.reserva import reserva_extrato_svc, reserva_svc
from forecaster.service import forecast_prediction_svc
from optools.errors import SolverFail, SolverRunningError
from optools.forms import (
    CancelarGruposForm,
    RemanejamentoSolverSolutionForm,
    SolverBooking,
    SolverChartered,
    SolverInputForm,
    SolverParams,
    SolverTrip,
)
from pagamento_parceiro.service import dia_parado_svc

buserlogger = logging.getLogger("buserlogger")

PAGTO_DIA_PARADO = Decimal("350.00")
CRITERIO_PAX_CANCELADOS = 25

SOLVER_RUNNING_KEY = "solver:running"
SOLVER_RUNNING_EXPIRATION = timedelta(minutes=15)

FATOR_CUSTO_REMANEJAR_DUMMY = Decimal("1.2")


class RemanejamentoSolverManager(RemanejamentoBase):
    def __init__(
        self,
        grupo_ids: list[int],
        grupos_ida_volta: dict[int, int],
        grupos_dict: dict[int, Grupo],
        range_minutos: int,
        range_km: int,
        marketplace: bool,
        hibrido: bool,
        downgrade: bool,
        versao_solver: str,
        now: datetime,
        trechos_classe_dict: dict,
        travels_dict: dict,
        gmv_travels_map: dict,
        receita_travels_map: dict,
        receita_real_grupos: dict,
        receita_prevista_grupos: dict,
        capacity_manager_map: dict,
        dummy_capacity_manager_map: dict,
        grupos_forecasts: dict,
        trechos_forecasts: dict,
        gmv_real_grupos: dict[int, Decimal],
        custo_remanejar: dict[int, list[tuple[int, CustosRemanejamento]]],
        remanejamento_travel_map: dict,
        gmv_previsto_grupos: dict[int, Decimal],
        receita_agregada_travel_map: dict[int, Decimal],
        receita_agregada_grupo_map: dict[int, Decimal],
        user_solver: User,
    ) -> None:
        self._grupo_ids = grupo_ids
        self._grupos_ida_volta = grupos_ida_volta
        self._range_minutos = range_minutos
        self._range_km = range_km
        self.versao_solver = versao_solver
        self._now = now
        self._grupos_dict = grupos_dict
        self._trechos_classe_dict = trechos_classe_dict
        self._travels_dict = travels_dict
        self._capacity_manager_map = capacity_manager_map
        self._dummy_capacity_manager_map = dummy_capacity_manager_map
        self._grupos_forecasts = grupos_forecasts
        self._trechos_forecasts = trechos_forecasts
        self._custo_remanejar = custo_remanejar
        self._remanejamento_travel_map = remanejamento_travel_map
        self._gmv_travels_map = gmv_travels_map
        self._receita_travels_map = receita_travels_map
        self._receita_real_grupos = receita_real_grupos
        self._receita_prevista_grupos = receita_prevista_grupos
        self._gmv_real_grupos = gmv_real_grupos
        self._gmv_previsto_grupos = gmv_previsto_grupos
        self._receita_agregada_travel_map = receita_agregada_travel_map
        self._receita_agregada_grupo_map = receita_agregada_grupo_map
        self.marketplace = marketplace
        self.hibrido = hibrido
        self.downgrade = downgrade
        self.range_minutos = range_minutos
        self.range_km = range_km
        self.user_solver = user_solver

    @classmethod
    @traced("solver_svc.from_grupos_check_de_cancelamento")
    def from_grupos_check_de_cancelamento(cls, form: SimulaRemanejamentoAutomatico, solver_params: SolverParams):
        """Constroi um RemanejamentoSolverManager a partir de um formulário de remanejamento.
        Emula considerações utilizadas durante o check de cancelamnento de grupos realizado pelo
        time de rotas manualmente

        Args:
            form (SimulaRemanejamentoAutomatico): _description_
            max_solver_time (int): _description_

        Returns:
            RemanejamentoSolverManager: _description_
        """
        user_solver, _ = User.objects.get_or_create(email="<EMAIL>")

        grupos = (
            Grupo.objects.select_related(
                "rotina_onibus__rota_principal",
                "rotina_onibus__rotina_par",
                "rota__origem__cidade",
                "rota__destino__cidade",
                "company",
            )
            .prefetch_related(
                "grupoclasse_set",
                Prefetch(
                    "trechoclasse_set",
                    queryset=TrechoClasse.objects.select_related(
                        "grupo_classe",
                        "price_manager",
                        "trecho_vendido__origem__cidade",
                        "trecho_vendido__destino__cidade",
                    ).prefetch_related("travel_set", "price_manager__buckets"),
                ),
                Prefetch(
                    "travel_set",
                    queryset=Travel.objects.select_related(
                        "grupo_classe",
                        "trecho_classe__grupo_classe",
                        "trecho_classe__trecho_vendido__origem",
                        "trecho_classe__trecho_vendido__destino",
                    ).exclude(status=Travel.Status.CANCELED),
                ),
            )
            .annotate(
                capacidade_grupo=Subquery(
                    GrupoClasse.objects.filter(grupo_id=OuterRef("pk"))
                    .values("grupo_id")
                    .annotate(total_capacidade=Sum("capacidade"))
                    .values("total_capacidade")
                ),
                frete_grupo=Coalesce((Coalesce(F("valor_frete"), F("rotina_onibus__frete_atual") / 2)), Decimal("0")),
            )
            .filter(id__in=form.grupo_ids)
            .order_by("datetime_ida")
        )

        grupos_dict, trechos_classe_dict, travels_dict = {}, {}, {}
        for grupo in grupos:
            setattr(grupo, "custo_assento", (grupo.frete_grupo / grupo.capacidade_grupo))
            grupos_dict[grupo.id] = grupo
            for tc in grupo.trechoclasse_set.all():
                trechos_classe_dict[tc.id] = tc

            for travel in grupo.travel_set.all():
                travels_dict[travel.id] = travel

        grupos_forecasts, trechos_forecasts = _get_forecasts(grupos)

        capacity_manager_map = bulk_prepare_capacity_manager(
            grupos, CMRemanejamento, count_seats_map_dummies_all=trechos_forecasts
        )

        dummy_capacity_manager_map = bulk_prepare_capacity_manager(
            grupos, CMRemanejamento, count_seats_map_dummies_all=trechos_forecasts
        )

        dummy_manager = DummyManager(grupos, dummy_capacity_manager_map)

        travels_dict = {travel.id: travel for travel in dummy_manager.travels + dummy_manager.dummy_travels}

        custo_remanejar = _calcula_custo_remanejar(trechos_classe_dict, form.range_km, form.range_minutos)

        remanejamento_travel_map = _calcula_remanejamento_travel_map(travels_dict, custo_remanejar)

        gmv_travels_map = accounting_svc.calcula_accops_travel_map(dummy_manager.travels, addons=True, promocao=False)
        for dummy_travel in dummy_manager.dummy_travels:
            gmv_travels_map[dummy_travel.id] = dummy_travel.gmv

        gmv_real_grupos, gmv_previsto_grupos = dummy_manager.get_accops_grupos(gmv_travels_map)
        receita_travels_map = accounting_svc.calcula_accops_travel_map(
            dummy_manager.travels, addons=True, promocao=True
        )
        for dummy_travel in dummy_manager.dummy_travels:
            receita_travels_map[dummy_travel.id] = dummy_travel.gmv

        receita_real_grupos, receita_prevista_grupos = dummy_manager.get_accops_grupos(receita_travels_map)
        receita_agregada_travel_map = _calcula_receita_agregada_travel_map(list(travels_dict.values()))
        receita_agregada_grupo_map = _calcula_receita_agregada_grupo_map(travels_dict, receita_agregada_travel_map)

        return cls(
            grupo_ids=form.grupo_ids,
            grupos_ida_volta=form.grupos_ida_volta,
            marketplace=form.marketplace,
            hibrido=form.hibrido,
            downgrade=form.downgrade,
            range_minutos=form.range_minutos,
            range_km=form.range_km,
            versao_solver=version("ortools"),
            now=now(),
            grupos_dict=grupos_dict,
            trechos_classe_dict=trechos_classe_dict,
            travels_dict=travels_dict,
            gmv_travels_map=gmv_travels_map,
            receita_travels_map=receita_travels_map,
            capacity_manager_map=capacity_manager_map,
            dummy_capacity_manager_map=dummy_capacity_manager_map,
            grupos_forecasts=grupos_forecasts,
            trechos_forecasts=trechos_forecasts,
            custo_remanejar=custo_remanejar,
            remanejamento_travel_map=remanejamento_travel_map,
            receita_real_grupos=receita_real_grupos,
            receita_prevista_grupos=receita_prevista_grupos,
            gmv_real_grupos=gmv_real_grupos,
            gmv_previsto_grupos=gmv_previsto_grupos,
            receita_agregada_travel_map=receita_agregada_travel_map,
            receita_agregada_grupo_map=receita_agregada_grupo_map,
            user_solver=user_solver,
        )

    @traced("solver_svc.build_solver_input_form")
    def build_solver_input_form(self, start_datetime: datetime, end_datetime: datetime) -> SolverInputForm:
        """Utiliza dados previamente salvos nas variáveis da classe
        para construção do dicionário de dados necessário para construir formulário
        de remanejamento do solver.

        Returns:
            SolverInputForm
        """

        grupo_ida_rotina_par_map = get_grupo_rotina_par(self._grupos_dict.values(), self._grupos_ida_volta)
        grupos_fora_da_janela_ids = _get_grupos_fora_da_janela_ids(
            self._grupos_dict, self._grupos_ida_volta, start_datetime, end_datetime
        )
        grupos = [
            SolverChartered(
                grupo_id=grupo.id,
                grupo_volta_id=self._grupos_ida_volta.get(grupo.id),
                eixo=grupo.rotina_onibus.rota_principal.eixo,
                frete=Decimal(getattr(grupo, "frete_grupo")),
                custo_assento=Decimal(getattr(grupo, "custo_assento")),
                solver_threshold=grupo.rotina_onibus.solver_threshold,
                status=grupo.status,
                closed_by_solver=all(gc.closed_reason == ClosedReasons.SOLVER for gc in grupo.grupoclasse_set.all()),
                receita_real=self._receita_real_grupos[grupo.id],
                receita_prevista=self._receita_prevista_grupos[grupo.id],
                receita_agregada=self._receita_agregada_grupo_map[grupo.id],
                gmv_real=self._gmv_real_grupos[grupo.id],
                gmv_previsto=self._gmv_previsto_grupos[grupo.id],
                fora_da_janela=grupo.id in grupos_fora_da_janela_ids,
                capacity=grupo.count_capacidade_onibus(),
                company_id=grupo.company_id,
                rotina_onibus_id=grupo.rotina_onibus_id,
                grupo_ida_rotina_par_id=grupo_ida_rotina_par_map.get(grupo.id),
                dummies=self._grupos_forecasts.get(grupo.id),
                pessoas=self._capacity_manager_map[grupo.id].total_de_pessoas,
            )
            for grupo in self._grupos_dict.values()
        ]

        trechos = []
        for tc in self._trechos_classe_dict.values():
            dummy_capacity_manager = self._dummy_capacity_manager_map[tc.grupo_id]

            forecast_trechoclasse = self._trechos_forecasts.get(tc.grupo_id, {})
            forecast = forecast_trechoclasse.get((tc.trecho_vendido_id, tc.grupo_classe.tipo_assento), None)
            trechos.append(
                SolverTrip(
                    trecho_classe_id=tc.id,
                    grupo_id=tc.grupo_id,
                    capacidade=tc.grupo_classe.capacidade,
                    tipo_assento=tc.grupo_classe.tipo_assento,
                    preco_atual=Decimal(tc.price_manager.value),
                    max_split_value=Decimal(tc.max_split_value),
                    forecast=forecast,
                    sobrepostos=dummy_capacity_manager.get_trechos_sobrepostos_ids(tc),
                    vagas=dummy_capacity_manager.vagas(tc),
                )
            )
        travels = [
            SolverBooking(
                travel_id=travel.id,
                count_seats=travel.count_seats,
                trecho_classe_id_inicial=travel.trecho_classe_id,
                gmv=self._gmv_travels_map[travel.id],
                receita=self._receita_travels_map[travel.id],
                receita_agregada=self._receita_agregada_travel_map[travel.id],
                remanejamento=self._remanejamento_travel_map[travel.id],
                dummy=isinstance(travel, DummyTravel),
            )
            for travel in self._travels_dict.values()
        ]

        return SolverInputForm(grupos=grupos, travels=travels, trechos=trechos, custo_remanejar=self._custo_remanejar)

    @traced("solver_svc.applies_solution")
    @transaction.atomic
    def applies_solution(self, solution: RemanejamentoSolverSolutionForm | None, dry_run: bool) -> list[str] | None:
        """
        Applies the solution generated by the solver to the current state of the system.

        This method updates the state of the system based on the solution generated by the solver.
        It calculates the statistics of the system before and after the solution is applied, and
        validates the solution against some constraints. If the solution is valid, it applies the
        recommended changes to the system, including remanejamentos and cancelamentos of groups.

        Returns:
            A list of failed validations, if any. Otherwise, an empty list.
        """
        if dry_run:
            return

        if solution is None:
            raise SolverFail(status_code="INVALID_SOLUTION", message="Não há solução para ser aplicada.")

        remanejamentos = self._get_valid_remanejamentos(solution)
        self._cancel_conexao_travels(solution)
        self._calcular_custo_remanejamento(remanejamentos)
        remanejamento_forms = self._prepare_remanejamento_forms(remanejamentos)
        self._apply_remanejamentos(remanejamento_forms)

        groups_to_confirm, groups_to_close, groups_to_cancel = self._process_groups_status(solution)

        self._confirm_groups(groups_to_confirm)
        self._close_groups(groups_to_close)
        self._cancel_groups(groups_to_cancel)

    def _get_valid_remanejamentos(self, solution: RemanejamentoSolverSolutionForm) -> list:
        return [
            rem
            for rem in solution.remanejar_travels
            if (
                rem["remanejar"]
                and not rem["is_dummy"]  # dummy travels são adicionadas pelo forecast
                and not self._travels_dict[rem["travel_id"]].travel_conexao_id  # se tem conexão deve ser cancelada
                and not rem["grupo_fora_da_janela"]
            )
        ]

    def _cancel_conexao_travels(self, solution: RemanejamentoSolverSolutionForm):
        conexao_travels = [
            rem["travel_id"]
            for rem in solution.remanejar_travels
            if (rem["remanejar"] and not rem["is_dummy"] and self._travels_dict[rem["travel_id"]].travel_conexao_id)
        ]

        if conexao_travels:
            reserva_svc.grupo_cancel_travels_conexao(
                conexao_travels,
                reason="REMANEJAMENTO_INTELIGENTE",
            )

    def _prepare_remanejamento_forms(self, remanejamentos: list) -> list:
        remanejamento_solution_map = self._remanejamentos_buseiro(remanejamentos)

        return [
            RemanejamentoForm(
                travel_id=rem.travel.pk,
                motivo="OFERTA",
                tipo_remanejamento="Remanejamento inteligente solver",
                trecho_classe_destino=rem.trecho_classe_destino.pk,
                datetime_orig=rem.datetime_orig,
                datetime_rem=rem.datetime_rem,
                dist_origem=rem.dist_origem,
                dist_destino=rem.dist_destino,
            )
            for rems in remanejamento_solution_map.values()
            for rem in rems
        ]

    def _apply_remanejamentos(self, remanejamento_forms: list):
        constraints_form = FormRemanejamentoConstraints(
            marketplace=self.marketplace,
            hibrido=self.hibrido,
            downgrade=self.downgrade,
            grupo_fechado=False,
            range_minutos=self.range_minutos,
            range_km=self.range_km,
            remanejar_separado=True,
            solver=True,
        )
        remanejamento_automatico(remanejamento_forms, constraints_form)

    def _confirm_groups(self, groups_to_confirm: set[Grupo]):
        if not groups_to_confirm:
            return

        groups_to_confirm_data = get_groups_data(list(groups_to_confirm))
        confirm_groups(
            groups_to_confirm_data,
            automatic=False,
            forecast=True,
            solver=True,
            user=self.user_solver,
            fluxo="confirmacao_inteligente",
        )

    def _close_groups(self, groups_to_close: set[Grupo]):
        if not groups_to_close:
            return

        close_groups(groups_to_close, reason=ClosedReasons.SOLVER)

    def _cancel_groups(self, groups_to_cancel: set[Grupo]):
        if not groups_to_cancel:
            return

        dias_parados = [
            {
                "deve_pagar": False,
                "registrar": True,
                "grupo": {"id": grupo.id},
                "motivo": "Cancelamento confirmação inteligente. NO_COMPANY e NO_PAX",
            }
            for grupo in groups_to_cancel
        ]

        dia_parado_svc.altera_dias_parados(dias_parados, self.user_solver, "solver_svc.applies_solution.cancel_groups")

        cancel_groups(
            groups_to_cancel,
            notificacao=["email", "sms", "inbox", "push", "zap"],
            acao_contabil=AcaoContabil.CREDITAR_E_ESTORNAR,
            canceled_reason="NO_COMPANY",
            reason_description="SOLVER CANCELOU GRUPO SEM EMPRESA",
        )

    def _process_groups_status(
        self, solution: RemanejamentoSolverSolutionForm
    ) -> tuple[set[Grupo], set[Grupo], set[Grupo]]:
        """Process and sort groups into confirm, close and cancel sets based on solution status"""
        groups_to_confirm, groups_to_close, groups_to_cancel = set(), set(), set()

        grupo_info_dict: dict[int, CancelarGruposForm] = {grupo.grupo_id: grupo for grupo in solution.cancelar_grupos}
        for grupo_ida_id, grupo_volta_id in self._grupos_ida_volta.items():
            grupo_ida_solver = grupo_info_dict[grupo_ida_id]
            grupo_volta_solver = grupo_info_dict[grupo_volta_id]
            grupo_ida_buser = self._grupos_dict[grupo_ida_id]
            grupo_volta_buser = self._grupos_dict[grupo_volta_id]
            # só confirma, fecha ou cancela grupos se a ida estiver na janela original de 24 horas
            if grupo_ida_solver.fora_da_janela:
                continue
            # se grupo ida e volta não estão com status para cancelar, então são confirmados
            elif not (grupo_ida_solver.cancelar or grupo_volta_solver.cancelar):
                groups_to_confirm.update((grupo_ida_buser, grupo_volta_buser))
            elif self._can_cancel_group(grupo_ida_solver) and self._can_cancel_group(grupo_volta_solver):
                groups_to_cancel.update((grupo_ida_buser, grupo_volta_buser))
            else:
                groups_to_close.update((grupo_ida_buser, grupo_volta_buser))

        return groups_to_confirm, groups_to_close, groups_to_cancel

    def _can_cancel_group(self, grupo: CancelarGruposForm) -> bool:
        """Check if return group can be cancelled (has no company and no passengers)"""
        return grupo.company_id is None and grupo.pessoas == 0

    @traced("solver_svc._calcular_custo_remanejamento")
    def _calcular_custo_remanejamento(self, remanejamentos):
        """
        Calculates the cost of a set of remanejamentos (rearrangements) and updates the cost
        of the corresponding travels.

        Args:
            remanejamentos (list): A list of remanejamentos, where each remanejamento is a dictionary
            with the following keys:
            - "remanejar" (bool): Whether the travel should be remanejada or not.
            - "original_trecho_classe_id" (int): The ID of the original trecho_classe of the travel.
            - "travel_id" (int): The ID of the travel.

        Returns:
            None
        """
        trecho_travels_remanejadas = defaultdict(list)
        travels_remanejadas_ids = []
        for rem in remanejamentos:
            if rem["remanejar"] is False:
                continue
            tc_original_id = rem["original_trecho_classe_id"]
            tc = self._trechos_classe_dict[tc_original_id]
            travel_id = rem["travel_id"]
            trecho_travels_remanejadas[tc].append(self._travels_dict[travel_id])
            travels_remanejadas_ids.append(travel_id)
        # extrato atual das travels
        bulk_extrato_travels = accounting_svc.bulk_extrato_travel(travels_remanejadas_ids)

        # calcula custo dos remanejamentos das travels e persiste em travel.custo_alteracao
        for trecho_classe_destino, travels_por_trecho in trecho_travels_remanejadas.items():
            reserva_extrato_svc.calcula_custo_alteracao_travel(
                travels_por_trecho, trecho_classe_destino, bulk_extrato_travels
            )

    @traced("solver_svc._remanejamentos_buseiro")
    def _remanejamentos_buseiro(self, remanejamentos):
        """
        Given a list of remanejamentos (rearrangements), returns a dictionary mapping
        original group IDs to a list of RemanejamentoBuseiro objects representing the
        rearrangements that were actually performed. A RemanejamentoBuseiro
        object represents a rearrangement of passengers from one group to another,
        and contains information about the travel, the original and destination
        trechos (segments), the cost of the rearrangement, and other relevant data.

        Args:
            remanejamentos (list): A list of dictionaries representing the rearrangements
            to be performed. Each dictionary should contain the following keys:
                - "travel_id": The ID of the travel associated with the rearrangement.
                - "count_seats": The number of seats to be rearranged.
                - "original_grupo_id": The ID of the group from which the passengers will be rearranged.
                - "destino_grupo_id": The ID of the group to which the passengers will be rearranged.
                - "destino_trecho_classe_id": The ID of the trecho_classe (segment class) to which
                the passengers will be rearranged.
                - "original_trecho_classe_id": The ID of the trecho_classe (segment class) from which
                the passengers will be rearranged.

        Returns:
            dict: A dictionary mapping original group IDs to a list of RemanejamentoBuseiro objects representing the
            rearrangements that were actually performed.
        """
        remanejamentos_buseiro = defaultdict(list)
        for rm in remanejamentos:
            travel = self._travels_dict[rm["travel_id"]]
            qtd_pax = rm["count_seats"]
            grupo_original = self._grupos_dict[rm["original_grupo_id"]]
            grupo_destino = self._grupos_dict[rm["destino_grupo_id"]]
            trecho_classe_destino = self._trechos_classe_dict[rm["destino_trecho_classe_id"]]
            trecho_classe_original = self._trechos_classe_dict[rm["original_trecho_classe_id"]]
            if self._capacity_manager_map[grupo_destino.id].vagas(trecho_classe_destino) > qtd_pax:
                self._capacity_manager_map[grupo_destino.id].adiciona_pessoas(trecho_classe_destino, qtd_pax)
                remanejamentos_buseiro[grupo_original.id].append(
                    RemanejamentoBuseiro(
                        travel=travel,
                        trecho_classe_destino=trecho_classe_destino,
                        custo_alteracao=travel.custo_alteracao,
                        origem_trecho=trecho_classe_original.trecho_vendido.origem.cidade.sigla,
                        destino_trecho=trecho_classe_original.trecho_vendido.destino.cidade.sigla,
                        datetime_orig=grupo_original.datetime_ida,
                        dist_origem=dist_origem(trecho_classe_original, trecho_classe_destino),
                        dist_destino=dist_destino(trecho_classe_original, trecho_classe_destino),
                        datetime_rem=grupo_destino.datetime_ida,
                    )
                )
        return remanejamentos_buseiro

    @transaction.atomic
    def confirma_grupos_com_breakeven(self, solver_input_form: SolverInputForm, dry_run: bool = False) -> None:
        """
        Para cada par de grupos, se a razão entre gmv previsto
        e frete for maior ou igual que o threshold então o par de
        grupos deve ser confirmado.
        Nada impede que o par de grupos seja confirmado mesmo
        que a razão entre gmv previsto e frete seja menor que o threshold.
        """
        grupos_para_confirmar = {}
        grupo_dict = {grupo.id: grupo for grupo in solver_input_form.grupos}
        grupo_ida = SolverChartered
        for grupo_ida in solver_input_form.grupos:
            if grupo_ida.fora_da_janela:
                continue

            grupo_volta_id = grupo_ida.chartered_volta_id
            if grupo_volta_id is None:
                continue

            grupo_volta = grupo_dict[grupo_volta_id]

            if (
                grupo_ida.id == grupo_volta_id
                or grupo_ida.company_id is None
                or grupo_ida.status == Grupo.Status.TRAVEL_CONFIRMED
                and grupo_volta.status == Grupo.Status.TRAVEL_CONFIRMED
            ):
                continue

            gmv_previsto = grupo_ida.gmv_previsto + grupo_volta.gmv_previsto
            frete = getattr(grupo_ida, "frete") + getattr(grupo_volta, "frete")

            if gmv_previsto / frete >= grupo_ida.solver_threshold and not float(grupo_ida.solver_threshold) == 0:
                grupo_ida.status = Grupo.Status.TRAVEL_CONFIRMED
                grupo_volta.status = Grupo.Status.TRAVEL_CONFIRMED
                grupo_ida_id = grupo_ida.id
                grupo_volta_id = grupo_volta.id
                grupos_para_confirmar[grupo_ida_id] = self._grupos_dict[grupo_ida_id]
                grupos_para_confirmar[grupo_volta_id] = self._grupos_dict[grupo_volta_id]

        grupos_para_confirmar_ids = list(grupos_para_confirmar.keys())
        if len(grupos_para_confirmar_ids) > 0:
            buserlogger.info(
                "confirma_grupos_com_breakeven",
                extra={"grupo_ids": grupos_para_confirmar_ids},
            )

        if not dry_run:
            grupos_para_confirmar_data = get_groups_data(list(grupos_para_confirmar.values()))
            confirm_groups(
                grupos_para_confirmar_data,
                automatic=True,
                forecast=True,
                solver=True,
                user=self.user_solver,
                fluxo="confirma_grupos_com_breakeven",
            )

    def _cancelamento_fora_da_base(
        self,
        grupo_ida_1: SolverChartered,
        grupo_volta_1: SolverChartered,
        grupo_ida_2: SolverChartered,
        grupo_volta_2: SolverChartered,
        grupos_para_confirmar: dict,
    ):
        """
        Realiza o cancelamento fora da base entre dois pares de grupos ida/volta.
        """
        # Atualiza status e company_id dos grupos
        grupo_ida_1.status = Grupo.Status.TRAVEL_CONFIRMED
        grupo_volta_2.status = Grupo.Status.TRAVEL_CONFIRMED
        grupo_volta_1.company_id = None
        grupo_ida_2.company_id = None

        # Obtém IDs dos grupos
        grupo_ida1_id = grupo_ida_1.id
        grupo_volta1_id = grupo_volta_1.id
        # grupo_ida2_id = grupo_ida_2.id
        grupo_volta2_id = grupo_volta_2.id

        # Atualiza grupos para confirmar
        grupos_para_confirmar[grupo_ida1_id] = self._grupos_dict[grupo_ida1_id]
        grupos_para_confirmar[grupo_volta2_id] = self._grupos_dict[grupo_volta2_id]

        # Atualiza o remanejamento_solver_dict
        grupo_ida_1.chartered_volta_id = grupo_volta2_id
        grupo_ida_2.chartered_volta_id = grupo_volta1_id

        # Log de informações
        grupos_para_confirmar_ids = list(grupos_para_confirmar.keys())
        if grupos_para_confirmar_ids:
            buserlogger.info("cancelamento_fora_da_base", extra={"grupo_ids": grupos_para_confirmar_ids})

        return grupos_para_confirmar

    @transaction.atomic
    def cancelamento_fora_da_base(self, solver_input_form: SolverInputForm, dry_run: bool = False) -> None:
        """
        Em um mesmo slot, realiza cancelamento de pernas intermediárias para maximizar o lucro,
        considerando o impacto em passageiros e custos de dia parado fora da base.
        """

        grupos_para_confirmar = {}
        grupos_por_company = defaultdict(list)

        # Organiza grupos por company_id
        for grupo in solver_input_form.grupos:
            grupos_por_company[grupo.company_id].append(grupo)

        # Itera sobre os grupos organizados por empresa
        for grupos in grupos_por_company.values():
            if len(grupos) < 4:
                continue  # Precisa de pelo menos 4 grupos

            grupo_ida_1, grupo_volta_1, grupo_ida_2, grupo_volta_2 = grupos[:4]

            # Verifica condições de elegibilidade
            if (
                not grupo_ida_1.company_id
                or grupo_ida_1.status == Grupo.Status.TRAVEL_CONFIRMED
                or grupo_volta_1.status == Grupo.Status.TRAVEL_CONFIRMED
                or grupo_ida_2.status == Grupo.Status.TRAVEL_CONFIRMED
                or grupo_volta_2.status == Grupo.Status.TRAVEL_CONFIRMED
            ):
                continue

            if grupo_ida_1.rotina_onibus_id != grupo_ida_2.rotina_onibus_id:
                continue

            # Calcula GMV previsto e custo
            gmv_previsto_forte = grupo_ida_1.gmv_previsto + grupo_volta_2.gmv_previsto
            frete = getattr(grupo_ida_1, "frete") + getattr(grupo_volta_2, "frete")

            # Calcula impacto em passageiros
            try:
                pessoas_no_grupo = self._grupos_forecasts[grupo_volta_1.id] + self._grupos_forecasts[grupo_ida_2.id]
            except KeyError:
                continue

            # Verifica viabilidade do cancelamento
            if (gmv_previsto_forte) / frete >= 1 and pessoas_no_grupo < CRITERIO_PAX_CANCELADOS:
                grupos_para_confirmar = self._cancelamento_fora_da_base(
                    grupo_ida_1,
                    grupo_volta_1,
                    grupo_ida_2,
                    grupo_volta_2,
                    grupos_para_confirmar,
                )

        # Confirma grupos selecionados
        if not dry_run:
            grupos_para_confirmar_data = get_groups_data(list(grupos_para_confirmar.values()))
            confirm_groups(
                grupos_para_confirmar_data,
                automatic=False,
                forecast=True,
                solver=False,
                user=self.user_solver,
                fluxo="cancelamento_fora_da_base",
            )


def _grupos_sentido_contrario(grupo_rotina_1: Grupo, grupo_rotina_2: Grupo) -> bool:
    cidades_itinerario_grupo = [ckp.local.cidade_id for ckp in grupo_rotina_1.rota.itinerario.all()]
    cidades_itinerario_grupo_2 = [ckp.local.cidade_id for ckp in grupo_rotina_2.rota.itinerario.all()]

    intersecao_1 = [cidade for cidade in cidades_itinerario_grupo if cidade in cidades_itinerario_grupo_2]
    intersecao_2 = [cidade for cidade in cidades_itinerario_grupo_2 if cidade in cidades_itinerario_grupo]

    if len(intersecao_1) < 2:
        return False
    # retorna se os grupos são em direção oposta
    return intersecao_1 == list(reversed(intersecao_2))


def _grupos_within_n_days(grupo_rotina_1: Grupo, grupo_rotina_2: Grupo, n_days: int = 2) -> bool:
    """Cuidado: Ao definir o número máximo de dias entre grupos, lembre-se de considerar grupos de ida e volta por causa do cancelamento fora da base."""
    tz_1 = grupo_rotina_1.rota.origem.cidade.timezone
    tz_2 = grupo_rotina_2.rota.origem.cidade.timezone
    diff = to_tz(grupo_rotina_1.datetime_ida, tz_1) - to_tz(grupo_rotina_2.datetime_ida, tz_2)
    if abs(diff.days) <= n_days:
        return True
    return False


def _grupos_mesma_classe(grupo_rotina_1: Grupo, grupo_rotina_2: Grupo) -> bool:
    # O impacto de trocar carros em grupos com classes diferentes é muito maior do que cancelar um único grupo.
    # Portanto, a regra de negócio é não permitir cancelamento cruzado entre grupos com classes diferentes.
    return sorted(grupo_rotina_1.grupoclasse_set.all().values_list("tipo_assento", flat=True)) == sorted(
        grupo_rotina_2.grupoclasse_set.all().values_list("tipo_assento", flat=True)
    )


def _get_grupo_ida_rotina_par(grupo: Grupo, grupos_rotina_par: list[Grupo]) -> Grupo | None:
    """
    No momento o cancelamento cruzado é executado para grupos de rotina par com frequência diária.
    Os grupos de ida das rotinas pares devem ter as mesmas classes e viajam no mesmo dia.
    """
    grupos_rotina_par = sorted(grupos_rotina_par, key=lambda g: g.datetime_ida)
    for grupo_rotina_par in grupos_rotina_par:
        if (
            _grupos_within_n_days(grupo, grupo_rotina_par, 1)
            and _grupos_sentido_contrario(grupo, grupo_rotina_par)
            and _grupos_mesma_classe(grupo, grupo_rotina_par)
        ):
            return grupo_rotina_par


def get_grupo_rotina_par(grupos: Iterable[Grupo], grupos_ida_volta: dict[int, int]) -> dict[int, int]:
    rotina_grupo_map: dict[int, list] = defaultdict(list)
    for grupo in grupos:
        rotina_grupo_map[grupo.rotina_onibus_id].append(grupo)
    grupo_rotina_par_map = {}
    for grupo in grupos:
        # somente grupos de ida tem grupo_rotina_par associado.
        if grupo.id not in grupos_ida_volta.keys():
            continue
        rotina_par_id = getattr(grupo.rotina_onibus, "rotina_par_id", None)
        if rotina_par_id is None:
            continue
        grupos_rotina_par = rotina_grupo_map[rotina_par_id]
        grupo_sentido_contrario = _get_grupo_ida_rotina_par(grupo, grupos_rotina_par)
        if grupo_sentido_contrario:
            grupo_rotina_par_map[grupo.id] = grupo_sentido_contrario.id
    return grupo_rotina_par_map


def _get_grupos_fora_da_janela_ids(
    grupos_dict: dict[int, Grupo], grupos_ida_volta: dict[int, int], start_datetime: datetime, end_datetime: datetime
) -> set[int]:
    grupos_fora_da_janela_ids = set()

    for grupo_ida_id, grupo_volta_id in grupos_ida_volta.items():
        grupo_ida = grupos_dict.get(grupo_ida_id)

        if grupo_ida:
            # verifica se o grupo de ida está fora da janela
            if grupo_ida.datetime_ida < start_datetime or grupo_ida.datetime_ida > end_datetime:
                # se a ida está fora da janela, a volta também está
                grupos_fora_da_janela_ids.update((grupo_ida_id, grupo_volta_id))

    return grupos_fora_da_janela_ids


@traced("solver_svc.trechos_por_cidade")
def trechos_por_cidade(trechos_classe_dict: dict):
    """
    Returns a dictionary mapping pairs of city IDs to lists of TrechoClasse objects.

    The keys of the dictionary are tuples of two city IDs, representing the origin
    and destination cities of a TrechoVendido object. The values are lists of TrechoClasse
    objects that correspond to TrechoVendido objects with the same origin and
    destination cities.

    Returns:
        dict: A dictionary mapping pairs of city IDs to lists of TrechoClasse objects.
    """
    trecho_tc_map = defaultdict(list)
    for tc in trechos_classe_dict.values():
        key = (tc.trecho_vendido.origem.cidade_id, tc.trecho_vendido.destino.cidade_id)
        trecho_tc_map[key].append(tc)
    return trecho_tc_map


def check_constraints(tc: TrechoClasse, tcp: TrechoClasse) -> bool:
    """Verifica se o trecho classe tcp é compatível com o trecho classe tc.

    Args:
        tc (TrechoClasse): Trecho classe de origem
        tcp (TrechoClasse): Trecho classe de destino possível

    Returns:
        bool: True se o trecho classe tcp é compatível com o trecho classe tc. False caso contrário.
    """
    duracao_viagem_horas = tc.duracao_ida.total_seconds() / 3600
    # TODO: O check de compatibilidade teria que ser diferente caso a duração da viagem nova
    # seja muito diferente da duração da viagem reservada inicialmenete
    if duracao_viagem_horas < 2:
        delta_time_minutes = 90
        delta_distance_kilometers = 9
    elif duracao_viagem_horas < 3:
        delta_time_minutes = 120
        delta_distance_kilometers = 9
    elif duracao_viagem_horas < 4:
        delta_time_minutes = 150
        delta_distance_kilometers = 12
    elif duracao_viagem_horas < 6:
        delta_time_minutes = 180
        delta_distance_kilometers = 12
    elif duracao_viagem_horas < 10:
        delta_time_minutes = 210
        delta_distance_kilometers = 12
    elif duracao_viagem_horas < 14:
        delta_time_minutes = 240
        delta_distance_kilometers = 12
    else:
        delta_time_minutes = 270
        delta_distance_kilometers = 15

    now_tc_tz = to_tz_required(now(), tc.trecho_vendido.origem.cidade.timezone)
    intervalo_inferior = max(tc.datetime_ida - timedelta(minutes=delta_time_minutes), now_tc_tz)
    intervalo_superior = tc.datetime_ida + timedelta(minutes=delta_time_minutes)
    fora_do_horario = not (intervalo_inferior <= tcp.datetime_ida <= intervalo_superior)
    origem_diferente = tc.trecho_vendido.origem.cidade_id != tcp.trecho_vendido.origem.cidade_id
    destino_diferente = tc.trecho_vendido.destino.cidade_id != tcp.trecho_vendido.destino.cidade_id
    downgrade_nao_permitido = tcp.grupo_classe.tipo_assento in DOWNGRADES_PROIBITIVOS.get(
        tc.grupo_classe.tipo_assento, []
    )
    embarque_muito_longe = dist_origem(tc, tcp) > delta_distance_kilometers
    desembarque_muito_longe = dist_destino(tc, tcp) > delta_distance_kilometers
    mesmo_grupo = tc.grupo_id == tcp.grupo_id
    constraints = [
        origem_diferente,
        destino_diferente,
        downgrade_nao_permitido,
        embarque_muito_longe,
        desembarque_muito_longe,
        fora_do_horario,
        mesmo_grupo,
    ]
    return not any(constraints)


@traced("solver_svc._calcula_custo_remanejar")
def _calcula_custo_remanejar(
    trechos_classe_dict: dict, range_km: int, range_minutos: int
) -> dict[int, list[tuple[int, CustosRemanejamento]]]:
    """Calcula custo de remanejamento para cada trecho classe possível.
    TODO: Estamos usando o valor médio das travels. Esse cálculo pode ser mais preciso se utilizarmos os buckets.
    Returns:
        list[dict]: Lista de dicionários com custo de remanejamento para cada trecho classe possível.
    """
    tc_tcp_map = map_parametros_trechos_classe_possiveis(trechos_classe_dict, range_km, range_minutos)
    custo_remanejar = defaultdict(list)
    for tc, tcps in tc_tcp_map.items():
        for tcp in tcps:
            custos = calcula_custos(tc, tcp["tcp"], tcp["params"])
            custo_remanejar[tc.id].append((tcp["tcp"].id, custos))
    return custo_remanejar


@traced("solver_svc._calcula_remanejamento_travel_map")
def _calcula_remanejamento_travel_map(
    travels_dict: dict[int, Travel | DummyTravel],
    custo_remanejar: dict[int, list[tuple[int, CustosRemanejamento]]],
) -> dict[int, dict[int, Decimal]]:
    """Calcula um dicionário de custos de remanejamento para cada travel e trecho classe de destino.

    Args:
        travels_dict: Dicionário que mapeia IDs de travel para objetos Travel ou DummyTravel
        custo_remanejar: Dicionário que mapeia (tc_origem_id, tc_destino_id) para custo

    Returns:
        Dicionário que mapeia IDs de travel para dicionários de IDs de trecho classe destino e seus custos de remanejamento.
    """
    remanejamento_travel_map = {}

    for travel_id, travel in travels_dict.items():
        remanejamento_travel_map[travel_id] = {}
        fator = FATOR_CUSTO_REMANEJAR_DUMMY if isinstance(travel, DummyTravel) else 1

        tc_destinos_com_custo = custo_remanejar.get(travel.trecho_classe_id, [])
        for tc_destino_id, custos in tc_destinos_com_custo:
            custo_remanejamento = Decimal(travel.gmv * custos.total) * fator
            remanejamento_travel_map[travel_id][tc_destino_id] = custo_remanejamento

    return remanejamento_travel_map


@traced("solver_svc.map_parametros_trechos_classe_possiveis")
def map_parametros_trechos_classe_possiveis(trechos_classe_dict: dict, range_km: int, range_minutos: int):
    """Constrói o mapa de trechos classe possiveis para remanejamento.

    Returns:
        dict[TrechoClasse, list[dict]]: Mapa de trechos classe possiveis para remanejamento.
    """
    _trechos_por_cidade = trechos_por_cidade(trechos_classe_dict)

    tc_tcp_map = defaultdict(list)
    for tc in trechos_classe_dict.values():
        for tcp in _trechos_por_cidade[(tc.trecho_vendido.origem.cidade_id, tc.trecho_vendido.destino.cidade_id)]:
            if _is_valid_tcp(tc, tcp):
                tc_tcp_map[tc].append({"tcp": tcp, "params": get_score_params(tc, tcp, range_km, range_minutos)})
    return tc_tcp_map


def _is_valid_tcp(tc: TrechoClasse, tcp: TrechoClasse) -> bool:
    constraints = [
        tcp.closed,
        tcp.grupo.company_id is None,
        tc.id == tcp.id,
        tc.grupo_id == tcp.grupo_id,
        not check_constraints(tc, tcp),
    ]

    return not any(constraints)


@traced("solver_svc.get_forecast_e_gmv_previsto_grupos")
def get_forecast_e_gmv_previsto_grupos(
    grupo_ids: list[int],
) -> tuple[dict[int, int | None], dict[int, Decimal], dict[int, Decimal], list[Grupo]]:
    grupos = (
        Grupo.objects.filter(id__in=grupo_ids)
        .select_related("rota")
        .prefetch_related(
            "grupoclasse_set",
            Prefetch(
                "trechoclasse_set",
                queryset=TrechoClasse.objects.select_related("trecho_vendido", "grupo_classe").prefetch_related(
                    "price_manager__buckets"
                ),
            ),
            Prefetch(
                "travel_set",
                queryset=Travel.objects.select_related("grupo_classe").filter(status="pending"),
            ),
        )
    )

    forecast_grupos, trechos_forecasts = _get_forecasts(grupos)
    grupos_com_forecast = [grupo for grupo in grupos if grupo.id in forecast_grupos]

    if not grupos_com_forecast:
        return {}, {}, {}, []

    capacity_manager_map = bulk_prepare_capacity_manager(
        grupos_com_forecast, CMRemanejamento, count_seats_map_dummies_all=trechos_forecasts
    )
    dummy_manager = DummyManager(grupos_com_forecast, capacity_manager_map)
    gmv_real, gmv_previsto = dummy_manager.get_accops_grupos(addons=True, promocao=False)
    return forecast_grupos, gmv_real, gmv_previsto, list(grupos)


def _get_forecasts(grupos: QuerySet[Grupo]) -> tuple[dict, dict]:
    trechos_classe_set = {tc for g in grupos for tc in g.trechoclasse_set.all()}
    prediction_info_trechos = [
        TrechoForecastItem(
            grupo_id=tc.grupo_id,
            trecho_vendido_id=tc.trecho_vendido_id,
            tipo_assento=tc.grupo_classe.tipo_assento,
        )
        for tc in trechos_classe_set
    ]
    forecast_trechoclasses = get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento(
        prediction_info_trechos
    )
    grupos_sem_forecast_trecho = set()
    for grupo in grupos:
        if grupo.modelo_venda == Grupo.ModeloVenda.BUSER:
            if grupo.id not in forecast_trechoclasses:
                grupos_sem_forecast_trecho.add(grupo.id)
    if grupos_sem_forecast_trecho:
        buserlogger.info("solver_svc.grupos_sem_forecast_trecho", extra={"grupos_id": grupos_sem_forecast_trecho})

    forecasts_grupo = forecast_prediction_svc.get_grupo_predictions([g.id for g in grupos])
    # grupos sem forecast não tem cálculo de gmv
    forecast_not_none = {grupo_id: forecast for grupo_id, forecast in forecasts_grupo.items() if forecast}
    return forecast_not_none, round_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento(forecast_trechoclasses)


@traced("solver_svc._calcula_receita_agregada_travel_map")
def _calcula_receita_agregada_travel_map(travels: list[Travel | DummyTravel]) -> dict[int, Decimal]:
    """
    Calcula a receita agregada para uma lista de travels. Desconsiderando promoções.

    Para objetos DummyTravel, é apenas o seu próprio GMV.
    Para objetos Travel ida, é a soma das seguintes receitas:
        - receita da própria travel
        - receita da travel de volta (se existir)
        - receita de quaisquer travels de conexão (tanto ida quanto volta, se existirem)
    Para objetos Travel volta, é a soma das seguintes receitas:
        - receita da própria travel
        - receita de conexão da travel, se existir
    Importante: Receita <> GMV. Receita não considera descontos.
    """
    travels_reais_map = {}
    receita_agregada_travel_map = {}
    for travel in travels:
        if isinstance(travel, DummyTravel):
            receita_agregada_travel_map[travel.id] = travel.gmv
        else:
            travels_reais_map[travel.id] = travel

    travels_ids = travels_reais_map.keys()
    travels_relacionadas = (
        Travel.objects.filter(Q(id__in=travels_ids) | Q(travel_ida_id__in=travels_ids))
        .select_related("travel_conexao", "travel_ida__travel_conexao")
        .prefetch_related(
            Prefetch(
                "travel_conexao__travel_set",
                queryset=Travel.objects.exclude(status=Travel.Status.CANCELED),
            ),
            Prefetch(
                "travel_ida__travel_conexao__travel_set",
                queryset=Travel.objects.exclude(status=Travel.Status.CANCELED),
            ),
        )
    )

    def _get_conexoes(travel: Travel) -> list[Travel]:
        """Função auxiliar para obter todas as conexões ativas de uma travel, incluindo ela mesma."""
        if travel.travel_conexao:
            return list(travel.travel_conexao.travel_set.all())
        return [travel]

    voltas_por_ida = defaultdict(list)
    travels_ida_e_volta_map = defaultdict(list)

    for travel in travels_relacionadas:
        if travel.is_volta:
            voltas_por_ida[travel.travel_ida_id].append(travel)

    all_travels = set()
    for travel in travels_relacionadas:
        if travel.is_volta:
            travels_ida_e_volta_map[travel].extend([_get_conexoes(travel.travel_ida), _get_conexoes(travel)])
            all_travels.update(*travels_ida_e_volta_map[travel])
        else:
            travels_ida = _get_conexoes(travel)
            travels_volta = [volta for travel_ida in travels_ida for volta in voltas_por_ida[travel_ida.id]]
            travels_ida_e_volta_map[travel].extend([travels_ida, travels_volta])
            all_travels.update(*travels_ida_e_volta_map[travel])

    receita_total_travel_map = accounting_svc.calcula_accops_travel_map(all_travels, addons=True, promocao=False)

    for travel_id, travel in travels_reais_map.items():
        travels_ida, travels_volta = travels_ida_e_volta_map[travel]
        receita_agregada = sum(receita_total_travel_map[t.id] for t in travels_ida + travels_volta)
        receita_agregada_travel_map[travel_id] = min(3 * receita_total_travel_map[travel_id], receita_agregada)

    return receita_agregada_travel_map


@traced("solver_svc._calcula_receita_agregada_grupo_map")
def _calcula_receita_agregada_grupo_map(
    travels_dict: dict[int, Travel],
    receita_agregada_travel_map: dict[int, Decimal],
) -> dict[int, Decimal]:
    receita_agregada_grupo_map = defaultdict(Decimal)

    for travel_id, receita_agregada in receita_agregada_travel_map.items():
        grupo_id = travels_dict[travel_id].grupo_id
        receita_agregada_grupo_map[grupo_id] += receita_agregada

    return receita_agregada_grupo_map


def set_solver_running() -> None:
    redis_client = redis.get_master_client()
    redis_client.set(name=SOLVER_RUNNING_KEY, value=1, ex=SOLVER_RUNNING_EXPIRATION)


def raise_if_solver_running() -> None:
    redis_client = redis.get_master_client()
    if redis_client.exists(SOLVER_RUNNING_KEY):
        raise SolverRunningError(
            f"O Solver está em execução, tente novamente daqui {int(SOLVER_RUNNING_EXPIRATION.total_seconds() / 60)} minutos"
        )
