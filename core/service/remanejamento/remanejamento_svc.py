import copy
import logging
from collections import defaultdict
from decimal import Decimal as D
from functools import partial
from typing import Any, Iterable

import sentry_sdk
from beeline import traced
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import F, Prefetch, Q, Sum, prefetch_related_objects
from django.db.models.functions import TruncDate

from accounting.models import AccountingOperation
from accounting.service import accounting_svc
from commons import dateutils, geoutils
from commons.dateutils import meia_noite_nunca_mais, to_default_tz_required
from commons.django_celery_utils import my_shared_task
from commons.django_model_utils import model_pk_list
from commons.django_utils import error_str
from commons.guard import is_rotas
from core import signals, tasks
from core.constants import MOTIVOS_REMANEJAMENTO, TIPOS_ASSENTO, TIPOS_ASSENTO_PESO
from core.forms.buckets_forms import RateioDataclass
from core.forms.remanejamento_forms import (
    FormRemanejamentoConstraints,
    MarcacaoAssentoPagoForm,
    MovePassengerForm,
    MoverBuseiroForm,
    RemanejamentoForm,
)
from core.models_company import OnibusClasse
from core.models_grupo import ClosedReasons, Grupo, GrupoClasse, PoltronaOnibus, TrechoClasse
from core.models_rota import Checkpoint, TrechoVendido
from core.models_travel import (
    AlteracaoTravel,
    HistoricoRemanejamento,
    Passageiro,
    RessarcimentoDowngrade,
    Travel,
)
from core.serializers import serializer_trecho_classe
from core.service import (
    globalsettings_svc,
    log_svc,
    multitrecho_svc,
    preco_svc,
    rodoviaria_svc,
    rotas_svc,
    travel_comunicada_svc,
)
from core.service.grupos_staff import grupo_crud_svc
from core.service.multitrecho_svc import CapacityManager
from core.service.notifications import user_notification_svc
from core.service.notifications.pcd_notification_svc import notify_staff_if_pcd_pax_lost_seat
from core.service.onibus_svc import get_available_capacidade
from core.service.remanejamento.commons.remanejamento_base import RemanejamentoBuseiro
from core.service.reserva import reserva_extrato_svc, rodoviaria_reserva_svc
from core.service.reserva.travel.travel_svc import (
    TravelsEmRemanejamento,
)
from core.service.ressarcimento_downgrade_svc import get_downgrade_value
from core.service.selecao_assento import NotEnoughSeats
from core.service.selecao_assento.fretamento import (
    FretamentoSeatController,
    SeatControllerManager,
    get_poltronas_disponiveis_por_tc,
)
from integrations.rodoviaria_client.exceptions import (
    RodoviariaRemanejamentoBulkException,
    RodoviariaRemanejamentoException,
)

buserlogger = logging.getLogger("buserlogger")
EARTH_CIRCUNFERENCE = 40075  # aproximadamente em km
DIST_FACTOR = EARTH_CIRCUNFERENCE / 360


class RemanejamentoOverbookingException(Exception):
    vagas_disponiveis = None

    def __init__(self, deficit_de_vagas):
        self.message = (
            f"O remanejamento vai causar overbooking no trecho classe de destino de {deficit_de_vagas} assento(s)"
        )


def get_grupos_para_remanejamento(grupos) -> list[Grupo]:
    ids = [grupo for grupo in grupos if isinstance(grupo, int)]
    grupos = [grupo for grupo in grupos if isinstance(grupo, Grupo)]

    select_related = ["rota", "onibus"]
    prefetch_related = [
        "onibus__classes",
        "grupoclasse_set",
        Prefetch(
            "trechoclasse_set",
            queryset=TrechoClasse.objects.select_related(
                "grupo_classe__grupo__rotina_onibus",
                "trecho_vendido__origem__cidade",
                "trecho_vendido__destino__cidade",
                "closed_by",
            ).prefetch_related("price_manager__buckets"),
        ),
        Prefetch(
            "travel_set",
            queryset=Travel.objects.select_related(
                "grupo_classe",
                "trecho_classe",
            ).order_by("created_on"),
        ),
        Prefetch(
            "rota__trechos_vendidos", queryset=TrechoVendido.objects.select_related("origem__cidade", "destino__cidade")
        ),
        Prefetch("rota__itinerario", queryset=Checkpoint.objects.select_related("local")),
    ]

    prefetch_related_objects(grupos, *select_related, *prefetch_related)
    qs = Grupo.objects.filter(id__in=ids).select_related(*select_related).prefetch_related(*prefetch_related)
    grupos.extend(qs)
    return grupos


def match_rateio_classes_bucketizadas(
    grupo,
    novas_classes_form: list[OnibusClasse],
    precificacao: dict[tuple[int, str], RateioDataclass],
    onibus=None,
):
    """
    atualiza os parâmetros para o bulk_change_class considerar a bucketização.
    """
    class_list = []
    trechos_vendidos = grupo.rota.get_trechos_vendidos()

    for classe in novas_classes_form:
        assento = classe.tipo
        capacidade = get_available_capacidade(grupo, onibus, classe)
        if capacidade <= 0:
            continue
        trecho_classe_list = []
        for trecho_vendido in trechos_vendidos:
            try:
                match: RateioDataclass = precificacao[trecho_vendido.id, assento]
            except KeyError:
                continue
            trecho_classe_list.append(
                {
                    "id": trecho_vendido.id,
                    "max_split_value": match.valor,
                    "ref_split_value": match.valor_ref,
                    "fator_precificacao": match.fator_precificacao + 1,
                    "assento": match.assento,
                    "tipo_assento_anterior": match.tipo_assento_anterior,
                    "buckets": match.buckets_to_dict(),
                    "origem": {"sigla": trecho_vendido.origem.cidade.sigla},
                    "destino": {"sigla": trecho_vendido.destino.cidade.sigla},
                }
            )

        class_list.append({"max_capacity": capacidade, "tipo_assento": assento, "trechos": trecho_classe_list})
    return class_list


def _prepare_params(grupos: Iterable[Grupo], params: dict[str, Any]) -> list[Any]:
    """sobrescreve o params["classes"] pelo novo params que considera as classes bucketizadas"""
    classes_form = [
        OnibusClasse(
            tipo=c["tipo_assento"], capacidade=int(c["max_capacity"]), capacidade_vendida=int(c["max_capacity"])
        )
        for c in params["classes"]
    ]
    grupos_params = []
    for grupo in grupos:
        precificacao = preco_svc.procura_match_rateio(grupo, classes_form)
        grupo_params = params.copy()
        grupo_params["classes"] = match_rateio_classes_bucketizadas(grupo, classes_form, precificacao)
        grupos_params.append((grupo, grupo_params))
    return grupos_params


def process_group_setup(
    grupo: Grupo, grupo_param: dict, trechos_vendidos_map: dict[int, TrechoVendido], capacity_manager_map: dict
) -> dict:
    """Process the setup for a given group."""
    grupo_cm = capacity_manager_map[grupo.id]
    new_group_setup = simular_remanejamento(grupo, grupo_param, trechos_vendidos_map, grupo_cm, closed_details=True)
    return _change_classes(grupo, new_group_setup)


def create_alteracao_travel_objects(
    grupo: Grupo, new_group_setup: dict, origem_alteracao_placa: str, causada_por: str, motivo_id: int | None
) -> list[AlteracaoTravel]:
    """Create AlteracaoTravel objects for the provided group setup."""
    alteracoes = []
    for transfer in new_group_setup["log_data"]["transfers"]:
        alteracoes.append(
            AlteracaoTravel(
                travel_id=transfer["travel_id"],
                antigo_tipo_assento=transfer["old_tipo_assento"],
                novo_tipo_assento=transfer["new_tipo_assento"],
                tipo=AlteracaoTravel.TipoAlteracao.MUDANCA_DE_CLASSE,
                fluxo=origem_alteracao_placa,
                causada_por=causada_por,
                motivo_id=motivo_id,
            )
        )
    return alteracoes


def _bulk_change_class_notify_users(
    downgraded_travel_ids: list[int],
    upgraded_travel_ids: list[int],
    old_assentos_map: dict,
    onibus_antigo: Any,
    travel_downgrade_info: dict,
) -> None:
    """Send notifications to users."""
    user_notification_svc.travels_grupo_classe_changed.delay(
        downgraded_travel_ids,
        old_assentos_map=old_assentos_map,
        downgraded=True,
        travel_downgrade_info=travel_downgrade_info,
        onibus_antigo=onibus_antigo,
    )
    user_notification_svc.travels_grupo_classe_changed.delay(
        upgraded_travel_ids, old_assentos_map=old_assentos_map, upgraded=True, onibus_antigo=onibus_antigo
    )


@my_shared_task(queue="staff")
def _notify_users_bus_change(
    travel_ids: list[int],
    downgraded_travel_ids: list[int],
    upgraded_travel_ids: list[int],
    onibus_antigo: Any,
):
    excluded_ids = downgraded_travel_ids + upgraded_travel_ids
    travels = Travel.objects.filter(pk__in=travel_ids).exclude(pk__in=excluded_ids).select_related("trecho_classe")

    for travel in travels:
        travel_comunicada_svc.salvar_travel_comunicada(travel, onibus=onibus_antigo)


def _atualiza_vagas_trechos_classe(log_data: list[tuple[Any, dict]]) -> None:
    """Update data after committing to the database."""
    for grupo, log in log_data:
        log_svc.log_remanejamento_grupo(grupo, log)
        tasks.atualiza_vagas_trechos_classe.delay(grupo)


@traced("remanejamento_svc.bulk_change_class")
@transaction.atomic
def bulk_change_class(
    params: dict, origem_alteracao_placa: str, causada_por: AlteracaoTravel.CausadaPor, motivo_id: int | None
) -> None:
    grupos = get_grupos_para_remanejamento(params["groups"])
    capacity_manager_map = multitrecho_svc.bulk_prepare_capacity_manager(grupos)
    _validate_escalar_onibus_rotas(grupos)

    seat_controller_manager = SeatControllerManager(FretamentoSeatController)

    onibus_antigo = params.pop("onibus_antigo", None)
    grupo_params = _prepare_params(grupos, params)

    rota_ids = {grupo.rota_id for grupo in grupos}
    trechos_vendidos = TrechoVendido.objects.select_related("origem", "destino").filter(rota__in=rota_ids)
    trechos_vendidos_map = {tv.id: tv for tv in trechos_vendidos}

    changes_data = _bulk_change_class_process_grupo_changes(
        grupo_params, trechos_vendidos_map, capacity_manager_map, origem_alteracao_placa, causada_por, motivo_id
    )

    _bulk_change_class_cleanup_old_classes(
        changes_data["old_grupos_classe_ids"], changes_data["old_trechos_classe_ids"]
    )
    AlteracaoTravel.objects.bulk_create(changes_data["alteracoes"], batch_size=1000)
    travels = changes_data["travels"]
    # TODO remover try/except apos a validação, esta aqui para testar a logica de marcacao de assentos sem quebrar o fluxo que já esta funcionando
    try:
        travels_antigas = changes_data.get("old_travels", [])
        marcacao_assentos_pagos_info = _lista_marcacao_de_assento_com_pagamento_info_nas_travels(travels_antigas)
        handle_marcacao_assentos_no_change_class(
            travels,
            travels_antigas,
            changes_data["downgraded_travel_ids"],
            changes_data["upgraded_travel_ids"],
            seat_controller_manager,
            marcacao_assentos_pagos_info,
        )
    except Exception as e:
        sentry_sdk.capture_exception(e)

    if params.get("notify_users_class_changed"):
        transaction.on_commit(
            partial(
                _bulk_change_class_notify_users,
                downgraded_travel_ids=changes_data["downgraded_travel_ids"],
                upgraded_travel_ids=changes_data["upgraded_travel_ids"],
                old_assentos_map=changes_data["old_assentos_map"],
                onibus_antigo=onibus_antigo,
                travel_downgrade_info=changes_data["travel_downgrade_info"],
            )
        )
        # as travels que não tiveram downgrade e nem upgrade ainda precisam ser comunicadas da mudança de ônibus/placa
        travel_ids = [t.id for t in travels]
        transaction.on_commit(
            partial(
                _notify_users_bus_change,
                travel_ids=travel_ids,
                downgraded_travel_ids=changes_data["downgraded_travel_ids"],
                upgraded_travel_ids=changes_data["upgraded_travel_ids"],
                onibus_antigo=onibus_antigo,
            )
        )

    transaction.on_commit(partial(_atualiza_vagas_trechos_classe, log_data=changes_data["log_data"]))


def _bulk_change_class_process_grupo_changes(
    grupo_params: list[tuple],
    trechos_vendidos_map: dict[int, TrechoVendido],
    capacity_manager_map: dict,
    origem_alteracao_placa: str,
    causada_por: str,
    motivo_id: int | None,
) -> dict:
    """Process changes for grupos and return required data."""
    downgraded_travel_ids, upgraded_travel_ids = [], []
    old_grupos_classe_ids, old_trechos_classe_ids = [], []
    log_data, alteracoes = [], []
    old_assentos_map = {}
    travels = []
    old_travels = []

    for grupo, grupo_param in grupo_params:
        new_group_setup = process_group_setup(grupo, grupo_param, trechos_vendidos_map, capacity_manager_map)
        travels.extend(new_group_setup["travels"])
        old_travels.extend(new_group_setup["old_travels"])
        downgraded_travel_ids += model_pk_list(new_group_setup["downgraded"])
        upgraded_travel_ids += model_pk_list(new_group_setup["upgraded"])
        old_assentos_map.update(new_group_setup["old_assentos_map"])
        old_grupos_classe_ids += model_pk_list(new_group_setup["old_classes"])
        old_trechos_classe_ids += model_pk_list(new_group_setup["old_trechos_classes"])

        log_data.append((grupo, new_group_setup["log_data"]))
        alteracoes += create_alteracao_travel_objects(
            grupo, new_group_setup, origem_alteracao_placa, causada_por, motivo_id
        )

    return {
        "downgraded_travel_ids": downgraded_travel_ids,
        "upgraded_travel_ids": upgraded_travel_ids,
        "old_assentos_map": old_assentos_map,
        "old_grupos_classe_ids": old_grupos_classe_ids,
        "old_trechos_classe_ids": old_trechos_classe_ids,
        "log_data": log_data,
        "alteracoes": alteracoes,
        "travel_downgrade_info": {},  # for backward-compartibility
        "travels": travels,
        "old_travels": old_travels,
    }


def _bulk_change_class_cleanup_old_classes(old_grupos_classe_ids: list[int], old_trechos_classe_ids: list[int]) -> None:
    buserlogger.info(
        "Trechos classes apagados: %s", repr(old_trechos_classe_ids), extra={"count": len(old_trechos_classe_ids)}
    )
    close_params = {
        "closed": True,
        "closed_by": None,
        "closed_at": dateutils.now(),
        "closed_reason": ClosedReasons.CLEANUP_OLD_CLASSES,
    }
    GrupoClasse.objects.filter(pk__in=old_grupos_classe_ids).update(**close_params)
    buserlogger.info(
        "Grupo classes apagados: %s", repr(old_grupos_classe_ids), extra={"count": len(old_grupos_classe_ids)}
    )
    TrechoClasse.objects.filter(pk__in=old_trechos_classe_ids).update(**close_params)


def simular_remanejamento_massivo(params: dict) -> dict[str, Any]:
    transfers = {}
    grupos = get_grupos_para_remanejamento(params["groups"])
    trechos_vendidos_map = _build_trechos_vendidos_map(params["classes"])
    _validate_escalar_onibus_rotas(grupos)
    classes_form = [
        OnibusClasse(
            tipo=c["tipo_assento"], capacidade=int(c["max_capacity"]), capacidade_vendida=int(c["max_capacity"])
        )
        for c in params["classes"]
    ]
    capacity_manager_map = multitrecho_svc.bulk_prepare_capacity_manager(grupos)

    for grupo in grupos:
        grupo_cm = capacity_manager_map[grupo.id]
        precificacao = preco_svc.procura_match_rateio(grupo, classes_form)
        grupo_params = params.copy()
        grupo_params["classes"] = match_rateio_classes_bucketizadas(grupo, classes_form, precificacao)
        new_group_setup = simular_remanejamento(grupo, grupo_params, trechos_vendidos_map, grupo_cm)
        for transfer_obj in new_group_setup["transfers"]:
            transfer = transfers.setdefault(
                transfer_obj["key"], {"pessoas": 0, "custo": 0, "grupo_ids": [], "travel_ids": []}
            )
            transfer["pessoas"] += transfer_obj["pessoas"]
            transfer["custo"] += transfer_obj["custo"]

            grupo_id = transfer_obj["grupo_id"]
            if grupo_id not in transfer["grupo_ids"]:
                transfer["grupo_ids"].append(grupo_id)

            travel_id = transfer_obj["travel_id"]
            if travel_id not in transfer["travel_ids"]:
                transfer["travel_ids"].append(travel_id)

            transfer.update(
                {
                    "valor_tc_original": transfer_obj["old_price"],
                    "valor_tc_novo": transfer_obj["new_price"],
                }
            )

    return {"transfers": transfers}


def _validate_escalar_onibus_rotas(grupos: Iterable[Grupo]):
    """Valida se todos os grupos selecionados são da mesma rota
    ou no caso de grupos da mesma rotina, se possuem o mesmo ônibus.
    """
    rotas = {grupo.rota for grupo in grupos}
    onibus = {grupo.onibus for grupo in grupos}
    if len(rotas) > 1:
        if len(onibus) > 1 or onibus == {None}:
            raise ValidationError("Só pode mudar massivo para grupos do mesmo trecho ou mesmo ônibus.")


@traced("remanejamento_svc.simular_remanejamento")
def simular_remanejamento(
    grupo: Grupo,
    params: dict[str, Any],
    trechos_vendidos_map: dict[int, TrechoVendido],
    grupo_cm: CapacityManager,
    closed_details: bool = False,
) -> dict[str, Any]:
    old_grupo_classes = list(grupo.grupoclasse_set.all())
    old_trechos_classes = list(grupo.trechoclasse_set.all())
    log_data = _simular_remanejamento_initialize_log_data(
        params, grupo, old_grupo_classes, old_trechos_classes, closed_details=closed_details
    )
    _validate_classes(grupo, params["classes"], g_capacity_manager=grupo_cm)
    travels = _get_travels_por_prioridade(grupo)
    old_travels = [copy.deepcopy(t) for t in travels]
    new_classes_por_tipo = _classes_novas_por_tipo_assento(
        grupo, old_grupo_classes, params["classes"], trechos_vendidos_map
    )
    results = _simular_remanejamento_process_travels(travels, new_classes_por_tipo)
    log_data["transfers"] = results["log_data_transfers"]
    return {
        "new_classes": [c["classe"] for c in new_classes_por_tipo if c["classe"]],
        "new_trechos_classes": _get_new_trechos_classes(new_classes_por_tipo),
        "old_classes": old_grupo_classes,
        "old_trechos_classes": old_trechos_classes,
        "transfers": results["transfers"],
        "log_data": log_data,
        "downgraded": results["downgraded"],
        "upgraded": results["upgraded"],
        "downgraded_travels": results["downgraded_travels"],
        "travels": results["travels"],
        "old_travels": old_travels,
        "old_assentos_map": results["old_assentos_map"],
    }


@traced("remanejamento_svc._simular_remanejamento_initialize_log_data")
def _simular_remanejamento_initialize_log_data(
    params: dict[str, Any],
    grupo: Grupo,
    old_classes: list[GrupoClasse],
    old_trechos_classes: list[TrechoClasse],
    closed_details: bool = False,
) -> dict[str, Any]:
    log_params = params.copy()
    log_params["groups"] = model_pk_list(log_params["groups"])
    return {
        "params": log_params,
        "old_grupo_classes": [c.to_dict_json(staff=True, closed_details=closed_details) for c in old_classes],
        "old_trechos_classes": serializer_trecho_classe.serialize(old_trechos_classes, with_price_info=True),
    }


@traced("remanejamento_svc._simular_remanejamento_process_travels")
def _simular_remanejamento_process_travels(travels: list[Travel], new_classes_por_tipo: list[dict]) -> dict[str, Any]:
    log_data_transfers, upgraded_travels, downgraded_travels, transfers = [], [], [], []
    old_travels_map, old_assentos_map = {}, {}
    count_seats_map = defaultdict(int)
    new_trechos_classes = [
        trecho[0] for new_classe in new_classes_por_tipo for trecho in new_classe["trechos"].values()
    ]

    for travel in travels:
        old_travels_map[travel.id] = travel.grupo_classe, travel.trecho_classe
        remanejar_travel(travel, new_classes_por_tipo, new_trechos_classes, count_seats_map)

    for travel in travels:
        if travel.status == "canceled":
            continue

        old_grupo_classe, old_trecho_classe = old_travels_map[travel.id]
        transfer = _simular_remanejamento_extract_transfer_data(
            travel, old_grupo_classe, old_trecho_classe, downgraded_travels, upgraded_travels, old_assentos_map
        )
        log_data_transfers.append(transfer["log_data"])
        transfers.append(transfer["transfer"])

    return {
        "transfers": transfers,
        "downgraded": [d["travel"] for d in downgraded_travels],
        "upgraded": upgraded_travels,
        "downgraded_travels": downgraded_travels,
        "travels": travels,
        "old_assentos_map": old_assentos_map,
        "log_data_transfers": log_data_transfers,
    }


def _simular_remanejamento_extract_transfer_data(
    travel: Travel,
    old_grupo_classe: GrupoClasse,
    old_trecho_classe: TrechoClasse,
    downgraded_travels: list[dict],
    upgraded_travels: list[Travel],
    old_assentos_map: dict,
) -> dict[str, dict[str, Any]]:
    old_tipo = old_grupo_classe.tipo_assento
    new_tipo = travel.grupo_classe.tipo_assento
    old_classe_peso = TIPOS_ASSENTO_PESO[old_tipo]
    new_classe_peso = TIPOS_ASSENTO_PESO[new_tipo]
    old_price = old_trecho_classe.max_split_value
    new_price = travel.trecho_classe.max_split_value
    custo = D(0)

    if old_classe_peso > new_classe_peso:
        old_assentos_map[travel.id] = old_tipo
        downgraded_travels.append({"travel": travel, "old_tipo": old_tipo, "new_tipo": new_tipo})
        custo += get_downgrade_value(travel, old_tipo, new_tipo)
    elif old_classe_peso < new_classe_peso:
        old_assentos_map[travel.id] = old_tipo
        upgraded_travels.append(travel)

    return {
        "transfer": {
            "key": f"{old_tipo}>{new_tipo}",
            "grupo_id": travel.grupo_id,
            "travel_id": travel.id,
            "pessoas": travel.count_seats,
            # Custo para a Buser.
            # No downgrade, perdemos dinheiro ressarcindo.
            # No upgrade, deixamos de ganhar dinheiro.
            "custo": custo,
            "old_price": old_price,
            "new_price": new_price,
        },
        "log_data": {
            "travel_id": travel.id,
            "old_classe_id": old_grupo_classe.id,
            "old_tipo_assento": old_grupo_classe.tipo_assento,
            "new_tipo_assento": travel.grupo_classe.tipo_assento,
            "travel_status": travel.status,
        },
    }


def _get_new_trechos_classes(new_classes_por_tipo) -> list[TrechoClasse]:
    new_tcs, new_tcs_buckets = [], []
    for c in new_classes_por_tipo:
        if c["trechos"]:
            for trecho_classe, bucket in c["trechos"].values():
                new_tcs.append(trecho_classe)
                new_tcs_buckets.append((trecho_classe, bucket))
    prefetch_related_objects(
        new_tcs,
        "grupo",
        "grupo_classe",
        "price_manager",
        "trecho_vendido__origem__cidade",
        "trecho_vendido__destino__cidade",
    )
    return new_tcs_buckets


def _build_trechos_vendidos_map(classes: list[dict[str, Any]]) -> dict[int, TrechoVendido]:
    trechos_vendidos_ids = set()
    for dclasse in classes:
        for dtrecho in dclasse["trechos"]:
            trechos_vendidos_ids.add(int(dtrecho["id"]))
    return TrechoVendido.objects.select_related(
        "origem",
        "destino",
    ).in_bulk(trechos_vendidos_ids)


def _create_new_trecho_classes(grupo: Grupo, new_group_setup: dict[str, Any]) -> None:
    # Criar novos trechos_classes no banco
    for tc, _ in new_group_setup["new_trechos_classes"]:
        tc.grupo_classe = tc.grupo_classe

    # TODO_REFATORACAO_ROTAS: Possível N+1 aqui.
    # Recalcular datetime_ida e duração dos trechos_classes
    duracoes_por_trecho = rotas_svc.get_duracoes_por_trecho_vendido_do_grupo(grupo)

    for trecho_classe, _ in new_group_setup["new_trechos_classes"]:
        update_trecho_class_times(grupo, trecho_classe, duracoes_por_trecho)

    create_trecho_class_with_prices(new_group_setup["new_trechos_classes"])


def update_trecho_class_times(grupo: Grupo, trecho_classe: TrechoClasse, duracoes_por_trecho: list[dict]) -> None:
    for trecho in duracoes_por_trecho:
        if trecho_classe.trecho_vendido.id == trecho["trecho_vendido_id"]:
            origem_tz = trecho_classe.trecho_vendido.origem.cidade.timezone
            trecho_classe.datetime_ida = meia_noite_nunca_mais(grupo.datetime_ida + trecho["timedelta_ida"], origem_tz)
            trecho_classe.duracao_ida = trecho["duracao_ida"]


def create_trecho_class_with_prices(trechos_classes_buckets: list[tuple[TrechoClasse, list[dict] | None]]) -> None:
    price_managers, price_logs, price_buckets = [], [], []

    for tc, buckets in trechos_classes_buckets:
        price_manager, price_bucket, price_log = preco_svc.determine_bucket_update(tc, buckets)
        price_buckets += price_bucket
        price_logs += price_log
        tc.price_manager = price_manager
        price_managers.append(price_manager)

    preco_svc.bulk_update_price_objects(price_managers, price_buckets, price_logs)

    trechos_classe_objects = []
    for tc, _ in trechos_classes_buckets:
        tc.price_manager_id = tc.price_manager.id
        trechos_classe_objects.append(tc)
    TrechoClasse.objects.bulk_create(trechos_classe_objects)


@traced("remanejamento_svc._change_classes")
def _change_classes(grupo: Grupo, new_group_setup: dict[str, Any]) -> dict[str, Any]:
    # Criar novas classes no banco
    GrupoClasse.objects.bulk_create(new_group_setup["new_classes"])

    _classes_update_log_data(new_group_setup)
    _create_new_trecho_classes(grupo, new_group_setup)
    _update_travels_grupo_trecho_classe(new_group_setup)

    multitrecho_svc.atualiza_vagas_trechos_classe_do_grupo(grupo)
    return new_group_setup


def _classes_update_log_data(new_group_setup: dict) -> None:
    map_travel_grupo_classe = {travel.id: travel.grupo_classe.id for travel in new_group_setup["travels"]}
    # Como acabou de criar as classes precisa atualizar o log data
    for transfer in new_group_setup["log_data"]["transfers"]:
        transfer["new_classe_id"] = map_travel_grupo_classe[transfer["travel_id"]]


def _update_travels_grupo_trecho_classe(new_group_setup: dict) -> None:
    for travel in new_group_setup["travels"]:
        # redefinir a relação com o mesmo valor faz atualizar o id no pai
        # parece que isso aí não faz nada, mas ele mantém a relação com o
        # mesmo objeto ao mesmo tempo que atualiza o id na travel
        travel.grupo_classe = travel.grupo_classe
        travel.trecho_classe = travel.trecho_classe

    Travel.objects.bulk_update(new_group_setup["travels"], ["grupo_classe", "trecho_classe"])


def remanejar_travel(
    travel: Travel,
    classes: list[dict[str, Any]],
    trechos_classe: list[TrechoClasse],
    count_seats_map: dict[tuple[int, str], int],
) -> bool:
    tipo_assento_idx = TIPOS_ASSENTO.index(travel.grupo_classe.tipo_assento)
    try:
        equivalent_class = next(c for c in classes if get_class_idx(c) == tipo_assento_idx)
    except StopIteration:
        equivalent_class = None

    # Mudar para mesma classe
    if equivalent_class and mudar_travel_para_classe(travel, equivalent_class, trechos_classe, count_seats_map):
        return True
    # Upgrade
    if upgrade_para_classe_mais_proxima(travel, classes, trechos_classe, count_seats_map):
        return True
    # Downgrade
    if downgrade_para_classe_mais_proxima(travel, classes, trechos_classe, count_seats_map):
        return True
    # Sobrou
    raise ValidationError(
        "Não foi possível remanejar esse grupo automaticamente pq seria necessário separar passageiros de uma mesma reserva."
    )


def upgrade_para_classe_mais_proxima(
    travel: Travel,
    classes: list[dict[str, Any]],
    trechos_classe: list[TrechoClasse],
    count_seats_map: dict[tuple[int, str], int],
) -> bool:
    tipo_assento_idx = TIPOS_ASSENTO.index(travel.grupo_classe.tipo_assento)
    upper_classes = [c for c in classes if get_class_idx(c) > tipo_assento_idx]
    for new_classe in upper_classes:
        new_classe_obj = new_classe["classe"]
        if new_classe_obj.capacidade < travel.count_seats:
            # A classe não tem capacidade.
            continue

        if mudar_travel_para_classe(travel, new_classe, trechos_classe, count_seats_map):
            return True

        # Dar upgrade nos travels acima até liberar vaga para esse travel
        while new_classe_obj.vagas < travel.count_seats and new_classe["travels"]:
            travel_com_prioridade = new_classe["travels"][0]
            remanejou_acima = upgrade_para_classe_mais_proxima(
                travel_com_prioridade, classes, trechos_classe, count_seats_map
            )
            if not remanejou_acima:
                # não tem mais remanejamento pra fazer
                break

        if mudar_travel_para_classe(travel, new_classe, trechos_classe, count_seats_map):
            return True
    return False


def downgrade_para_classe_mais_proxima(
    travel: Travel,
    classes: list[dict[str, Any]],
    trechos_classe: list[TrechoClasse],
    count_seats_map: dict[tuple[int, str], int],
) -> bool:
    tipo_assento_idx = TIPOS_ASSENTO.index(travel.grupo_classe.tipo_assento)
    lower_classes = [c for c in classes if get_class_idx(c) < tipo_assento_idx]
    for classe in reversed(lower_classes):
        if mudar_travel_para_classe(travel, classe, trechos_classe, count_seats_map):
            return True
    return False


def mudar_travel_para_classe(
    travel: Travel,
    new_classe: dict[str, Any],
    trechos_classe: list[TrechoClasse],
    count_seats_map: dict[tuple[int, str], int],
) -> bool:
    new_classe_obj = new_classe["classe"]
    new_trecho_classe = new_classe["trechos"][travel.trecho_vendido_id][0]

    if hasattr(travel, "new_classe") and travel.new_classe == new_classe:
        return True

    if travel.status == "canceled":
        # Travel cancelada não depende de ter vagas.
        travel.grupo_classe = new_classe_obj
        travel.trecho_classe = new_trecho_classe
        return True

    count_seats_map[(travel.trecho_vendido_id, new_classe["tipo_assento"])] += travel.count_seats
    capacity_manager = multitrecho_svc.prepare_capacity_manager(travel.grupo.rota_id, trechos_classe, count_seats_map)
    if capacity_manager.overbooking():  # verifica se a mudança vai causar overbooking
        count_seats_map[(travel.trecho_vendido_id, new_classe["tipo_assento"])] -= travel.count_seats
        return False

    if hasattr(travel, "new_classe"):
        # Ajusta travel que já tinha sido mudada de classe.
        travel.grupo_classe.pessoas -= travel.count_seats
        travel.trecho_classe.pessoas -= travel.count_seats
        travel.new_classe["travels"].remove(travel)
        count_seats_map[(travel.trecho_vendido_id, travel.new_classe["tipo_assento"])] -= travel.count_seats

    for trecho_classe in trechos_classe:
        trecho_classe.vagas = capacity_manager.vagas(trecho_classe)

    travel.grupo_classe = new_classe_obj
    travel.trecho_classe = new_trecho_classe
    travel.new_classe = new_classe
    new_classe_obj.pessoas += travel.count_seats
    new_trecho_classe.pessoas += travel.count_seats
    new_classe["travels"].append(travel)

    return True


def _validate_classes(
    grupo: Grupo,
    dclasses: list[dict[str, Any]],
    g_capacity_manager: CapacityManager,
) -> None:
    if grupo.status not in {Grupo.Status.PENDING, Grupo.Status.TRAVEL_CONFIRMED}:
        raise ValidationError("Só pode mudar grupo pending ou confirmed")

    new_capacidade = sum([int(c["max_capacity"]) for c in dclasses])

    if new_capacidade < max(g_capacity_manager.ocupacao_por_trecho_vendido().values()):
        raise ValidationError("Tem mais passageiros que vagas!")

    # Não pode ter tipo de assento duplicado
    tipos_assento = [c["tipo_assento"] for c in dclasses]
    if len(set(tipos_assento)) != len(tipos_assento):
        raise ValidationError("tipo_assento duplicado. Simplifique as classes")

    # Validar trechos vendidos (não duplicado e não faltando)
    trecho_vendido_ids = [tv.id for tv in grupo.rota.get_trechos_vendidos()]
    for c in dclasses:
        for tv_id in trecho_vendido_ids:
            trechos_respectivos = [t for t in c["trechos"] if t["id"] == tv_id]
            if len(trechos_respectivos) != 1:
                raise Exception("Tá faltando ou sobrando trecho vendido")


def get_class_idx(c):
    return TIPOS_ASSENTO.index(c["tipo_assento"])


def _classes_novas_por_tipo_assento(
    grupo: Grupo,
    old_classes: list[GrupoClasse],
    dclasses: list[dict[str, Any]],
    trechos_vendidos_map: dict[int, TrechoVendido],
) -> list[dict[str, Any]]:
    dclasses_map = {}
    for dclasse in dclasses:
        dclasses_map.setdefault(dclasse["tipo_assento"], dclasse)

    new_classes = []
    grupo_ta_fechado = all(gc.closed for gc in old_classes)
    # Precisa seguir a ordem de TIPOS_ASSENTO.
    for tipo_assento in TIPOS_ASSENTO:
        dclasse = dclasses_map.get(tipo_assento)
        if not dclasse:
            continue

        parametros_fechamento, closed_by_id = _parametros_fechamento_classe(old_classes, tipo_assento, grupo_ta_fechado)
        dclasse.update(parametros_fechamento)
        classe, trechos_classes = grupo_crud_svc.create_grupo_e_trecho_classe(
            grupo,
            dclasse,
            dclasse["trechos"],
            save=False,
            trechos_vendidos_map=trechos_vendidos_map,
            closed_by_id=closed_by_id,
        )
        new_classes.append({"classe": classe, "tipo_assento": tipo_assento, "travels": [], "trechos": trechos_classes})
    return new_classes


def _parametros_fechamento_classe(
    gcs_do_grupo: list[GrupoClasse], tipo_assento: str, grupo_ta_fechado: bool
) -> tuple[dict[str, str | bool | None], int | None]:
    gcs_tipo_assento = [gc for gc in gcs_do_grupo if gc.tipo_assento == tipo_assento]
    gc = gcs_tipo_assento[0] if gcs_tipo_assento else None

    closed_reason = None
    closed_by_id = None
    closed = False
    if gc:
        closed = gc.closed
        closed_reason = gc.closed_reason
        closed_by_id = gc.closed_by_id
    elif grupo_ta_fechado:
        closed = True
        # Copia o closed_reason de qualquer grupo classe para manter o tracking
        closed_reason = gcs_do_grupo[0].closed_reason

    return {"closed": closed, "closed_reason": closed_reason}, closed_by_id


def _get_travels_por_prioridade(grupo: Grupo) -> list[Any]:
    travels = list(
        grupo.travel_set.all()
        .select_related("grupo_classe")
        .prefetch_related(
            Prefetch("passageiro_set", queryset=Passageiro.objects.filter(removed=False)),
        )
    )

    travels.sort(reverse=True, key=lambda t: (TIPOS_ASSENTO.index(t.grupo_classe.tipo_assento), t.count_seats))
    return travels


def dict_pagamento_estornavel_devolucao_info(pe):
    return {
        "estorno_value": pe.value_estornavel,
        "method": pe.pagamento.method,
        "card_last_digits": pe.pagamento.card_last_digits,
        "card_brand": pe.pagamento.card_brand,
        "account_number": pe.pagamento.account_number,
    }


def mover_buseiros(params: MoverBuseiroForm) -> None:
    remanejamento_async_rodoviaria = globalsettings_svc.get("remanejamento_async_rodoviaria", False)
    seat_controller_manager = SeatControllerManager(FretamentoSeatController)

    with transaction.atomic():
        remanejamentos, deficit_de_vagas = simula_mover_buseiros(params, lock=True)
        try:
            travels = [remanejamento.travel for remanejamento in remanejamentos]
            trechos_classe = _get_tc_from_remanejamentos([params.trecho_classe_id])
            tcd_travel_map = {trechos_classe[params.trecho_classe_id]: travels}
            poltronas_bloqueadas_por_pax_id = _gerar_de_para_poltronas_compradas(
                travels, tcd_travel_map, seat_controller_manager
            )
        except Exception as e:
            # TODO: remover esse try/except quando os testes forem concluidos
            poltronas_bloqueadas_por_pax_id = {}
            sentry_sdk.capture_exception(e)

        if not params.bypass and deficit_de_vagas > 0:
            raise RemanejamentoOverbookingException(deficit_de_vagas)
        if remanejamentos:
            primeiro_remanejamento = remanejamentos[0]
            from_grupo = primeiro_remanejamento.travel.grupo

            rodov_bulk_exception = RodoviariaRemanejamentoBulkException()
            for remanejamento in remanejamentos:
                remanejamento.poltronas_bloqueadas_por_pax_id = poltronas_bloqueadas_por_pax_id
                try:
                    # não executa rodoviária caso o remanejamento async marketplace esteja ativo
                    remanejamento.apply(execute_on_rodoviaria=(not remanejamento_async_rodoviaria))
                except RodoviariaRemanejamentoException as ex:
                    rodov_bulk_exception.resp_list.append(ex.resp)

            if error_str(rodov_bulk_exception):
                raise rodov_bulk_exception

            if remanejamento_async_rodoviaria:
                rodoviaria_reserva_svc.remanejamento_async(
                    primeiro_remanejamento.travel.trecho_classe,
                    primeiro_remanejamento.trecho_classe_destino,
                    remanejamentos,
                )

            to_grupo = primeiro_remanejamento.trecho_classe_destino.grupo
            destino = primeiro_remanejamento.trecho_classe_destino.trecho_vendido.destino
            datetime_orig = from_grupo.datetime_ida.strftime("%d/%m/%Y %H:%M")
            datetime_rem = primeiro_remanejamento.datetime_rem.strftime("%d/%m/%Y %H:%M")
            dist_origem = primeiro_remanejamento.dist_origem
            dist_destino = primeiro_remanejamento.dist_destino

    for remanejamento in remanejamentos:
        buserlogger.info(
            "remanejamento.signal",
            extra={"old_travel_id": remanejamento.travel.id, "new_travel_id": remanejamento.travel_remanejada.id},
        )
    # TODO: remover esse try/except quando os testes forem concluidos
    try:
        _atribui_poltronas_novos_passageiros_do_remanejamento(remanejamentos, seat_controller_manager)
    except Exception as e:
        sentry_sdk.capture_exception(e)

    signals.remanejamento_signal.send(None, remanejamentos=remanejamentos)

    log_svc.log_move_passengers(
        fromgrupo_id=from_grupo.id,
        togrupo=to_grupo,
        to_trecho_classe_id=params.trecho_classe_id,
        travels=[remanejamento.travel for remanejamento in remanejamentos],
        reason="Mover buseiro",
        motivo=params.motivo,
        automatic=False,
        datetime_orig=datetime_orig,
        datetime_rem=datetime_rem,
        dist_origem=dist_origem,
        dist_destino=dist_destino,
        destino_is_desembarque_rapido=destino.desembarque_rapido,
    )


def simula_mover_buseiros(params: MoverBuseiroForm, lock=False) -> tuple[list[RemanejamentoBuseiro], int]:
    # TODO: levar em consideração restrições de desembarque rápido
    travels = Travel.objects.select_related("trecho_classe", "grupo", "grupo_classe")
    if lock:
        travels = travels.select_for_update()

    travels_para_mover = (
        travels.filter(pk__in=params.travel_ids)
        .exclude(status="canceled")
        .select_related("trecho_vendido__origem", "trecho_vendido__destino", "grupo")
    )
    if len(travels_para_mover) == 0:
        raise ValidationError("Sem travels para mover")
    trecho_classe_destino = TrechoClasse.objects.select_related(
        "grupo_classe", "grupo", "trecho_vendido__origem", "trecho_vendido__destino"
    ).get(id=params.trecho_classe_id)
    origem = trecho_classe_destino.trecho_vendido.origem.cidade.sigla
    destino = trecho_classe_destino.trecho_vendido.destino.cidade.sigla
    origem_coords = trecho_classe_destino.trecho_vendido.origem.coords_map
    destino_coords = trecho_classe_destino.trecho_vendido.destino.coords_map

    rodoviaria_svc.simula_mover_buseiro_marketplace(travels_para_mover, trecho_classe_destino)

    reserva_extrato_svc.calcula_custo_alteracao_travel(travels_para_mover, trecho_classe_destino)

    remanejamentos = []
    for travel in travels_para_mover:
        origem_coords_travel = travel.trecho_classe.trecho_vendido.origem.coords_map
        destino_coords_travel = travel.trecho_classe.trecho_vendido.destino.coords_map
        dist_origem = round((geoutils.distance(origem_coords, origem_coords_travel) or 0) / 1000, 2)
        dist_destino = round((geoutils.distance(destino_coords, destino_coords_travel) or 0) / 1000, 2)
        remanejamentos.append(
            RemanejamentoBuseiro(
                travel=travel,
                trecho_classe_destino=trecho_classe_destino,
                custo_alteracao=travel.custo_alteracao,
                receita_gerada_marketplace=travel.receita_gerada_marketplace,
                divergencia_repasse=travel.divergencia_repasse,
                origem_trecho=origem,
                destino_trecho=destino,
                dist_origem=dist_origem,
                dist_destino=dist_destino,
                datetime_orig=travel.grupo.datetime_ida,
                datetime_rem=trecho_classe_destino.grupo.datetime_ida,
                push_inbox_sms_email=params.push_inbox_sms_email,
                whatsapp=params.whatsapp,
                motivo=params.motivo,
            )
        )

    pax_para_mover = sum(travel.count_seats for travel in travels_para_mover)
    capacity_manager = multitrecho_svc.prepare_capacity_manager_from_grupo(trecho_classe_destino.grupo)
    deficit_de_vagas = max(
        pax_para_mover - capacity_manager.vagas(trecho_classe_destino, check_secundario=params.trecho_secundario), 0
    )
    return remanejamentos, deficit_de_vagas


def _create_tcd_travel_map(remanejamentos, travels, trechos_classe):
    # popula tcd_travel_map {tc_destino: [travels]} pra depois calcular o custo_alteracao_travel.
    # pq tem n trechoclasse e pra cada um desses, m travels. Assim consegue resolver o problema de calcular o
    # custo_alteracao de cada travel.
    tcd_travel_map = defaultdict(list)

    for remanejamento in remanejamentos:
        travel = travels.get(remanejamento.travel_id)
        tc = trechos_classe.get(remanejamento.trecho_classe_destino)
        tcd_travel_map[tc].append(travel)

    return tcd_travel_map


@traced("remanejamento_svc.remanejamento_automatico")
def remanejamento_automatico(
    remanejamentos: list[RemanejamentoForm], constraints: FormRemanejamentoConstraints, is_async: bool = False
):
    travels_id = [rem.travel_id for rem in remanejamentos]
    travels_id.sort()
    tcs_ids = [r.trecho_classe_destino for r in remanejamentos]
    travels_remanejamento = TravelsEmRemanejamento(travels_id=travels_id)
    travels_remanejamento.verifica_travel_em_remanejamento()

    # Confirmar que nenhuma Travel está com status cancelado antes de iniciar a operação.
    if Travel.objects.filter(id__in=travels_id, status=Travel.Status.CANCELED).exists():
        raise ValidationError(
            "Não é possível remanejar travels canceladas. Simule novo remanejamento e tente novamente."
        )

    # uma consulta só pra trazer o extrato de todas as travels
    bulk_extrato_travels = accounting_svc.bulk_extrato_travel(travels_id)

    remanejamentos_realizados, logs_move_passenger = _process_remanejamentos(
        travels_remanejamento,
        remanejamentos,
        travels_id,
        tcs_ids,
        bulk_extrato_travels,
        constraints,
        is_async,
    )

    _log_remanejamentos(remanejamentos_realizados)
    _log_move_passenger(logs_move_passenger)


@traced("remanejamento_svc._process_remanejamentos")
def _process_remanejamentos(
    travels_remanejamento,
    remanejamentos,
    travels_id,
    tcs_ids,
    bulk_extrato_travels,
    constraints,
    is_async,
):
    with transaction.atomic():
        travels_remanejamento.registra_travel_em_remanejamento()
        travels = _get_travels_from_remanejamentos(travels_id)
        trechos_classe = _get_tc_from_remanejamentos(tcs_ids)
        rodov_bulk_exception = RodoviariaRemanejamentoBulkException()

        tcd_travel_map = _create_tcd_travel_map(remanejamentos, travels, trechos_classe)
        seat_controller_manager = SeatControllerManager(FretamentoSeatController)

        try:
            poltronas_bloqueadas_por_pax_id = _gerar_de_para_poltronas_compradas(
                travels, tcd_travel_map, seat_controller_manager
            )
        except Exception as e:
            # TODO: remover esse try/except quando os testes forem concluidos
            poltronas_bloqueadas_por_pax_id = {}
            sentry_sdk.capture_exception(e)

        for tc, travels_list in tcd_travel_map.items():
            reserva_extrato_svc.calcula_custo_alteracao_travel(travels_list, tc, bulk_extrato_travels)

        constraints = constraints.dict()
        logs_move_passenger = []

        remanejamentos_realizados = [
            _process_remanejamento(
                constraints,
                is_async,
                remanejamento,
                rodov_bulk_exception,
                logs_move_passenger,
                travels,
                trechos_classe,
                poltronas_bloqueadas_por_pax_id,
            )
            for remanejamento in remanejamentos
        ]
        # TODO: remover esse try/except quando os testes forem concluidos
        try:
            _atribui_poltronas_novos_passageiros_do_remanejamento(remanejamentos_realizados, seat_controller_manager)
        except Exception as e:
            sentry_sdk.capture_exception(e)

        historicos = HistoricoRemanejamento.objects.bulk_create(rem.historico for rem in remanejamentos_realizados)
        travels_remanejamento.remove_travel_em_remanejamento()

    def _notify_risco_pax_pcd_affected():
        old_travels_ids = [remanejamento.travel.id for remanejamento in remanejamentos_realizados]
        notify_staff_if_pcd_pax_lost_seat.delay("remanejamento", old_travels_ids=old_travels_ids)

    # Não, isso aqui não está identado errado. É on_commit da transaction pra cima dessa.
    transaction.on_commit(_notify_risco_pax_pcd_affected)
    transaction.on_commit(partial(_atualiza_vagas, remanejamentos_realizados=remanejamentos_realizados))
    transaction.on_commit(
        partial(
            _ajusta_contabilidade,
            is_async=is_async,
            historicos=historicos,
            remanejamentos_realizados=remanejamentos_realizados,
        )
    )

    return remanejamentos_realizados, logs_move_passenger


@traced("remanejamento_svc._process_remanejamento")
def _process_remanejamento(
    constraints,
    is_async,
    remanejamento,
    rodov_bulk_exception,
    logs_move_passenger,
    travels,
    trechos_classe,
    poltronas_bloqueadas_por_pax_id={},
):
    travel = travels.get(remanejamento.travel_id)
    grupo_original_id = travel.grupo_id
    tc_original_id = travel.trecho_classe_id
    tc = trechos_classe.get(remanejamento.trecho_classe_destino)

    rem_buseiros = RemanejamentoBuseiro(
        travel=travel,
        trecho_classe_destino=tc,
        custo_alteracao=travel.custo_alteracao,
        whatsapp=remanejamento.whatsapp,
        push_inbox_sms_email=remanejamento.push_inbox_sms_email,
        motivo=remanejamento.motivo,
        reason=remanejamento.reason,
        poltronas_bloqueadas_por_pax_id=poltronas_bloqueadas_por_pax_id,
    )
    remanejamento.datetime_orig = to_default_tz_required(rem_buseiros.antigo_trecho_classe.datetime_ida).strftime(
        "%d/%m/%Y %H:%M"
    )
    remanejamento.datetime_rem = to_default_tz_required(rem_buseiros.trecho_classe_destino.datetime_ida).strftime(
        "%d/%m/%Y %H:%M"
    )
    remanejamento.dist_origem = rem_buseiros.dist_origem
    remanejamento.dist_destino = rem_buseiros.dist_destino
    try:
        rem_buseiros.apply(
            execute_on_rodoviaria=constraints["marketplace"],
            atualiza_vagas=False,
            cria_historico_remanejamento=False,
            is_async=is_async,
            remarcacao_gratuita=remanejamento.remarcacao_gratuita,
        )
    except RodoviariaRemanejamentoException as ex:
        rodov_bulk_exception.resp_list.append(ex.resp)
        if error_str(rodov_bulk_exception):
            raise rodov_bulk_exception

    logs_move_passenger.append(
        MovePassengerForm(
            fromgrupo_id=grupo_original_id,
            togrupo=tc.grupo,
            to_trecho_classe_id=tc.id,
            travels=[travel],
            reason=remanejamento.reason,
            motivo=remanejamento.motivo,
            fromtc_id=tc_original_id,
            automatic=True,
            constraints=constraints,
            datetime_orig=remanejamento.datetime_orig,
            datetime_rem=remanejamento.datetime_rem,
            dist_origem=remanejamento.dist_origem,
            dist_destino=remanejamento.dist_destino,
        )
    )
    return rem_buseiros


def _atualiza_vagas(remanejamentos_realizados):
    grupos_para_atualizar = set()
    for remanejamento in remanejamentos_realizados:
        grupos_para_atualizar.add(remanejamento.travel.grupo)
        grupos_para_atualizar.add(remanejamento.trecho_classe_destino.grupo)
    multitrecho_svc.bulk_atualiza_vagas(list(grupos_para_atualizar))


def _ajusta_contabilidade(is_async, historicos, remanejamentos_realizados):
    if is_async:
        HistoricoRemanejamento.objects.filter(id__in=(hist.id for hist in historicos)).update(
            status=HistoricoRemanejamento.Status.CRIANDO_REGISTROS_CONTABEIS
        )
        return
    signals.remanejamento_signal.send(None, remanejamentos=remanejamentos_realizados)
    HistoricoRemanejamento.objects.filter(id__in=(hist.id for hist in historicos)).update(
        status=HistoricoRemanejamento.Status.CONCLUIDO
    )


def _log_remanejamentos(remanejamentos_realizados):
    for remanejamento in remanejamentos_realizados:
        buserlogger.info(
            "remanejamento.signal",
            extra={"old_travel_id": remanejamento.travel.id, "new_travel_id": remanejamento.travel_remanejada.id},
        )


def _log_move_passenger(logs_move_passenger):
    for log in logs_move_passenger:
        log_svc.log_move_passengers(**log.dict())


def _get_travels_from_remanejamentos(travel_ids: list[int]) -> dict[int, Travel]:
    travels = (
        Travel.objects.filter(id__in=travel_ids)
        .select_related(
            "user",
            "grupo",
            "trecho_classe__grupo",
            "trecho_classe__grupo_classe",
            "trecho_vendido__origem__cidade",
            "trecho_vendido__destino__cidade",
            "trecho_classe__trecho_vendido__origem",
            "trecho_classe__trecho_vendido__destino",
            "grupo_classe__grupo",
            "cupom",
        )
        .prefetch_related("passageiro_set", "taxacancelamentotravel_set")
        .in_bulk()
    )
    return travels


def _get_tc_from_remanejamentos(trecho_classe_ids: list[int]) -> dict[int, TrechoClasse]:
    trechos_classe = (
        TrechoClasse.objects.filter(id__in=trecho_classe_ids)
        .select_related(
            "grupo",
            "grupo_classe",
            "trecho_vendido__origem",
            "trecho_vendido__destino",
            "price_manager",
        )
        .prefetch_related("price_manager__buckets")
        .in_bulk()
    )
    return trechos_classe


def grupo_motivos_remanejamento(user):
    user_rotas = is_rotas(user)

    motivos_dataclass = MOTIVOS_REMANEJAMENTO

    if not user_rotas:
        # remove motivos exclusivos do time de rotas
        motivos_dataclass = MOTIVOS_REMANEJAMENTO.motivos_user_not_rotas()

    reasons = [{"code": motivo.code, "text": motivo.nome} for motivo in motivos_dataclass.motivos]

    return {"motivos": reasons, "default_code": "OFERTA" if user_rotas else None}


def get_travel_remanejada(travel_antiga_id: int) -> Travel | None:
    # volta a NOVA reserva

    try:
        return HistoricoRemanejamento.objects.get(travel_antiga_id=travel_antiga_id).travel_nova
    except HistoricoRemanejamento.DoesNotExist:
        return None
    except HistoricoRemanejamento.MultipleObjectsReturned:
        buserlogger.info("multiple_travels_remanejadas", extra={"travel_antiga_id": travel_antiga_id})
        return None


def get_history_alteracao_placa(group_ids: list) -> tuple:
    overbooking = (
        HistoricoRemanejamento.objects.values(group_id=F("travel_antiga__grupo_id"))
        .annotate(
            date=TruncDate("created_at"),
            total_cost=Sum("custo_alteracao"),
        )
        .filter(
            travel_antiga__grupo_id__in=group_ids,
        )
        .order_by("-date")
    )

    downgrade = (
        RessarcimentoDowngrade.objects.values(group_id=F("passenger__travel__grupo_id"))
        .annotate(
            date=TruncDate("created_at"),
            total_cost=Sum("value"),
        )
        .filter(
            passenger__travel__grupo_id__in=group_ids,
        )
        .order_by("-date")
    )
    return (
        overbooking,
        downgrade,
    )


def _lista_marcacao_de_assento_com_pagamento_info_nas_travels(
    travels_list: list[Travel],
) -> list[MarcacaoAssentoPagoForm]:
    paid_seats_query = (
        Passageiro.objects.filter(
            travel__in=travels_list,
            accountingoperation__source="MARCACAO_ASSENTO",
            poltrona__isnull=False,
            removed=False,
        )
        .annotate(pax_id=F("id"), trecho_classe_id=F("travel__trecho_classe__pk"))
        .values("trecho_classe_id", "travel_id", "poltrona", "pax_id")
    )
    return [MarcacaoAssentoPagoForm(**p) for p in paid_seats_query]


def _busca_xyz_das_poltronas_compradas(marcacao_assentos_pagos_info: list[MarcacaoAssentoPagoForm]) -> dict:
    tcs_travels_poltronas = {}
    dict_tc_poltrona = {}
    query_conditions = []
    pax_poltronas_compradas = []

    # Agrupa dados da marcação por tcs/travel/pax para ser usado no remanejamento e tc e poltrona para a busca de poltrona onibus
    for info in marcacao_assentos_pagos_info:
        dict_tc_poltrona.setdefault(info.trecho_classe_id, set()).add(info.poltrona)
        pax_poltronas_compradas.append(
            {
                "old_travel_poltrona": info.poltrona,
                "x_y_z": None,
                "trecho_classe_id": info.trecho_classe_id,
                "travel_id": int(info.travel_id),
                "pax_id": info.pax_id,
            }
        )

    # monta as condições para a busca de poltronas e tc
    for pk, poltronas in dict_tc_poltrona.items():
        query_conditions.append(Q(onibus__grupo__trechoclasse__id=pk, poltrona__in=poltronas))

    # monta a query para a busca de poltronas onibus
    final_query = Q()
    for condition in query_conditions:
        final_query |= condition

    # busca por poltronaOnibus que possuem poltronas compradas e monta um dict com o trecho_classe_id, poltrona e x_y_z para ser usado no de/para do novo onibus no remanejamento
    trecho_classe_poltronas_xyz = {}

    if not final_query:
        return {}

    for p in (
        PoltronaOnibus.objects.filter(final_query)
        .distinct("poltrona", "andar", "coluna", "linha")
        .annotate(trecho_classe_id=F("onibus__grupo__trechoclasse__id"))
        .values("trecho_classe_id", "poltrona", "andar", "coluna", "linha")
    ):
        tc_id = p.get("trecho_classe_id")
        poltrona = p.get("poltrona")
        x_y_z = f"{p.get('coluna')}_{p.get('linha')}_{p.get('andar')}"
        trecho_classe_poltronas_xyz.setdefault(tc_id, {})
        trecho_classe_poltronas_xyz[tc_id][poltrona] = x_y_z

    # lista os pax com poltronas compradas, busca o x_y_z da poltrona e agrupa a informação por travel
    for pax_poltrona in pax_poltronas_compradas:
        poltrona_x_y_z = trecho_classe_poltronas_xyz.get(pax_poltrona.get("trecho_classe_id"), {}).pop(
            pax_poltrona.get("old_travel_poltrona"), None
        )
        if poltrona_x_y_z:
            pax_poltrona["x_y_z"] = poltrona_x_y_z
            tcs_travels_poltronas.setdefault(pax_poltrona.pop("travel_id"), {}).setdefault(
                pax_poltrona.get("pax_id"), pax_poltrona
            )
    return tcs_travels_poltronas


# TODO quebrar em duas funções quando o controller passar a usar cache na geração de layout,  hoje é feito assim para diminuir a quantidade de queries
def _lista_e_bloqueia_poltronas_disponiveis_por_tc(
    tcd_travel_map: dict[TrechoClasse, list[Travel]],
    dict_tc_pax_poltrona_comprada: dict,
    seat_controller_manager: SeatControllerManager,
) -> dict:
    poltronas_bloqueadas_por_pax_id = {}
    poltronas_disponiveis_por_tc = {}

    for tc, travels_list in tcd_travel_map.items():
        poltronas_to_block = []

        if tc.grupo.modelo_venda != Grupo.ModeloVenda.BUSER or not tc.get_has_marcacao_assento():
            continue

        for t in travels_list:
            pax_info = list(dict_tc_pax_poltrona_comprada.get(t.id, {}).values())
            poltronas_to_block += pax_info
        if not poltronas_to_block:
            continue

        fretamento_seat_controller = seat_controller_manager.get(tc)
        poltronas_disponiveis_por_tc = get_poltronas_disponiveis_por_tc(
            fretamento_seat_controller, [pax.get("pax_id") for pax in poltronas_to_block]
        )
        for pax_info in poltronas_to_block:
            poltrona_to_block = poltronas_disponiveis_por_tc.pop(pax_info.get("x_y_z"), None)
            if poltrona_to_block:
                fretamento_seat_controller.bloquear_poltrona(poltrona_to_block, 15)
                poltronas_bloqueadas_por_pax_id.setdefault(pax_info.get("pax_id"), poltrona_to_block.numero)
                poltronas_bloqueadas_por_pax_id.setdefault(pax_info.get("pax_id"), poltrona_to_block.numero)
    return poltronas_bloqueadas_por_pax_id


def _gerar_de_para_poltronas_compradas(
    travels, tcd_travel_map, seat_controller_manager, marcacao_assentos_pagos_info=None
) -> dict:
    if not marcacao_assentos_pagos_info:
        marcacao_assentos_pagos_info = _lista_marcacao_de_assento_com_pagamento_info_nas_travels(travels)
    dict_tc_pax_poltrona_comprada = _busca_xyz_das_poltronas_compradas(marcacao_assentos_pagos_info)
    poltronas_bloqueadas_por_pax_id = _lista_e_bloqueia_poltronas_disponiveis_por_tc(
        tcd_travel_map, dict_tc_pax_poltrona_comprada, seat_controller_manager
    )

    return poltronas_bloqueadas_por_pax_id


def handle_marcacao_assentos_no_change_class(
    novas_travels,
    travels_antigas,
    downgraded_travel_ids,
    upgraded_travel_ids,
    seat_controller_manager,
    marcacao_assentos_pagos_info,
):
    pax_to_cancel_accops = []
    tc_pax_map_para_atribuir_poltronas = {}
    pax_to_update = []
    pax_to_remove_poltrona = []
    tcd_travel_map = {}
    tc_pax_map_de_para = {}

    old_travels_map = {ta.id: ta for ta in travels_antigas}
    tc_has_marcacao_assento = {}

    for travel in novas_travels:
        old_travel = old_travels_map.get(travel.id)
        if not old_travel:
            continue

        is_upgraded = travel.id in upgraded_travel_ids
        is_downgraded = travel.id in downgraded_travel_ids
        has_class_change = travel.trecho_classe.id != old_travel.trecho_classe.id
        has_bus_change = travel.grupo.onibus != old_travel.grupo.onibus

        if not any([is_upgraded, is_downgraded, has_class_change, has_bus_change]):
            continue

        paxes = travel.passageiro_set.all()

        has_marcacao_assento = tc_has_marcacao_assento.get(travel.trecho_classe.id)

        if has_marcacao_assento is None:
            has_marcacao_assento = (
                travel.trecho_classe.grupo.modelo_venda == Grupo.ModeloVenda.BUSER
                and travel.trecho_classe.get_has_marcacao_assento()
            )
            tc_has_marcacao_assento[travel.trecho_classe.id] = has_marcacao_assento

        if not is_downgraded and has_marcacao_assento:
            # travels para fazer a logica de de-para de poltronas
            tcd_travel_map.setdefault(travel.trecho_classe, []).append(travel)
            tc_pax_map_de_para.setdefault(travel.trecho_classe, []).extend(paxes)
            continue

        needs_cancel = is_downgraded or not has_marcacao_assento

        if needs_cancel:
            pax_to_cancel_accops.extend(paxes)

        if has_marcacao_assento:
            tc_pax_map_para_atribuir_poltronas.setdefault(travel.trecho_classe, []).extend(paxes)
            pax_to_remove_poltrona.extend(paxes)

    poltronas_bloqueadas_por_pax_id = (
        _gerar_de_para_poltronas_compradas(
            novas_travels, tcd_travel_map, seat_controller_manager, marcacao_assentos_pagos_info
        )
        if marcacao_assentos_pagos_info
        else {}
    )

    for tc, paxes in tc_pax_map_de_para.items():
        for pax in paxes:
            nova_poltrona = poltronas_bloqueadas_por_pax_id.get(pax.id)
            if nova_poltrona:
                pax.poltrona = nova_poltrona
                pax_to_update.append(pax)
                continue

            pax_to_cancel_accops.append(pax)
            tc_pax_map_para_atribuir_poltronas.setdefault(tc, []).append(pax)
            pax_to_remove_poltrona.extend(paxes)

    Passageiro.objects.bulk_update(pax_to_update, ["poltrona"])
    _remove_poltronas_passageiros(pax_to_remove_poltrona)

    for tc, pax_list in tc_pax_map_para_atribuir_poltronas.items():
        if not pax_list:
            continue

        if tc.grupo.modelo_venda == Grupo.ModeloVenda.BUSER and tc.get_has_marcacao_assento():
            fretamento_controller = seat_controller_manager.get(tc)
            for pax in pax_list:
                pax.poltrona = None
            try:
                fretamento_controller.atribui_poltronas(passageiros=pax_list)
            except NotEnoughSeats as e:
                sentry_sdk.capture_exception(e)

    cancel_accops_marcacao_assentos(pax_to_cancel_accops)


def _atribui_poltronas_novos_passageiros_do_remanejamento(
    remanejamentos_realizados: list[RemanejamentoBuseiro], seat_controller_manager: SeatControllerManager
):
    tc_novos_pax_map = {}
    for rem in remanejamentos_realizados:
        tc_novos_pax_map.setdefault(rem.trecho_classe_destino, []).extend(rem.novos_passageiros)

    for tc, passageiros in tc_novos_pax_map.items():
        if tc.grupo.modelo_venda == Grupo.ModeloVenda.BUSER and tc.get_has_marcacao_assento() and passageiros:
            seat_controller = seat_controller_manager.get(tc)
            seat_controller.atribui_poltronas(passageiros)


def cancel_accops_marcacao_assentos(pax_to_cancel_accops):
    accops = AccountingOperation.objects.filter(
        passageiro__in=pax_to_cancel_accops,
        source=AccountingOperation.Source.MARCACAO_ASSENTO,
    )

    if accops:
        accounting_svc.cancela_accops(list(accops))


def cancelar_accops_e_remarcar_assentos(travels: list[Travel]):
    seat_controller_manager = SeatControllerManager(FretamentoSeatController)
    pax_to_cancel_accops = []
    tc_paxes_map = {}

    for travel in travels:
        paxes = travel.passageiro_set.all()
        pax_to_cancel_accops.extend(paxes)
        if travel.grupo.modelo_venda == Grupo.ModeloVenda.BUSER and travel.trecho_classe.get_has_marcacao_assento():
            tc_paxes_map.setdefault(travel.trecho_classe, []).extend(paxes)

    cancel_accops_marcacao_assentos(pax_to_cancel_accops)
    _remove_poltronas_passageiros(pax_to_cancel_accops)

    for tc, paxes in tc_paxes_map.items():
        for pax in paxes:
            pax.poltrona = None
        seat_controller = seat_controller_manager.get(tc)
        seat_controller.atribui_poltronas(paxes)


def _remove_poltronas_passageiros(pax_to_remove_poltrona: list[Passageiro]):
    Passageiro.objects.filter(id__in=[pax.id for pax in pax_to_remove_poltrona]).update(poltrona=None)
