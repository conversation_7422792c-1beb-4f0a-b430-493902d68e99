import logging
import random
from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal as D
from json.decoder import <PERSON><PERSON>NDecodeError
from typing import Iterable

import requests
from beeline import traced
from celery import shared_task
from celery.utils.log import get_task_logger
from dateutil.parser import parse
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db.models import Count, F, Prefetch, QuerySet, prefetch_related_objects
from django.db.models.query_utils import Q
from django.dispatch import receiver
from django.utils import timezone
from sentry_sdk.api import capture_exception

from commons.dateutils import timedelta_to_hours_minutes_str, to_tz, today_midnight
from commons.django_celery_utils import my_shared_task
from commons.django_utils import error_str
from commons.memoize import memoize, memoize_local
from commons.utils import chunks, pluralize
from core import signals
from core.forms.buckets_forms import TrechoClasseForm
from core.forms.rodoviaria_forms import ComprarForm
from core.forms.staff_forms import (
    CheckPaxForm,
    CheckPaxMultipleForm,
    CriarGruposMarketplaceForm,
    ListaEmpresasAPIParams,
    ListaFormasPagamentoRodoviariaForm,
    RodoviariaListarTiposAssentosParams,
    UpdateLinkLocalForm,
)
from core.models_company import Company
from core.models_grupo import Grupo, GrupoClasse, TrechoClasse
from core.models_rota import Checkpoint, Cidade, LocalEmbarque, LocalRetiradaMarketplace, Rota, TrechoVendido
from core.models_travel import Buseiro, Passageiro, Travel
from core.serializers.serializer_locais_retirada import LocaisRetiradaSerializer
from core.service import (
    globalsettings_svc,
    itinerario_svc,
    locais_retirada_svc,
    log_svc,
    preco_svc,
    rodoviaria_link_trecho_classe_svc,
)
from core.service.grupos_staff import grupo_crud_svc
from core.service.itinerario_dinamico_svc import ItinerarioDinamico
from core.service.rodoviaria import bpe_svc, hibrido_svc
from core.service.rodoviaria_client import (
    RodoviariaDeprecatedClient,
)
from integrations import get_client
from integrations.rodoviaria_client import RodoviariaClient
from integrations.rodoviaria_client.exceptions import (
    AutorizacaoNaoCadastradaRodoviaria,
    FetchTrechosVendidosError,
    PoltronaExpiradaException,
    PoltronaTrocadaException,
    RodoviariaCancelamentoException,
    RodoviariaClientError,
    RodoviariaConnectionException,
    RodoviariaException,
    RodoviariaMoverBuseiroException,
    RodoviariaNotBlockingException,
    RodoviariaOverbooking,
    RodoviariaViagemBloqueada,
    RotaNaoCadastradaRodoviaria,
)
from marketplace.models import TrechoClasseMarketplaceLogger, TrechoEstoqueManager
from marketplace.services.create_group_svc import get_estoque_manager
from marketplace.services.trechoclasse_logger_svc import log_change_trechos_classes_in_bulk
from search_result.adapter.cidade_adapter import CidadeAdapter
from search_result.adapter.local_embarque_adapter import LocalEmbarqueAdapter

HARD_STOP_PREFIX = "Hard Stop"
DUPLICADO_NOVO_ESTOQUE_CLOSED_REASON = "[HOT_UPDATE_FOUND] Duplicado pelo hot update"
LOG_SIMULACAO_REMANEJAMENTO = (
    "SIMULAÇÂO REMANEJAMENTO RODOVIÁRIA (trecho_classe_destino={trecho_classe_id}, numero_pax={numero_pax})"
)

buserlogger = logging.getLogger("buserlogger")
task_logger = get_task_logger("rodoviaria")
_client = RodoviariaDeprecatedClient(settings.RODOVIARIA_API_URL)


def _is_prod():
    return globalsettings_svc.get("rodoviaria_prod", False)


def _is_marketplace(grupo):
    return grupo.modelo_venda == Grupo.ModeloVenda.MARKETPLACE


def _is_integrado_marketplace_or_hibrido_roderotas(grupo):
    return _is_integrado(grupo=grupo) and (
        grupo.is_marketplace or hibrido_svc.is_grupo_hibrido_integrado_rode_rotas(grupo)
    )


@memoize(timeout=2 * 60)
def _is_integrado(trechoclasse_id=None, grupo=None, only_marketplace=False):
    if not _is_prod():
        return False

    if not trechoclasse_id and not grupo:
        return False  # ValueError?

    try:
        if isinstance(grupo, int):
            grupo = Grupo.objects.get(pk=grupo)
        elif trechoclasse_id:
            grupo = Grupo.objects.get(trechoclasse=trechoclasse_id)
    except Grupo.DoesNotExist:
        return False

    if grupo.modelo_venda not in {Grupo.ModeloVenda.MARKETPLACE, Grupo.ModeloVenda.HIBRIDO}:
        return False
    if only_marketplace and grupo.modelo_venda != Grupo.ModeloVenda.MARKETPLACE:
        return False

    company_id = grupo.company_id
    modelo_venda = grupo.modelo_venda

    client: RodoviariaClient = get_client("rodoviaria")
    try:
        empresas_integradas = client.get_empresas_integradas()["empresas"]
    except client.exceptions.ClientError:
        return False
    company_id = hibrido_svc.company_id(grupo)
    return any(
        company_id == empresa["company_internal_id"] and modelo_venda == empresa["modelo_venda"]
        for empresa in empresas_integradas
    )


def rotinas_integradas_hibrido_list():
    client: RodoviariaClient = get_client("rodoviaria")
    map_rotinas_integradas_hibrido = client.map_rotinas_integradas()
    empresas_integradas = client.get_empresas_integradas()["empresas"]
    rotinas_integradas_list = []
    for company_id, rotinas in map_rotinas_integradas_hibrido.items():
        if any(
            int(company_id) == int(empresa["company_internal_id"])
            and Grupo.ModeloVenda.HIBRIDO == empresa["modelo_venda"]
            for empresa in empresas_integradas
        ):
            rotinas_integradas_list.extend(rotinas)
    return rotinas_integradas_list


@memoize(timeout=2 * 60)
def is_integrado_and_marketplace(trechoclasse=None, grupo=None):
    if not grupo and trechoclasse:
        grupo = trechoclasse.grupo
    return _is_marketplace(grupo) and _is_integrado(grupo=grupo)


@memoize(timeout=2 * 60)
def _is_integrado_and_hibrido(trechoclasse=None, grupo=None):
    if not grupo and trechoclasse:
        grupo = trechoclasse.grupo
    return grupo.is_hibrido and _is_integrado(grupo=grupo)


@traced("rodoviaria_svc.dados_bpe_passagem_batch")
def dados_bpe_passagem_batch(grupo):
    # usado só pelo modelo híbrido e se é modelo híbrido tá integrado
    # if not _is_integrado(trechoclasse_id):
    #     return
    # atualmente não filtramos apenas por reservas com status pending. Faz sentido?
    travels_ids = Travel.objects.filter(grupo_id=grupo.id).values_list("id", flat=True)

    batch_response = _client.dados_bpe_passagem_batch(list(travels_ids))
    return bpe_svc.serialize_objects_grupo(grupo.id, batch_response)


def get_map_poltronas(trechoclasse_id):
    return _client.get_map_poltronas(trechoclasse_id)


def _fecha_trecho(trechoclasse_id, message, vagas=None):
    trecho_classe = TrechoClasse.objects.select_related(
        "grupo",
        "grupo_classe",
        "trecho_vendido__origem__cidade",
        "trecho_vendido__destino__cidade",
    ).get(pk=trechoclasse_id)
    if vagas is not None:
        trecho_classe.vagas = vagas
    trecho_classe.closed_reason = message
    trecho_classe.closed = True
    trecho_classe.closed_at = timezone.now()
    trecho_classe.closed_by = None
    trecho_classe.save(
        update_fields=[
            "vagas",
            "closed",
            "closed_reason",
            "closed_at",
            "closed_by",
            "updated_on",
        ]
    )
    log_svc.log_fechamento_trechos([trecho_classe], message, log_type="integracao_rodoviaria_fechamento_trecho")


def _abre_trecho(trechoclasse_id, vagas=None):
    trecho_classe = TrechoClasse.objects.get(pk=trechoclasse_id)
    if not trecho_classe.closed:
        return
    if RodoviariaOverbooking.message not in trecho_classe.closed_reason:
        return
    if vagas is not None:
        trecho_classe.vagas = vagas
    trecho_classe.closed_reason = None
    trecho_classe.closed = False
    trecho_classe.closed_at = None
    trecho_classe.closed_by = None
    trecho_classe.save(
        update_fields=[
            "vagas",
            "closed",
            "closed_reason",
            "closed_at",
            "closed_by",
            "updated_on",
        ]
    )


def lista_passageiros_viagem(grupo_id):
    passageiros = _lista_passageiros_buser(grupo_id)
    passageiros_map = {}
    travels_ids = []
    for passageiro in passageiros:
        passageiros_map[passageiro.buseiro_id] = passageiro
        travels_ids.append(passageiro.travel_id)
    if not travels_ids:
        return []
    lista_passagens_rodoviaria = _client.get_passageiros(travels_ids)

    passageiros_buser = []
    for passagem in lista_passagens_rodoviaria:
        passageiro = passageiros_map[passagem["buseiro_id"]]
        passageiros_buser.append(
            {
                "nome": passageiro.buseiro.name,
                "origem": passagem.get("origem") or passageiro.travel.trecho_classe.trecho_vendido.origem.cidade.name,
                "destino": passagem.get("destino")
                or passageiro.travel.trecho_classe.trecho_vendido.destino.cidade.name,
                "id_origem": passageiro.travel.trecho_classe.trecho_vendido.origem.cidade_id,
                "id_destino": passageiro.travel.trecho_classe.trecho_vendido.destino.cidade_id,
                "cpf": passageiro.buseiro.cpf,
                "documento": passageiro.buseiro.rg_number,
                "poltrona": passagem["poltrona"],
                "localizador": passagem["localizador"],
                "passagem": passagem["numero_passagem"],
                "status": passagem["status"],
                "travel_id": passagem["travel_id"],
                "buseiro_id": passagem["buseiro_id"],
            }
        )
    return passageiros_buser


@memoize_local(timeout=60)
def _lista_passageiros_buser(grupo_id: int) -> list[Passageiro]:
    return list(
        Passageiro.objects.filter(travel__grupo_id=grupo_id).select_related(
            "buseiro__user__profile",
            "travel__trecho_classe__trecho_vendido__origem__cidade",
            "travel__trecho_classe__trecho_vendido__destino__cidade",
        )
    )


def emitir_passagens_pendentes(grupo, *, passageiros_grupo, segunda_tentativa=False):
    passageiros_buser = passageiros_grupo
    passageiros_buser_map = {}
    travels_ids = set()
    error = False
    for p_buser in passageiros_buser:
        passageiros_buser_map[(p_buser.travel_id, p_buser.buseiro_id)] = p_buser
        travels_ids.add(p_buser.travel_id)
    if travels_ids:
        passageiros_rodoviaria = _client.get_passageiros(list(travels_ids))
        for p_rodoviaria in passageiros_rodoviaria:
            passageiros_buser_map.pop((p_rodoviaria["travel_id"], p_rodoviaria["buseiro_id"]), None)
        for passageiro_pendente in passageiros_buser_map.values():
            retorno = add_pax_na_lista_por_passageiro(passageiro_pendente)
            if "error_type" in retorno:
                error = retorno
                break
        erro_sem_vagas = error and error["error_type"] in [
            "service_not_found",
            "overbooking",
        ]
        horario_retry = grupo.datetime_ida - timedelta(hours=2, minutes=40) > timezone.now()
        if erro_sem_vagas and horario_retry and not segunda_tentativa:
            cadastrar_grupos_hibridos_rodoviaria(grupo.company_id, [grupo.id])
            emitir_passagens_pendentes(grupo, passageiros_grupo=passageiros_grupo, segunda_tentativa=True)


def add_pax_na_lista_por_passageiro(passageiro):
    params = CheckPaxForm.parse_obj(
        {
            "trechoclasseId": passageiro.travel.trecho_classe_id,
            "travelId": passageiro.travel_id,
            "valorPorBuseiro": passageiro.travel.max_split_value,
            "passenger": {
                "buseiroId": passageiro.buseiro_id,
                "name": passageiro.buseiro.name,
                "cpf": passageiro.buseiro.cpf,
                "rgNumber": passageiro.buseiro.rg_number,
                "phone": passageiro.buseiro.phone or passageiro.buseiro.user.profile.cell_phone,
                "buyer_cpf": passageiro.buseiro.user.profile.cpf,
            },
            "idOrigem": passageiro.travel.trecho_classe.trecho_vendido.origem_id,
            "idDestino": passageiro.travel.trecho_classe.trecho_vendido.destino_id,
        }
    )
    return add_pax_na_lista(params.dict(), listar_passageiros=False)


def tenta_atualizar_trechos_do_grupo_para_itinerario(grupo_id, max_tentativas):
    trechos_classe = (
        TrechoClasse.objects.prefetch_related("price_manager__buckets")
        .filter(grupo_id=grupo_id)
        .order_by("datetime_ida")[:max_tentativas]
    )

    tentativas = 0
    for tc in trechos_classe:
        link_response = update_status_integracao_trecho_classe(trecho_classe=tc)
        link_status = link_response.get(str(tc.id))["status"]
        if link_status == "integracao_ok" or "integracao_price_error" in link_status:
            break
        tentativas += 1
        if tentativas == max_tentativas:
            return False
    else:
        return False
    return True


def get_status_integracao_grupo(grupo_id, trecho_classe_id):
    status_rodoviaria = _client.get_trecho_classe_integracao(grupo_id, trecho_classe_id)
    map_trechos = _get_map_trechos(grupo_id, trecho_classe_id)
    return check_status_integracao_trechos(
        status_rodoviaria,
        map_trechos_classe=map_trechos,
        gatilho_atualizacao=TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL,
        atualiza_trechos=False,
    )


def update_status_integracao_grupo(grupo_id):
    response = _client.update_trecho_classe_integracao(grupo_id=grupo_id)
    return response


def update_status_integracao_trecho_classe(trecho_classe: TrechoClasse):
    status_rodoviaria = _client.update_trecho_classe_integracao(trecho_classe_id=trecho_classe.id)

    return check_status_integracao_trechos(
        status_rodoviaria,
        map_trechos_classe={trecho_classe.id: trecho_classe},
        gatilho_atualizacao=TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL,
        atualiza_trechos=True,
    )


@traced("rodoviaria_svc.atualizar_trechos_classes")
def atualizar_trechos_classes(
    trechos_classes: Iterable[TrechoClasse],
    gatilho_atualizacao: TrechoClasseMarketplaceLogger.GatilhoAtualizacao,
):
    log_change_trechos_classes_in_bulk(trechos_classes, gatilho_atualizacao)

    TrechoClasse.objects.bulk_update(
        trechos_classes,
        [
            "updated_on",
            "closed",
            "closed_reason",
            "closed_at",
            "closed_by",
            "max_split_value",
            "ref_split_value",
            "vagas",
            "price_manager_id",
            "extra",
        ],
        batch_size=150,
    )


def check_status_integracao_trechos(
    status_rodoviaria,
    map_trechos_classe: dict[int, TrechoClasse],
    gatilho_atualizacao: TrechoClasseMarketplaceLogger.GatilhoAtualizacao,
    atualiza_trechos=False,
):
    trechos_status = {}
    price_managers, price_logs, price_buckets = [], [], []
    for tc_id, status in status_rodoviaria.items():
        tc_buser = map_trechos_classe.get(int(tc_id))
        if not tc_buser:
            continue
        atualiza_preco, atualiza_vagas = get_atualizacoes_feitas_por_grupo(tc_buser.grupo)
        status["status_details"] = ""
        tc_buser.extra["mkp_last_synced_at"] = timezone.now()
        if atualiza_trechos and not atualiza_preco:
            tag_not_found = rodoviaria_link_trecho_classe_svc.Tags.Closed.NOT_FOUND
            if status["status"] == "integracao_ok":
                if tc_buser.closed and tc_buser.closed_reason and tag_not_found in tc_buser.closed_reason:
                    _update_trecho_grupo_open_trecho(tc_buser)
                if atualiza_vagas:
                    _, status_details_vagas = update_trecho_grupo_update_vagas(
                        tc_buser, int(status["vagas"]), gatilho_atualizacao
                    )
                    status["status_details"] = status_details_vagas
            if status["status"] == "integracao_nao_encontrada" and not tc_buser.closed:
                _update_trecho_grupo_close_trecho(
                    tc_buser, f"{tag_not_found} {status.get('error', 'Serviço não encontrado na API')}"
                )
                status["status_details"] += "(trecho fechado)"
        elif atualiza_trechos and _is_marketplace(tc_buser.grupo):
            # atualiza trecho com base nos dados do status e detalha alteracoes no campo status_details
            _atualiza_dados_trecho_e_status_details(tc_buser, status, atualiza_vagas, gatilho_atualizacao)
            buckets = preco_svc.get_active_buckets(tc_buser)
            price_manager, price_bucket, price_log = preco_svc.determine_bucket_update(tc_buser, buckets)
            price_buckets += price_bucket
            price_logs += price_log
            tc_buser.price_manager = price_manager
            price_managers.append(price_manager)
        else:
            if status["status"] == "integracao_ok":
                preco_rodoviaria = D(str(status["preco_rodoviaria"]))
                if tc_buser.max_split_value != preco_rodoviaria:
                    status["status"] = f"integracao_price_error (R${preco_rodoviaria})"

        trechos_status[tc_id] = {}
        trechos_status[tc_id]["status"] = status["status"]
        trechos_status[tc_id]["status_details"] = status["status_details"]
        trechos_status[tc_id]["closed"] = tc_buser.closed
        trechos_status[tc_id]["closed_reason"] = tc_buser.closed_reason
        trechos_status[tc_id]["vagas"] = tc_buser.vagas
        trechos_status[tc_id]["max_split_value"] = tc_buser.max_split_value
        trechos_status[tc_id]["ref_split_value"] = tc_buser.ref_split_value
        trechos_status[tc_id]["error"] = status.get("error")

    if atualiza_trechos:
        preco_svc.bulk_update_price_objects(price_managers, price_buckets, price_logs)
        for tc in map_trechos_classe.values():
            tc.price_manager_id = tc.price_manager.id
        atualizar_trechos_classes(list(map_trechos_classe.values()), gatilho_atualizacao)

    return trechos_status


def _get_map_trechos(grupo_id=None, trecho_classe_id=None):
    filter = None
    if trecho_classe_id:
        filter = Q(id=trecho_classe_id)
    elif grupo_id:
        filter = Q(grupo_id=grupo_id)

    if filter is None:
        return {}

    return (
        TrechoClasse.objects.select_related("grupo").prefetch_related("price_manager__buckets").filter(filter).in_bulk()
    )


def _atualiza_dados_trecho_e_status_details(
    tc_buser, status, atualiza_vagas: bool, gatilho_atualizacao: TrechoClasseMarketplaceLogger.GatilhoAtualizacao
):
    tag_not_found = rodoviaria_link_trecho_classe_svc.Tags.Closed.NOT_FOUND
    if status["status"] == "integracao_ok":
        preco_rodoviaria = D(str(status["preco_rodoviaria"]))
        vagas = int(status["vagas"])
        if tc_buser.closed and tag_not_found in tc_buser.closed_reason:
            _update_trecho_grupo_open_trecho(tc_buser)
            status["status_details"] += "(trecho reaberto)"

        if tc_buser.max_split_value != preco_rodoviaria:
            status["status_details"] += f"(preço atualizado de: {tc_buser.max_split_value} para: {preco_rodoviaria})"
            tc_buser.max_split_value = preco_rodoviaria
            tc_buser.ref_split_value = preco_rodoviaria

        if atualiza_vagas:
            _, status_details_vagas = update_trecho_grupo_update_vagas(tc_buser, vagas, gatilho_atualizacao)
            status["status_details"] += status_details_vagas

    elif status["status"] == "integracao_nao_encontrada" and not tc_buser.closed:
        _update_trecho_grupo_close_trecho(
            tc_buser, f"{tag_not_found} {status.get('error', 'Serviço não encontrado na API')}"
        )
        status["status_details"] += "(trecho fechado)"
    elif status["status"] == "erro_api_nao_respondeu":
        status["status_details"] += "(API não respondeu conforme esperado)"
    elif status["status"] == "erro_inesperado":
        status_details = status["error"][:30]
        if len(status["error"]) > 30:
            status_details += "..."
        status["status_details"] += f"({status_details})"


def update_trecho_grupo_update_vagas(
    tc_buser: TrechoClasse,
    vagas: int | None,
    gatilho_atualizacao: TrechoClasseMarketplaceLogger.GatilhoAtualizacao,
    save: bool = False,
) -> tuple[TrechoClasse, str]:
    status_details = ""
    if vagas is not None and tc_buser.vagas != vagas:
        tag_no_seats = rodoviaria_link_trecho_classe_svc.Tags.Closed.NO_SEATS_AVAILABLE
        status_details += f"(vagas atualizadas de: {tc_buser.vagas} para: {vagas})"
        tc_buser.vagas = vagas
        tc_buser.extra["mkp_last_synced_at"] = timezone.now()

        if vagas == 0 and (
            not tc_buser.closed
            or (tc_buser.closed and tc_buser.closed_reason and tag_no_seats in tc_buser.closed_reason)
        ):
            _update_trecho_grupo_close_trecho(tc_buser, f"{tag_no_seats} Sem vagas disponíveis na API")
            status_details += "(trecho fechado)"

        elif tc_buser.closed and tc_buser.closed_reason and tag_no_seats in tc_buser.closed_reason:
            _update_trecho_grupo_open_trecho(tc_buser)
            status_details += "(trecho reaberto)"

    if save:
        atualizar_trechos_classes([tc_buser], gatilho_atualizacao)

    return tc_buser, status_details


def _update_trecho_grupo_close_trecho(tc_buser, reason):
    tc_buser.closed = True
    tc_buser.closed_at = timezone.now()
    tc_buser.closed_reason = reason
    return tc_buser


def _update_trecho_grupo_open_trecho(tc_buser):
    tc_buser.closed = False
    tc_buser.closed_at = None
    tc_buser.closed_reason = None
    return tc_buser


@traced("rodoviaria_svc.update_grupos_classe_rodoviaria")
def update_grupos_classe_rodoviaria(grupos_classes):
    # update_grupos_classe_rodoviaria verifica modelo_venda do grupo. Precisa desse prefetch pra evitar n+1.
    prefetch_related_objects(grupos_classes, "grupo")

    gc_old_id_map = {}
    for gc in grupos_classes:
        gc_old_id_map[gc.OLD_ID] = gc
    grupos_classe_updates = []
    for old_id, gc in gc_old_id_map.items():
        if _is_prod() and gc.grupo.is_hibrido:
            grupos_classe_updates.append({"old_id": old_id, "new_id": gc.id})
    if grupos_classe_updates:
        update_grupos_classe_rodoviaria_task.delay(grupos_classe_updates)


@my_shared_task(queue="rodoviaria")
def update_grupos_classe_rodoviaria_task(grupos_classe_updates):
    task_logger.debug(f"Atualizando link grupo_classe: {str(grupos_classe_updates)}")
    return _client.update_grupos_classe_rodoviaria(grupos_classe_updates)


def cadastrar_trecho_rodoviaria(trecho_classe_id):
    trecho_classe = (
        TrechoClasse.objects.select_related("grupo_classe")
        .select_related("trecho_vendido__origem")
        .select_related("trecho_vendido__destino")
        .select_related("grupo")
        .get(id=trecho_classe_id)
    )
    company_id = trecho_classe.grupo.company_id
    trecho_classe_info_dict = _cadastrar_trecho_dict(trecho_classe)
    return _client.cadastrar_trechos_rodoviaria({"company_id": company_id, "trechos": [trecho_classe_info_dict]})


def cadastrar_trechos_rodoviaria_por_grupo(grupo_id):
    grupo = Grupo.objects.get(id=grupo_id)
    trechos_classe = (
        grupo.trechoclasse_set.select_related("grupo_classe")
        .select_related("trecho_vendido__origem")
        .select_related("trecho_vendido__destino")
        .all()
    )
    trechos_infos = []
    for trecho in trechos_classe:
        trecho_classe_info_dict = _cadastrar_trecho_dict(trecho)
        trechos_infos.append(trecho_classe_info_dict)
    return _client.cadastrar_trechos_rodoviaria({"company_id": grupo.company_id, "trechos": trechos_infos})


def _cadastrar_trecho_dict(trecho_classe):
    tv = trecho_classe.trecho_vendido
    return {
        "cidade_origem_id": tv.origem.cidade_id,
        "cidade_destino_id": tv.destino.cidade_id,
        "local_origem_id": tv.origem.id,
        "local_destino_id": tv.destino.id,
        "classe": trecho_classe.grupo_classe.tipo_assento,
        "max_split_value": trecho_classe.max_split_value,
    }


def update_link_local_embarque(params: UpdateLinkLocalForm):
    response = {}
    if params.editar_link:
        internal_local_id = params.local_embarque_buser_id
        internal_city_id = params.cidade_embarque_buser_id
        if internal_local_id is not None:
            try:
                LocalEmbarque.objects.get(id=internal_local_id)
            except LocalEmbarque.DoesNotExist:
                return {"error": "Erro ao encontrar local de embarque do buser_django"}
        if internal_city_id is not None:
            cidade = Cidade.objects.filter(id=internal_city_id).exists()
            if not cidade:
                return {"error": "Erro ao encontrar cidade de embarque do buser_django"}
        response = _client.update_link_local_embarque(internal_local_id, params.link_id, internal_city_id)
        if isinstance(response, dict) and "error" in response:
            return response
    if params.local_retirada:
        if params.local_retirada.id:
            locais_retirada_svc.edit_local_retirada_marketplace(params.local_retirada)
        else:
            locais_retirada_svc.create_local_retirada_marketplace(params.local_retirada)
        if not response:
            response = {"sucesso": "Local de retirada cadastrado"}
    return response


def list_links_locais_embarque(params):
    sort_by_local_retirada = False
    if params["paginator"]["sortBy"] == "local_retirada":
        params["paginator"]["sortBy"] = None
        sort_by_local_retirada = True
    links = _client.list_links_local_embarque(params)
    locais_buser_ids = set()
    companies_ids = set()

    for link in links.get("items"):
        if link.get("local_embarque_buser"):
            locais_buser_ids.add(link["local_embarque_buser"])
        if link.get("empresa_id"):
            companies_ids.add(link["empresa_id"])
    empresas = Company.objects.in_bulk(list(companies_ids))

    links["items"] = [link for link in links["items"] if link.get("empresa_id") in empresas]

    locais_buser = LocalEmbarque.objects.in_bulk(list(locais_buser_ids))
    serializer = LocaisRetiradaSerializer()
    locais_retirada = {}
    if empresas:
        for empresa in empresas.values():
            locais_retirada[empresa.id] = {
                lr.local_embarque_id: lr
                for lr in LocalRetiradaMarketplace.objects.to_serialize(serializer).filter(
                    local_embarque_id__in=list(locais_buser_ids),
                    company_id=empresa,
                )
            }
    for link in links.get("items"):
        if link.get("buser_cidade_id"):
            city_id = link.pop("buser_cidade_id")
            cidade_embarque_buser = Cidade.objects.get(id=city_id)
            link["buser_cidade"] = cidade_embarque_buser.to_dict_json()
            link["buser_cidade"]["ativa"] = cidade_embarque_buser.ativo
        if link.get("local_embarque_buser"):
            local_embarque_buser = locais_buser[link["local_embarque_buser"]]
            if (
                locais_retirada
                and locais_retirada.get(link.get("empresa_id"))
                and locais_retirada.get(link.get("empresa_id")).get(link["local_embarque_buser"])
            ):
                local_retirada = locais_retirada[link["empresa_id"]][link["local_embarque_buser"]]
                link["local_retirada"] = local_retirada.serialize()
            link["local_embarque_buser"] = local_embarque_buser.to_dict_json()
        if link.get("empresa_id"):
            company = empresas[link["empresa_id"]]
            link["empresa"] = company.to_dict_json()
            link["empresa"]["company_rodoviaria_id"] = link["empresa_rodoviaria_id"]
    if sort_by_local_retirada:
        links["items"] = sorted(
            links.get("items"),
            key=lambda local: f"{local['local_retirada']['tipo']}" if local.get("local_retirada") else "",
            reverse=params["paginator"]["descending"],
        )
    return links


def efetua_cancelamento(travel, cancel_strategy_type=""):
    extra_log = {"travel_id": travel.id}
    buserlogger.error("rodoviaria_svc.efetua_cancelamento", extra=extra_log)
    if not _is_prod():
        return

    grupo = Grupo.objects.get(trechoclasse=travel.trecho_classe_id)
    if grupo.modelo_venda not in {Grupo.ModeloVenda.MARKETPLACE, Grupo.ModeloVenda.HIBRIDO}:
        return

    _raise_for_antecedencia_cancelamento(travel)

    if not _is_integrado(travel.trecho_classe_id):
        return

    cancelamento_log = "[RODOVIARIA CANCELAMENTO]"
    try:
        resp = _client.efetua_cancelamento(travel.id)
        _abre_trecho(travel.trecho_classe_id)
    except RodoviariaNotBlockingException as exc:
        log_svc.log_cancel_travel_by_user_request_error_marketplace(travel, str(exc))
        buserlogger.warning(
            f"{cancelamento_log} erro não bloqueante ao tentar cancelar passagens. Erro: {error_str(exc)}",
            extra=extra_log,
        )
        return
    except (RodoviariaConnectionException, requests.Timeout) as exc:
        _solicita_cancelamento(travel.trecho_classe_id, travel.id)
        log_svc.log_cancel_travel_by_user_request_error_marketplace(travel, str(exc))
        return
    except PoltronaTrocadaException as exc:
        log_svc.log_cancel_travel_by_user_request_error_marketplace(travel, error_str(exc), type=exc.type)
        raise
    except Exception as exc:
        log_svc.log_cancel_travel_by_user_request_error_marketplace(travel, error_str(exc))
        if "UNPAID" in cancel_strategy_type:
            buserlogger.warning(
                f"{cancelamento_log} Erro ao cancelar passagem com pagamento cancelado. Disparado cancelamento assíncrono. Erro: {error_str(exc)}",
                extra=extra_log,
            )
            _solicita_cancelamento(travel.trecho_classe_id, travel.id)
            return
        buserlogger.error(f"{cancelamento_log} erro ao cancelar passagens. Erro: {error_str(exc)}", extra=extra_log)
        raise
    buserlogger.info(f"{cancelamento_log} cancelado com sucesso", extra=extra_log)
    return resp


def _raise_for_antecedencia_cancelamento(travel):
    """Em geral empresas rodoviárias possuem antecedência miníma de 3 horas para cancelamento.
    Algumas utilizam outros periodos e encapsulamos nesse método essas diferenças"""
    EMPRESAS_HORARIO_CUSTOM = {
        323: 12,  # Viação Sertaneja
        6227: 12,  # Bus Transportes
    }
    ANTECEDENCIA_GERAL_HORAS = 3
    horas_antecedencia = EMPRESAS_HORARIO_CUSTOM.get(travel.trecho_classe.grupo.company_id, ANTECEDENCIA_GERAL_HORAS)

    prazo_maximo_cancelamento = travel.trecho_classe.datetime_ida - timedelta(hours=horas_antecedencia)
    acima_prazo_maximo = timezone.now() > prazo_maximo_cancelamento

    if acima_prazo_maximo:
        # se já não tiver passagens pra cancelar, permite cancelar travel
        tem_passagens_confirmadas = bool(_client.get_passageiros([travel.id]))
        if not tem_passagens_confirmadas:
            return

        raise RodoviariaCancelamentoException(
            f"Passagem só pode ser cancelada no sistema do parceiro até {horas_antecedencia}h antes da partida"
        )


def remover_pax_da_lista(params):
    grupo = params["grupo"]
    travel_id = params["travel_id"]
    buseiro_id = params["buseiro_id"]
    travel = Travel.objects.select_related("grupo", "trecho_classe").get(pk=travel_id)
    passenger = Passageiro.objects.get(travel_id=travel_id, buseiro_id=buseiro_id)
    if travel.grupo.modelo_venda != Grupo.ModeloVenda.HIBRIDO:
        _raise_for_antecedencia_cancelamento(travel)
    pax_valido = not travel.status == "canceled" and not passenger.removed
    response = _client.efetua_cancelamento(travel_id, buseiro_id, pax_valido, handle_response_cancelar=False)
    trechoclasse_id = travel.trecho_classe_id
    _abre_trecho(trechoclasse_id)
    if "error" in response or "warning" in response:
        return response
    return lista_passageiros_viagem(grupo.id)


def handle_rodoviaria_after_error(ex):
    if isinstance(ex, RodoviariaViagemBloqueada):
        _fecha_trecho(ex.trechoclasse_id, "Trecho bloqueado pelo parceiro")


def _handle_exception_efetua_compra(ex, trechoclasse_id):
    if ex.type == "service_not_for_sale":
        msg = f"TrechoClasse {trechoclasse_id} Marketplace sendo fechado por venda estar impedida \
             no sistema do parceiro"
        buserlogger.info(msg)
        raise RodoviariaViagemBloqueada(trechoclasse_id=trechoclasse_id) from ex
    if ex.type == "connection_error":
        raise RodoviariaConnectionException from ex
    if ex.type == "invalid_document":
        raise RodoviariaException(
            message="Documento inválido.", type=ex.type, help="Verifique os documentos cadastrados e tente novamente."
        ) from ex
    if ex.type == "campo_obrigatorio":
        raise RodoviariaException(
            message=ex.message, type=ex.type, help="Verifique os documentos cadastrados e tente novamente."
        ) from ex
    if ex.type == "poltrona_expirada":
        raise PoltronaExpiradaException from ex
    if not ex.type or ex.type == "desconhecido":
        capture_exception(ex)
        raise RodoviariaException(message="Ocorreu um erro", help="Tente novamente em alguns minutos") from ex
    raise ex


def efetua_compra_unica(comprar_form: ComprarForm, timeout=180):
    try:
        return _client.efetua_compra_rodoviaria(
            comprar_form.trechoclasse_id,
            comprar_form.travel_id,
            comprar_form.poltronas,
            comprar_form.valor_cheio,
            [x.dict() for x in comprar_form.buseiros],  # type: ignore TODO: Refatorar o _client.efetua_compra pra receber o form direto
            comprar_form.categoria_especial,
            comprar_form.extra_poltronas,
            timeout=timeout,
        )
    except (JSONDecodeError, RodoviariaClientError) as ex:
        raise RodoviariaException from ex
    except RodoviariaException as ex:
        _handle_exception_efetua_compra(ex, comprar_form.trechoclasse_id)
    except Exception as e:
        capture_exception(e)
        raise e


def _solicita_cancelamento(trechoclasse_id, travel_id):
    if _is_integrado(trechoclasse_id):
        buserlogger.info(
            f"Rodoviaria Error - Algo deu errado na comunicação com o serviço, passagem com travel_id \
                        {travel_id} vai ser marcada como cancelamento pendente"
        )
        _client.solicitar_cancelamento(trechoclasse_id, travel_id)


def solicitar_cancelamento_passagens_por_id(passagens_ids):
    return _client.solicitar_cancelamento_por_ids(passagens_ids)


def add_pax_na_lista(params, listar_passageiros=True):
    response = _client.add_pax_na_lista(params)
    if "error" in response or "warning" in response:
        return response
    if listar_passageiros:
        grupo = TrechoClasse.objects.get(id=params["trechoclasse_id"]).grupo
        return lista_passageiros_viagem(grupo.id)
    return response


def _add_multiple_pax_na_lista_requests(lista_passagens):
    buseiros_ids = {p.buseiro_id for p in lista_passagens}

    passagens_by_trechoclasse = defaultdict(list)
    for p in lista_passagens:
        passagens_by_trechoclasse[p.trechoclasse_id].append(p)

    trechosclasse = TrechoClasse.objects.select_related("grupo").select_related("trecho_vendido")
    trechosclasse = trechosclasse.filter(id__in=list(passagens_by_trechoclasse.keys()))
    trechosclasse = trechosclasse.in_bulk(field_name="id")

    buseiros = Buseiro.objects.select_related("user__profile").filter(id__in=buseiros_ids).in_bulk(field_name="id")

    requests_by_trechoclasse = []
    for tc_id in passagens_by_trechoclasse:
        trechoclasse = trechosclasse[tc_id]
        passagens = passagens_by_trechoclasse[tc_id]
        travels = defaultdict(list)
        travels_prices = defaultdict(int)
        for p in passagens:
            buseiro = buseiros[p.buseiro_id]
            travels_prices[p.travel_id] = p.valor_pago
            travels[p.travel_id].append(
                {
                    "id": buseiro.id,
                    "name": buseiro.name,
                    "cpf": buseiro.cpf or "",
                    "rg_number": buseiro.rg_number,
                    "phone": buseiro.phone or buseiro.user.profile.cell_phone,
                }
            )
        req = {
            "trechoclasse_id": trechoclasse.id,
            "valor_por_buseiro": trechoclasse.max_split_value,
            "id_origem": trechoclasse.trecho_vendido.origem_id,
            "id_destino": trechoclasse.trecho_vendido.destino_id,
            "travels": [
                {
                    "travel_id": travel_id,
                    "valor_por_buseiro": travels_prices[travel_id],
                    "buseiros": buseiros,
                }
                for travel_id, buseiros in travels.items()
            ],
        }
        requests_by_trechoclasse.append(req)
    return (
        requests_by_trechoclasse,
        trechoclasse.grupo.company_id,
        trechoclasse.grupo_id,
    )


def get_linkagem_description(origem_id, company_id):
    linkagem = LocalRetiradaMarketplace.objects.filter(local_embarque_id=origem_id, company_id=company_id).first()

    if not linkagem:
        return None

    return linkagem.descricao_pax


@traced("rodoviaria_svc.dados_bpe_passagem")
def dados_bpe_passagem(travel):
    client: RodoviariaClient = get_client("rodoviaria")
    try:
        rodoviaria_response = client.dados_bpe_passagem(travel.id, travel.trecho_classe_id)
    except client.exceptions.HTTPErrorForbidden:
        return {}
    return bpe_svc.serialize_object(travel.id, rodoviaria_response)


def has_bpe(travel):
    if not _is_prod():
        return
    grupo = travel.grupo
    is_hibrido_com_bpe = _is_integrado(grupo=grupo) and hibrido_svc.is_grupo_hibrido_integrado_rode_rotas(grupo)
    if travel.is_pagamento_ok and (is_integrado_and_marketplace(grupo=grupo) or is_hibrido_com_bpe):
        return _client.has_bpe(travel.id, travel.trecho_classe_id)
    return


def remove_passageiro(travel, passenger):
    if not _is_prod() or not _is_integrado(travel.trecho_classe_id):
        return
    _raise_for_antecedencia_cancelamento(travel)
    pax_valido = not travel.status == "canceled" and not passenger.removed
    try:
        resp = _client.efetua_cancelamento(travel.id, passenger.buseiro_id, pax_valido)
        _abre_trecho(travel.trecho_classe_id)
        return resp
    except RodoviariaNotBlockingException:
        return


def itinerario(grupo_buser):
    itinerario_response = _client.itinerario(grupo_buser.company_id, grupo_buser.id)
    itinerario_rodoviaria = itinerario_response.get("itinerario")
    if not itinerario_rodoviaria:
        return None, None

    itinerario_buser = _get_itinerario_buser(grupo_buser)
    erros = compare_itinerario(itinerario_rodoviaria, itinerario_buser)

    return itinerario_rodoviaria, erros


def _get_itinerario_buser(grupo):
    itinerario = ItinerarioDinamico(grupo, grupo.rota.get_itinerario())
    itinerario = itinerario.to_dict_json()
    itinerario_svc.append_arrival_and_departure_times(itinerario, grupo.datetime_ida)

    return itinerario


def compare_itinerario(itinerario_rodoviaria, itinerario_buser):
    set_buser = {iti["local_id"] for iti in itinerario_buser}
    rodoviaria_map = {c["local_id"]: c for c in itinerario_rodoviaria}
    erros = defaultdict(set)

    for checkpoint_rodoviaria in itinerario_rodoviaria:
        local_id = checkpoint_rodoviaria["local_id"]
        if local_id not in set_buser:
            erros[local_id].add("BUSER_NOT_FOUND")

    for checkpoint_buser in itinerario_buser:
        local_id = checkpoint_buser["local_id"]

        try:
            checkpoint_rodoviaria = rodoviaria_map[local_id]
        except KeyError:
            erros[local_id].add("RODOVIARIA_NOT_FOUND")
        else:
            rodoviaria_last_checkpoint = checkpoint_rodoviaria == itinerario_rodoviaria[-1]
            buser_last_checkpoint = checkpoint_buser["arrival"] and not checkpoint_buser["departure"]
            if rodoviaria_last_checkpoint or buser_last_checkpoint:
                rodoviaria_stop_time = checkpoint_rodoviaria["arrival"]
                if not rodoviaria_last_checkpoint:
                    rodoviaria_stop_time = checkpoint_rodoviaria["departure"]

                if parse(rodoviaria_stop_time) != parse(checkpoint_buser["arrival"]):
                    erros[local_id].add("ARRIVAL_MISMATCH")
            else:
                if checkpoint_buser["departure"] and parse(checkpoint_rodoviaria["departure"]) != parse(
                    checkpoint_buser["departure"]
                ):
                    erros[local_id].add("DEPARTURE_MISMATCH")

    # TODO: Fazer o mapa com o external_local_id para resolver os casos que não
    # existe o checkpoint interno?
    return {local_id: sorted(err) for local_id, err in erros.items() if local_id}


def _get_locais_itinerarios_marketplace(itinerarios):
    ids_locais_buser = {item["local_id"] for sublist in itinerarios["items"] for item in sublist["checkpoints"]}
    locais_buser = list(
        LocalEmbarque.objects.filter(pk__in=ids_locais_buser)
        .annotate(name=F("cidade__name"), uf=F("cidade__uf"))
        .values("id", "name", "uf", "nickname")
    )
    locais_buser = {x["id"]: x for x in locais_buser}
    return locais_buser


def _get_count_grupos_itinerarios_marketplace(itinerarios):
    rota_ids = [r["id_internal"] for r in itinerarios["items"] if r["id_internal"]]
    rows = Grupo.objects.filter(rota_id__in=rota_ids, status__in=["pending", "travel_confirmed"])
    rows = rows.values_list("rota_id").annotate(count=Count("id"))
    rotas_count = {r[0]: r[1] for r in rows}
    return rotas_count


def get_itinerarios_marketplace(company_rodoviaria_id, filters):
    itinerarios = _client.get_itinerarios_marketplace(company_rodoviaria_id, filters)

    locais_buser = _get_locais_itinerarios_marketplace(itinerarios)
    rotas_count = _get_count_grupos_itinerarios_marketplace(itinerarios)
    locais_retirada = _get_locais_retirada(itinerarios["company_internal_id"])

    for itinerario in itinerarios["items"]:
        try:
            itinerario["count_grupos"] = rotas_count[itinerario["id_internal"]]
        except KeyError:
            pass

        tem_todos_locais_retirada = True
        tem_local_linkado = False
        for checkpoint in itinerario["checkpoints"]:
            if checkpoint["local_id"]:
                checkpoint["local"] = locais_buser[checkpoint["local_id"]]
                tem_local_linkado = True
                if locais_retirada.get(checkpoint["local_id"]):
                    checkpoint["local_retirada"] = locais_retirada[checkpoint["local_id"]]
                else:
                    tem_todos_locais_retirada = False
        itinerario["locais_retirada_cadastrados"] = tem_local_linkado and tem_todos_locais_retirada

    return itinerarios


def _get_locais_retirada(company_id):
    serializer = LocaisRetiradaSerializer()
    locais_retirada = {
        lr.local_embarque_id: lr.serialize()
        for lr in LocalRetiradaMarketplace.objects.to_serialize(serializer).filter(company_id=company_id)
    }
    return locais_retirada


def _get_locais_rota_marketplace(rota):
    ids_locais_buser = {item["local_id"] for item in rota["checkpoints"]}
    locais_buser = list(LocalEmbarque.objects.filter(pk__in=ids_locais_buser))
    locais_buser = {x.id: x.to_dict_json() for x in locais_buser}
    return locais_buser


def get_rota_marketplace_para_criar(rodoviaria_rota_id):
    rota = _client.get_rota_marketplace_para_criar(rodoviaria_rota_id)
    if not rota:
        return {}

    locais_buser = _get_locais_rota_marketplace(rota)

    for trechovendido in rota["trechos_vendidos"]:
        try:
            trechovendido["origem"] = locais_buser[trechovendido["origem_id"]]
            trechovendido["destino"] = locais_buser[trechovendido["destino_id"]]
        except KeyError:
            pass

    for checkpoint in rota["checkpoints"]:
        checkpoint["local"] = locais_buser[checkpoint["local_id"]]

    return rota


def verifica_compra_hibrido(travel, travel_destino):
    if _is_integrado_and_hibrido(travel_destino.trecho_classe):
        passageiros = travel.passageiro_set.all().select_related("buseiro")
        passageiros_travel = []
        for passageiro in passageiros:
            buseiro = passageiro.buseiro
            passageiros_travel.append(
                {
                    "grupo_id": travel_destino.grupo_id,
                    "trechoclasse_id": travel_destino.trecho_classe_id,
                    "travel_id": travel_destino.id,
                    "valor_por_buseiro": travel_destino.max_split_value,
                    "passenger": {
                        "id": passageiro.id,
                        "buseiro_id": buseiro.id,
                        "name": buseiro.name,
                        "cpf": buseiro.cpf,
                        "rg_number": buseiro.rg_number,
                        "phone": buseiro.phone,
                        "tipo_documento": buseiro.tipo_documento,
                    },
                    "id_origem": travel_destino.trecho_classe.trecho_vendido.origem_id,
                    "id_destino": travel_destino.trecho_classe.trecho_vendido.destino_id,
                }
            )
        return passageiros_travel


@shared_task(queue="staff")
def compra_hibrido_task(req):
    _client.add_pax_na_lista_batch(req)


def remaneja_passageiros_hibrido_batch(remanejamentos):
    passageiros = []
    for remanejamento in remanejamentos:
        travel = remanejamento.travel
        travel_destino = remanejamento.travel_remanejada
        is_trechoclasse_origem_integrado = _is_integrado_marketplace_or_hibrido_roderotas(travel.grupo)
        is_trechoclasse_destino_integrado = _is_integrado_marketplace_or_hibrido_roderotas(travel_destino.grupo)
        if not is_trechoclasse_origem_integrado and not is_trechoclasse_destino_integrado:
            passageiros_travel = verifica_compra_hibrido(travel, travel_destino)
            if passageiros_travel:
                passageiros += passageiros_travel
    if passageiros:
        compra_hibrido_task.delay({"passageiros": passageiros})


def fetch_rotas(company_id=None, modelo_venda=None, grupo_id=None):
    return _client.fetch_rotas(company_id, modelo_venda, grupo_id)


def _update_trechos_data(trecho_classe, new_preco_rodoviaria, new_vagas):
    tv = trecho_classe.trecho_vendido
    old_tv_preco_rodoviaria = tv.preco_rodoviaria
    company_name = Company.objects.filter(pk=trecho_classe.grupo.company_id).values_list("name", flat=True).first()
    if old_tv_preco_rodoviaria != new_preco_rodoviaria:
        tv.preco_rodoviaria = new_preco_rodoviaria
        tv.save()
        buserlogger.debug(
            f"TrechoVendido {tv.id} da empresa {company_name}"
            + f" teve seu preco atualizado de {old_tv_preco_rodoviaria} para {new_preco_rodoviaria}"
        )
    tipo_assento = trecho_classe.grupo_classe.tipo_assento
    old_tc_preco_rodoviaria = trecho_classe.max_split_value
    modified = False
    if new_vagas is not None and trecho_classe.vagas != new_vagas:
        tag_no_seats = rodoviaria_link_trecho_classe_svc.Tags.Closed.NO_SEATS_AVAILABLE
        msg = (
            f"TrechoClasse {trecho_classe.id} {tipo_assento} da empresa {company_name}"
            + f" teve suas vagas atualizadas de {trecho_classe.vagas} para {new_vagas}"
        )
        buserlogger.debug(msg)

        trecho_classe.vagas = new_vagas
        modified = True
        if new_vagas == 0 and not trecho_classe.closed:
            trecho_classe.closed = True
            trecho_classe.closed_at = timezone.now()
            trecho_classe.closed_reason = f"{tag_no_seats} Sem vagas disponíveis na API"
            log_svc.log_fechamento_trechos(
                [trecho_classe],
                trecho_classe.closed_reason,
                log_type="_update_trechos_data_fechamento_trecho",
            )
        elif trecho_classe.closed and tag_no_seats in trecho_classe.closed_reason:
            trecho_classe.closed = False
            trecho_classe.closed_at = None
            trecho_classe.closed_reason = None
            log_svc.log_abertura_trechos([trecho_classe], None)

    if old_tc_preco_rodoviaria != new_preco_rodoviaria:
        trecho_classe.max_split_value = new_preco_rodoviaria
        trecho_classe.ref_split_value = new_preco_rodoviaria
        if preco_svc._company_id_marketplace_bucketizada(trecho_classe.grupo.company_id):
            buckets = preco_svc.get_active_buckets(trecho_classe)
        else:
            buckets = None
        price_manager, price_bucket, price_log = preco_svc.determine_bucket_update(trecho_classe, buckets)
        preco_svc.bulk_update_price_objects([price_manager], price_bucket, price_log)
        trecho_classe.price_manager = price_manager
        modified = True
        msg = (
            f"TrechoClasse {trecho_classe.id} {tipo_assento} da empresa {company_name}"
            + f" teve seu preco atualizado de {old_tc_preco_rodoviaria} para {new_preco_rodoviaria}"
        )
        origem_cidade_name = (
            LocalEmbarque.objects.filter(pk=trecho_classe.trecho_vendido.origem_id)
            .values_list("cidade__name", flat=True)
            .get()
        )
        destino_cidade_name = (
            LocalEmbarque.objects.filter(pk=trecho_classe.trecho_vendido.destino_id)
            .values_list("cidade__name", flat=True)
            .get()
        )
        log_svc.log_atualiza_preco_rodoviaria(
            trecho_classe.grupo,
            origem_cidade_name,
            destino_cidade_name,
            trecho_classe.id,
            old_tc_preco_rodoviaria,
            new_preco_rodoviaria,
            tipo_assento,
        )
        buserlogger.debug(msg)

    if modified:
        trecho_classe.save()


@traced("rodoviaria_svc.update_trechos_classe_data")
def update_trechos_classe_data(
    tag=None, integracao=None, company_id=None, modelo_venda=None, grupo_id=None, trechoclasse_ids=None
):
    get_trechos_classe_response = _client.get_rodoviaria_trechos_classe(
        tag, integracao, company_id, modelo_venda, grupo_id, trechoclasse_ids
    )

    if "error" in get_trechos_classe_response:
        return get_trechos_classe_response

    updated_trechoclasse_ids = []
    trechos = get_trechos_classe_response.get("trechos")
    trechos_classes_qr = (
        TrechoClasse.objects.select_related("trecho_vendido")
        .prefetch_related("price_manager__buckets")
        .filter(id__in=(s["trechoclasse_internal_id"] for s in trechos))
    )
    trechos_classes = {tc.id: tc for tc in trechos_classes_qr}
    for s in trechos:
        try:
            if s["trechoclasse_internal_id"] not in trechos_classes:
                buserlogger.error("Tentativa de atualizacao de preco de trechoclasse_id inexistente")
                continue
            tc = trechos_classes[s["trechoclasse_internal_id"]]
            preco_api = D(str(s["preco_rodoviaria"]))
            vagas_api = int(str(s["vagas"])) if s["vagas"] is not None else None
            _update_trechos_data(tc, preco_api, vagas_api)
            updated_trechoclasse_ids.append(tc.id)
        except TrechoClasse.DoesNotExist:
            buserlogger.error("Tentativa de atualizacao de preco de trechoclasse_id inexistente")
        except Exception as ex:
            capture_exception(ex)
    if tag and updated_trechoclasse_ids:
        remove_tags_response = _client.remove_tags_from_trechos_classe(
            tag,
            integracao,
            company_id,
            modelo_venda,
            grupo_id,
            trechoclasse_ids=",".join([str(e) for e in updated_trechoclasse_ids]),
        )
        return dict(
            get_trechos_classe_response=get_trechos_classe_response,
            remove_tags_response=remove_tags_response,
        )
    return dict(get_trechos_classe_response=get_trechos_classe_response)


def remove_tags_from_trechos_classe(
    tag, integracao=None, company_id=None, modelo_venda=None, grupo_id=None, trechoclasse_ids=None
):
    return _client.remove_tags_from_trechos_classe(
        tag, integracao, company_id, modelo_venda, grupo_id, trechoclasse_ids
    )


def trechos_vendidos_rodoviaria(grupo_id):
    grupo = Grupo.objects.select_related("rota").get(id=grupo_id)
    try:
        trechos_vendidos_rodoviaria = _client.trechos_vendidos_rodoviaria(grupo_id)
    except FetchTrechosVendidosError as ex:
        return {ex.tipo: ex.mensagem}
    trechos_classe = grupo.trechoclasse_set.select_related("trecho_vendido").all()
    trechos_classe_buser = {}
    for tc in trechos_classe:
        trecho_vendido = tc.trecho_vendido
        trechos_classe_buser[trecho_vendido.origem_id, trecho_vendido.destino_id] = tc
    for tv_rodoviaria in trechos_vendidos_rodoviaria:
        tc_buser = trechos_classe_buser.pop((tv_rodoviaria["origem_id"], tv_rodoviaria["destino_id"]), None)
        tv_rodoviaria = _trecho_vendido_with_status(tc_buser, tv_rodoviaria)
    trechos_nao_existentes_api = [
        {
            "trecho_classe_id": trechos_classe_buser[(origem_id, destino_id)].id,
            "origem": trechos_classe_buser[(origem_id, destino_id)].trecho_vendido.origem.nickname,
            "destino": trechos_classe_buser[(origem_id, destino_id)].trecho_vendido.destino.nickname,
        }
        for (origem_id, destino_id) in trechos_classe_buser
    ]
    return {
        "trechos_rodoviaria": trechos_vendidos_rodoviaria,
        "trechos_nao_existentes_api": trechos_nao_existentes_api,
    }


def solicitar_cancelamento_travels_grupo(grupo_id, travels):
    if not _is_integrado(grupo=grupo_id):
        return
    travel_ids = [t.id for t in travels]
    _client.solicitar_cancelamento_travels(travel_ids)
    buserlogger.info(f"Cancelamento solicitado para as travels {', '.join([str(t) for t in travel_ids])}")


@memoize_local(timeout=20)
def _cancelar_bpes_travels(grupo_ids) -> list:
    return list(
        Travel.objects.filter(grupo_id__in=grupo_ids, status="pending").order_by("id").values_list("id", flat=True)
    )


def cancelar_bpes(grupo_ids):
    travel_ids = _cancelar_bpes_travels(grupo_ids)
    return _client.solicitar_cancelamento_travels(list(travel_ids))


def _time_str_to_timedelta(time_str):
    if "days" in time_str:
        days = int(time_str.split("days")[0])
    elif "day" in time_str:
        days = 1
    else:
        days = 0
    t_aux = datetime.strptime(time_str.split(", ")[-1], "%H:%M:%S")
    duracao_rodoviaria = timedelta(days=days, hours=t_aux.hour, minutes=t_aux.minute, seconds=t_aux.second)
    return duracao_rodoviaria


def _trecho_vendido_with_status(tc_buser, tv_rodoviaria):
    if not tc_buser:
        tv_rodoviaria["status"] = "NAO_VENDIDO"
        return tv_rodoviaria
    preco_ok = tc_buser.max_split_value == tv_rodoviaria["preco"]
    classe_ok = tc_buser.grupo_classe.tipo_assento == tv_rodoviaria.get("classe_buser")
    duracao_rodoviaria = _time_str_to_timedelta(tv_rodoviaria["duracao"])

    duracao_ok = tc_buser.duracao_ida == duracao_rodoviaria
    if preco_ok and classe_ok and duracao_ok:
        tv_rodoviaria["status"] = "VENDIDO_OK"
        return tv_rodoviaria
    divergencias = []
    if not preco_ok:
        divergencias.append("PRECO")
    if not classe_ok:
        divergencias.append("CLASSE")
    if not duracao_ok:
        divergencias.append("DURACAO")
    tv_rodoviaria["status"] = "VENDIDO_NOT_OK"
    tv_rodoviaria["divergencias"] = ", ".join(divergencias)
    return tv_rodoviaria


def escala_motoristas(grupo):
    if grupo.modelo_venda != Grupo.ModeloVenda.HIBRIDO or not _is_integrado(grupo=grupo.id):
        return

    for driver in (grupo.driver_one, grupo.driver_two):
        if driver:
            _client.escala_motorista(grupo.id, driver)


def pos_salvar_rota(params):
    return _client.pos_salvar_rota(params)


def bulk_pos_salvar_rota(params):
    return _client.bulk_pos_salvar_rota(params)


def get_classes_e_precos_rota(rota_id):
    ids_trechos_vendidos = list(TrechoVendido.objects.filter(rota_id=rota_id).values_list("id", flat=True))
    return _client.get_classes_e_precos_rota(rota_id, ids_trechos_vendidos)


def empresas_by_features(features):
    if not _is_prod():
        return []
    return _client.get_empresas_by_features(features)["empresas"]


def get_empresa_id_by_integracoes(integracoes: list[str]) -> list[int]:
    if not _is_prod():
        return []
    empresas = _client.get_empresas_by_integracoes(integracoes)["empresas"]
    return [e["company_internal_id"] for e in empresas if e["modelo_venda"] == "marketplace"]


def get_empresa_info(company_id):
    if not _is_prod():
        return {}
    client: RodoviariaClient = get_client("rodoviaria")
    try:
        return client.get_company_info(company_id)
    except client.exceptions.HTTPErrorNotFound:
        return None


def ids_empresas_marketplace_auto_integra_operacao():
    empresas_integradas = empresas_by_features(["auto_integra_operacao"])
    if not empresas_integradas:
        return []
    return [e["company_internal_id"] for e in empresas_integradas if e["modelo_venda"] == "marketplace"]


def list_empresas_hibridas_ids():
    empresas_integradas = _client.get_empresas_integradas().get("empresas")
    return [e["company_internal_id"] for e in empresas_integradas if e["modelo_venda"] == "hibrido"]


def list_empresas_marketplace_ids():
    empresas_integradas = _client.get_empresas_integradas().get("empresas")
    return [e["company_internal_id"] for e in empresas_integradas if e["modelo_venda"] == "marketplace"]


def list_empresas_marketplace():
    empresas_integradas = _client.get_empresas_integradas().get("empresas")
    return [e for e in empresas_integradas if e["modelo_venda"] == "marketplace"]


def list_empresas_paginator(name, status, modelo_venda, rows_per_page, page, order_by):
    return _client.get_empresas_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )


def cadastrar_grupos_hibridos_rodoviaria_params(company_id, rota_id):
    return _client.cadastrar_grupos_hibridos_rodoviaria_params(company_id, rota_id)


@my_shared_task(queue="rodoviaria")
def cadastrar_grupos_hibridos_rodoviaria(company_id, grupos_ids):
    task_logger.debug(f"cadastrar_grupos_hibridos_rodoviaria: {str(grupos_ids)}")
    grupos = (
        Grupo.objects.select_related("rota__origem__cidade")
        .select_related("onibus")
        .prefetch_related("grupoclasse_set")
        .prefetch_related("onibus__classes")
        .filter(id__in=grupos_ids)
    )

    grupos_params = []
    for g in grupos:
        onibus = g.onibus
        grupos_params.append(_params_cadastrar_um_grupo_hibrido(g, onibus))
    json_body = {"company_id": company_id, "grupos": grupos_params}
    sincronizar_rotas_hibrido_por_grupos(list(grupos))
    response = _client.cadastrar_grupos_hibridos_rodoviaria(json_body)
    if response and "error" in response and response["error_type"] == "rota_nao_cadastrada":
        raise RotaNaoCadastradaRodoviaria(rota_id=response["rota_id"], company_id=response["company_id"])
    return response


def verifica_criacao_grupos_hibrido(grupos):
    grupo_modelo = grupos[0]
    grupo_modelo.refresh_from_db()
    if grupo_modelo.modelo_venda != Grupo.ModeloVenda.HIBRIDO:
        return
    if not grupo_modelo.company_id:
        return
    if not grupo_modelo.onibus_id:
        return
    cadastrar_grupos_hibridos_rodoviaria.delay(grupo_modelo.company_id, [g.id for g in grupos])


def verifica_criacao_grupos_hibrido_n_empresas(grupos):
    empresas_e_grupos = {}
    for grupo in grupos:
        if not grupo.company_id or grupo.modelo_venda != Grupo.ModeloVenda.HIBRIDO:
            continue
        if grupo.company_id in empresas_e_grupos:
            empresas_e_grupos[grupo.company_id].append(grupo)
        else:
            empresas_e_grupos[grupo.company_id] = [grupo]

    for _empresa_id, grupos_empresa in empresas_e_grupos.items():
        verifica_criacao_grupos_hibrido(grupos_empresa)


def verifica_criacao_grupos_hibrido_n_rotas(grupos):
    rotas_e_grupos = {}
    for grupo in grupos:
        if not grupo.company_id or grupo.modelo_venda != Grupo.ModeloVenda.HIBRIDO:
            continue
        if grupo.rota_id in rotas_e_grupos:
            rotas_e_grupos[grupo.rota_id].append(grupo)
        else:
            rotas_e_grupos[grupo.rota_id] = [grupo]

    for _rota_id, grupos_rota in rotas_e_grupos.items():
        verifica_criacao_grupos_hibrido(grupos_rota)


@my_shared_task(queue="rodoviaria")
def cadastrar_trechos_batch_task(grupo_id):
    cadastrar_trechos_rodoviaria_por_grupo(grupo_id)


def _params_cadastrar_um_grupo_hibrido(grupo, onibus):
    datetime_ida = to_tz(grupo.datetime_ida, grupo.rota.origem.cidade.timezone)
    grupos_classe_ids_map_tipo_assento = {}
    for gc_id, gc_tipo_assento in grupo.grupoclasse_set.values_list("id", "tipo_assento"):
        grupos_classe_ids_map_tipo_assento[gc_tipo_assento] = gc_id
    return {
        "data_partida": datetime_ida.strftime("%Y-%m-%d"),
        "hora_saida": datetime_ida.strftime("%H:%M"),
        "rota_internal_id": grupo.rota_id,
        "veiculo_placa": onibus.placa,
        "veiculo_internal_id": onibus.id,
        "grupo_id": grupo.id,
        "classes": [
            {
                "grupo_classe_id": grupos_classe_ids_map_tipo_assento.get(oc.tipo),
                "tipo": oc.tipo,
                "capacidade": oc.capacidade,
            }
            for oc in onibus.classes.order_by("-id").all()
        ],
    }


def tempo_total_checkpoint(checkpoint, posicao, tamanho_rota):
    if checkpoint.duracao:
        if posicao == tamanho_rota - 1:
            return checkpoint.duracao
        return checkpoint.duracao + checkpoint.tempo_embarque
    return checkpoint.tempo_embarque


def _rota_hibrido(grupo_id, company_id, rota_id_external=None, rota=None):
    if not rota:
        rota = Grupo.objects.select_related("rota").get(id=grupo_id).rota
    params = {"company_id": company_id, "id_rota_internal": rota.id}
    if rota_id_external:
        params["id_rota_external"] = rota_id_external
    rota_checkpoints = rota.itinerario.all().order_by("idx").select_related("local")
    checkpoints = []
    for index, checkpoint in enumerate(rota_checkpoints):
        checkpoints.append(
            {
                "local_embarque_id": checkpoint.local_id,
                "cidade_destino_id": checkpoint.local.cidade_id,
                "duracao": timedelta_to_hours_minutes_str(checkpoint.duracao),
                "tempo_embarque": timedelta_to_hours_minutes_str(checkpoint.tempo_embarque),
                "tempo_total": timedelta_to_hours_minutes_str(
                    tempo_total_checkpoint(checkpoint, index, len(rota_checkpoints))
                ),
                "ponto_embarque": checkpoint.local.nickname,
                "km": checkpoint.distancia_km,
            }
        )
    params["checkpoints"] = checkpoints
    return params


def abrir_trechos(grupos_ids):
    trechos_fechados = (
        TrechoClasse.objects.filter(closed=True, grupo_id__in=grupos_ids)
        .exclude(closed_reason__contains=HARD_STOP_PREFIX)
        .exclude(closed_reason=DUPLICADO_NOVO_ESTOQUE_CLOSED_REASON)
    )
    trechos_fechados.update(closed=False, closed_by_id=None, closed_reason=None, closed_at=None)
    return trechos_fechados


def criar_itinerario_hibrido(rota_hibrido_params):
    params = _rota_hibrido(
        grupo_id=rota_hibrido_params.grupo_id,
        company_id=rota_hibrido_params.company_id,
        rota_id_external=_hasattr_rota_id_external(rota_hibrido_params),
    )
    criar_response = _client.criar_itinerario_hibrido(params)
    cadastrar_trechos_rodoviaria_por_grupo(rota_hibrido_params.grupo_id)
    return criar_response


def _hasattr_rota_id_external(rota_hibrido_params):
    return rota_hibrido_params.rota_id_external if hasattr(rota_hibrido_params, "rota_id_external") else None


@my_shared_task(queue="rodoviaria")
def _recadastrar_grupos_hibridos(rota_id):
    grupos = Grupo.objects.filter(
        datetime_ida__gt=timezone.now(),
        rota_id=rota_id,
        status__in=(Grupo.Status.PENDING, Grupo.Status.TRAVEL_CONFIRMED),
        modelo_venda=Grupo.ModeloVenda.HIBRIDO,
        onibus__isnull=False,
    )
    grupos_por_company = defaultdict(list)
    for g in grupos:
        grupos_por_company[g.company_id].append(g.id)
    for company_id, grupos_ids in grupos_por_company.items():
        cadastrar_grupos_hibridos_rodoviaria.delay(company_id, grupos_ids)


def atualizar_embarques_hibrido(rota_hibrido_params):
    params = _rota_hibrido(
        grupo_id=rota_hibrido_params.grupo_id,
        company_id=rota_hibrido_params.company_id,
        rota_id_external=_hasattr_rota_id_external(rota_hibrido_params),
    )
    atualizar_response = _client.atualizar_embarques_hibrido(params)
    cadastrar_trechos_rodoviaria_por_grupo(rota_hibrido_params.grupo_id)
    return atualizar_response


def editar_ou_criar_rota_hibrido(grupo, autorizacao, grupo_ids, rota_rodoviaria_id=None):
    # vai verificar se a rota já esta cadastrada
    # se a rota estiver cadastrada, só editar
    # se a rota não estiver cadastrada
    # tenta linkar com base no match de checkpoints (o match não precisa ser perfeito, vai duplicar algumas rotas)
    # se nao conseguir linkar, cria outra
    params = _rota_hibrido(
        grupo_id=grupo.id,
        company_id=grupo.company_id,
        rota_id_external=rota_rodoviaria_id,
    )
    params["cidade_destino_id"] = autorizacao.cidade_destino_id
    params["cidade_origem_id"] = autorizacao.cidade_origem_id
    params["prefixo"] = autorizacao.prefixo
    params["grupo_ids"] = grupo_ids
    criar_response = _client.editar_ou_criar_rota_hibrido(params)
    cadastrar_trechos_rodoviaria_por_grupo(grupo.id)
    return criar_response


def sincronizar_rota_hibrido_por_grupo(grupo=None, grupo_id=None):
    if not grupo and grupo_id:
        grupo = Grupo.objects.select_related("rota", "autorizacao_hibrido").get(id=grupo_id)
    params = _rota_hibrido(grupo_id=grupo.id, company_id=grupo.company_id, rota=grupo.rota)
    autorizacao = grupo.autorizacao_hibrido
    if not autorizacao:
        raise AutorizacaoNaoCadastradaRodoviaria(grupo.id)
    params["cidade_destino_id"] = autorizacao.cidade_destino_id
    params["cidade_origem_id"] = autorizacao.cidade_origem_id
    params["prefixo"] = autorizacao.prefixo
    sincronizar_response = _client.sincronizar_rota(params)
    cadastrar_trechos_rodoviaria_por_grupo(grupo.id)
    return sincronizar_response


def criar_rota_hibrido(rota_hibrido_params):
    params = _rota_hibrido(
        grupo_id=rota_hibrido_params.grupo_id,
        company_id=rota_hibrido_params.company_id,
        rota_id_external=_hasattr_rota_id_external(rota_hibrido_params),
    )
    params["cidade_destino_id"] = rota_hibrido_params.cidade_destino_id
    params["cidade_origem_id"] = rota_hibrido_params.cidade_origem_id
    params["prefixo"] = rota_hibrido_params.prefixo
    if rota_hibrido_params.rota_rodoviaria_id:
        return editar_rota_hibrido(params, rota_hibrido_params.grupo_id, rota_hibrido_params.rota_rodoviaria_id)
    criar_response = _client.criar_rota_hibrido(params)
    cadastrar_trechos_rodoviaria_por_grupo(rota_hibrido_params.grupo_id)
    return criar_response


def editar_rota_hibrido(params, grupo_id, rota_rodoviaria_id):
    del params["checkpoints"]
    params["id_rota_external"] = rota_rodoviaria_id
    grupo = Grupo.objects.get(id=grupo_id)
    grupos_futuros = Grupo.objects.filter(
        rota=grupo.rota,
        company=grupo.company,
        datetime_ida__gt=timezone.now() + timedelta(hours=3),
    ).values_list("id", flat=True)
    params["grupo_ids"] = list(grupos_futuros)
    editar_response = _client.editar_rota_hibrido(params)
    cadastrar_trechos_rodoviaria_por_grupo(grupo.id)
    return editar_response


def fechar_grupo_rodoviaria(grupo, *, grupos_classe_to_update):
    if not _is_integrado_and_hibrido(grupo=grupo):
        return
    grupos_classe = list(grupo.grupoclasse_set.all())
    for gc in grupos_classe:
        if not gc.closed:
            gc.closed = True
            gc.closed_reason = "Grupo híbrido com ônibus de fretamento"
            gc.closed_by = None
            gc.closed_at = timezone.now()
    grupos_classe_to_update.extend(grupos_classe)
    return _client.fechar_grupos_classe_hibridos(grupo.company_id, [gc.id for gc in grupos_classe])


def cancelar_grupo(grupo, travels=None):
    if not _is_integrado(grupo=grupo):
        return
    if travels is None:
        travels = list(Travel.objects.filter(grupo_id=grupo.id, status="pending").order_by("id"))
    return solicitar_cancelamento_travels_grupo(grupo, travels)


@my_shared_task(queue="rodoviaria")
def alterar_grupo_hibrido(grupo_id, grupo_params):
    passageiros = Passageiro.objects.select_related("travel").filter(travel__grupo_id=grupo_id)
    if passageiros:
        pax_params = []
        for p in passageiros:
            pax_params.append(
                {
                    "trechoclasse_id": p.travel.trecho_classe_id,
                    "travel_id": p.travel_id,
                    "buseiro_id": p.buseiro_id,
                    "valor_pago": p.travel.max_split_value,
                }
            )
        emitir_bpes_params, _, _ = _add_multiple_pax_na_lista_requests(CheckPaxMultipleForm.parse_obj(pax_params))
        grupo_params["passageiros"] = emitir_bpes_params
    return _client.alterar_grupo_hibrido(grupo_params)


def escalar_onibus_grupo_hibrido(grupo_novo, onibus, grupos_classe_ids_antigos):
    params = {
        "company_id": grupo_novo.company_id,
        "grupos_classe_ids_antigos": list(grupos_classe_ids_antigos),
        "grupo": _params_cadastrar_um_grupo_hibrido(grupo_novo, onibus),
    }
    return alterar_grupo_hibrido.s(grupo_novo.id, params)


def alterar_horario_grupo_hibrido(grupo):
    params = {
        "company_id": grupo.company_id,
        "grupos_classe_ids_antigos": list(grupo.grupoclasse_set.values_list("id", flat=True)),
        "grupo": _params_cadastrar_um_grupo_hibrido(grupo, grupo.onibus),
    }
    return alterar_grupo_hibrido.s(grupo.id, params)


def list_all_possible_empresa_features(is_staff):
    return _client.get_all_possible_company_features(is_staff)


def create_empresa(name, company_internal_id, modelo_venda, features, login, max_percentual_divergencia, integracao):
    if modelo_venda == Grupo.ModeloVenda.MARKETPLACE:
        response = _client.create_company(
            name=name,
            company_internal_id=company_internal_id,
            modelo_venda=modelo_venda,
            features=features,
            login=login,
            max_percentual_divergencia=max_percentual_divergencia,
            integracao=integracao,
        )
    else:
        company = Company.objects.get(id=company_internal_id)
        response = _client.create_company_transbrasil(
            name=company.razao_social,
            company_internal_id=company_internal_id,
            company_external_id=login["company_external_id"] if login else None,
        )
        set_parent_company_hibrido(company)
    add_globalsetting(company_internal_id)
    return response


def update_empresa(name, company_internal_id, modelo_venda, features, login, max_percentual_divergencia, integracao):
    response = _client.update_company(
        name=name,
        company_internal_id=company_internal_id,
        modelo_venda=modelo_venda,
        features=features,
        login=login,
        max_percentual_divergencia=max_percentual_divergencia,
        integracao=integracao,
    )
    return response


def get_empresa_login(integracao, company_id, modelo_venda):
    response = _client.get_company_login(integracao=integracao, company_id=company_id, modelo_venda=modelo_venda)
    return response


def fetch_locais_empresa(company_rodoviaria_id):
    response = _client.fetch_company_locais(company_rodoviaria_id=company_rodoviaria_id)
    return response


def get_locais_retirada_empresa(company_internal_id, company_rodoviaria_id, associado_rota, linkado_buser):
    locais_rodov = _client.list_links_local_embarque(
        {"empresa_id_filter": company_rodoviaria_id, "associado_rota": associado_rota, "linkado_buser": linkado_buser}
    )

    locais_internal_ids = [local["local_embarque_buser"] for local in locais_rodov["items"]]

    locais_retirada = LocalRetiradaMarketplace.objects.select_related("local_embarque").filter(
        local_embarque_id__in=locais_internal_ids, company_id=company_internal_id
    )

    locais_retirada_map = locais_retirada.distinct("local_embarque_id").in_bulk(
        locais_internal_ids, field_name="local_embarque_id"
    )

    locais_internos_map = LocalEmbarque.objects.in_bulk(locais_internal_ids, field_name="pk")

    resp = []
    for local in locais_rodov["items"]:
        local_pk = local.get("local_embarque_buser")
        local_embarque_name = ""
        local_embarque_endereco = ""
        local_embarque_desc = ""
        if local_pk:
            local_embarque = locais_internos_map[local_pk]
            local_embarque_name = local_embarque.nickname
            local_embarque_endereco = local_embarque.endereco
            local_embarque_desc = local_embarque.description

        local_retirada = locais_retirada_map.get(local_pk)
        local_retirada_desc = ""
        if local_retirada:
            local_retirada_desc = local_retirada.tipo
            if local_retirada.descricao:
                local_retirada_desc += f" - {local_retirada.descricao}"

        resp.append(
            {
                "local_embarque_API": local["localidade_external"],
                "local_embarque_nome": local_embarque_name,
                "local_embarque_endereco": local_embarque_endereco,
                "local_embarque_descricao": local_embarque_desc,
                "local_retirada_descricao": local_retirada_desc,
            }
        )

    return resp


def fetch_external_totalbus_companies(user, password, tenant_id):
    external_companies = _client.fetch_external_totalbus_companies(user=user, password=password, tenant_id=tenant_id)
    return external_companies


def fetch_formas_pagamento(params: ListaFormasPagamentoRodoviariaForm):
    formas_pagamento = _client.fetch_formas_pagamento(params.integracao, params.login.dict())
    return formas_pagamento


def add_globalsetting(company_internal_id):
    settings_key = "show_rodoviaria_staff_features"
    lista_de_empresas = globalsettings_svc.get.uncached(settings_key)
    if company_internal_id not in lista_de_empresas:
        lista_de_empresas.append(company_internal_id)
        globalsettings_svc.set(settings_key, lista_de_empresas)


def set_parent_company_hibrido(company):
    company_id_transbrasil = 1003
    company.parent_company_hibrido_id = company_id_transbrasil
    company.save(update_fields=["parent_company_hibrido_id"])


def sincronizar_rotas_hibrido_por_grupos(grupos):
    rotas_grupo_map = {}
    for g in grupos:
        rotas_grupo_map[(g.rota_id, g.company_id)] = g
    for g in rotas_grupo_map.values():
        sincronizar_rota_hibrido_por_grupo(g)


def atualizar_link_trechos_classes(
    trechos_a_validar: list[TrechoClasse],
    use_update_price_queue=False,
    use_hot_update_price_queue=False,
    use_update_top_divergencias=False,
):
    trechos_a_validar = trechos_a_validar[:40_000]
    trechos_json = []
    for t in trechos_a_validar:
        trechos_json.append(
            {
                "company_internal_id": t.grupo.company_id,
                "modelo_venda": t.grupo.modelo_venda,
                "grupoclasse_internal_id": t.grupo_classe.id,
                "tipo_assento": t.grupo_classe.tipo_assento,
                "grupo_internal_id": t.grupo.id,
                "grupo_datetime_ida": (
                    to_tz(t.grupo.datetime_ida, t.trecho_vendido.origem.cidade.timezone).strftime("%Y-%m-%dT%H:%M:%S")
                ),
                "origem_timezone": t.trecho_vendido.origem.cidade.timezone,
                "trechoclasse_internal_id": t.id,
                "origem_internal_id": t.trecho_vendido.origem_id,
                "destino_internal_id": t.trecho_vendido.destino_id,
                "datetime_ida": (
                    to_tz(t.datetime_ida, t.trecho_vendido.origem.cidade.timezone).strftime("%Y-%m-%dT%H:%M:%S")
                ),
            }
        )

    chunked_trechos_json = chunks(trechos_json, 4_000)
    for trechos in chunked_trechos_json:
        _client.atualizar_link_trechos_classes(
            trechos, use_update_price_queue, use_hot_update_price_queue, use_update_top_divergencias
        )


@receiver([signals.remanejamento_signal])
def remanejamento_listener(sender=None, **kwargs) -> None:
    remanejamentos = kwargs["remanejamentos"]
    remaneja_passageiros_hibrido_batch(remanejamentos)


def get_trechos_a_ser_atualizados_buser_django():
    return _client.get_trechos_a_ser_atualizados_buser_django()


def get_trechos_overbooking():
    return _client.get_trechos_overbooking()


def get_trechos_sem_servico_hibrido():
    return _client.get_trechos_sem_servico_hibrido()


def deleta_trechos_classe_error(trechos_classe_ids):
    return _client.deleta_trechos_classe_error(trechos_classe_ids)


def remover_tags_trechos_atualizados(trechos_por_tag):
    return _client.remover_tags_trechos_atualizados(trechos_por_tag)


def fetch_trechos_vendidos_por_rota(rodov_rota_id):
    response = _client.fetch_trechos_vendidos_por_rota(rodov_rota_id)
    return response


def passageiros_rodoviaria_por_company_periodo(companies_integradas_ids, data_inicial, data_final):
    passagens = _client.passageiros_rodoviaria_por_company_periodo(companies_integradas_ids, data_inicial, data_final)
    passagens = {(p["travel_id"], p["buseiro_id"]): p["passagem_id"] for p in passagens}
    return passagens


def add_multiple_pax_async(passagens_para_emitir: list[CheckPaxForm], modelo_venda):
    passagens_para_emitir = [p.dict() for p in passagens_para_emitir]
    return _client.add_multiple_pax_async(passagens_para_emitir, modelo_venda)


@my_shared_task(queue="rodoviaria")
def _atualizar_link_rota(rota_id, old_rota_id):
    return _client.atualizar_link_rota(rota_id, old_rota_id)


@traced("rodoviaria_svc.pos_edit_rota")
def pos_edit_rota(rota_id, old_rota_id, trechos_vendidos):
    trechos_vendidos_map = {}
    for tv in trechos_vendidos:
        trechos_vendidos_map[tv.OLD_ID] = tv.pk

    try:
        _atualizar_link_rota.delay(rota_id, old_rota_id)
        _recadastrar_grupos_hibridos.delay(rota_id)
        _atualizar_trechos_vendidos_de_rota.delay(rota_id, trechos_vendidos_map)
    except Exception as ex:
        capture_exception(ex)
        pass


def verify_praxio_login(nome, senha, cliente):
    _client.verify_praxio_login(nome, senha, cliente)
    return {"success": True}


def lista_empresas_api(params: ListaEmpresasAPIParams):
    return _client.lista_empresas_api(params.login_params.dict())


def get_status_bpe(passagem_rodoviaria_id):
    return _client.get_status_bpe(passagem_rodoviaria_id)


def get_rotinas_rota_marketplace(rodoviaria_rota_id, rota_internal_id, start_date, end_date):
    ids_trechos_vendidos_filter = TrechoVendido.objects.filter(rota_id=rota_internal_id).values_list("id", flat=True)
    return get_rotinas_rota_marketplace_apenas_trechos_integrados(
        rodoviaria_rota_id, ids_trechos_vendidos_filter, start_date, end_date
    )


def get_rotinas_rota_marketplace_apenas_trechos_integrados(
    rodoviaria_rota_id, ids_trechos_vendidos_filter, start_date, end_date
):
    return _client.get_rotinas_rota_marketplace(rodoviaria_rota_id, ids_trechos_vendidos_filter, start_date, end_date)


def tem_grupos_abertos(rota_id, company_id, date_filter):
    return Grupo.objects.filter(
        company_id=company_id,
        rota_id=rota_id,
        datetime_ida__date__in=date_filter,
        status__in=[Grupo.Status.PENDING, Grupo.Status.TRAVEL_CONFIRMED],
    ).exists()


def _is_trecho_hot_update(company: Company, tv: TrechoVendido):
    assert tv.origem.cidade.slug
    assert tv.destino.cidade.slug
    manager: TrechoEstoqueManager = get_estoque_manager(
        tv.origem.cidade.slug,
        tv.destino.cidade.slug,
    )
    return manager.should_create(company.cnpj, TrechoEstoqueManager.Trigger.SEARCH_BACKGROUND)


@traced("rodoviaria_svc.criar_grupos_markeplace")
def criar_grupos_markeplace(params: CriarGruposMarketplaceForm, user):
    params_raw = params.dict()
    rota = Rota.objects.get(pk=params.rota_id)
    trechos_vendidos_rota = rota.trechos_vendidos.select_related("origem__cidade", "destino__cidade").all()
    grupos = []
    for rotina in params.rotinas:
        if not rotina.datas_filtradas:
            # todas as rotinas vem no params, mesmo que não tenham datas selecionadas para criar
            continue

        trechos_classes = {}
        for tv in trechos_vendidos_rota:
            tv_rotina = rotina.trechos_vendidos.get(tv.id)
            closed_reason = None
            if tv_rotina is not None:
                trechos_classes[tv.id] = {}
                for classe in rotina.classes:
                    preco = rotina.trechos_vendidos[tv.id].get(classe.tipo_assento)
                    if preco is not None:
                        trechos_classes[tv.id][classe.id] = TrechoClasseForm(
                            max_split_value=preco,
                            ref_split_value=preco,
                            additional_properties={
                                "closed": bool(closed_reason),
                                "closed_reason": closed_reason,
                                "closed_by_id": user.id,
                            },
                        )

        grupos_rotina = grupo_crud_svc.creategroup(
            params.rota_id,
            Grupo.Status.TRAVEL_CONFIRMED,
            "high",
            Grupo.ModeloVenda.MARKETPLACE,
            params.company_id,
            None,
            None,
            None,
            False,
            None,
            rotina.datas_filtradas,
            rotina.horario,
            params.percentual_repasse,
            params.percentual_taxa_servico,
            trechos_classes,
            rotina.classes,
            None,
            user,
            params_raw,
            permite_mesmo_horario_grupo_existente_na_rota=True,
        )
        grupos += grupos_rotina
    return grupos


@my_shared_task(queue="rodoviaria")
def _atualizar_trechos_vendidos_de_rota(rota_id, trechos_vendidos_map):
    return _client.atualizar_trechos_vendidos_de_rota(rota_id, trechos_vendidos_map)


def get_passagens_rodoviaria(travel_ids, buseiro_ids, status_passagens=None, with_preco_rodoviaria=False):
    response = _client.get_passagens_rodoviaria(travel_ids, buseiro_ids, status_passagens, with_preco_rodoviaria)
    return response["passagens"]


def get_passagens_relatorio_repasse_rodoviaria(
    company_id,
    start_date_saida=None,
    end_date_saida=None,
    start_date_compra_e_cancelamento=None,
    end_date_compra_e_cancelamento=None,
    current_page=None,
    items_per_page=None,
    travel_ids=None,
    status_passagens=None,
):
    response = _client.get_passagens_relatorio_repasse_rodoviaria(
        company_id,
        start_date_saida,
        end_date_saida,
        start_date_compra_e_cancelamento,
        end_date_compra_e_cancelamento,
        current_page,
        items_per_page,
        travel_ids,
        status_passagens,
    )
    return response["passagens"], response["fator_conexoes"], response["total_rows"]


def get_passagem_info(travel_id, buseiro_id):
    rodoviaria_response = _client.get_passagem_info(travel_id, buseiro_id)
    return rodoviaria_response


def hard_stop_empresa(company_internal_id, mensagem_fechamento, staff_user=None):
    count_fechados = _fechar_todos_grupos_classe_empresa_hard_stop(company_internal_id, mensagem_fechamento, staff_user)
    msg = f"{HARD_STOP_PREFIX}: A empresa rodoviaria de {company_internal_id=} aplicou HardStop para {count_fechados} grupos"
    buserlogger.info(msg)
    remove_features_empresa(company_internal_id, Grupo.ModeloVenda.MARKETPLACE)
    return count_fechados


def _fechar_todos_grupos_classe_empresa_hard_stop(company_internal_id, mensagem_fechamento, staff_user=None):
    if company_internal_id not in list_empresas_marketplace_ids():
        raise RodoviariaException("Empresa não integrada. Não é possível fechar os trechos")
    grupos_classes = GrupoClasse.objects.filter(
        grupo__company_id=company_internal_id,
        grupo__datetime_ida__gt=today_midnight(),
        grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
        closed=False,
    )
    grupos_ids = []
    if grupos_classes:
        grupos_ids = list(grupos_classes.values_list("grupo__id", flat=True))

    count = grupos_classes.update(
        closed=True,
        closed_reason=f"{HARD_STOP_PREFIX} - {mensagem_fechamento}",
        closed_by=staff_user,
        closed_at=timezone.now(),
    )

    log_svc.log_hard_stop_rodoviaria(company_internal_id, mensagem_fechamento, staff_user, grupos_ids, count)

    return count


def remove_features_empresa(company_internal_id, modelo_venda):
    return _client.remove_features_empresa(company_internal_id, modelo_venda)


def revert_hardstop_empresa(company_internal_id, staff_user):
    raise_if_unable_hard_stop(company_internal_id)
    count_reabertos = _abrir_todos_grupos_classe_empresa_revert_hard_stop(company_internal_id, staff_user)
    revert_hardstop_empresa_rodoviaria(company_internal_id, Grupo.ModeloVenda.MARKETPLACE)
    msg = f"Revert hard stop: A empresa rodoviaria de {company_internal_id=} reverteu o HardStop e reabriu {count_reabertos} grupos"
    buserlogger.info(msg)
    return count_reabertos


def _abrir_todos_grupos_classe_empresa_revert_hard_stop(company_internal_id, staff_user):
    grupos_classes = GrupoClasse.objects.filter(
        grupo__company_id=company_internal_id,
        grupo__datetime_ida__gt=today_midnight(),
        grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
        closed_reason__contains=f"{HARD_STOP_PREFIX}",
        closed=True,
    )
    grupos_ids = []
    if grupos_classes:
        grupos_ids = list(grupos_classes.values_list("grupo__id", flat=True))
    count = grupos_classes.update(closed=False, closed_reason=None, closed_by=None, closed_at=None)
    log_svc.log_hard_stop_revert_rodoviaria(company_internal_id, staff_user, grupos_ids, count)
    return count


def raise_if_unable_hard_stop(company_internal_id):
    client: RodoviariaClient = get_client("rodoviaria")
    try:
        client.raise_if_unable_hard_stop_rodoviaria(company_internal_id)
    except client.exceptions.HTTPErrorUnprocessableEntity as ex:
        raise ValidationError(ex.response.json()["error"])


def revert_hardstop_empresa_rodoviaria(company_internal_id, modelo_venda):
    client: RodoviariaClient = get_client("rodoviaria")
    try:
        response = client.revert_hardstop_empresa(company_internal_id, modelo_venda)
    except client.exceptions.HTTPErrorBadRequest as ex:
        raise ValidationError(ex.response.json()["error"])
    return response


def simula_mover_buseiro_marketplace(travels, trecho_classe_destino):
    numero_pax = 0
    grupo_origem = travels[0].grupo
    is_origem_integrada = _is_integrado(grupo=grupo_origem)

    travels_com_menos_de_3h = []
    for travel in travels:
        numero_pax += travel.count_seats
        if is_origem_integrada and travel.trecho_classe.datetime_ida < timezone.now() + timedelta(hours=3):
            travels_com_menos_de_3h.append(travel)

    if travels_com_menos_de_3h and not globalsettings_svc.get("marketplace_permite_mover_3h", False):
        raise RodoviariaMoverBuseiroException(
            f"Travels {[t.reservation_code for t in travels_com_menos_de_3h]} não podem ser canceladas pois estão há menos de 3h para viagem."
        )

    is_destino_integrada = _is_integrado_marketplace_or_hibrido_roderotas(trecho_classe_destino.grupo)
    if not is_destino_integrada:
        return

    status_trechos = update_status_integracao_trecho_classe(trecho_classe_destino)
    qtd_remanejamentos_pendentes = quantidade_remanejamentos_pendentes_grupo(trecho_classe_destino.grupo_id)

    remanejamento_summary_log = LOG_SIMULACAO_REMANEJAMENTO.format(
        trecho_classe_id=trecho_classe_destino.id, numero_pax=numero_pax
    )
    buserlogger.info(f"{remanejamento_summary_log}: {status_trechos=} com {qtd_remanejamentos_pendentes=}")

    status_trecho = status_trechos[str(trecho_classe_destino.id)]
    message = None
    vagas_disponiveis = status_trecho.get("vagas", 0) - qtd_remanejamentos_pendentes
    if status_trecho["status"] != "integracao_ok":
        message = status_trecho["error"]
    elif vagas_disponiveis < numero_pax:
        message = _remanejamento_sem_vagas_message(vagas_disponiveis, qtd_remanejamentos_pendentes)
    else:
        _check_poltronas_livres(numero_pax, qtd_remanejamentos_pendentes, trecho_classe_destino)
    if message:
        buserlogger.info(f"{remanejamento_summary_log} ERRO: {message}")
        raise RodoviariaMoverBuseiroException(message)
    trecho_classe_destino.refresh_from_db()


def _remanejamento_sem_vagas_message(vagas_disponiveis, qtd_remanejamentos_pendentes):
    if vagas_disponiveis <= 0:
        message = "Não há vagas disponíveis"
    else:
        message = f"Apenas {pluralize(vagas_disponiveis, 'vaga disponível', 'vagas disponíveis')}"
    if qtd_remanejamentos_pendentes > 0:
        message += (
            f", pois existem {qtd_remanejamentos_pendentes} remanejamentos ainda sendo executados assincronamente"
        )
    return message


def _check_poltronas_livres(numero_pax, qtd_remanejamentos_pendentes, trecho_classe_destino):
    remanejamento_summary_log = LOG_SIMULACAO_REMANEJAMENTO.format(
        trecho_classe_id=trecho_classe_destino.id, numero_pax=numero_pax
    )
    mapa_poltronas = get_map_poltronas(trecho_classe_destino.id)
    buserlogger.info(f"{remanejamento_summary_log} MAPA DE POLTRONAS: {mapa_poltronas}")
    poltronas_livres = list(mapa_poltronas.values()).count("livre")
    vagas_disponiveis = poltronas_livres - qtd_remanejamentos_pendentes
    if vagas_disponiveis < numero_pax:
        message = _remanejamento_sem_vagas_message(vagas_disponiveis, qtd_remanejamentos_pendentes)
        buserlogger.info(f"{remanejamento_summary_log} ERRO NO CHECK: {message}")
        raise RodoviariaMoverBuseiroException(message)


def quantidade_remanejamentos_pendentes_grupo(grupo_id):
    remanejamentos_pendentes = _client.remanejamentos_pendentes(grupo_id)
    return sum(t["count_seats"] for t in remanejamentos_pendentes["remanejamentos"])


def grupos_com_bpe_emitido(grupos: QuerySet) -> QuerySet:
    passageiros_map = defaultdict(list)

    passageiros = list(
        Passageiro.objects.all()
        .filter(travel__status="pending", travel__grupo__in=grupos, removed=False)
        .values("travel_id", "buseiro_id", "travel__grupo_id")
    )

    # manda apenas se tiver algum passageiro no grupo.
    for g in grupos:
        for passageiro in passageiros:
            passageiros_map[g.id].append(
                {
                    "travel_id": passageiro["travel_id"],
                    "buseiro_id": passageiro["buseiro_id"],
                }
            )

    groups_ids = []
    for items in chunks(passageiros_map.items(), 10):
        partial_passageiros_map = dict(items)
        map_grupos_passagem_emitidos = _client.bulk_grupo_tem_passagem_emitida(partial_passageiros_map)
        groups_ids.extend([g for (g, tem_emissao) in map_grupos_passagem_emitidos.items() if tem_emissao])

    return grupos.filter(id__in=groups_ids)


def get_ids_rotas_ativas_empresa(company_internal_id, modelo_venda):
    return _client.get_ids_rotas_ativas_empresa(company_internal_id, modelo_venda)


def get_ids_empresas_auto_integra_operacao_agora():
    client: RodoviariaClient = get_client("rodoviaria")
    return client.get_ids_empresas_auto_integra_operacao_agora()


def top_trechos_com_divergencia(days_behind, quantity):
    return _client.top_trechos_com_divergencia(days_behind, quantity)


def get_detalhes_rotinas_por_rota(
    rodoviaria_rota_ids, trechos_vendidos_ids_por_rota_id, datetimes_a_ignorar_por_rota_id, extend_dates=True
):
    return _client.get_detalhes_rotinas_por_rota(
        rodoviaria_rota_ids, trechos_vendidos_ids_por_rota_id, datetimes_a_ignorar_por_rota_id, extend_dates
    )


def list_integracoes_rodoviaria():
    return _client.list_integracoes_rodoviaria()


def get_data_limite_rotas_integradas(company_ids):
    return _client.get_data_limite_rotas_integradas(company_ids)


def get_rotas_novas_para_integrar(ids_empresas):
    return _client.get_rotas_novas_para_integrar(ids_empresas)


def get_rotas_ativas_para_reintegrar(ids_empresas):
    return _client.get_rotas_ativas_para_reintegrar(ids_empresas)


def get_rotinas_inativas_por_rota_integrada_ativa(ids_empresas):
    return _client.get_rotinas_inativas_por_rota_integrada_ativa(ids_empresas)


def get_ids_rotas_integradas_inativas(ids_empresas):
    return _client.get_ids_rotas_integradas_inativas(ids_empresas)


@my_shared_task(queue="rodoviaria")
@traced("rodoviaria_svc.compare_grupos_marketplace")
def compare_grupos_marketplace(company_id):
    grupos = (
        Grupo.objects.prefetch_related(
            Prefetch("rota__itinerario", queryset=Checkpoint.objects.select_related("local__cidade")),
        )
        .filter(
            company_id=company_id,
            modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
            datetime_ida__gt=timezone.now(),
            datetime_ida__lte=timezone.now() + timedelta(days=30),
        )
        .exclude(
            status="canceled",
        )
        .order_by(
            "datetime_ida",
        )
    )
    for grupo in grupos:
        _, erros = itinerario(grupo)
        grupo.rodoviaria_compativel = not erros

    Grupo.objects.bulk_update(grupos, ["rodoviaria_compativel"])


def get_informacoes_passagem_api_parceiro(buseiro_id, modelo_venda, travel_id):
    client: RodoviariaClient = get_client("rodoviaria")
    try:
        return client.get_informacoes_passagem_api_parceiro(buseiro_id, modelo_venda, travel_id)
    except client.exceptions.HTTPErrorNotFound:
        return []
    except client.exceptions.HTTPErrorNotImplemented as exc:
        raise ServiceNotImplementedInRodoviaria(str(exc)) from exc
    except client.exceptions.ClientError as exc:
        raise IntegrationErrorRodoviaria(str(exc)) from exc


def listar_tipos_assentos(params: RodoviariaListarTiposAssentosParams):
    client: RodoviariaClient = get_client("rodoviaria")
    return client.listar_tipos_assentos(params)


def linkar_tipo_assento(id_assento, tipo_assento_buser_preferencial, tipos_assentos_buser_match):
    client: RodoviariaClient = get_client("rodoviaria")
    return client.linkar_tipo_assento(id_assento, tipo_assento_buser_preferencial, tipos_assentos_buser_match)


def get_cronograma_atualizacao_rodoviaria(params):
    client: RodoviariaClient = get_client("rodoviaria")
    return client.get_cronograma_atualizacao(params)


def update_cronograma_atualizacao_rodoviaria(horarios):
    client: RodoviariaClient = get_client("rodoviaria")
    return client.update_cronograma_atualizacao(horarios)


def grupo_nao_atualiza_preco(grupo: Grupo):
    client: RodoviariaClient = get_client("rodoviaria")
    response = client.get_empresas_by_features(["nao_atualiza_preco"])
    return any(
        grupo.company_id == e["company_internal_id"] and grupo.modelo_venda == e["modelo_venda"]
        for e in response["empresas"]
    )


@memoize(timeout=86400)
def get_atualizacoes_feitas_por_company(company_id: int, modelo_venda: str):
    client: RodoviariaClient = get_client("rodoviaria")
    try:
        response = client.get_company_info(company_id, modelo_venda)
    except client.exceptions.ClientError:
        return True, True
    features = response.get("features", [])
    return "nao_atualiza_preco" not in features, "nao_atualiza_vagas" not in features


def get_atualizacoes_feitas_por_grupo(grupo: Grupo):
    return get_atualizacoes_feitas_por_company(grupo.company_id, grupo.modelo_venda)


@memoize(timeout=5 * 60)
def busca_viagens_api_na_search(
    origem: LocalEmbarqueAdapter | CidadeAdapter, destino: LocalEmbarqueAdapter | CidadeAdapter, data_ida: datetime
):
    rodoviaria_prod = globalsettings_svc.get("rodoviaria_prod")
    percentual_search_para_buscar_na_api_rodoviaria = globalsettings_svc.get(
        "percentual_search_para_buscar_na_api_rodoviaria"
    )
    if (
        not rodoviaria_prod
        or not percentual_search_para_buscar_na_api_rodoviaria
        or not all([origem, destino, data_ida])
        or random.random() * 100 > percentual_search_para_buscar_na_api_rodoviaria
    ):
        return
    client: RodoviariaClient = get_client("rodoviaria")
    client.buscar_viagens_todas_empresas_api_na_search(origem.cidade.id, destino.cidade.id, data_ida.date())
    return True


class ServiceNotImplementedInRodoviaria(RuntimeError):
    pass


class IntegrationErrorRodoviaria(RuntimeError):
    pass
