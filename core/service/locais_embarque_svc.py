from datetime import datetime, timedelta

from django.db.models.expressions import RawSQL

from core.models_commons import Cidade
from core.models_rota import LocalEmbarque, Weekdays
from core.serializers.serializer_cidade import CidadeSerializer


def list_locais_embarque_ativos_by_cidade(cidade_slug):
    cidade_queryset = Cidade.objects.to_serialize(CidadeSerializer)
    cidade = cidade_queryset.get(slug=cidade_slug)
    locais_embarque = (
        list_locais_embarque_ativos()
        .filter(cidade=cidade.pk)
        .values(
            "nickname",
            "nickname_slug",
            "endereco_bairro",
            "bairro_slug",
            "endereco_logradouro",
            "endereco_numero",
            "latitude",
            "longitude",
        )
    )
    return {"pontos": list(locais_embarque), "cidade": cidade.serialize()}


def list_locais_embarque_ativos(limit=None):
    local_embarque_qs = (
        LocalEmbarque.objects.filter(ativo=True)
        .select_related("cidade")
        .annotate(
            count_total_rotas=RawSQL(
                "SELECT count(id) FROM core_rota r WHERE r.origem_id = core_localembarque.id OR r.destino_id = core_localembarque.id",
                [],
            )
        )
        .order_by("-count_total_rotas")
    )

    if limit is not None:
        local_embarque_qs = local_embarque_qs[:limit]

    return local_embarque_qs


def calculate_arrival_for_first_pde(
    boarding_point: LocalEmbarque | None, departure_datetime: datetime | None
) -> datetime | None:
    if boarding_point is None or departure_datetime is None:
        return None

    maximum_stay_minutes = boarding_point.max_minutos_permanencia
    weekdays = list(boarding_point.weekdays.all())

    if restriction := filter_weekdays_by_usage_time(weekdays, departure_datetime):
        maximum_stay_minutes = restriction.max_minutos_permanencia

    return departure_datetime - timedelta(minutes=maximum_stay_minutes or 0)


def filter_weekdays_by_usage_time(weekdays: list[Weekdays], usage_datetime: datetime) -> Weekdays | None:
    restriction = None
    dia = usage_datetime.isoweekday()
    for weekday in weekdays:
        if (
            weekday
            and weekday.dia == dia
            and weekday.start_time
            and weekday.end_time
            and weekday.start_time <= usage_datetime.time() <= weekday.end_time
        ):
            restriction = weekday
    return restriction
