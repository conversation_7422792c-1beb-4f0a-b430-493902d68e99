from datetime import date
from decimal import Decimal as D
from enum import Enum
from functools import cached_property
from typing import TYPE_CHECKING

import django
from beeline import traced
from django.contrib.auth.models import User
from django.contrib.postgres.fields import ArrayField
from django.core.validators import MaxValueValidator, MinLengthValidator, MinValueValidator
from django.db import models
from django.db.models import JSONField, Sum
from django.db.models.functions import Coalesce
from django.db.models.signals import post_save
from django.forms.models import model_to_dict
from django.utils.text import slugify
from django_fsm import FSMField, transition
from django_fsm_log.decorators import fsm_log_by, fsm_log_description
from django_qserializer.serialization import SerializableManager
from psqlextra.manager import PostgresManager
from simple_history.models import HistoricalRecords

from commons import dateutils, storage, ufs_brasil
from commons.dateutils import now, to_default_tz
from commons.django.constraints import UniqueConstraint
from commons.django_utils import EnumChoice
from commons.enum import ModeloVenda
from commons.storage import private_media_storage
from commons.utils import only_alphanum, only_numbers
from core.constants import STATUS_ONIBUS_CHOICES, TIPOS_ASSENTO_CHOICES, TIPOS_ONIBUS_CHOICES
from core.models_commons import Cidade, DocValidationProvider, PaymentOptionMixin, Reputation

ICMS_TIPOS = [(c, c) for c in ["normal", "com_desconto", "isento", "outros"]]
_2DECIMAL = D("1e-2")

if TYPE_CHECKING:
    from django.db.models.manager import RelatedManager

    from pagamento_parceiro.models import ConfiguracaoPagamento


class Company(PaymentOptionMixin):
    class Regional(models.TextChoices):
        BH = "BH"
        SP = "SP"
        NE = "NE"

    class Vinculo(models.TextChoices):
        FIXA = "fixa"
        TEMPORARIA = "temporaria"
        MKTPLACE = "marketplace"
        PROSPECT = "prospect"
        SOCORRO = "socorro"
        SEO = "seo"

    class VinculoReal(models.TextChoices):
        FRETAMENTO = "fretamento"
        MARKETPLACE = "marketplace"
        HIBRIDO = "hibrido"

    class Regime(models.TextChoices):
        SIMPLES = "simples"
        PRESUMIDO = "presumido"
        REAL = "real"

    class SemanasRepasse(models.IntegerChoices):
        UMA_SEMANA = 1
        DUAS_SEMANAS = 2

    objects: SerializableManager = SerializableManager(
        select_related=(
            "cidade",
            "corresp_cidade",
            "reputation",
            "bank_account",
            "gestor",
            "gestor_qualidade",
        )
    )
    id: int
    name = models.CharField(max_length=128)
    friendly_name = models.TextField(null=True, blank=True)
    is_enabled = models.BooleanField(default=True)
    saque_enabled = models.BooleanField(default=True)
    emissao_nf_enabled = models.BooleanField(default=False)
    data_ativacao_pedagio = models.DateField(null=True, default=None)
    cnpj = models.CharField(max_length=14, blank=True)
    inscricao_estadual = models.CharField(max_length=14, blank=True, null=True)
    registro_estadual = models.CharField(max_length=30, blank=True, null=True, default="")
    razao_social = models.CharField(max_length=128, blank=True, null=True)
    nome_fantasia = models.CharField(max_length=128, blank=True, null=True)  # TODO: verificar se ainda é necessário
    endereco_logradouro = models.CharField(max_length=2048, blank=True, null=True)
    endereco_numero = models.CharField(max_length=14, blank=True, null=True)
    endereco_complemento = models.CharField(max_length=14, blank=True, null=True)
    endereco_bairro = models.CharField(max_length=1024, blank=True, null=True)
    endereco_cep = models.CharField(max_length=8, blank=True, null=True)
    cidade_id: int
    cidade = models.ForeignKey(Cidade, null=True, blank=True, on_delete=models.CASCADE)
    regional = models.CharField(max_length=2, null=True, blank=True, choices=Regional.choices)
    corresp_endereco_logradouro = models.CharField(max_length=2048, blank=True, null=True)
    corresp_endereco_numero = models.CharField(max_length=14, blank=True, null=True)
    corresp_endereco_complemento = models.CharField(max_length=14, blank=True, null=True)
    corresp_endereco_bairro = models.CharField(max_length=1024, blank=True, null=True)
    corresp_endereco_cep = models.CharField(max_length=8, blank=True, null=True)
    corresp_cidade_id: int
    corresp_cidade = models.ForeignKey(
        Cidade, null=True, blank=True, related_name="corresp_cidade", on_delete=models.CASCADE
    )
    contato_name = models.CharField(max_length=1024, null=True, blank=True)
    representante_legal_name = models.CharField(max_length=128, null=True, blank=True)
    representante_legal_email = models.CharField(max_length=128, null=True, blank=True)
    phone = models.CharField(max_length=1024, null=True, blank=True)  # DEPRECATED: telefones de contato com a empresa
    whatsapp_phone = models.CharField(max_length=16, blank=True, null=True)  # telefone para pax contactar pelo whatsapp
    comercial_phone = models.CharField(max_length=16, blank=True, null=True)  # telefone para pax
    operational_phone = models.CharField(max_length=16, blank=True, null=True)  # telefone usado para emissão de CTeOS
    emergencial_phone = models.CharField(
        max_length=16, blank=True, null=True
    )  # telefone para time de risco e segurança
    # http://www.antt.gov.br/passageiros/TAF__Resolucoes_publicadas__Empresas_com_autorizacao.html
    taf = models.CharField(max_length=12, blank=True, null=True)
    cteos_serie = models.IntegerField(default=2, null=True, blank=True)
    is_simples_nacional = models.BooleanField(default=False)
    internal_note = models.TextField(null=True, blank=True, max_length=1024, verbose_name="Nota interna")
    has_accepted_contract = models.BooleanField(default=False)
    reputation_id: int
    reputation = models.ForeignKey(Reputation, null=True, on_delete=models.CASCADE)
    tecnospeed_handle = models.IntegerField(null=True, blank=True)
    has_sent_digital_certificate = models.BooleanField(default=False)
    tipo_icms = models.CharField(max_length=32, default="normal", choices=ICMS_TIPOS, null=True, blank=True)
    datetime_expiracao_certificado = models.DateTimeField(null=True, blank=True)
    nibo_id = models.CharField(max_length=36, blank=True, null=True)
    gestor_id: int
    gestor = models.ForeignKey(User, related_name="empresas_geridas", null=True, on_delete=models.SET_NULL)
    gestor_qualidade_id: int
    gestor_qualidade = models.ForeignKey(
        User, related_name="empresas_geridas_qualidade", null=True, on_delete=models.SET_NULL
    )
    gestor_marketplace_id: int
    gestor_marketplace = models.ForeignKey(
        User, related_name="empresas_geridas_marketplace", null=True, on_delete=models.SET_NULL
    )
    gestor_extras_id: int
    gestor_extras = models.ForeignKey(
        User, related_name="empresas_geridas_extras", null=True, on_delete=models.SET_NULL
    )
    slug = models.CharField(max_length=256, editable=False, null=True, unique=True)
    logo_s3key = models.CharField(max_length=256, null=True, blank=True)
    vinculo = models.CharField(max_length=20, default="prospect", choices=Vinculo.choices)
    cnpj_saque_alternativo = models.CharField(max_length=14, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    parent_company_hibrido_id: int
    parent_company_hibrido = models.ForeignKey("Company", blank=True, null=True, on_delete=models.SET_NULL)
    casos_particulares = models.TextField(null=True)
    regime = models.TextField(null=True, choices=Regime.choices)
    aliquota = models.FloatField(null=True)
    prazo_repasse_semana = models.IntegerField(default=1, choices=SemanasRepasse.choices)
    # sistema de gestão financeiro da empresa:
    erp_system = models.TextField(null=True)
    # sistema de gestão operacional da empresa:
    ops_system = models.TextField(null=True)
    # Idwall
    id_wall_percent = models.FloatField(null=True)

    # em migração: profile.company vai morrer ainda
    drivers = models.ManyToManyField(User, related_name="empresas_como_motorista", blank=True, through="CompanyDriver")
    partners = models.ManyToManyField(User, related_name="empresas_como_parceiro", blank=True)

    # empresas com cnpj diferente, mas que são do mesmo dono
    economic_group = models.ManyToManyField("self", blank=True)

    if TYPE_CHECKING:
        configuracoes_pagamento = RelatedManager[ConfiguracaoPagamento]()

    class Meta:
        permissions = [
            ("can_manage_company_critical_data", "Can manage company's critical data"),
        ]

    def __str__(self):
        return self.name

    @property
    def logo_url(self):
        if not self.logo_s3key:
            return None
        return storage.public_thumbor_url(self.logo_s3key, width=200)

    def save(self, *args, **kwargs):
        if not self.reputation:
            self.reputation = Reputation.get_or_create()

        self.slug = slugify(self.name)
        super(Company, self).save(*args, **kwargs)

    @property
    def cnpj_hibrido(self):
        return self.parent_company_hibrido.cnpj if self.parent_company_hibrido else ""

    @property
    def cobra_taxa_servico_checkout(self):
        try:
            taxa_obj = TaxaServicoCheckout.objects.get(company_id=self.id)
        except TaxaServicoCheckout.DoesNotExist:
            return False
        return taxa_obj.ativa

    def to_dict_json(self) -> dict:
        d = super(Company, self).to_dict_json()
        d.update(
            {
                "id": self.id,
                "name": self.name,
                "cnpj": self.cnpj,
                "inscricao_estadual": self.inscricao_estadual,
                "registro_estadual": self.registro_estadual,
                "razao_social": self.razao_social,
                "nome_fantasia": self.nome_fantasia,
                "endereco_logradouro": self.endereco_logradouro,
                "endereco_numero": self.endereco_numero,
                "endereco_complemento": self.endereco_complemento,
                "endereco_bairro": self.endereco_bairro,
                "endereco_cep": self.endereco_cep,
                "cidade": self.cidade.to_dict_json() if self.cidade else None,
                "regional": self.regional,
                "corresp_endereco_logradouro": self.corresp_endereco_logradouro,
                "corresp_endereco_numero": self.corresp_endereco_numero,
                "corresp_endereco_complemento": self.corresp_endereco_complemento,
                "corresp_endereco_bairro": self.corresp_endereco_bairro,
                "corresp_endereco_cep": self.corresp_endereco_cep,
                "corresp_cidade": self.corresp_cidade.to_dict_json() if self.corresp_cidade else None,
                "contato_name": self.contato_name,
                "representante_legal_name": self.representante_legal_name,
                "representante_legal_email": self.representante_legal_email,
                "phone": self.phone,
                "comercial_phone": self.comercial_phone,
                "whatsapp_phone": self.whatsapp_phone,
                "operational_phone": self.operational_phone,
                "emergencial_phone": self.emergencial_phone,
                "taf": self.taf,
                "has_accepted_contract": self.has_accepted_contract,
                "is_enabled": self.is_enabled,
                "id_wall_percent": self.id_wall_percent,
                "saque_enabled": self.saque_enabled,
                "is_simples_nacional": self.is_simples_nacional,
                "emissao_nf_enabled": self.emissao_nf_enabled,
                "has_sent_digital_certificate": self.has_sent_digital_certificate,
                "cteos_serie": self.cteos_serie,
                "tipo_icms": self.tipo_icms if self.tipo_icms else None,
                "datetime_expiracao_certificado": (
                    self.datetime_expiracao_certificado.isoformat() if self.datetime_expiracao_certificado else None
                ),
                "gestor": {"id": self.gestor.id, "name": self.gestor.get_full_name()} if self.gestor else None,
                "gestor_qualidade": (
                    {"id": self.gestor_qualidade.id, "name": self.gestor_qualidade.get_full_name()}
                    if self.gestor_qualidade
                    else None
                ),
                "gestor_marketplace": (
                    {
                        "id": self.gestor_marketplace.id,
                        "name": self.gestor_marketplace.get_full_name(),
                    }
                    if self.gestor_marketplace
                    else None
                ),
                "gestor_extras": (
                    {"id": self.gestor_extras.id, "name": self.gestor_extras.get_full_name()}
                    if self.gestor_extras
                    else None
                ),
                "slug": self.slug,
                "logo_url": self.logo_url,
                "vinculo": self.vinculo,
                "data_ativacao_pedagio": self.data_ativacao_pedagio.isoformat() if self.data_ativacao_pedagio else None,
                "casos_particulares": self.casos_particulares,
                "regime": self.regime,
                "aliquota": self.aliquota,
                "prazo_repasse_semana": self.prazo_repasse_semana,
            }
        )
        return d


class CompanyStatusHistory(models.Model):
    class Reason(models.TextChoices):
        BANNED = "Banido"
        CHURN = "Churn"
        OTHER = "Outro"

    id: int
    status = models.BooleanField(default=False)
    reason = models.CharField(max_length=8, choices=Reason.choices, null=True, blank=True)
    created_at = models.DateTimeField("criado em", auto_now_add=True)
    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    description = models.TextField(null=True, blank=True)


class CompanyDriver(models.Model):
    class ContractType(models.TextChoices):
        CLT_TRADICIONAL = "CLT - Tradicional"
        CLT_INTERMITENTE = "CLT - Regime de Trabalho Intermitente"
        CLT_TEMPORARIO = "CLT - Contrato de Trabalho Temporário"
        PJ_SIMPLES = "PJ - Simples"
        MEI = "MEI"
        FREELANCER_TOTAL = "Freelancer total (sem CLT e sem CNPJ)"

    driver_id: int
    driver = models.ForeignKey("auth.User", db_column="user_id", on_delete=models.CASCADE)
    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    contract_type = models.TextField(choices=ContractType.choices, max_length=37, null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "driver_id": self.driver.id,
            "company": self.company.id,
            "contract_type": self.contract_type,
        }

    class Meta:
        db_table = "core_company_drivers"


class CompanyDocumentoEstadual(models.Model):
    class DocTypes(models.TextChoices):
        TA = "ta"  # embarque e desembarque em SP
        TAS = "tas"  # embarque e desembarque em SP

    class DocStatus(models.TextChoices):
        APROVADO = "aprovado"  # embarque e desembarque em SP
        REPROVADO = "reprovado"
        PENDENTE = "analise_pendente"

    id: int
    doc = models.FileField(upload_to="private/company_files", max_length=255, storage=private_media_storage)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    uf = models.CharField(max_length=2, choices=ufs_brasil.UFS)
    type = models.TextField(choices=DocTypes.choices)
    expire_date = models.DateField(null=True)
    status = models.TextField(choices=DocTypes.choices, default=DocStatus.PENDENTE)
    invalidated_reason = models.TextField(null=True, default=None)
    removed = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class CompanyGarage(models.Model):
    id: int
    objects: SerializableManager = SerializableManager()

    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)

    endereco_logradouro = models.CharField(max_length=2048, blank=True, null=True)
    endereco_numero = models.CharField(max_length=14, blank=True, null=True)
    endereco_complemento = models.CharField(max_length=14, blank=True, null=True)
    endereco_bairro = models.CharField(max_length=1024, blank=True, null=True)
    endereco_cep = models.CharField(max_length=8, blank=True, null=True)
    cidade_id: int
    cidade = models.ForeignKey(Cidade, null=True, blank=True, on_delete=models.CASCADE)


class OnibusQuerySet(models.QuerySet):
    def active(self):
        qs = self.exclude(removed=True)
        return qs

    def with_feature(self, feature):
        qs = self.filter(jsondata__features__contains=feature)
        return qs


class Onibus(models.Model):
    ModeloVenda = ModeloVenda

    id: int
    removed = models.BooleanField(
        default=False, help_text="Indica a exclusão do ônibus da plataforma, porém mantendo o histórico"
    )
    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    name = models.CharField(max_length=256)
    placa = models.CharField(
        max_length=7, validators=[MinLengthValidator(7, message="Placa deve conter 7 dígitos.")], unique=True
    )
    renavam = models.CharField(max_length=20, null=True, blank=True)
    uf = models.CharField(max_length=2, null=True, blank=True, choices=ufs_brasil.UFS)
    cadeirinhas = models.IntegerField(default=0)  # todo: deprecated
    telemetria_datetime = models.DateTimeField(null=True, blank=True)
    tipo = models.CharField(max_length=32, default=None, null=True, choices=TIPOS_ONIBUS_CHOICES)
    ano = models.IntegerField(validators=[MinValueValidator(1900)], null=True, blank=True)

    reputation_id: int
    reputation = models.ForeignKey(Reputation, null=True, on_delete=models.CASCADE)
    ano_fabricacao = models.IntegerField(validators=[MinValueValidator(1900)], null=True, blank=True)
    ano_modelo = models.IntegerField(validators=[MinValueValidator(1900)], null=True, blank=True)
    foto_placa = models.CharField(max_length=256, null=True, blank=True)
    foto_externa_thumbnail = models.CharField(max_length=256, null=True, blank=True)
    foto_interna = models.CharField(max_length=256, null=True, blank=True)

    # campo desatualizado, ver propriedade bus_status
    status = models.CharField(max_length=32, default="pendente", null=True, choices=STATUS_ONIBUS_CHOICES)
    mensagem_status = models.TextField(null=True, blank=True)
    temporario = models.BooleanField(null=True, blank=True)
    marca = models.CharField(max_length=256, null=True, blank=True)
    modelo = models.CharField(max_length=256, null=True, blank=True)
    motor = models.CharField(max_length=256, null=True, blank=True)
    numero_eixos = models.PositiveIntegerField(null=True)
    plano_gold = models.BooleanField(null=True)
    available = models.BooleanField(
        default=True,
        help_text="Indica a disponibilidade um Ônibus (ativo ou inativo) permitindo impedir um ônibus até sua adequação",
    )
    tem_banheiro = models.BooleanField(default=True)
    locadora = models.BooleanField(null=True)
    modelo_venda = models.CharField(max_length=32, default=ModeloVenda.BUSER, choices=ModeloVenda.choices)
    capacidade_antt = models.IntegerField(null=True, blank=True)

    pendente_em = models.DateTimeField(auto_now=True, null=True)
    avaliado_em = models.DateTimeField(auto_now=False, null=True)

    objects = OnibusQuerySet.as_manager()

    if TYPE_CHECKING:
        infoonibus_set = RelatedManager["InfoOnibus"]()
        classes = RelatedManager["OnibusClasse"]()

    class Meta:
        ordering = ["-id"]

    @cached_property
    def classe_capacidade(self):
        separator = " / "
        classes = sorted(
            (
                (classe.tipo, classe.capacidade, classe.capacidade_vendida)
                for classe in self.classes.all()
                if classe.capacidade > 0
            ),
            key=lambda x: x[0],
            reverse=True,
        )
        try:
            classe_list, capacidade_list, capacidade_vendida_list = zip(*classes)
        except ValueError:
            classe_list, capacidade_list, capacidade_vendida_list = [], [], []
        return {
            "classe": separator.join(classe_list),
            "capacidade": separator.join(map(str, capacidade_list)),
            "capacidade_vendida": separator.join(map(str, capacidade_vendida_list)),
            "capacidade_total": sum(capacidade_list),
        }

    @property
    def bus_status(self):
        """
        débito técnico
        o campo status está altamente desatualizado.
        precisa-se ajeitar sua atualização + fazer um comando para corrigir erros
        """
        infoonibus = self.infoonibus_set.all()
        status_map = {i.tipo: i.status for i in infoonibus}
        important_fields = ["dut", "seguro", "assentos"]

        any_pendente = any(status_map[i] == "pendente" for i in important_fields)
        any_reproved = any(status_map[i] == "reprovado" for i in important_fields)
        approved_foto = status_map["foto_externa"] == "aprovado"
        if any_pendente:
            return "pendente"
        elif any_reproved:
            return "rejeitado"
        elif approved_foto:
            return "aprovado"
        else:
            return "aprovado com pendências"

    @property
    def homologado(self):
        return self.bus_status in ["aprovado", "aprovado com pendências"]

    @property
    def classe(self):
        return self.classe_capacidade["classe"]

    @property
    def capacidade(self):
        return self.classe_capacidade["capacidade"]

    @property
    def capacidade_vendida(self):
        return self.classe_capacidade["capacidade_vendida"]

    @property
    def foto_externa_public_url(self):
        if not self.foto_externa_thumbnail:
            return None
        return storage.public_thumbor_url(self.foto_externa_thumbnail, width=900, height=400, filters=("format(jpeg)",))

    @property
    def capacidade_total(self) -> int:
        return self.classe_capacidade["capacidade_total"]

    @property
    def foto_placa_url(self):
        return storage.storage_url(self, "foto_placa")

    @property
    def foto_externa_url(self):
        return storage.storage_url(self, "foto_externa")

    @property
    def has_driver_seat(self):
        return self.tipo not in ("toco", "trucado")

    @property
    def foto_interna_url(self):
        return storage.storage_url(self, "foto_interna")

    @property
    def is_plotado(self):
        return "plotagem_buser" in self.features

    @property
    def features(self):
        if not self.jsondata:
            return []

        return self.jsondata.get("features", [])

    @property
    def tipo_simplificado(self):
        if self.tipo in ("toco", "trucado"):
            return "baixo"
        return self.tipo

    @property
    def qtd_pneus(self):
        combinacoes = {"2baixo": 6, "3baixo": 8, "3LD": 8, "3DD": 8, "4LD": 10, "4DD": 10}
        tipo = self.tipo_simplificado
        if tipo in ("micro-onibus", "van", "carro"):
            return 4
        return combinacoes[f"{self.numero_eixos}{tipo}"]

    # EXEMPLO DE JSONDATA
    # jsondata = {
    #     'features': [
    #         'telemetria',
    #         'camera_fadiga',
    #         'plotagem_buser',
    #         'seguro_buser',
    #         'encosto_cabeca',
    #         'cinto_seguranca',
    #         'cartao_pre_pago',
    #         'tomadas_individuais',
    #         'tomadas_coletivas',
    #         'usb_individual',
    #         'wifi',
    #         'som',
    #         'tv',
    #         'video_boas_vindas',
    #         'assento_extra_motorista'
    #     ],
    #     'licences': [
    #         'DER_SP',
    #         'ANTT'
    #     ]
    # }

    def jsondata_default():
        return {"features": [], "licences": []}

    jsondata = JSONField(default=jsondata_default, null=True, blank=True)

    def to_dict_json(self) -> dict:
        d = {
            "id": self.id,
            "name": self.name,
            "alias": f"{self.placa} ({self.company.name})",
            "placa": self.placa,
            "removed": self.removed,
            "renavam": self.renavam,
            "uf": self.uf,
            "classe": self.classe,
            "capacidade": self.capacidade,
            "capacidade_vendida": self.capacidade_vendida,
            "tipo": self.tipo,
            "infos": {info.tipo: info.to_dict_json() for info in self.infoonibus_set.all()},
            "tem_banheiro": self.tem_banheiro,
            "numero_eixos": self.numero_eixos,
            "plano_gold": self.plano_gold,
            "available": self.available,
        }
        return d

    @property
    def has_marcacao_assento(self):
        return PoltronaOnibus.objects.filter(onibus=self, ativo=True).exists()


class OnibusDocumentacao(models.Model):
    id: int
    bus_id: int
    bus = models.ForeignKey(Onibus, on_delete=models.CASCADE)
    renavam = models.TextField(null=True, blank=True)
    plate = models.CharField(max_length=7, validators=[MinLengthValidator(7, message="Placa deve conter 7 dígitos.")])
    state = models.CharField(max_length=2, null=True, blank=True, choices=ufs_brasil.UFS)
    chassis = models.TextField(null=True, blank=True)
    licensed_at = models.DateField(null=True, blank=True)
    license_term = models.IntegerField(null=True, blank=True)
    brand_and_model = models.TextField(null=True, blank=True)
    model_year = models.IntegerField(null=True, blank=True)
    manufacturer_year = models.IntegerField(null=True, blank=True)
    notes = models.TextField(null=True, blank=True)
    insurance_expiration = models.DateField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True, null=True)


class OnibusDocumentoEstadual(models.Model):
    class DocTypes(models.TextChoices):
        CVS = "cvs"  # embarque e desembarque em SP

    class DocStatus(models.TextChoices):
        APROVADO = "aprovado"
        REPROVADO = "reprovado"
        PENDENTE = "pendentes"

    id: int
    doc = models.FileField(upload_to="private/bus_files", max_length=255, storage=private_media_storage)
    bus_id: int
    bus = models.ForeignKey(Onibus, on_delete=models.CASCADE)
    uf = models.CharField(max_length=2, choices=ufs_brasil.UFS)
    type = models.TextField(choices=DocTypes.choices)
    expire_date = models.DateField(null=True)
    status = models.TextField(choices=DocStatus.choices, default="pendente")
    mensagem = models.TextField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class OnibusAprovacao(models.Model):
    class Status(models.TextChoices):
        PROCESSANDO = "processando"
        APROVADO = "aprovado"
        REPROVADO = "reprovado"

    id: int
    provider = models.TextField(choices=DocValidationProvider.choices)
    bus_id: int
    bus = models.ForeignKey(Onibus, on_delete=models.CASCADE)
    document_id: int
    document = models.ForeignKey(OnibusDocumentacao, on_delete=models.CASCADE)
    status = models.TextField(choices=Status.choices, null=True, blank=True)
    reason = models.TextField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)


class OnibusAprovacaoLog(models.Model):
    id: int
    """Armazena as respostas do serviço de validação"""

    #: Serviço que a gente ta usando para fazer a aprovação
    provider = models.TextField(choices=DocValidationProvider.choices)

    #: Aprovação associada a esse log
    aprovacao_id: int
    aprovacao = models.ForeignKey(OnibusAprovacao, null=False, on_delete=models.CASCADE)

    #: Dados recebidos da requisição
    data = models.JSONField()

    created_at = models.DateTimeField(auto_now_add=True, null=True)


class OnibusClasse(models.Model):
    id: int
    tipo = models.CharField(max_length=256, choices=TIPOS_ASSENTO_CHOICES)
    capacidade = models.PositiveIntegerField(default=0)
    tem_acessibilidade = models.BooleanField(null=True, blank=True)
    # capacidade_vendida representa a quantidade de assentos vendidos
    # quando a viagem tem 2 motoristas.
    capacidade_vendida = models.PositiveIntegerField(default=0)
    onibus_id: int
    onibus = models.ForeignKey(Onibus, on_delete=models.CASCADE, related_name="classes")


class InfoOnibus(models.Model):
    """
    Certas propriedades do ônibus precisam ser validadas por analistas.
    Este modelo representa o estado da aprovação da propriedade + dados da propriedade em si.
    """

    INFO_TYPE_CHOICES = [
        ("dut", "dut"),
        ("seguro", "seguro"),
        ("foto_externa", "foto_externa"),
        ("assentos", "assentos"),
        ("laudo_inspecao", "laudo_inspecao"),
        ("dedetizacao", "dedetizacao"),
        ("aet", "aet"),
    ]
    INFO_STATUS_CHOICES = [("aprovado", "aprovado"), ("reprovado", "reprovado"), ("pendente", "pendente")]
    SEGURO_TYPE_CHOICES = [("antt", "antt"), ("buser", "buser")]
    id: int
    onibus_id: int
    onibus = models.ForeignKey(Onibus, on_delete=models.CASCADE)
    tipo = models.CharField(max_length=20, choices=INFO_TYPE_CHOICES)
    numeracao = models.CharField(max_length=255, null=True, blank=True)
    foto = models.CharField(max_length=256, null=True, blank=True)
    foto_reduzida = models.CharField(max_length=256, null=True, blank=True)
    status = models.CharField(max_length=20, choices=INFO_STATUS_CHOICES, default="pendente")
    mensagem = models.TextField()
    tipo_seguro = models.CharField(max_length=20, choices=SEGURO_TYPE_CHOICES, null=True, blank=True)
    data_vencimento = models.DateTimeField(null=True)
    ano_de_exercicio = models.IntegerField(validators=[MinValueValidator(1900)], null=True)
    uf = models.CharField(max_length=2, choices=ufs_brasil.UFS, null=True)
    uptated_at = models.DateTimeField(auto_now=True)

    @classmethod
    def get_types(cls):
        return [type[0] for type in cls.INFO_TYPE_CHOICES]

    def to_dict_json(self) -> dict:
        return {
            "numeracao": self.numeracao,
            "foto": self.foto,
            "foto_url": storage.storage_url(self, "foto"),
            "status": self.status,
            "mensagem": self.mensagem,
            "tipo_seguro": self.tipo_seguro,
            "uptated_at": self.uptated_at,
            "data_vencimento": self.data_vencimento,
            "uf": self.uf,
            "ano_de_exercicio": self.ano_de_exercicio,
        }

    class Meta:
        unique_together = ["onibus", "tipo"]


class Vendor(PaymentOptionMixin):
    name = models.CharField(max_length=128)
    is_enabled = models.BooleanField(default=True)
    cnpj = models.CharField(max_length=14, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

    def get_formatted_cnpj(self):
        cnpj = self.cnpj
        if cnpj and len(cnpj) == 14:
            return "%s.%s.%s/%s-%s" % (cnpj[0:2], cnpj[2:5], cnpj[5:8], cnpj[8:12], cnpj[12:14])

    def to_dict_json(self) -> dict:
        d = super(Vendor, self).to_dict_json()
        d.update(
            {
                "id": self.id,
                "name": self.name,
                "is_enabled": self.is_enabled,
                "cnpj": self.get_formatted_cnpj(),
            }
        )
        return d


class StatusSefaz(EnumChoice):
    AUTORIZADA = "autorizada"  # Enviado ao Sefaz e autorizado com sucesso:
    CANCELADA = "cancelada"  # Evento de cancelamento solicitado ao Sefaz e realizado com sucesso:
    REJEITADA = "rejeitada"  # Enviado ao Sefaz porém tinha erros de informação e não foi aceito:
    INUTILIZADA = (
        "inutilizada"  # Ocorre geralmente para numerações, quando a numeração é pulada e não se deseja utilizar a mesma
    )
    DENEGADA = "denegada"  # Nota rejeitada quando existe alguma pendência na Sefaz de algum dos autores na CTe:
    ENVIADA = "enviada"  # Enviado ao Sefaz, porém ainda não foi recebido o retorno:
    REGISTRADA = "registrada"  # Enviado a Tecnospeed, porém ainda não foi enviado ao Sefaz:
    RECEBIDA = "recebida"  # Enviado ao Sefaz com retorno do protocolo de consulta, porém sem uma situação final:

    @classmethod
    def choices(cls):
        return [(status, status.value) for status in cls]


class AcaoSefaz(EnumChoice):
    CANCELAR = "cancelar"  # Esteira da morte
    CANCELAR_FALHOU = "cancelar_falhou"  # Esteira da morte que falhou
    EMITIR = "emitir"  # Esteira da emissão
    EMITIR_FALHOU = "emitir_falhou"  # Esteira da emissão que falhou
    NOOP = "noop"  # Estado final desejado


class NotaFiscal(models.Model):
    id: int
    objects: SerializableManager = SerializableManager(
        select_related=("company", "payment"),
        prefetch_related=["grupo_set"],
    )

    class Status(models.TextChoices):
        WAITING_PAYMENT = "waiting_payment"
        PAID = "paid"
        DISAPPROVED = "disapproved"
        WAITING_APPROVAL = "waiting_approval"

    vendor_id: int
    vendor = models.ForeignKey(Vendor, blank=True, null=True, on_delete=models.CASCADE)
    company_id: int
    company = models.ForeignKey(Company, blank=True, null=True, on_delete=models.CASCADE)
    numero = models.CharField(max_length=64, null=True, blank=True)
    valor = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    aliquota_icms = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    reducao_bc = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    valor_pagamento = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    pago = models.BooleanField(default=False)  # todo: remover
    status = models.CharField(max_length=32, default=Status.WAITING_APPROVAL, choices=Status.choices)
    cteos_key = models.CharField(max_length=64, null=True, blank=True)
    cteos_serie = models.IntegerField(null=True, blank=True)  # todo: nao tem porque esse cara ser IntegerField
    discriminacao = models.TextField(null=True, blank=True)
    source = models.TextField(null=True, blank=True)
    cnpj = models.CharField(max_length=14, blank=True)
    s3key = models.CharField(max_length=256, null=True, blank=True)
    boleto_s3key = models.CharField(max_length=256, null=True, blank=True)
    xml_s3key = models.CharField(max_length=256, null=True, blank=True)
    data_emissao = models.DateTimeField(null=True, blank=True)
    data_competencia = models.DateField(null=True, blank=True)
    data_vencimento = models.DateField(null=True, blank=True)
    fix_instructions = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    removed = models.BooleanField(default=False)

    provider = models.CharField(max_length=32, null=True, blank=True)  # upload, tecnospeed
    acao_sefaz = models.CharField(max_length=32, choices=AcaoSefaz.choices(), default=AcaoSefaz.NOOP)
    status_sefaz = models.CharField(max_length=32, choices=StatusSefaz.choices(), null=True, blank=True)
    cancelada_posteriormente = models.BooleanField(default=False, null=True, blank=True)
    data_cancelamento = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return str(self.cnpj) + " " + str(self.numero)

    @property
    def emissao_auto(self):
        return self.provider != "upload"

    @property
    def status_parceiro(self):
        if self.status == "paid":
            return "paga"
        elif self.emissao_auto is False and self.cancelada_posteriormente is True:
            return "upload"
        elif self.acao_sefaz == AcaoSefaz.NOOP and self.status_sefaz in (
            StatusSefaz.CANCELADA,
            StatusSefaz.REJEITADA,
            StatusSefaz.DENEGADA,
            StatusSefaz.INUTILIZADA,
        ):
            return "cancelada"
        elif self.acao_sefaz == AcaoSefaz.NOOP and self.status_sefaz == StatusSefaz.AUTORIZADA:
            return "emitida"
        elif self.acao_sefaz == AcaoSefaz.EMITIR:
            return "emitindo"
        elif self.acao_sefaz == AcaoSefaz.CANCELAR:
            return "cancelando"
        elif self.acao_sefaz == AcaoSefaz.EMITIR_FALHOU or self.acao_sefaz == AcaoSefaz.CANCELAR_FALHOU:
            return self.acao_sefaz

    @classmethod
    def strip_numero(cls, numero):
        if numero:
            n = only_alphanum(str(numero)).lower()
            if only_numbers(n) == n:
                return str(int(n))
            return n
        return numero

    def update_from_cteos(self, cteos):
        try:
            self.numero = cteos["numero"]
        except KeyError:
            pass
        try:
            self.cteos_serie = cteos["serie"]
        except KeyError:
            pass
        try:
            self.cteos_key = cteos["chave"]
        except KeyError:
            pass
        try:
            self.status_sefaz = cteos["situacao"].lower()
        except KeyError:
            pass
        if self.status_sefaz == StatusSefaz.REJEITADA.value and cteos.get("motivo"):
            self.fix_instructions = f"{self.fix_instructions or ''} - Motivo:{cteos.get('motivo')}".strip(" - ")

        self.save(
            update_fields=[
                "numero",
                "cteos_serie",
                "cteos_key",
                "status_sefaz",
                "fix_instructions",
                "updated_at",
            ]
        )

    def get_formatted_cnpj(self):
        cnpj = self.cnpj
        if cnpj and len(cnpj) == 14:
            return "%s.%s.%s/%s-%s" % (cnpj[0:2], cnpj[2:5], cnpj[5:8], cnpj[8:12], cnpj[12:14])
        return None

    def pagar(self, value, user):
        self.status = "paid"
        self.save(
            update_fields=[
                "status",
                "updated_at",
            ]
        )

    def get_url(self):
        return storage.storage_url(self, "s3key")

    def cancelar_pagamento(self, reason):
        self.status = NotaFiscal.Status.WAITING_PAYMENT
        self.save(
            update_fields=[
                "status",
                "updated_at",
            ]
        )

    def aprovar(self, update_fields=None):
        self.status = NotaFiscal.Status.WAITING_PAYMENT
        self.fix_instructions = None
        fields = [
            "status",
            "fix_instructions",
            "updated_at",
        ]
        if update_fields:
            fields.extend(update_fields)
        self.save(update_fields=fields)

    def reprovar(self, reason):
        self.status = NotaFiscal.Status.DISAPPROVED
        self.fix_instructions = reason
        self.save(
            update_fields=[
                "status",
                "fix_instructions",
                "updated_at",
            ]
        )

    def update_from_dict(self, d):
        if d.get("data_emissao"):
            self.data_emissao = dateutils.to_default_tz(d["data_emissao"])
        if d.get("data_competencia"):
            self.data_competencia = d["data_competencia"]
        if d.get("cnpj"):
            self.cnpj = only_numbers(d["cnpj"])
        if d.get("discriminacao"):
            self.discriminacao = d["discriminacao"]
        if d.get("data_vencimento"):
            self.data_vencimento = d["data_vencimento"]
        if d.get("numero"):
            self.numero = self.strip_numero(d["numero"])
        if d.get("valor"):
            self.valor = d["valor"]
        if d.get("valor_pagamento"):
            self.valor_pagamento = d["valor_pagamento"]
        if self.valor and not self.valor_pagamento:
            self.valor_pagamento = self.valor
        self.save(
            update_fields=[
                "data_emissao",
                "data_competencia",
                "cnpj",
                "discriminacao",
                "data_vencimento",
                "numero",
                "valor",
                "valor_pagamento",
                "valor_pagamento",
                "updated_at",
            ]
        )

    def to_dict_json(self, *, grupo_id=None):
        if grupo_id is None:
            # TODO: Essa query não pode ser feita no `to_dict_json()`.
            grupo_id = self.grupo_set.values_list("id", flat=True).first()
        return {
            "id": self.id,
            "grupo_id": grupo_id,
            "numero": self.numero,
            "serie": self.cteos_serie,
            "status": self.status,
            "status_sefaz": self.status_sefaz,
            "valor": self.valor,
            "aliquota_icms": self.aliquota_icms,
            "reducao_bc": self.reducao_bc,
            "valor_pagamento": self.valor_pagamento,
            "cnpj": self.get_formatted_cnpj(),
            "company_name": self.company.razao_social if self.company else None,
            "data_competencia": self.data_competencia.isoformat() if self.data_competencia else None,
            "data_emissao": to_default_tz(self.data_emissao).isoformat() if self.data_emissao else None,
            "created_at": to_default_tz(self.created_at).isoformat(),
            "discriminacao": self.discriminacao,
            "pago": self.pago,  # TODO: eliminar os usos disso aqui
            "data_vencimento": self.data_vencimento,
            "boleto_url": storage.storage_url(self, "boleto_s3key"),
            "url": self.get_url(),
            "xml_url": storage.storage_url(self, "xml_s3key"),
            "cteos_key": self.cteos_key,
            "acao_sefaz": self.acao_sefaz,
            "emissao_auto": self.emissao_auto,
            "provider": self.provider,
            "status_parceiro": self.status_parceiro,
        }


class PedidoReembolso(models.Model):
    company = models.ForeignKey(Company, blank=True, null=True, on_delete=models.CASCADE)
    grupo = models.ForeignKey("core.Grupo", on_delete=models.CASCADE)
    valor = models.DecimalField(max_digits=12, decimal_places=2)
    descricao = models.TextField()
    s3key = models.CharField(max_length=256, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    removed = models.BooleanField(default=False)

    @property
    def url(self):
        return storage.storage_url(self, "s3key")

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "created_at": to_default_tz(self.created_at).isoformat(),
            "valor": self.valor,
            "descricao": self.descricao,
            "url": self.url,
            "removed": self.removed,
        }


class ContratoParceiros(models.Model):
    id: int
    s3key = models.CharField(max_length=256)
    what_changed_text = models.TextField(blank=True, default="")
    created_at = models.DateTimeField("criado em", auto_now_add=True)
    last_updated = models.DateTimeField("atualizado em", auto_now=True)

    def to_dict_json(self) -> dict:
        return {
            "url": storage.storage_url(self, "s3key"),
            "what_changed_text": self.what_changed_text,
            "last_updated": to_default_tz(self.last_updated).isoformat(),
        }

    def save(self, *args, **kwargs):
        companies = Company.objects.filter(has_accepted_contract=True)
        for company in companies:
            company.has_accepted_contract = False
            company.save()
        super(ContratoParceiros, self).save(*args, **kwargs)


class AvisosParceirosQuerySet(models.QuerySet):
    def unread_by_user(self, user):
        qs = self.exclude(lido_por_admins=user)
        return qs

    def unread_by_company(self, company):
        qs = self.exclude(lido_por_empresas=company)
        return qs


class AvisosParceiros(models.Model):
    id: int
    title = models.CharField(max_length=512, blank=True)
    text = models.TextField()
    created_at = models.DateTimeField("criado em", auto_now_add=True)
    lido_por_empresas = models.ManyToManyField(Company, blank=True)
    lido_por_admins = models.ManyToManyField(User, blank=True)
    is_active = models.BooleanField(default=True)
    avisar_empresas = models.ManyToManyField(Company, related_name="avisos_da_empresa", blank=True)
    avisar_todas_empresas = models.BooleanField(default=True)
    avisar_vinculos = ArrayField(
        models.CharField(max_length=64, null=False, blank=False, choices=Company.VinculoReal.choices),
        blank=True,
        default=list,
    )
    anexos = ArrayField(models.CharField(max_length=512), null=True, blank=True)
    created_by_id: int
    created_by = models.ForeignKey(
        User, related_name="avisos_criados", null=True, on_delete=django.db.models.deletion.SET_NULL
    )

    objects = AvisosParceirosQuerySet.as_manager()

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "title": self.title,
            "text": self.text,
            "is_active": self.is_active,
            "avisar_todas_empresas": self.avisar_todas_empresas,
            "anexos": storage.storage_urls(self, "anexos"),
            "created_at": to_default_tz(self.created_at).isoformat(),
        }


class PartnerTerm(models.Model):
    id: int
    objects: SerializableManager = SerializableManager()

    class TermTypes(models.TextChoices):
        TERMOS_E_CONDICOES_DE_USO = "termos_e_condicoes_de_uso"
        CODIGO_DE_CONDUTA = "codigo_de_conduta"
        CONTRATO_DE_VIAGENS_EXTRAS = "contrato_de_viagens_extras"

    is_active = models.BooleanField(default=True, null=True, blank=True)
    s3_key = models.CharField(max_length=256)
    term_version = models.IntegerField(default=1, null=True)
    term_type = models.CharField(choices=TermTypes.choices, max_length=32)
    limit_date = models.DateField(null=True)

    companies = models.ManyToManyField(Company, blank=True)
    vinculos_reais = ArrayField(
        models.CharField(max_length=15, choices=Company.VinculoReal.choices), null=True, blank=True
    )

    created_at = models.DateTimeField(auto_now_add=True)
    created_by_id: int
    created_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL, related_name="created_by")
    updated_at = models.DateTimeField(auto_now=True)
    updated_by_id: int
    updated_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL, related_name="updated_by")
    history = HistoricalRecords()

    def to_dict_json_base(self):
        return {
            "id": self.id,
            "url": storage.storage_url(self, "s3_key"),
            "term_type": self.term_type,
            "term_version": self.term_version,
            "limit_date": self.limit_date,
            "vinculos_reais": self.vinculos_reais,
            "created_by": model_to_dict(self.created_by, ["id", "email"]) if self.created_by else None,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "is_active": self.is_active,
            "only_specific_companies": self.companies.exists(),
        }

    @classmethod
    def post_create(cls, sender, instance, created, *args, **kwargs):
        if not created:
            return
        result = (
            PartnerTerm.objects.exclude(pk=instance.pk)
            .exclude(term_type=PartnerTerm.TermTypes.CONTRATO_DE_VIAGENS_EXTRAS)
            .filter(term_type=instance.term_type, vinculos_reais=instance.vinculos_reais)
        )
        if result:
            result.update(is_active=False)


post_save.connect(PartnerTerm.post_create, sender=PartnerTerm)


class PartnerTermAcceptances(models.Model):
    id: int
    user_id: int
    user = models.ForeignKey(User, unique=False, on_delete=models.CASCADE)
    company_id: int
    company = models.ForeignKey(Company, unique=False, on_delete=models.CASCADE)
    accepted_at = models.DateTimeField(auto_now=True)
    accepted_term_id: int
    accepted_term = models.ForeignKey(PartnerTerm, related_name="acceptances", unique=False, on_delete=models.PROTECT)


class CompanyLending(models.Model):
    id: int
    objects: SerializableManager = SerializableManager()

    class Frequency(models.TextChoices):
        DAILY = "daily"
        WEEKLY = "weekly"
        MONTHLY = "monthly"

    class Type(models.TextChoices):
        EMPRESTIMO = "EMPRESTIMO"
        ADIANTAMENTO = "ADIANTAMENTO"
        MUTUO = "MUTUO"
        REFIN = "REFIN"
        CCB = "CCB"

    class Status(models.TextChoices):
        CANCELADO = "CANCELADO"
        CONGELADO = "CONGELADO"
        QUITADO = "QUITADO"
        REFINANCIADO = "REFINANCIADO"
        ATIVO = "ATIVO"
        AGUARDANDO_ACEITE = "AGUARDANDO_ACEITE"
        NAO_ACEITO = "NAO_ACEITO"
        BAIXA_DEFAULT = "BAIXA_DEFAULT"  # usado em caso de calote apenas por script

    company_id: int
    company = models.ForeignKey(Company, null=True, blank=True, on_delete=models.CASCADE)
    value = models.DecimalField(max_digits=12, decimal_places=2)
    # prazo de pagamento em meses
    term = models.IntegerField()
    # número de parcelas
    installments_number = models.IntegerField()
    frequency = models.CharField(max_length=15, choices=Frequency.choices, default=Frequency.WEEKLY)
    # data do primeiro pagamento
    start_date = models.DateField()
    # taxa anual de juros, precisa dividir por 100 antes de usar
    annual_interest_rate = models.DecimalField(max_digits=7, decimal_places=4)
    created_by_id: int
    created_by = models.ForeignKey("auth.User", on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    description = models.TextField(null=True, blank=True)
    refinancing_id: int
    refinancing = models.ForeignKey(
        "CompanyLending", null=True, blank=True, on_delete=models.SET_NULL, related_name="%(class)s_refinancing"
    )
    original_id: int
    original = models.ForeignKey(
        "CompanyLending", null=True, blank=True, on_delete=models.SET_NULL, related_name="%(class)s_original"
    )
    parent_id: int
    parent = models.ForeignKey(
        "CompanyLending", null=True, blank=True, on_delete=models.SET_NULL, related_name="%(class)s_parent"
    )
    status = FSMField(max_length=20, choices=Status.choices, default=Status.AGUARDANDO_ACEITE)

    accepted = models.BooleanField(null=True, blank=True)
    decided_at = models.DateTimeField(null=True, blank=True)
    decided_by_id: int
    decided_by = models.ForeignKey("auth.User", related_name="+", null=True, on_delete=models.SET_NULL)

    @property
    def is_ccb(self):
        return CompanyLending.Type.CCB in self.description if self.description else False

    @property
    def aliquota_iof(self):
        aliquota_padrao = D("0.000041")
        if date(2021, 9, 20) <= self.original.start_date <= date(2021, 12, 31):  # decreto nº 10.797.2021
            aliquota_padrao = D("0.0000559")

        simples_nacional = self.company.is_simples_nacional
        valor_emprestado = self.value
        return D("0.0000137") if simples_nacional and valor_emprestado < D("30000") else aliquota_padrao

    @property
    def saldo_inadimplente(self):
        saldo = D("0")
        for parcela in self.parcelas_inadimplentes:
            saldo += parcela.total_value
        return saldo

    @property
    def sem_juros(self):
        return not bool(self.annual_interest_rate)

    @property
    def tem_parcelas_v2(self):
        if self.id is None:
            return False
        return self.installments_v2.exists()

    def saldo_devedor(self, installments=None, attr="principal_value"):
        if not installments:
            installments = self.installments_v2
        return installments.aggregate(sum=Coalesce(Sum(attr), D("0")))["sum"]

    def accrual_por_atraso(self, semanas_atraso):
        """
        Calcula o accrual: montante devido a carência de pagamento de parcelas.

        O : saldo devedor
        R : 1 + % Taxa de juros semanal
        N : quantidade de parcelas de carência
        A : accrual

        A = O * ((R ^ N) -1)
        """

        if self.sem_juros:
            return D("0")

        O = self.saldo_devedor()
        R = 1 + self.weekly_interest_rate
        N = semanas_atraso
        A = O * (pow(R, N) - 1)

        return A.quantize(_2DECIMAL)

    @property
    def op_credito(self):
        source = CompanyLendingOperationV2.Source.CREDITO
        if self.is_refinancing:
            source = CompanyLendingOperationV2.Source.CREDITO_REFINANCIADO
        return self.installments_v2.filter(source=source).first()

    @property
    def iof_por_refin(self):
        """
        Se o montante de IOF do refin for maior que do fin, a diferença é cobrada
        """

        if self.sem_juros:
            return D("0")

        return max(self.iof_total - self.original.iof_total, D("0"))

    @property
    def iof_total(self):
        """
        Calcula o IOF total do empréstimo, considerando todas as parcelas.

        Esse montante é cobrado do parceiro na concessão do empréstimo
        """

        if self.sem_juros:
            return D("0")

        if self.pk is None:
            return D("0")

        installments = self.installments_v2.filter(source=CompanyLendingOperationV2.Source.PARCELA)

        return sum(i.iof_total for i in installments)

    @property
    def iof_concessao(self):
        return self.iof_total if not self.is_refinancing else self.iof_por_refin

    @property
    def last_due_date(self):
        ultima_parcela = self.installments_v2.last()
        return ultima_parcela.due_date if self.tem_parcelas_v2 else self.start_date

    @property
    def friendly_description(self) -> str:
        return f"Empréstimo {self.id}"

    @property
    def friendly_status(self):
        return {
            CompanyLending.Status.CANCELADO: "Cancelado",
            CompanyLending.Status.CONGELADO: "Congelado",
            CompanyLending.Status.QUITADO: "Quitado",
            CompanyLending.Status.REFINANCIADO: f"Refinanciado para o contrato {self.refinancing_id}",
            CompanyLending.Status.ATIVO: "Em andamento",
            CompanyLending.Status.AGUARDANDO_ACEITE: "Aguardando aceite",
            CompanyLending.Status.NAO_ACEITO: "Não aceito",
        }.get(self.status, "Indefinido")

    @property
    def next_body_id(self):
        if self.tem_parcelas_v2:
            return self.installments_v2.order_by("body_id").last().body_id + 1
        return 1

    @property
    def due_date_concessao(self):
        if self.description and "CCB" in self.description:
            return self.start_date  # contrato CCB a transferência não é feita pelo sistema
        op_concessao = self.original.installments_v2.filter(
            source=CompanyLendingOperationV2.Source.TRANSFERENCIA_CREDITO
        )
        return op_concessao.first().due_date if op_concessao else dateutils.now().date()

    @property
    def parcelas_inadimplentes(self):
        return self.parcelas_a_vencer.filter(due_date__lte=dateutils.now().date())

    @property
    def parcelas_a_vencer(self):
        installments = self.installments_v2
        ultima_parcela = installments.order_by("installment").last().installment
        parcelas_a_vencer = []
        for installment in range(1, ultima_parcela + 1):
            filtered = installments.filter(installment=installment)
            devendo_parcela = self.saldo_devedor(filtered, attr="value")
            if devendo_parcela < 0:
                parcelas_a_vencer.append(
                    filtered.filter(value__lt=0).order_by("id").last().id
                )  # pega o último evento da parcela devida
        return installments.filter(id__in=parcelas_a_vencer).order_by("due_date")

    @property
    def daily_interest_rate(self):
        # 252 é a média de dias úteis no ano
        days_in_one_year = 252
        return (1 + self.annual_interest_rate / 100) ** (1 / D(days_in_one_year)) - 1

    @property
    def weekly_interest_rate(self):
        weeks_in_one_year = 52
        return (1 + self.annual_interest_rate / 100) ** (1 / D(weeks_in_one_year)) - 1

    @property
    def monthly_interest_rate(self):
        months_in_a_year = 12
        return (1 + self.annual_interest_rate / 100) ** (1 / D(months_in_a_year)) - 1

    @property
    def interest_rate_by_frequency(self):
        return {
            CompanyLending.Frequency.DAILY: self.daily_interest_rate,
            CompanyLending.Frequency.WEEKLY: self.weekly_interest_rate,
            CompanyLending.Frequency.MONTHLY: self.monthly_interest_rate,
        }.get(self.frequency, 0)

    @property
    def principal_value(self):
        """
        Valor do principal de uma parcela.
        """
        value = self.value / self.installments_number
        return value

    @property
    def is_refinancing(self):
        return bool(self.parent)

    @property
    def contract_file(self):
        return storage.public_url("public/static/Minuta-do-contrato-Adiantamento-de-Pagamento-de-Frete.pdf")

    def _criar_op_quitado(self, refinancing=False):
        source = CompanyLendingOperationV2.Source.CONTRATO_LIQUIDADO
        if refinancing:
            source = CompanyLendingOperationV2.Source.CONTRATO_LIQUIDADO_REFINANCIADO
        CompanyLendingOperationV2.objects.get_or_create(
            lending=self,
            source=source,
            value=D("0"),
            principal_value=D("0"),
            defaults=dict(
                body_id=self.next_body_id, due_date=dateutils.now().date(), source_date=dateutils.now().date()
            ),
        )

    def tem_saldo_devedor(self):
        return self.saldo_devedor() >= 0

    @property
    def motivo_nao_pode_decidir(self):
        if self.accepted:
            return "contrato já foi aceito"
        if self.accepted is False:
            return "contrato já foi rejeitado"
        return "motivo indeterminado"

    @property
    def data_primeira_parcela(self):  # Data do último evento da primeira parcela
        return self.installments_v2.filter(installment=1).order_by("id").last().due_date

    @property
    def primeira_parcela_nao_venceu(self):
        hoje = dateutils.now().date()
        return self.data_primeira_parcela >= hoje

    def pode_confirmar(self):
        return self.primeira_parcela_nao_venceu and self.nao_decidido()

    def nao_decidido(self):
        return self.accepted is None

    @transition(field=status, source=Status.AGUARDANDO_ACEITE, target=Status.ATIVO, conditions=[nao_decidido])
    def confirmar(self):
        self.accepted = True

    @transition(field=status, source=Status.ATIVO, target=Status.QUITADO, conditions=[tem_saldo_devedor])
    def quitar(self):
        self._criar_op_quitado()

    @transition(
        field=status,
        source=[Status.ATIVO, Status.CONGELADO],
        target=Status.BAIXA_DEFAULT,
        conditions=[tem_saldo_devedor],
    )
    def baixar(self):
        pass

    @transition(field=status, source=Status.ATIVO, target=Status.CANCELADO)
    def cancelar(self):
        pass

    @transition(field=status, source=[Status.CANCELADO, Status.ATIVO], target=Status.REFINANCIADO)
    def refinanciar(self, new_lending):
        self.refinancing = new_lending
        self._criar_op_quitado(refinancing=True)

    @transition(field=status, source=Status.AGUARDANDO_ACEITE, target=Status.NAO_ACEITO, conditions=[nao_decidido])
    def negar(self):
        self.accepted = False

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "company": self.company.name,
            "value": self.value,
            "term": self.term,
            "annual_interest_rate": self.annual_interest_rate,
            "start_date": self.start_date,
            "created_at": self.created_at.date(),
            "description": self.friendly_description,
            "contract_file": self.contract_file,
            "accepted": self.accepted,
            "saldo_devedor": self.saldo_devedor(),
            "status": self.friendly_status,
            "last_due_date": self.last_due_date,
            "iof_value": self.iof_concessao,
            "operations": [
                p.to_dict_json()
                for p in self.installments_v2.filter(source__contains="PARCELA")
                .order_by("installment", "-installment_version", "-body_id")
                .distinct("installment")
            ],
        }


class CompanyLendingFile(models.Model):
    nome = models.TextField(blank=True, null=True)
    lending = models.ForeignKey(CompanyLending, related_name="files", on_delete=models.CASCADE)
    s3_url = models.FileField(upload_to="private/lending_files", storage=private_media_storage)
    removed = models.BooleanField(default=False)
    removido_por = models.ForeignKey(
        User, related_name="removedor_user", blank=True, null=True, on_delete=models.PROTECT
    )
    criado_por = models.ForeignKey(User, related_name="criador_user", blank=True, null=True, on_delete=models.PROTECT)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @traced("models_company.companyLendingFile_to_dict_json")
    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "nome": self.nome if self.nome else f"Arquivo {self.id}",
            "s3_url": self.s3_urlpresigned,
            "created_at": self.created_at.date(),
            "criado_por": self.criado_por.email if self.criado_por else "Desconhecido",
        }

    @property
    def s3_urlpresigned(self) -> str:
        if self.s3_url:
            return f"https://staff.buser.com.br/api/storage/{self.s3_url.name}"
        return "Link Desconhecido"


class CompanyLendingOperation(models.Model):
    objects = None

    STATUSES = ["AGUARDANDO_ACEITE", "A_VENCER", "PAGA", "VENCIDA", "CANCELADA"]

    class Provider(models.TextChoices):
        STARK = "stark"
        INTER = "inter"
        MONEY_PLUS = "money_plus"

    id: int
    lending_id: int
    lending = models.ForeignKey(CompanyLending, on_delete=models.CASCADE, related_name="installments")
    value = models.DecimalField(max_digits=12, decimal_places=2)
    principal_value = models.DecimalField(max_digits=12, decimal_places=2, null=True)
    interest_value = models.DecimalField(max_digits=12, decimal_places=2, null=True)
    paid_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    accrual = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    status = models.CharField(max_length=20, choices=[(c, c) for c in STATUSES], default=STATUSES[0])
    due_date = models.DateField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    description = models.TextField()
    provider = models.CharField(max_length=15, choices=Provider.choices, default=Provider.STARK)

    class Meta:
        indexes = [
            # foco na busca do cron
            models.Index(fields=["due_date", "status"])
        ]
        ordering = ("id",)

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "value": self.value,
            "paid_value": self.paid_value,
            "interest_value": self.interest_value,
            "principal_value": self.principal_value,
            "due_date": self.due_date,
            "status": self.status,
            "description": self.description,
        }

    def pay(self):
        self.paid_value = self.value
        self.status = "PAGA"
        self.save()

    def cancel(self):
        self.paid_value = 0
        self.status = "CANCELADA"
        self.save(update_fields=["paid_value", "status", "updated_at"])


class BonusCompany(models.Model):
    class Status(models.TextChoices):
        NOVO = "novo"
        PAGAMENTO_REALIZADO = "pagamento_realizado"
        PAGAMENTO_CANCELADO = "pagamento_cancelado"
        BONUS_NEGADO = "bonus_negado"

    id: int
    company_id: int
    company = models.ForeignKey(Company, null=True, on_delete=models.PROTECT)
    mes = models.IntegerField(null=True)
    ano = models.IntegerField(null=True)
    status = FSMField(max_length=32, choices=Status.choices, default=Status.NOVO, protected=False)
    valor = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    faturamento = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    ocorrencias = models.IntegerField(null=True)

    objects: SerializableManager = SerializableManager()
    objects_sqlextra = PostgresManager()

    class Meta:
        unique_together = ("company", "mes", "ano")

    @fsm_log_by
    @fsm_log_description
    @transition(field=status, source=Status.NOVO, target=Status.BONUS_NEGADO)
    def bonus_negado(self, by=None, description=None):
        pass

    @fsm_log_by
    @transition(field=status, source=[Status.BONUS_NEGADO, Status.PAGAMENTO_CANCELADO], target=Status.NOVO)
    def bonus_reset(self, by=None):
        pass

    @fsm_log_by
    @fsm_log_description
    @transition(field=status, source=Status.NOVO, target=Status.PAGAMENTO_REALIZADO)
    def bonus_pagamento_realizado(self, by=None, description=None):
        pass

    @fsm_log_by
    @fsm_log_description
    @transition(field=status, source=Status.PAGAMENTO_REALIZADO, target=Status.PAGAMENTO_CANCELADO)
    def bonus_pagamento_cancelado(self, by=None, description=None):
        pass


class CompanyLendingOperationV2(models.Model):
    class Source(models.TextChoices):
        CREDITO = "CREDITO"
        TRANSFERENCIA_CREDITO = "TRANSFERENCIA_CREDITO"
        CREDITO_REFINANCIADO = "CREDITO_REFINANCIADO"
        TRANSFERENCIA_CREDITO_REFINANCIADO = "TRANSFERENCIA_CREDITO_REFINANCIADO"
        PARCELA = "PARCELA"
        PARCELA_PAGA = "PARCELA_PAGA"
        PARCELA_REFINANCIADA_OUT = "PARCELA_REFINANCIADA_OUT"
        PARCELA_ABONADA = "PARCELA_ABONADA"
        CONTRATO_LIQUIDADO = "CONTRATO_LIQUIDADO"
        CONTRATO_LIQUIDADO_REFINANCIADO = "CONTRATO_LIQUIDADO_REFINANCIADO"
        PARCELA_ADIADA_OUT = "PARCELA_ADIADA_OUT"
        PARCELA_ADIADA_IN = "PARCELA_ADIADA_IN"
        PARCELA_VARIAVEL_IN = "PARCELA_VARIAVEL_IN"
        PARCELA_VARIAVEL_OUT = "PARCELA_VARIAVEL_OUT"
        PARCELA_ANTECIPADA_OUT = "PARCELA_ANTECIPADA_OUT"
        PARCELA_ANTECIPADA_IN = "PARCELA_ANTECIPADA_IN"
        PARCELA_ESTORNADA = "PARCELA_ESTORNADA"
        PARCELA_BAIXADA = "PARCELA_BAIXADA"

    class Provider(models.TextChoices):
        STARK = "stark"
        ITAU = "itau"
        INTER = "inter"

    id: int
    # ids
    body_id = models.IntegerField(default=1)
    lending_id: int
    lending = models.ForeignKey(CompanyLending, on_delete=models.CASCADE, related_name="installments_v2")
    lending_original_id: int
    lending_original = models.ForeignKey(CompanyLending, null=True, blank=True, on_delete=models.CASCADE)
    installment_version = models.IntegerField(default=1)
    installment = models.IntegerField(default=0)

    # valores
    value = models.DecimalField(max_digits=12, decimal_places=2)
    principal_value = models.DecimalField(max_digits=12, decimal_places=2)
    interest_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    accrual_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    irrf_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    iof_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    paid_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    irrf_paid_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    iof_paid_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)

    # gerais
    source = models.CharField(max_length=40, choices=Source.choices)
    source_date = models.DateField()
    due_date = models.DateField()
    provider = models.CharField(max_length=10, choices=Provider.choices, null=True)
    created_at = models.DateTimeField(default=now)
    updated_at = models.DateTimeField(auto_now=True)

    @property
    def inadimplente(self):
        lending = self.lending
        eventos_parcela = lending.installments_v2.filter(installment=self.installment)
        return lending.saldo_devedor(eventos_parcela, "value") < D("0")

    @property
    def friendly_source(self):
        return {
            CompanyLendingOperationV2.Source.PARCELA_PAGA: "Paga",
            CompanyLendingOperationV2.Source.PARCELA_REFINANCIADA_OUT: "Refinanciada",
            CompanyLendingOperationV2.Source.CREDITO_REFINANCIADO: "Crédito Refinanciamento",
        }.get(self.source, "Devida")

    @property
    def total_value(self):
        return self.value + self.iof_value + self.irrf_value

    @property
    def description(self):
        if self.installment != 0:
            return f"Parcela {self.installment}/{self.lending.installments_number}"
        return "Concessão de crédito"

    @property
    def days_since_concessao(self):
        return (self.due_date - self.lending.due_date_concessao).days

    def iof_por_atraso(self, adiada_out):
        """
        Como o IOF é cobrado no começo do contrato, quando há atraso
        apenas a diferença é cobrada
        """

        return -max(self.iof_total - adiada_out.iof_total, D("0"))

    @property
    def iof_total(self):
        """
        Calcula o IOF: Imposto devido pelo operador e
        descontado do operador para a Buser efetuar o pagamento.

        d : dias passados desde a concessão do crédito na carteira
        A(simples_nacional) : Aliquota diária (depende se é simples nacional)
        Ad : Aliquota adicional (fixa em 0.38%)
        I : iof
        P : valor principal da parcela

        I = ((d * A) + Ad) * P se ((d * A) + Ad) < 1.5
        I = 1.5 * P se ((d * A) + Ad) > 1.5
        """

        if self.lending.sem_juros:
            return D("0")

        d = self.days_since_concessao
        A = self.lending.aliquota_iof
        P = self.principal_value
        Ad = D("0.0038")

        I = (min(d * A, D("1.50")) + Ad) * P

        return abs(I).quantize(_2DECIMAL)

    @property
    def irrf_total(self):
        """
        Calcula o IRRF: Imposto devido pela Buser e
        cedido de crédito ao operador para ele efetuar o pagamento.

        d : dias passados desde a concessão do crédito na carteira
        j : juros da parcela
        A(d) : Aliquota (dependente de d)
        I : irrf

        I = J * A
        """

        if self.lending.sem_juros:
            return D("0")

        j = abs(self.interest_value) + abs(self.accrual_value)
        A = self.aliquota_irrf
        I = j * A

        return abs(I).quantize(_2DECIMAL)

    @property
    def aliquota_irrf(self):
        days = self.days_since_concessao
        if days <= 180:
            return D("0.225")
        elif days <= 360:
            return D("0.2")
        elif days <= 720:
            return D("0.175")
        else:
            return D("0.15")

    def atualiza_impostos(self, accrual_value=None, iof_value=None, irrf_value=None):
        if accrual_value:
            self.accrual_value += accrual_value
            self.value = self.interest_value + self.accrual_value + self.principal_value
        if iof_value:
            self.iof_value += iof_value
        if irrf_value:
            self.irrf_value = irrf_value

    def pay(self, irrf=False, iof=False, provider=Provider.STARK):
        if iof:
            self.iof_paid_value = self.iof_value
        elif irrf:
            self.irrf_paid_value = self.irrf_value
        else:
            self.paid_value = self.value
        self.provider = provider
        self.save()

    def to_dict_json(self) -> dict:
        d = {
            "installment": self.installment,
            "installment_version": self.installment_version,
            "value": self.value,
            "principal_value": self.principal_value,
            "interest_value": self.interest_value,
            "accrual_value": self.accrual_value,
            "source": self.friendly_source,
            "due_date": self.due_date,
            "provider": self.provider,
            "irrf_value": self.irrf_value,
            "iof_value": self.iof_value,
            "description": self.description,
        }
        return d


class CommonRates(models.Model):
    id: int
    date = models.DateField()
    ref_date = models.DateTimeField(blank=True, null=True)
    pax_transportados = models.IntegerField(default=0)
    pax_transportados_totais = models.IntegerField(default=0, blank=True, null=True)
    viagens = models.IntegerField(default=0)
    viagens_totais = models.IntegerField(default=0, blank=True, null=True)
    frequencia = models.IntegerField(default=0)
    recencia = models.IntegerField(default=0)
    precisa_melhorar = models.IntegerField(default=0)
    empresa = models.ForeignKey(Company, on_delete=models.CASCADE)
    nota = models.FloatField(default=0, null=True)
    deleted = models.BooleanField(default=False)

    objects: SerializableManager = SerializableManager()

    class Meta:
        abstract = True


class DriverRate(CommonRates):
    motorista_id: int
    motorista = models.ForeignKey(User, on_delete=models.CASCADE)

    class Meta:
        constraints = [UniqueConstraint(fields=["date", "empresa", "motorista"], name="unique_driver_rate_date")]


class BusRate(CommonRates):
    onibus_id: int
    onibus = models.ForeignKey(Onibus, on_delete=models.CASCADE)

    class Meta:
        constraints = [UniqueConstraint(fields=["date", "empresa", "onibus"], name="unique_bus_rate_date")]


class MotivoMulta(models.Model):
    id: int
    objects: SerializableManager = SerializableManager()

    codigo = models.CharField(max_length=4)
    descricao = models.TextField(null=True, blank=True)
    percentual_padrao = models.DecimalField(
        decimal_places=4, max_digits=5, validators=[MaxValueValidator(1), MinValueValidator(0)], null=True
    )
    valor_fixo = models.DecimalField(max_digits=7, decimal_places=2, null=True)
    base_calculo = models.TextField(null=True)
    is_active = models.BooleanField(default=True)


class Multa(models.Model):
    class Status(models.TextChoices):
        INDICADA = "indicada"
        APLICADA = "aplicada"
        DESCONTADA = "descontada"
        CANCELADA = "cancelada"
        CONTESTADA = "contestada"
        ESTORNADA = "estornada"

    objects: SerializableManager = SerializableManager()

    id: int
    grupo_id: int
    grupo = models.ForeignKey("core.Grupo", on_delete=models.CASCADE)
    company_id: int
    company = models.ForeignKey("Company", on_delete=models.CASCADE, null=True)
    motivo_id: int
    motivo = models.ForeignKey(MotivoMulta, null=True, on_delete=models.SET_NULL)
    criado_por_id: int
    criado_por = models.ForeignKey("auth.User", null=True, on_delete=models.SET_NULL)
    valor_multa = models.DecimalField(max_digits=7, decimal_places=2, null=True)
    status = models.CharField(max_length=50, null=True, blank=True, choices=Status.choices, default=Status.INDICADA)
    observacao = models.TextField(null=True, blank=True)
    origem = models.CharField(max_length=50, null=True)
    incidente = models.CharField(max_length=10, null=True)
    justificativa_contestacao = models.TextField(null=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, db_index=True)
    data_aplicacao = models.DateTimeField(null=True, db_index=True)
    descricao = models.TextField(null=True, blank=True)
    anexos_evidencias = ArrayField(models.TextField(), null=True, blank=True)

    history = HistoricalRecords()

    def estornar(self) -> None:
        if not self.pode_ser_estornada():
            raise ValueError(f"Impossível estornar multa com status {self.status}")
        self.status = Multa.Status.ESTORNADA
        self.save(update_fields=["status"])

    def contestar(self, justificativa_contestacao: str) -> None:
        status_permitidos = [Multa.Status.APLICADA, Multa.Status.DESCONTADA]
        if self.status not in status_permitidos:
            raise ValueError(f"Impossível contestar multa com status {self.status}")
        self.justificativa_contestacao = justificativa_contestacao
        self.status = Multa.Status.CONTESTADA
        self.save(update_fields=["justificativa_contestacao", "status"])

    def cancelar(self) -> None:
        status_permitidos = [Multa.Status.INDICADA, Multa.Status.APLICADA, Multa.Status.CONTESTADA]
        if self.status not in status_permitidos:
            raise ValueError(f"Impossível cancelar multa com status {self.status}")
        self.status = Multa.Status.CANCELADA
        self.save(update_fields=["status"])

    def aplicar(self) -> None:
        if self.status != Multa.Status.INDICADA:
            raise ValueError(f"Impossível aplica multa com status {self.status}")
        self.status = Multa.Status.APLICADA
        self.data_aplicacao = dateutils.now()
        self.save(update_fields=["status", "data_aplicacao"])

    def descontar(self) -> None:
        status_permitidos = [Multa.Status.APLICADA, Multa.Status.CONTESTADA]
        if self.status not in status_permitidos:
            raise ValueError(f"Impossível descontar multa com status {self.status}")
        self.status = Multa.Status.DESCONTADA
        self.save(update_fields=["status"])

    def pode_ser_estornada(self) -> bool:
        return self.status in [Multa.Status.DESCONTADA, Multa.Status.CONTESTADA]


if TYPE_CHECKING:
    HistoricalMulta: Multa


class MotivoMultaID(Enum):
    INICIAR_VIAGEM_SEM_DOCUMENTACAO = 12
    ID_PLACA_DIVERGENTE = 5


class MultaContestationDocument(models.Model):
    id: int
    multa_id: int
    multa = models.ForeignKey(
        Multa,
        on_delete=models.CASCADE,
    )
    descricao = models.TextField(null=True)
    file = models.FileField(upload_to="private/company_files", max_length=255, storage=private_media_storage)

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "description": self.descricao,
            "name": self.file.name,
            "url": self.file.url,
        }


class TaxaServicoCheckout(models.Model):
    objects: SerializableManager = SerializableManager()
    id: int
    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    percentual_referencia_marketplace = models.DecimalField(max_digits=4, decimal_places=2, default=D(0))
    ativa = models.BooleanField()

    history = HistoricalRecords()

    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)
    updated_by_id: int
    updated_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL)


# TO-DO criar classe ConfiguracaoPagamento que indique o percentual de repasse no caso
# de mktplace e híbrido. Apagando essa daqui.
class RepasseMarketplace(models.Model):
    id: int
    objects: SerializableManager = SerializableManager()

    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    percentual_repasse = models.DecimalField(decimal_places=2, max_digits=5, null=True)

    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)
    updated_by_id: int
    updated_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL)


class PoltronaOnibus(models.Model):
    id: int
    onibus_id: int
    onibus = models.ForeignKey(Onibus, on_delete=models.CASCADE, related_name="poltronas")
    tipo = models.TextField(choices=TIPOS_ASSENTO_CHOICES)
    ativo = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by_id: int
    updated_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL)
    poltrona = models.IntegerField(null=False, blank=False)
    andar = models.IntegerField(
        default=1,
        validators=[
            MinValueValidator(1),
            MaxValueValidator(2),
        ],
        null=False,
        blank=False,
    )
    linha = models.IntegerField(default=1, null=False, blank=False)
    coluna = models.IntegerField(
        default=1, null=False, blank=False, validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
