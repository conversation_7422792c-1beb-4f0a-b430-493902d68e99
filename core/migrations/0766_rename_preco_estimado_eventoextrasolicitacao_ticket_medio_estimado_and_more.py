# Generated by Django 4.1.13 on 2025-07-15 17:39

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0765_alter_grupo_modelo_operacao"),
    ]

    operations = [
        migrations.RenameField(
            model_name="eventoextrasolicitacao",
            old_name="preco_estimado",
            new_name="ticket_medio_estimado",
        ),
        migrations.RenameField(
            model_name="historicaleventoextrasolicitacao",
            old_name="preco_estimado",
            new_name="ticket_medio_estimado",
        ),
        migrations.RemoveField(
            model_name="eventoextranegociacao",
            name="cash_in_gmv",
        ),
        migrations.RemoveField(
            model_name="eventoextranegociacao",
            name="distancia_por_perna",
        ),
        migrations.RemoveField(
            model_name="eventoextrasolicitacaoperna",
            name="datetime_ida",
        ),
        migrations.RemoveField(
            model_name="historicaleventoextranegociacao",
            name="cash_in_gmv",
        ),
        migrations.RemoveField(
            model_name="historicaleventoextranegociacao",
            name="distancia_por_perna",
        ),
        migrations.RemoveField(
            model_name="historicaleventoextrasolicitacaoperna",
            name="datetime_ida",
        ),
        migrations.AlterField(
            model_name="eventoextrasolicitacao",
            name="prioridade",
            field=models.TextField(
                choices=[("baixa", "Baixa"), ("media", "Media"), ("alta", "Alta")],
                help_text="Prioridade da solicitação. Menor valor tem maior prioridade",
            ),
        ),
        migrations.AlterField(
            model_name="historicaleventoextrasolicitacao",
            name="prioridade",
            field=models.TextField(
                choices=[("baixa", "Baixa"), ("media", "Media"), ("alta", "Alta")],
                help_text="Prioridade da solicitação. Menor valor tem maior prioridade",
            ),
        ),
    ]
