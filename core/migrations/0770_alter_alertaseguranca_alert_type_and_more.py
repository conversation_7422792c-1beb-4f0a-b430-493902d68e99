# Generated by Django 4.1.13 on 2025-07-17 16:38

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0769_grupo_evento_extra"),
    ]

    operations = [
        migrations.AlterField(
            model_name="alertase<PERSON>ran<PERSON>",
            name="alert_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("DESATENCAO_GRAVE", "Desatencao Grave"),
                    ("DESATENCAO_MEDIA", "Desatencao Media"),
                    ("DESATENCAO_LEVE", "Desatencao Leve"),
                    ("CELULAR", "Celular"),
                    ("CANSACO_GRAVISSIMO", "Cansaco Gravissimo"),
                    ("CANSACO_INTENSO", "Cansaco Intenso"),
                    ("CANSACO_MEDIO", "Cansaco Medio"),
                    ("CANSACO_LEVE", "Cansaco Leve"),
                    ("BOCEJO", "Bocejo"),
                    ("VELOCIDADE_ALTA", "Velocidade Alta"),
                    ("VELOCIDADE_MUITO_ALTA", "Velocidade Muito Alta"),
                    ("PLACA_NAO_IDENTIFICADA", "Placa Nao Identificada"),
                    ("CINTO_DE_SEGURANCA", "Cinto De Seguranca"),
                    ("USO_DE_CELULAR", "Uso De Celular"),
                    ("LENTE_COBERTA", "Lente Coberta"),
                    ("RISCO_DE_COLISAO", "Risco De Colisao"),
                    ("COLISAO_COM_PEDESTRE", "Colisao Com Pedestre"),
                    ("ALERTA_DE_VIOLACAO", "Alerta De Violacao"),
                    ("DISTANCIA_INSEGURA", "Distancia Insegura"),
                    ("TROCA_DE_MOTORISTAS_EM_POSICOES_DISTOANTES", "Troca De Motoristas Em Posicoes Distoantes"),
                    ("FREADA_BRUSCA", "Freada Brusca"),
                    ("FORCA_G_LATERAL_FORTE", "Forca G Lateral Forte"),
                    ("FORCA_G_LATERAL_MEDIA", "Forca G Lateral Media"),
                    ("ACELERACAO_BRUSCA", "Aceleracao Brusca"),
                ],
                max_length=64,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="smartcamalarm",
            name="type",
            field=models.TextField(
                blank=True,
                choices=[
                    ("PERDA_DE_VIDEO", "Perda Video"),
                    ("LENTE_COBERTA", "Lente Coberta"),
                    ("USO_DE_CELULAR", "Celular"),
                    ("DISTRACAO", "Distracao"),
                    ("COLISAO_FRONTAL", "Colisao Frontal"),
                    ("BOCEJO", "Bocejo"),
                    ("COLISAO_COM_PEDESTRE", "Colisao Pedestre"),
                    ("CINTO_DE_SEGURANCA", "Cinto"),
                    ("OLHO_FECHADO", "Olho Fechado"),
                    ("PERDA_GPS", "Perda Gps"),
                    ("TRASLADO", "Traslado"),
                    ("SEM_MOTORISTA", "Sem Motorista"),
                    ("ANORMALIDADE_DE_MEMORIA", "Anormalidade De Memoria"),
                    ("DESLIGAMENTO_ILEGAL", "Desligamento Ilegal"),
                    ("SAIDA_DE_FAIXA", "Saida De Faixa"),
                    ("MOTORISTA_FUMANDO", "Fumante"),
                    ("BAIXA_TENSAO", "Baixa Tensao"),
                    ("EXCESSO_VELOCIDADE_PISTA_SECA", "Excesso Velocidade Pista Seca"),
                    ("EXCESSO_VELOCIDADE_PISTA_MOLHADA", "Excesso Velocidade Pista Molhada"),
                    ("EXCESSO_VELOCIDADE_ROTOGRAMA_PISTA_SECA", "Excesso Velocidade Rotograma Pista Seca"),
                    ("FREADA_BRUSCA", "Freada Brusca"),
                    ("FORCA_G_LATERAL_FORTE", "Forca G Lateral Forte"),
                    ("FORCA_G_LATERAL_MEDIA", "Forca G Lateral Media"),
                    ("ACELERACAO_BRUSCA", "Aceleracao Brusca"),
                    ("EXCESSO_VELOCIDADE_ROTOGRAMA_PISTA_MOLHADA", "Excesso Velocidade Rotograma Pista Molhada"),
                ],
                null=True,
            ),
        ),
    ]
