import functools
import gzip
import json
from datetime import date, datetime, timedelta
from decimal import Decimal as D
from functools import cached_property
from string import Formatter
from typing import TYPE_CHECKING

from django.conf import settings
from django.contrib.auth import hashers
from django.contrib.auth.models import User
from django.contrib.gis.db.models.fields import Point<PERSON>ield
from django.contrib.postgres.fields import <PERSON>rrayField, DateRangeField
from django.contrib.postgres.indexes import GinIndex
from django.contrib.postgres.search import SearchVectorField
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django.db.models import Q
from django.template import Context, Template
from django.urls import reverse
from django.utils import timezone
from django.utils.text import slugify
from django_fsm import FSMField, transition
from django_qserializer.serialization import SerializableManager
from psqlextra.manager import PostgresManager
from sentry_sdk import capture_message

from buser.roles import CAN_PAY_WITH_MONEY
from commons import dateutils, guard, storage, ufs_brasil
from commons.bancos_brasil import get_bank_name
from commons.dateutils import now, to_default_tz, today
from commons.memoize import memoize
from commons.storage import public_media_storage
from commons.tests import is_pytest
from commons.thumbor import generate_thumbor_url
from commons.utils import only_numbers, random_code
from core.constants import TIPOS_ONIBUS_CHOICES

if TYPE_CHECKING:
    from django.db.models.manager import RelatedManager

    from core.models_company import Company  # noqa: F401
    from core.models_rota import LocalEmbarque


class GlobalSetting(models.Model):
    key = models.CharField(max_length=256, unique=True)
    value = models.TextField()

    def __str__(self):
        return self.key

    def clean(self):
        try:
            return json.loads(self.value)
        except json.JSONDecodeError as e:
            raise ValidationError("JSON value inválido.") from e

    @property
    def parsed_value(self):
        try:
            return json.loads(self.value)
        except json.JSONDecodeError as e:
            if any((settings.DEBUG, is_pytest())):
                raise e
            capture_message(f"Global setting com valor inválido. Key: {self.key}", "info")
            return None

    @parsed_value.setter
    def parsed_value(self, value):
        self.value = json.dumps(value)


class BankAccount(models.Model):
    class AccountType(models.TextChoices):
        CHECKING = "checking"
        SAVINGS = "savings"
        SALARY = "salary"
        PAYMENT = "payment"

    id: int
    agencia = models.CharField(default="", blank=True, max_length=10)
    agencia_dv = models.CharField(default="", blank=True, max_length=4)
    bank_code = models.CharField(default="", blank=True, max_length=8)
    conta = models.CharField(default="", blank=True, max_length=20)
    conta_dv = models.CharField(default="", blank=True, max_length=4)
    document_number = models.CharField(default="", blank=True, max_length=20)
    legal_name = models.CharField(default="", blank=True, max_length=256)
    account_type = models.CharField(default=AccountType.CHECKING, max_length=10, choices=AccountType.choices)

    def __str__(self):
        return "ag: %s, cc: %s, doc: %s" % (
            self.agencia,
            self.conta,
            self.document_number,
        )

    @classmethod
    def get_or_create(cls, **kwargs):
        if "id" in kwargs:
            return cls.objects.get(pk=kwargs["id"])
        else:
            cc = cls(**kwargs)
            cc.save()
            return cc

    @property
    def bank_name(self):
        return get_bank_name(self.bank_code)

    @property
    def full_account(self):
        agencia_dv = f"-{self.agencia_dv}" if self.agencia_dv else ""
        conta_dv = f"-{self.conta_dv}" if self.conta_dv else ""
        return f"Agência {self.agencia}{agencia_dv}/Conta {self.conta}{conta_dv}/Doc {self.document_number}/Banco {self.bank_name}"

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "agencia": self.agencia,
            "agencia_dv": self.agencia_dv,
            "bank_code": self.bank_code,
            "bank_name": self.bank_name,
            "conta": self.conta,
            "conta_dv": self.conta_dv,
            "document_number": self.document_number,
            "legal_name": self.legal_name,
            "account_type": self.account_type,
        }

    __dictjson__ = to_dict_json


class PaymentOptionMixin(models.Model):
    payment_option = models.CharField(max_length=10, default="", blank=True)
    bank_account = models.ForeignKey(BankAccount, blank=True, null=True, on_delete=models.CASCADE)

    class Meta:
        abstract = True

    def create_or_update_bank_account(self, **kwargs):
        if not self.bank_account:
            self.bank_account = BankAccount.objects.create(**kwargs)
            self.save(update_fields=["bank_account"])
        else:
            fields = ["agencia", "agencia_dv", "bank_code", "conta", "conta_dv", "document_number", "legal_name"]
            for k in fields:
                if k in kwargs:
                    setattr(self.bank_account, k, kwargs[k])
            self.bank_account.save(update_fields=fields)

    def to_dict_json(self) -> dict:
        return {
            "payment_option": self.payment_option,
            "bank_account": self.bank_account,
        }


VERIFICATION_CHOICES = [(c, c) for c in ["unverified", "pending", "verified", "denied"]]


class Reputation(models.Model):
    id: int
    count_feedbacks = models.IntegerField(default=0)
    count_comments = models.IntegerField(default=0)
    rating = models.FloatField(default=5)

    @classmethod
    def get_or_create(cls, **kwargs):
        if "id" in kwargs:
            return cls.objects.get(pk=kwargs["id"])
        else:
            cc = cls(**kwargs)
            cc.save()
            return cc

    def append(self, feedback):
        new_count = self.count_feedbacks + 1
        self.rating = (self.rating * self.count_feedbacks + feedback["nota"]) / new_count
        self.count_feedbacks = new_count
        self.save(update_fields=["rating", "count_feedbacks"])

    def append_comment(self):
        self.count_comments += 1
        self.save(update_fields=["count_comments"])

    def to_dict_json(self) -> dict:
        return {
            "reputation_id": self.id,
            "count_feedbacks": self.count_feedbacks,
            "count_comments": self.count_comments,
            "rating": self.rating,
        }

    __dictjson__ = to_dict_json


class ProfileQuerySet(models.QuerySet):
    def role(self, role):
        return self.filter(user__groups__name=role, user__is_active=True)

    def drivers(self, *, company=None):
        kwargs = {}

        if company:
            kwargs["company"] = company

        return self.role("driver").filter(**kwargs)

    def company_users(self):
        return self.role("company")

    def with_reputation(self):
        return self.select_related("reputation")


class Revendedor(models.Model):
    class Kind(models.TextChoices):
        PROMOTOR = "promotor"
        PONTO_DE_VENDA = "ponto_de_venda"
        ANALISTA = "analista"
        PARCEIRO = "parceiro"  # usuarios que vendem via api

    objects: SerializableManager = SerializableManager()
    id: int
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    ponto_de_venda = models.ForeignKey("core.PontoDeVenda", blank=True, null=True, on_delete=models.SET_NULL)
    user_id: int
    ponto_de_venda_id: int
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    team_leader_revendedor = models.ForeignKey("self", null=True, on_delete=models.SET_NULL)
    kind = models.CharField(max_length=32, null=True, choices=Kind.choices)
    taxa_revenda = models.DecimalField(
        max_digits=5, decimal_places=2, default=D(10), null=True, blank=True
    )  # taxa_revenda = 10 -> 10%
    dinheiro_bloqueado = models.BooleanField(default=False)
    active = models.BooleanField(default=True)

    def revoke_permissions(self):
        if guard.can_pay_with_money(self.user):
            guard.set_permission(self.user, CAN_PAY_WITH_MONEY, _bool=False)
        if guard.is_revendedor(self.user):
            guard.set_role(self.user, "Revendedor", _bool=False)

    def update_kind(self):
        if self.kind:
            return
        if self.user.email and self.user.email.split("@")[1] == "buser.com.br":
            self.kind = self.Kind.ANALISTA
            return
        self.kind = self.Kind.PROMOTOR

    def save(self, *args, **kwargs):
        if not self.active:
            self.revoke_permissions()
        else:
            if not guard.is_revendedor(self.user):
                guard.set_role(self.user, "Revendedor")
            self.update_kind()
        super(Revendedor, self).save(*args, **kwargs)


class Tag(models.Model):
    id: int
    nome = models.CharField(max_length=50, unique=True)
    descricao = models.TextField(blank=True, null=True)
    hex_color = models.CharField(max_length=7, null=True)
    hex_color_background = models.CharField(max_length=7, null=True)

    def __str__(self):
        return self.nome


class AtribuicaoTag(models.Model):
    id: int
    tag = models.ForeignKey(Tag, on_delete=models.PROTECT, related_name="atribuicoes")
    cpf = models.CharField(max_length=16)
    atribuido_em = models.DateTimeField(auto_now_add=True)
    atribuido_por = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name="tags_atribuidas")
    referencia = models.TextField(blank=True, null=True)

    class Meta:
        constraints = [models.UniqueConstraint(fields=["tag", "cpf"], name="unique_tag_cpf")]

    def __str__(self):
        return f"{self.tag.nome} - {self.cpf}"


class Profile(models.Model):
    objects = ProfileQuerySet.as_manager()

    class EmergencyContactRelationship(models.TextChoices):
        FAMILIA = "familia", "Família"
        CONJUGE = "conjuge", "Cônjuge ou Companheiro(a)"
        AMIGO = "amigo", "Amigo(a)"
        OUTRO = "outro", "Outro"

    id: int
    user_id: int
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    is_blocked = models.BooleanField(default=False)
    block_reason = models.TextField(blank=True, null=True)
    revendedor_user_id: int
    revendedor_user = models.ForeignKey(
        User, blank=True, null=True, related_name="revendedor_user", on_delete=models.SET_NULL
    )
    birth_date = models.DateField(null=True, blank=True)
    cell_phone = models.CharField(max_length=16, null=True, blank=True)
    cell_phone_confirmation_code = models.CharField(max_length=6, null=True, blank=True)
    cell_phone_confirmation_attempts = models.IntegerField(default=0)
    cell_phone_confirmed = models.BooleanField(default=False)
    cell_phone_confirmation_resent = models.BooleanField(default=False)
    cell_phone_to_confirm = models.CharField(max_length=16, null=True, blank=True)
    cell_phone_confirmation_date = models.DateTimeField(null=True, blank=True)
    emergency_contact_name = models.CharField(max_length=256, null=True, blank=True)
    emergency_contact_phone = models.CharField(max_length=16, null=True, blank=True)
    emergency_contact_relationship = models.CharField(
        max_length=32, choices=EmergencyContactRelationship.choices, null=True, blank=True
    )
    fullname = models.CharField(max_length=256, null=True, blank=True)
    rg = models.CharField(max_length=32, null=True, blank=True)
    cpf = models.CharField(max_length=16, null=True, blank=True)
    passaporte = models.CharField(max_length=32, null=True, blank=True)
    verification_status = models.CharField(max_length=16, default="unverified", choices=VERIFICATION_CHOICES)
    verification_rejection_reason = models.CharField(max_length=1024, null=True, blank=True)
    invitecode = models.CharField(max_length=16, null=True, blank=True)
    inviter_id: int
    inviter = models.ForeignKey(User, null=True, blank=True, related_name="inviter", on_delete=models.CASCADE)
    photo_url = models.TextField(null=True, blank=True)
    company_id: int
    company = models.ForeignKey("Company", null=True, blank=True, on_delete=models.CASCADE)
    vendor_id: int
    vendor = models.ForeignKey("Vendor", null=True, blank=True, on_delete=models.CASCADE)
    gave_register_bonus = models.BooleanField(default=False)
    login_token = models.CharField(max_length=256, null=True, blank=True)
    login_token_attempts = models.IntegerField(default=0)
    complete_registration = models.BooleanField(default=True)
    bank_account_id: int
    bank_account = models.ForeignKey(BankAccount, blank=True, null=True, on_delete=models.CASCADE)
    dados_receita = models.TextField(null=True, blank=True)
    inactivation_reason = models.TextField(null=True, blank=True)
    reactivation_reason = models.TextField(null=True, blank=True)
    annotation = models.TextField(null=True, blank=True)
    cep = models.CharField(max_length=8, null=True, blank=True)
    # Contabilidade
    rate_app = models.BooleanField(default=True)
    reputation_id: int
    reputation = models.ForeignKey(Reputation, null=True, on_delete=models.CASCADE)
    updated_at = models.DateTimeField(auto_now=True)
    # Driver fields
    passed_onboarding = models.BooleanField(default=False)
    cnh_expire_date = models.DateField(null=True, blank=True)
    cnh_foto = models.CharField(max_length=256, null=True, blank=True)
    cnh_uf = models.CharField(max_length=256, null=True, blank=True)
    cnh_city = models.CharField(max_length=256, null=True, blank=True)
    curso_expire_date = models.DateField(null=True, blank=True)
    curso_foto = models.CharField(max_length=256, null=True, blank=True)
    driver_docs_status = models.CharField(max_length=32, default="não enviado")
    driver_avaliado_em = models.DateTimeField(null=True, blank=True)
    driver_gender = models.CharField(max_length=16, null=True, blank=True)
    mensagem_status_docs = models.TextField(null=True, blank=True)
    ead_progress = models.DecimalField(null=True, blank=True, max_digits=4, decimal_places=3)

    class Meta:
        indexes = [
            models.Index(fields=["invitecode"]),
            models.Index(fields=["cell_phone"]),
            models.Index(fields=["cpf"]),
            models.Index(fields=["passaporte"]),
            models.Index(fields=["verification_status"]),
        ]
        permissions = [
            ("viajar_gratis", "Viajar Grátis"),
            ("criar_cupom", "Criar cupom"),
            ("new_checkout", "Acessar Novo Checkout"),
        ]

    def get_facebook_url(self):
        if self.photo_url and "facebook.com/" in self.photo_url and "/picture" in self.photo_url:
            fbid = self.photo_url.split("/picture")[0].split("facebook.com/")[1]
            return "https://www.facebook.com/profile.php?id=%s" % fbid
        return None

    def get_user_email(self):
        return self.user.email if "@invalid.email" not in self.user.email else None

    def to_dict_json(self, *, user=True):
        """
        Colocar `user=False` faz com que nenhum campo de User seja serializado,
        para evitar carregar o objeto. Esse caso é pra ser usado em conjunto
        com os serializadores, que já sabem fazer a mesma coisa, usando o `user`
        diretamente ao invés de ter que dar a volta `user.profile.user`.
        """
        data = {
            "rate_app": self.rate_app,
            "birth_date": self.birth_date.strftime("%Y-%m-%d") if self.birth_date else None,
            "cell_phone": self.cell_phone,
            "cell_phone_confirmed": self.cell_phone_confirmed,
            "cell_phone_to_confirm": self.cell_phone_to_confirm,
            "cell_phone_confirmation_date": (
                to_default_tz(self.cell_phone_confirmation_date).isoformat()
                if self.cell_phone_confirmation_date
                else None
            ),
            "emergency_contact": dict(
                name=self.emergency_contact_name if self.emergency_contact_name else "",
                phone=self.emergency_contact_phone if self.emergency_contact_phone else "",
                relationship=self.emergency_contact_relationship if self.emergency_contact_relationship else "",
            ),
            "complete_registration": self.complete_registration,
            "verification_status": self.verification_status,
            "verification_rejection_reason": self.verification_rejection_reason,
            "fullname": self.fullname,
            "rg": self.rg,
            "cpf": self.cpf,
            "cep": self.cep,
            "passaporte": self.passaporte,
            "invitecode": self.invitecode,
            "invited": self.inviter_id,
            "photo_url": self.photo_url,
            "facebook_url": self.get_facebook_url(),
            "pushtoken": False,  # TODO verificar se é realmente necessário
        }
        if user:
            data.update(
                {
                    "id": self.user.id,
                    "name": self.user.get_full_name(),
                    "username": self.user.username,
                    "email": self.get_user_email(),
                    "first_name": self.user.first_name,
                    "last_name": self.user.last_name,
                }
            )
        return data


class ConfirmedChangeRequest(models.Model):
    """
    Registro das solicitações de mudança que exigem validação por email.
    """

    class Type(models.TextChoices):
        USER_EMAIL = "user_email"

    id: int
    user = models.ForeignKey("auth.User", on_delete=models.CASCADE)
    code = models.TextField(null=False)
    new_value = models.TextField(null=False)
    type = models.CharField(max_length=20, choices=Type.choices)
    created_at = models.DateTimeField(auto_now_add=True, null=False)
    updated_at = models.DateTimeField(auto_now=True, null=False)
    expires_at = models.DateTimeField(null=False)
    used_at = models.DateTimeField(null=True)
    canceled = models.BooleanField(default=False)
    social_token = models.CharField(max_length=32, db_index=True, null=True)

    def resend_timeout(self):
        """
        Used to determined when user can resend the confirmation email again
        """
        return self.created_at + timedelta(seconds=90)

    def validate_code(self, code):
        return hashers.check_password(code, self.code)

    def seconds_until_resend_timeout(self):
        timeout_time = self.resend_timeout()
        current_time = timezone.now()
        if current_time < timeout_time:
            delta = timeout_time - current_time
            return delta.seconds
        else:
            return 0


class DocValidationProvider(models.TextChoices):
    BGC = "BGC"  # deprecated
    SERPRO = "SERPRO"  # deprecated
    MANUAL = "MANUAL"


class MotoristaAprovacao(models.Model):
    """Representa a aprovação (ou não) de um motorista."""

    class Status(models.TextChoices):
        PROCESSANDO = "processando"
        EM_ANALISE = "em_analise"
        AGUARDANDO_CURSO = "aguardando_curso"
        APROVADO = "aprovado"
        APROVADO_MANUALMENTE = "aprovado_manualmente"
        REPROVADO_MANUALMENTE = "reprovado_manualmente"
        REPROVADO = "reprovado"

    id: int
    #: Serviço que a gente ta usando para fazer a aprovação
    provider = models.TextField(choices=DocValidationProvider.choices)

    #: ID externo do provedor
    external_id = models.CharField(null=True, blank=True, max_length=64)

    #: Um usuário pode precisar passar por várias aprovações (exemplo: CNH expirou)
    user = models.ForeignKey(User, on_delete=models.CASCADE)

    # Deprecated. Este campo armazenava a CNH para validações.
    # A tabela CNH era alimentada pelo SERPRO, mas a integração com o SERPRO foi descontinuada.
    # Mantendo o campo para que eventuais análises ainda possam ser feitas no futuro pelo datalake.
    cnh = models.IntegerField(null=True, blank=True)

    #: Status do processo de validação
    status = models.CharField(max_length=32, choices=Status.choices, null=True, blank=True)

    #: Razão da reprovação
    reason = models.TextField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)

    @property
    def aprovado(self):
        return self.status not in ("reprovado_manualmente", "reprovado")


class MotoristaAprovacaoLog(models.Model):
    """Armazena as respostas do serviço de validação"""

    id: int
    #: Serviço que a gente ta usando para fazer a aprovação
    provider = models.TextField(choices=DocValidationProvider.choices)

    #: Aprovação associada a esse log
    aprovacao = models.ForeignKey(MotoristaAprovacao, null=False, on_delete=models.CASCADE)

    #: Dados recebidos da requisição
    data = models.JSONField()

    created_at = models.DateTimeField(auto_now_add=True, null=True)


class DocumentoMotorista(models.Model):
    class Status(models.TextChoices):
        PROCESSADO = "processado"
        PROCESSANDO = "processando"
        PENDENTE = "em análise"
        NAO_ENVIADO = "não enviado"
        REPROVADO = "reprovado"

    class Reason(models.TextChoices):
        DOCUMENTO_INVALIDO = "documento inválido"
        DOCUMENTO_DIVERGENTE = "documento diverge com cadastro"
        VALIDACAO_REPROVADA = "reprovado na validação"

    id: int
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    status = models.CharField(max_length=32, choices=Status.choices, null=True, blank=True)
    reason = models.CharField(max_length=64, choices=Reason.choices, null=True, blank=True)
    # a validação da cnh é async, o retorno vem por um webhook,
    # o search_id é o que identifica à qual consulta corresponde a resposta
    cnh_search_id = models.CharField(max_length=64, null=True, blank=True)
    cnh_aprovada = models.BooleanField(default=False, null=True, blank=True)
    cnh_validation_date = models.DateTimeField(null=True)
    cnh_validation_json = models.JSONField(null=True, blank=True)
    ocr_validation_json = models.JSONField(null=True, blank=True)

    def foi_reprovado(self):
        return self.status == DocumentoMotorista.Status.REPROVADO

    def ocr_json_dict(self):
        if isinstance(self.ocr_validation_json, str):
            return json.loads(self.ocr_validation_json)

        return self.ocr_validation_json


class MotoristaDispositivo(models.Model):
    id: int
    user = models.ForeignKey(User, on_delete=models.CASCADE, db_constraint=False)
    device_id = models.CharField(max_length=64)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)


class DadosReceita(models.Model):
    id: int
    cpf = models.CharField(max_length=11, null=False, blank=False, unique=True)
    dados_receita = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)

    @property
    def nome(self):
        try:
            nome = self.dados_receita["nome"]
        except (KeyError, TypeError):
            nome = None

        return nome


class Lead(models.Model):
    id: int
    user = models.OneToOneField(User, null=True, blank=True, on_delete=models.CASCADE)
    landing_page = models.ForeignKey("LandingPage", null=True, blank=True, on_delete=models.SET_NULL)
    email = models.CharField(max_length=256, null=True, blank=True)
    phone = models.CharField(max_length=16, null=True, blank=True)
    anonymous_user_id = models.CharField(max_length=32, null=True, blank=True)
    name = models.CharField(max_length=256, null=True, blank=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    new_lead_journey_on = models.DateTimeField(blank=True, null=True)
    phone_opt_out_at = models.DateTimeField(blank=True, null=True)
    email_opt_out_at = models.DateTimeField(blank=True, null=True)
    push_opt_out_at = models.DateTimeField(blank=True, null=True)

    objects: SerializableManager = SerializableManager()

    class Meta:
        indexes = [
            models.Index(fields=["phone"]),
            models.Index(fields=["email"]),
            models.Index(fields=["anonymous_user_id", "created_on"]),
            models.Index(fields=["updated_on"]),
        ]

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "name": self.name,
            "email": self.email.strip() if self.email else None,
            "user_id": self.user_id,
            "created_on": self.created_on.isoformat() if self.created_on else None,
            "anonymous_user_id": self.anonymous_user_id,
            "phone": f"55{self.phone}" if self.phone else None,
        }


class LeadImportError(models.Model):
    """Registra os erros ocorridos durante a importação do lead."""

    id: int
    lead = models.JSONField(null=False)
    message = models.TextField(null=False)
    created_at = models.DateTimeField(auto_now_add=True, null=False)


TOKEN_TYPE_CHOICES = [(c, c) for c in ["invitation_claim", "reset_password"]]


class ExpiringToken(models.Model):
    id: int
    type = models.CharField(max_length=32, null=True, blank=True, choices=TOKEN_TYPE_CHOICES)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    used = models.BooleanField(default=False)
    code = models.CharField(max_length=32, null=True, blank=True)
    user = models.ForeignKey(User, null=True, blank=True, on_delete=models.CASCADE)
    phone = models.CharField(max_length=16, null=True, blank=True)

    @classmethod
    def create(cls, **kwargs):
        cc = cls(**kwargs)
        code = random_code(8)
        while ExpiringToken.objects.filter(code=code).exists():
            code = random_code(8)
        cc.code = code
        cc.save()
        return cc

    def is_valid(self):
        return not self.used and self.expires_at > now()


class Invite(models.Model):
    id: int
    fromuser = models.ForeignKey(User, related_name="invite_from", on_delete=models.CASCADE)
    email = models.CharField(max_length=512)
    status = models.CharField(max_length=32)  # failed, pending e done
    status_travel = models.CharField(max_length=32, default="none")  # todo deprecatd
    failed_travel_reason = models.CharField(max_length=256, null=True, blank=True)
    reward = models.FloatField()  # todo deprecated
    reward_travel = models.FloatField(default=0)  # todo deprecated
    accepteduser = models.ForeignKey(
        User, null=True, blank=True, related_name="invite_accepted", on_delete=models.CASCADE
    )
    accepteduser_phone = models.CharField(max_length=16, null=True, blank=True)
    redeemed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=["updated_at"]),
        ]

    def fail(self):
        if self.status == "pending":
            self.status = "failed"
            self.save(update_fields=["status", "updated_at"])

    def acceptedby(self, user):
        if self.status == "pending":
            self.status = "done"
            self.accepteduser = user
            self.save(update_fields=["status", "accepteduser", "updated_at"])

    def to_dict_json(self) -> dict:
        return {
            "email": self.accepteduser.email if self.accepteduser else self.email,
            "name": (
                self.accepteduser.first_name + " " + self.accepteduser.last_name if self.accepteduser else self.email
            ),
            "photo_url": self.accepteduser.profile.photo_url if self.accepteduser else None,
            "status": self.status,
            "reward": self.reward,
        }


TIPOS_SUGESTAO = [
    (c, c)
    for c in [
        "DEFAULT",
        "TRECHO",
    ]
]


class Sugestao(models.Model):
    id: int
    user = models.ForeignKey(User, null=True, on_delete=models.CASCADE)
    lead = models.ForeignKey(Lead, null=True, on_delete=models.CASCADE)
    type = models.CharField(max_length=64, default="DEFAULT", choices=TIPOS_SUGESTAO)
    description = models.TextField(blank=True)
    jsondata = models.TextField(blank=True)
    email = models.CharField(max_length=256, blank=True, null=True)
    created = models.DateTimeField(auto_now_add=True)


class CampanhaArtesp(models.Model):
    id: int
    user = models.ForeignKey(User, null=True, on_delete=models.CASCADE)
    name = models.TextField(blank=True)
    email = models.CharField(max_length=256, blank=True, null=True)
    campanha = models.CharField(max_length=16, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [models.Index(fields=["campanha", "-id"])]


class PersonaNonGrataManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(restricao_ativa=True)


class PersonaNonGrata(models.Model):
    class RestrictionType(models.TextChoices):
        CHARGEBACK = "chargeback"
        FISCAL_INFILTRADO = "fiscal_infiltrado"
        COMPORTAMENTO_INADIMISSIVEL = "comportamento_inadimissivel"
        OUTRO = "outro"

    cpf = models.CharField(max_length=16, unique=True)
    reason = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_by = models.ForeignKey(User, null=True, on_delete=models.CASCADE)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    restricao_ativa = models.BooleanField(default=True)
    tipo = models.TextField(choices=RestrictionType.choices, default=RestrictionType.OUTRO)

    bloqueados = PersonaNonGrataManager()
    objects = models.Manager()

    class Meta:
        indexes = [
            models.Index(
                fields=[
                    "cpf",
                ]
            ),
        ]

    def __str__(self):
        return "CPF: " + (self.cpf or "") + ". Motivo: " + (self.reason or "")

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "cpf": self.cpf,
            "reason": self.reason,
            "restricao_ativa": self.restricao_ativa,
            "created_at": to_default_tz(self.created_at).isoformat(),
            "updated_at": to_default_tz(self.updated_at).isoformat(),
        }

    def append_reason(self, reason: str) -> None:
        if not reason:
            return
        if not self.reason:
            self.reason = reason
        else:
            self.reason += "\n" + reason

    def clean(self):
        if self.cpf and not self.cpf.isnumeric():
            raise ValidationError("O campo deve conter apenas números")


class ActivityLog(models.Model):
    id: int
    #: ID único gerado pela aplicação.
    uid = models.UUIDField(primary_key=False, editable=False, null=True)
    type = models.CharField("tipo de log", max_length=64)
    logged_user_id: int | None
    logged_user = models.ForeignKey(User, null=True, blank=True, on_delete=models.CASCADE)
    fromuser = models.ForeignKey(
        User, null=True, blank=True, related_name="activitylogs_withfromuser", on_delete=models.CASCADE
    )
    touser = models.ForeignKey(
        User, null=True, blank=True, related_name="activitylogs_withtouser", on_delete=models.CASCADE
    )
    grupo_id: int | None
    grupo = models.ForeignKey("Grupo", null=True, on_delete=models.CASCADE)
    grupo_classe_id: int | None
    grupo_classe = models.ForeignKey("GrupoClasse", null=True, on_delete=models.CASCADE)
    trecho_classe_id: int | None
    trecho_classe = models.ForeignKey("TrechoClasse", null=True, on_delete=models.CASCADE)
    trecho_vendido_id: int | None
    trecho_vendido = models.ForeignKey("TrechoVendido", null=True, on_delete=models.CASCADE)
    reembolso_id: int | None
    reembolso = models.ForeignKey("PedidoReembolso", null=True, on_delete=models.CASCADE)
    notafiscal_id: int | None
    notafiscal = models.ForeignKey("NotaFiscal", null=True, on_delete=models.CASCADE)
    travel_id: int | None
    travel = models.ForeignKey("Travel", null=True, on_delete=models.CASCADE)
    rota_id: int | None
    rota = models.ForeignKey("Rota", null=True, on_delete=models.CASCADE)
    pagamento_id: int | None
    pagamento = models.ForeignKey("Pagamento", null=True, on_delete=models.CASCADE)
    accounting_transaction_id: int | None
    accounting_transaction = models.ForeignKey("AccountingTransaction", null=True, on_delete=models.CASCADE)
    vendor_id: int | None
    vendor = models.ForeignKey("Vendor", null=True, on_delete=models.CASCADE)
    company_id: int | None
    company = models.ForeignKey("Company", null=True, on_delete=models.CASCADE)
    origem = models.CharField(max_length=256, null=True, blank=True)
    destino = models.CharField(max_length=256, null=True, blank=True)
    datetime_ida = models.DateTimeField(null=True)
    datetime_volta = models.DateTimeField(null=True)
    jsondata = models.TextField(null=True, blank=True)
    invitecode = models.CharField(max_length=6, null=True, blank=True)
    invitestatus = models.CharField(max_length=64, null=True, blank=True)
    created_at = models.DateTimeField("criado em", default=timezone.now)
    user_device = models.CharField(max_length=64, null=True, blank=True)
    user_os = models.CharField(max_length=64, null=True, blank=True)
    user_agent = models.CharField(max_length=64, null=True, blank=True)
    user_session = models.CharField(max_length=256, null=True, blank=True)
    user_tabid = models.CharField(max_length=12, null=True, blank=True)
    user_ip = models.CharField(max_length=45, null=True, blank=True)
    referrer = models.TextField(null=True, blank=True)  # TODO: transformar em texto
    utm_campaign = models.CharField(max_length=256, null=True, blank=True)
    utm_params = models.TextField(null=True, blank=True)
    anonymous_user_id = models.CharField(max_length=32, null=True, blank=True)

    class Meta:
        verbose_name_plural = "atividade de logs"
        verbose_name = "atividade de log"
        ordering = ("-created_at",)
        indexes = [
            models.Index(fields=["-created_at", "type", "logged_user_id"]),
            models.Index(fields=["type"]),
        ]

    def __str__(self):
        return "%s / %s / %s / %s / %s" % (self.type, self.logged_user, self.origem, self.destino, self.created_at)

    @cached_property
    def jsondata_dict(self):
        if self.jsondata:
            return json.loads(self.jsondata)


class DatabricksActivityLog(models.Model):
    class Meta:
        db_table = "core_activitylog"

    id: int
    uid = models.UUIDField(primary_key=True, editable=False, null=False)
    type = models.TextField()
    logged_user_id = models.BigIntegerField(null=True, blank=True)
    fromuser_id = models.BigIntegerField(null=True, blank=True)
    touser_id = models.BigIntegerField(null=True, blank=True)
    grupo_id = models.BigIntegerField(null=True)
    grupo_classe_id = models.BigIntegerField(null=True)
    trecho_classe_id = models.BigIntegerField(null=True)
    trecho_vendido_id = models.BigIntegerField(null=True)
    reembolso_id = models.BigIntegerField(null=True)
    notafiscal_id = models.BigIntegerField(null=True)
    travel_id = models.BigIntegerField(null=True)
    rota_id = models.BigIntegerField(null=True)
    pagamento_id = models.BigIntegerField(null=True)
    accounting_transaction_id = models.BigIntegerField(null=True)
    corporate_payment_id = models.BigIntegerField(null=True)
    vendor_id = models.BigIntegerField(null=True)
    company_id = models.BigIntegerField(null=True)
    origem = models.TextField(null=True, blank=True)
    destino = models.TextField(null=True, blank=True)
    datetime_ida = models.DateTimeField(null=True)
    datetime_volta = models.DateTimeField(null=True)
    jsondata = models.TextField(null=True, blank=True)
    invitecode = models.TextField(null=True, blank=True)
    invitestatus = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField("criado em", default=timezone.now)
    user_device = models.TextField(null=True, blank=True)
    user_os = models.TextField(null=True, blank=True)
    user_agent = models.TextField(null=True, blank=True)
    user_session = models.TextField(null=True, blank=True)
    user_tabid = models.TextField(null=True, blank=True)
    user_ip = models.TextField(null=True, blank=True)
    referrer = models.TextField(null=True, blank=True)  # TODO: transformar em texto
    utm_campaign = models.TextField(null=True, blank=True)
    utm_params = models.TextField(null=True, blank=True)
    anonymous_user_id = models.TextField(null=True, blank=True)

    @cached_property
    def jsondata_dict(self):
        if self.jsondata:
            return json.loads(self.jsondata)


class EmailTemplate(models.Model):
    id: int
    #: Hash único do template, veja `py:func:djamail.TemplateParsed::checksum`
    hash = models.TextField(unique=True)
    #: Caminho do template (exemplo: notificacao/user/login/criar_senha/email.html)
    path = models.TextField()
    #: Template completo do Django
    template = models.TextField()
    #: Metadados do template (exemplo: subject)
    metadata = models.JSONField()
    #: Data e hora de criação do created_at
    created_at = models.DateTimeField(auto_now_add=True)


class TransactionEmail(models.Model):
    id: int
    objects: SerializableManager = SerializableManager()

    DEFAULT_ENCODING = "gzip"

    to_user_id: int
    to_user = models.ForeignKey(
        User, null=True, blank=True, related_name="transactionmail_withtouser", on_delete=models.CASCADE
    )
    from_user_id: int
    from_user = models.ForeignKey(
        User, null=True, blank=True, related_name="transactionmail_withfromuser", on_delete=models.CASCADE
    )
    to_mail = models.CharField(max_length=256, null=True, blank=True)
    subject = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField("criado em", default=timezone.now)
    #: Encoding usado para guardar o conteúdo do e-mail
    # encoding = models.TextField(null=True, blank=True)
    # encoded_content = models.BinaryField(null=True)
    context = models.JSONField(null=True)
    email_template_id: int
    email_template = models.ForeignKey("EmailTemplate", null=True, on_delete=models.SET_NULL, db_index=False)

    def __str__(self):
        return "%s / %s" % (
            self.subject,
            self.to_mail,
        )

    def to_dict_json(self) -> dict:
        to_user = self.to_user
        from_user = self.from_user
        return {
            "id": self.id,
            "to_user": {"id": to_user.id, "name": to_user.get_full_name()},
            "from_user": {"id": from_user.id, "name": from_user.get_full_name()} if from_user else None,
            "to_mail": self.to_mail[2:-2],
            "subject": self.subject,
            "content": self.content,
            "created_at": to_default_tz(self.created_at),
            "channel": "email",
        }

    @cached_property
    def content(self):
        if self.email_template is not None:
            return self._render_content()
        else:
            # todos os emails até 03/05/2023 foram enviados para o s3 ...
            return self._fetch_content_from_s3()

    def _render_content(self):
        template = Template(self.email_template.template)
        context = Context(self.context)
        if self.to_user:
            context["user"] = {"first_name": self.to_user.first_name, "last_name": self.to_user.last_name}
        return template.render(context)

    def _fetch_content_from_s3(self):
        """O conteúdo de e-mails antigos estão no S3,
        veja py:func:`core.management.commands.upload_transaction_email_to_s3::comand`
        """

        # cada arquivo no S3 tem o conteúdo de 100 emails enviados, o objetivo disso é cada
        # objeto salvo ter mais de 128kb, que é o tamanho mínimo que a AWS cobra,
        # veja https://aws.amazon.com/s3/pricing/
        n_elems = 500
        filename = f"private/emails/{self.pk // n_elems}"
        emails = _fetch_and_read_emails_from_s3(filename)
        return emails[str(self.pk)]


@functools.lru_cache(maxsize=200)
def _fetch_and_read_emails_from_s3(filename):
    contents = storage.read_bytes(filename)
    return json.loads(gzip.decompress(contents))


class Notification(models.Model):
    class NotificationType(models.TextChoices):
        REMANEJAMENTO = "remanejamento"
        TORRE = "torre"

    id: int
    created_at = models.DateTimeField("criado em", auto_now_add=True)
    title = models.TextField()
    message = models.TextField()
    is_new = models.BooleanField(default=True)
    routes_to = models.CharField(max_length=64, blank=True, null=True)
    route_params = models.TextField(null=True, blank=True)
    user_id: int
    user = models.ForeignKey(User, related_name="notifications", on_delete=models.CASCADE)
    travel_id: int
    travel = models.ForeignKey("Travel", blank=True, null=True, on_delete=models.CASCADE)
    touser_id: int
    touser = models.ForeignKey(User, null=True, related_name="notifications_touser", on_delete=models.CASCADE)
    notification_type = models.CharField(max_length=30, choices=NotificationType.choices, null=True, blank=True)

    class Meta:
        ordering = ("-created_at",)
        indexes = [models.Index(fields=["user", "is_new"])]

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "created_at": to_default_tz(self.created_at).isoformat(),
            "title": self.title,
            "message": self.message,
            "is_new": self.is_new,
            "routes_to": self.routes_to,
            "user_id": self.user_id,
            "travel_id": self.travel_id,
            "touser_id": self.touser_id,
            "route_params": self.route_params if self.route_params else None,
            "notification_type": self.notification_type,
        }

    __dictjson__ = to_dict_json


class WhatsappMessage(models.Model):
    id: int
    objects: SerializableManager = SerializableManager()

    user_id: int
    user = models.ForeignKey(User, null=True, on_delete=models.CASCADE)
    sent_by_id: int
    sent_by = models.ForeignKey(User, related_name="+", null=True, blank=True, on_delete=models.CASCADE)
    travel_id: int
    travel = models.ForeignKey("Travel", null=True, blank=True, on_delete=models.CASCADE)
    rendered_message = models.TextField(null=True, blank=True)
    contact_reason = models.TextField(null=True, blank=True)
    type = models.CharField(max_length=256)
    phone = models.CharField(max_length=256)
    created_at = models.DateTimeField("criado em", auto_now_add=True)
    sent_to_freshchat = models.BooleanField(default=False)

    class Meta:
        indexes = [
            models.Index(fields=["sent_to_freshchat"]),
            models.Index(fields=["phone"]),  # Query do BX no metabase busca os templates recebidos por phone
        ]

    def render(self):
        created_at = to_default_tz(self.created_at).strftime("%d/%m/%Y às %H:%M:%S")
        travel = self.travel
        name = self.sent_by.get_full_name() if self.sent_by else None
        reservation_code = travel.reservation_code if travel else None
        travel_link = f"https://staff.buser.com.br/staff/travels/{travel.id}" if travel else None
        group_link = f"https://staff.buser.com.br/staff/grupos/{travel.grupo_id}" if travel else None
        contact_reason = self.contact_reason if self.contact_reason else None

        template_message = f"Template *{self.type}* enviado dia {created_at}"
        if name:
            template_message += f" por {name}."
        else:
            template_message += " automaticamente pelo sistema."
        if travel:
            template_message += f"\n\nCódigo da reserva: {reservation_code}"
            template_message += f"\nLink para reserva: {travel_link}"
            template_message += f"\nLink para o grupo: {group_link}"
        if contact_reason:
            template_message += f"\n\nExplicação deixada por quem enviou o template:\n\n{contact_reason}"

        template_message += "\n\n_________________________"
        template_message += f'\n\nConteúdo da mensagem que o passageiro recebeu:\n\n"{self.rendered_message}"'

        return template_message

    def to_dict_json(self) -> dict:
        sent_by = {"id": self.sent_by.id, "name": self.sent_by.get_full_name()} if self.sent_by else None
        travel = {"id": self.travel.id, "reservation_code": self.travel.reservation_code} if self.travel else None
        return {
            "id": self.id,
            "created_at": to_default_tz(self.created_at),
            "channel": "whatsapp",
            "sent_by": sent_by,
            "contact_reason": self.contact_reason,
            "sent_to_freshchat": self.sent_to_freshchat,
            "phone": self.phone,
            "travel": travel,
            "msgid": self.type,
            "rendered_message": self.rendered_message,
        }


class TravelCommunication(models.Model):
    class CommunicationType(models.TextChoices):
        WHATSAPP = "whatsapp"
        EMAIL = "email"

    id: int
    type = models.TextField(null=True, choices=CommunicationType.choices)
    communication_id = models.IntegerField()
    travel_id: int
    travel = models.ForeignKey("Travel", blank=True, null=True, on_delete=models.CASCADE)
    grupo_id: int
    grupo = models.ForeignKey("Grupo", blank=True, null=True, on_delete=models.CASCADE)
    created_at = models.DateTimeField("criado em", auto_now_add=True)

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "created_at": to_default_tz(self.created_at).isoformat(),
            "type": self.type,
            "communication_id": self.communication_id,
            "travel_id": self.travel_id,
            "grupo_id": self.grupo_id,
        }


class HelpMail(models.Model):
    id: int
    user = models.ForeignKey(User, null=True, on_delete=models.CASCADE)
    subject = models.CharField(max_length=128)
    description = models.TextField()
    email = models.CharField(max_length=256)
    slug = models.CharField(max_length=256)
    created_at = models.DateTimeField("criado em", auto_now_add=True)


class HelpQuestion(models.Model):
    id: int
    title = models.CharField(max_length=128, null=True, blank=True)
    slug = models.CharField(max_length=64)
    description = models.TextField(null=True, blank=True)
    parent = models.ForeignKey("self", null=True, blank=True, on_delete=models.CASCADE)
    created_at = models.DateTimeField("criado em", auto_now_add=True)
    updated_at = models.DateTimeField("atualizado em", auto_now=True)
    index = models.IntegerField(null=True, blank=True)
    hidden = models.BooleanField(default=False)
    removed = models.BooleanField(default=False)
    version = models.IntegerField(default=1)

    def to_dict_json(self) -> dict:
        d = {
            "id": self.id,
            "index": self.index,
            "title": self.title,
            "slug": self.slug,
            "hidden": self.hidden,
            "version": self.version,
            "description": self.description,
            "atualizado_em": to_default_tz(self.updated_at).isoformat(),
            "parent_id": self.parent_id,
        }
        return d

    __dictjson__ = to_dict_json


class TextoEstatico(models.Model):
    id: int
    slug = models.CharField(max_length=100, unique=True)
    titulo = models.CharField(max_length=100)
    html = models.TextField(null=True, blank=True)
    atualizado_em = models.DateField("atualizado em", blank=True, null=True)  # data que aparecerá no site

    created_at = models.DateTimeField("criado em", auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, blank=True, null=True, on_delete=models.CASCADE)

    def to_dict_json(self) -> dict:
        d = {
            "id": self.id,
            "titulo": self.titulo,
            "slug": self.slug,
            "html": self.html,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "updated_by": self.updated_by.username if self.updated_by else None,
            "atualizado_em": self.atualizado_em,
        }
        return d


class UserTextAcceptance(models.Model):
    id: int
    user = models.ForeignKey(User, unique=False, on_delete=models.CASCADE, blank=True, null=True)
    agreement_time = models.DateTimeField(auto_now=True)
    agreed_document = models.ForeignKey(
        TextoEstatico, related_name="agreed_document", unique=False, on_delete=models.PROTECT
    )


# todo: deprecated
class DitoAnonymousEvents(models.Model):
    id: int
    user = models.ForeignKey(User, null=True, blank=True, on_delete=models.CASCADE)  # Todo: remover
    lead = models.ForeignKey(Lead, null=True, blank=True, on_delete=models.CASCADE)
    anonymous_user_id = models.CharField(max_length=16)
    created_at = models.DateTimeField("criado em", auto_now_add=True)
    action = models.CharField(max_length=64)
    revenue = models.DecimalField(
        max_digits=12, decimal_places=2, default=D(0)
    )  # Todo: isso é usado? Todos no BD estão como 0
    data = models.TextField()

    class Meta:
        indexes = [
            models.Index(fields=["anonymous_user_id"]),
        ]


CHOICES_TIMEZONES = [
    ("America/Araguaina",) * 2,
    ("America/Bahia",) * 2,
    ("America/Belem",) * 2,
    ("America/Boa_Vista",) * 2,
    ("America/Campo_Grande",) * 2,
    ("America/Cuiaba",) * 2,
    ("America/Eirunepe",) * 2,
    ("America/Fortaleza",) * 2,
    ("America/Maceio",) * 2,
    ("America/Manaus",) * 2,
    ("America/Noronha",) * 2,
    ("America/Porto_Velho",) * 2,
    ("America/Recife",) * 2,
    ("America/Rio_Branco",) * 2,
    ("America/Santarem",) * 2,
    ("America/Sao_Paulo",) * 2,
    # Cidades estrangeiras
    ("America/Santiago",) * 2,
    ("America/Argentina/Buenos_Aires",) * 2,
    ("America/Argentina/Cordoba",) * 2,
]


class CidadeInfo(models.Model):
    id: int
    descricao = models.TextField(null=True, blank=True)
    sumario = models.TextField(max_length=160, null=True, blank=True)
    pontos_turisticos = models.TextField(null=True, blank=True)
    hospedagens = models.TextField(null=True, blank=True)
    restaurantes = models.TextField(null=True, blank=True)
    eventos = models.TextField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    cidade = models.OneToOneField("core.Cidade", null=True, blank=True, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.cidade.name} ({self.cidade.uf}) - {self.descricao}"

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "descricao": self.descricao,
            "sumario": self.sumario,
            "pontos_turisticos": self.pontos_turisticos,
            "hospedagens": self.hospedagens,
            "restaurantes": self.restaurantes,
            "eventos": self.eventos,
            "last_updated": to_default_tz(self.updated_at).isoformat(),
        }

    __dictjson__ = to_dict_json


class CidadeMetrica(models.Model):
    id: int
    cidade = models.OneToOneField("core.Cidade", on_delete=models.CASCADE)
    viagens_count = models.IntegerField(default=0)
    page_views = models.IntegerField(default=0, null=True)

    class Meta:
        indexes = [models.Index(fields=["-viagens_count"]), models.Index(fields=["-page_views"])]


class Cidade(models.Model):
    id: int
    objects: SerializableManager = SerializableManager()

    name = models.CharField(max_length=256)
    uf = models.CharField(max_length=2, null=True, blank=True, choices=ufs_brasil.UFS)
    slug = models.CharField(max_length=256, editable=False, null=True)
    sigla = models.CharField(max_length=3, null=True)
    ativo = models.BooleanField(default=True)
    timezone = models.CharField(null=True, max_length=256, choices=CHOICES_TIMEZONES, default="America/Sao_Paulo")
    img_cc = models.TextField(null=True, blank=True)
    image = models.ImageField(
        max_length=256, null=True, blank=True, upload_to="public/cidade", storage=public_media_storage
    )
    # preencher de acordo com a planilha codigo_cidade_ibge na pasta docs
    city_code_ibge = models.IntegerField(null=True, blank=True)
    uf_code_ibge = models.IntegerField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    centroid = PointField(null=True, srid=4326, geography=True)

    if TYPE_CHECKING:
        localembarque_set: RelatedManager[LocalEmbarque]

    def __str__(self):
        return self.name

    class Meta:
        ordering = ("slug",)
        indexes = [
            models.Index(fields=["slug"]),
            models.Index(fields=["ativo"]),
            models.Index(fields=["updated_at"]),
        ]

    def save(self, *args, **kwargs):
        self.slug = slugify(f"{self.name[:251]}-{self.uf}")
        super(Cidade, self).save(*args, **kwargs)

    @property
    def picture_url(self):
        if self.image:
            return self.image.url

    @property
    def is_exterior(self):
        return self.uf == ufs_brasil.UFEnum.EX.name

    @memoize(timeout=1 * 60 * 60)
    def thumbor_url(self, width=400, height=300):
        if self.picture_url:
            return generate_thumbor_url(self.picture_url, width=width, height=height)

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "name": self.name,
            "uf": self.uf,
            "slug": self.slug,
            "sigla": self.sigla,
            "timezone": self.timezone,
            "picture_url": self.thumbor_url(),
        }

    __dictjson__ = to_dict_json


class AliasPlace(models.Model):
    id: int
    alias = models.CharField(max_length=256)
    slug = models.SlugField(max_length=260, editable=False, unique=True)
    cidade = models.ForeignKey(Cidade, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        self.slug = slugify(f"{self.alias}-{self.cidade.uf}")
        super(AliasPlace, self).save(*args, **kwargs)

    class Meta:
        unique_together = ("alias", "cidade")


class Imagem(models.Model):
    id: int

    class Tipo(models.TextChoices):
        COVER = "cover"
        GALLERY = "gallery"
        THUMB = "thumb"

    class Provider(models.TextChoices):
        DEVICE = "device"
        WIKIPEDIA = "wikipedia"

    tipo = models.CharField(max_length=15, choices=Tipo.choices, null=False, blank=False)
    origin_url = models.TextField(null=True, blank=True)
    descricao = models.TextField(null=True, blank=True)
    provider = models.CharField(max_length=15, choices=Provider.choices, null=True, blank=False)
    cidade_info = models.ForeignKey(CidadeInfo, null=True, blank=True, on_delete=models.CASCADE)
    termo = models.ForeignKey("glossario.Termo", null=True, blank=True, on_delete=models.CASCADE)
    post = models.ForeignKey("blog.Post", null=True, blank=True, on_delete=models.CASCADE)
    ponto_turistico = models.ForeignKey("blog.PontoTuristico", null=True, blank=True, on_delete=models.CASCADE)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    image_file = models.ImageField(upload_to="public/Imagem", null=True, max_length=256, storage=public_media_storage)

    class Meta:
        ordering = ["id"]

    def __str__(self):
        return self.descricao

    def to_dict_json_resized(self, *args, **kwargs):
        from commons.thumbor import generate_thumbor_url

        size_map = {
            self.Tipo.COVER: {"width": 1150, "height": 235},
            self.Tipo.THUMB: {"width": 370, "height": 220},
            self.Tipo.GALLERY: {"width": 350, "height": 240},
        }
        size = size_map[self.tipo]

        json = self.to_dict_json()

        if not json:
            return

        if json["url"]:
            json["url"] = generate_thumbor_url(json["url"], *args, **size, **kwargs)

        return json

    def to_dict_json(self) -> dict:
        url = None
        if self.image_file:
            # Hotfix que força URL utilizando cloudfront para corrigir problemas de inconsistência em
            # URL publicas com chave de autenticação.
            #
            # Mais detalhes: https://gitlab.buser.com.br/buser/buser_django/-/merge_requests/8489

            custom_domain = settings.S3_MEDIA_CUSTOM_DOMAIN
            if custom_domain:
                url = f"https://{custom_domain}/{self.image_file.name}"
            else:
                url = self.image_file.url

        return {
            "id": self.id,
            "tipo": self.tipo,
            "url": url or "",
            "origin_url": self.origin_url,
            "descricao": self.descricao,
            "provider": self.provider,
            "last_updated": to_default_tz(self.updated_at).isoformat(),
            "created": to_default_tz(self.created_at).isoformat(),
        }

    __dictjson__ = to_dict_json


class WikipediaInfo(models.Model):
    id: int
    intro = models.TextField(blank=True)  # alterar para salvar o extracts
    title = models.CharField(max_length=256, null=True, blank=True)
    url = models.CharField(max_length=1024, null=True, blank=True)
    created_at = models.DateTimeField("criado em", auto_now_add=True)
    updated_at = models.DateTimeField("atualizado em", auto_now=True)
    cidade = models.ForeignKey(Cidade, null=True, blank=True, on_delete=models.CASCADE)

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "intro": self.intro,
            "title": self.title,
            "url": self.url,
            "last_updated": to_default_tz(self.updated_at).isoformat(),
        }

    __dictjson__ = to_dict_json


class UserAcquisitionData(models.Model):
    id: int
    lead_id: int
    lead = models.ForeignKey(Lead, null=True, blank=True, on_delete=models.CASCADE)
    referrer = models.TextField(null=True, blank=True)
    utm_campaign = models.TextField(null=True, blank=True)
    utm_content = models.TextField(null=True, blank=True)
    utm_medium = models.TextField(null=True, blank=True)
    utm_source = models.TextField(null=True, blank=True)
    utm_term = models.TextField(null=True, blank=True)
    date = models.DateTimeField(null=True, blank=True)
    anonymous_user_id = models.CharField(max_length=32, null=True)
    created_at = models.DateTimeField("criado em", auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField("atualizado em", auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=["anonymous_user_id"], name="uad_anonymous_user_id_idx"),
        ]

    def as_dict(self):
        return {
            "lead_id": self.lead_id,
            "referrer": self.referrer,
            "utm": {
                "campaign": self.utm_campaign,
                "content": self.utm_content,
                "medium": self.utm_medium,
                "source": self.utm_source,
                "term": self.utm_term,
            },
            "date": self.date.isoformat(),
            "anonymous_user_id": self.anonymous_user_id,
        }

    def as_tuple(self):
        return (
            self.referrer,
            self.utm_campaign,
            self.utm_content,
            self.utm_medium,
            self.utm_source,
            self.utm_term,
            self.anonymous_user_id,
        )


class SearchLog(models.Model):
    id: int
    # search|origin_search|destination_search
    type = models.CharField(max_length=20)
    user_id: int
    user = models.ForeignKey(User, null=True, blank=True, on_delete=models.CASCADE)
    origem_id: int
    origem = models.ForeignKey(
        Cidade,
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.CASCADE,
        db_index=False,
    )
    destino_id: int
    destino = models.ForeignKey(
        Cidade,
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.CASCADE,
        db_index=False,
    )
    local_origem_id: int
    local_origem = models.ForeignKey(
        "core.LocalEmbarque", blank=True, null=True, related_name="+", on_delete=models.PROTECT
    )
    local_destino_id: int
    local_destino = models.ForeignKey(
        "core.LocalEmbarque", blank=True, null=True, related_name="+", on_delete=models.PROTECT
    )
    date_ida = models.DateField(null=True, blank=True)
    date_volta = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        indexes = [
            models.Index(fields=["user", "type", "-id"], name="searchlog_type_user_id_idx"),
            models.Index(fields=["origem", "destino"], name="searchlog_origem_destino_idx"),
        ]
        ordering = ["-id"]


# todo: deprecated
class VotoBus(models.Model):
    id: int
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    votou_em = models.CharField(max_length=10)


# todo: deprecated
class CandidatosFlaLibertaBus(models.Model):
    id: int
    token = models.CharField(max_length=256, null=True, blank=True)
    name = models.CharField(max_length=256, null=True, blank=True)
    email = models.CharField(max_length=256, null=True, blank=True)
    phone = models.CharField(max_length=16, null=True, blank=True)
    birthday = models.DateField(null=True, blank=True)
    profissao = models.TextField(max_length=10)
    cpf = models.CharField(max_length=16, null=True, blank=True)
    document_number = models.CharField(max_length=256, null=True, blank=True)
    document_orgao = models.CharField(max_length=50, null=True, blank=True)
    document_s3key = models.CharField(max_length=256, null=True, blank=True)
    ingresso_s3key = models.CharField(max_length=256, null=True, blank=True)
    created_at = models.DateTimeField("criado em", auto_now_add=True)
    updated_at = models.DateTimeField("atualizado em", auto_now=True)


CHOICES_STATUS = [(c, c) for c in ["open", "redirected_vendas", "redirected_grupo", "closed"]]


class Atendimento(models.Model):
    id: int
    status = FSMField(default="open", choices=CHOICES_STATUS, max_length=64, protected=False)
    topic = models.CharField(max_length=256, null=True, blank=True)
    user = models.ForeignKey(User, blank=True, null=True, on_delete=models.SET_NULL)
    freshchat_restore_id = models.CharField(max_length=256, null=True, blank=True)
    freshchat_user_id = models.CharField(max_length=256, null=True, blank=True)
    freshchat_group_id = models.CharField(max_length=256, null=True, blank=True)
    freshchat_channel_id = models.CharField(max_length=256, null=True, blank=True)
    freshchat_conversation_id = models.CharField(max_length=256, null=True, blank=True)
    freshchat_agent_id = models.CharField(max_length=256, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    last_message_sent = models.DateTimeField(null=True, blank=True)
    conversation_id = models.CharField(max_length=256)  # deprecated

    class Meta:
        indexes = [
            models.Index(fields=["freshchat_restore_id"]),
            models.Index(fields=["freshchat_conversation_id"]),
            models.Index(fields=["freshchat_user_id"]),
        ]

    @transition(field=status, source="closed", target="open")
    def reopen(self):
        pass

    # Utilizado apenas pelo redirect de vendas
    @transition(field=status, source="*", target="redirected_vendas")
    def redirect_vendas(self):
        pass

    @transition(field=status, source="*", target="redirected_grupo")
    def redirect_grupo(self):
        pass

    @transition(field=status, source="*", target="closed")
    def close(self):
        self.topic = None


class Device(models.Model):
    id: int

    class Kind(models.TextChoices):
        APNS = "apns"
        FCM = "fcm"
        FCM_MOTORISTA = "fcm_motorista"
        WEB = "webpush"

    class AppTrackingTransparencyStatus(models.TextChoices):
        AUTHORIZED = "authorized"
        RESTRICTED = "restricted"
        DENIED = "denied"
        NOT_DETERMINED = "notDetermined"
        UNKNOWN = "unknown"

    token = models.TextField()
    kind = models.CharField(max_length=50, choices=Kind.choices)
    endpoint_arn = models.CharField(max_length=256, blank=True, null=True)
    profile_id: int
    profile = models.ForeignKey("core.Profile", related_name="devices", blank=True, null=True, on_delete=models.CASCADE)
    lead_id: int
    lead = models.ForeignKey("core.Lead", related_name="devices", blank=True, null=True, on_delete=models.CASCADE)
    att_status = models.CharField(
        # guarda a permissão dada pelo usuário iOS segundo o framework App Tracking Transparency
        # https://developer.apple.com/app-store/user-privacy-and-data-use/
        max_length=50,
        choices=AppTrackingTransparencyStatus.choices,
        blank=True,
        null=True,
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=["profile", "kind"], name="unique_profile_kind"),
            models.UniqueConstraint(fields=["lead", "kind"], name="unique_lead_kind"),
        ]
        indexes = [models.Index(fields=["token", "kind"], name="token_kind_idx")]

    def __str__(self):
        if self.profile_id and self.lead_id:
            owner = f"Profile: {self.profile_id} & Lead: {self.lead_id}"
        elif self.profile_id:
            owner = f"Profile: {self.profile_id}"
        else:
            owner = f"Lead: {self.lead_id}"

        return f"{owner}, <{self.kind.lower()}> {self.token}"

    @property
    def parsed_token(self):
        if self.kind == self.Kind.WEB:
            return {"subscription": json.loads(self.token)}

        return {"token": self.token, "endpoint_arn": self.endpoint_arn}

    @property
    def platform(self):
        platform_map = {
            Device.Kind.FCM: "Android OS",
            Device.Kind.FCM_MOTORISTA: "Android OS",
            Device.Kind.APNS: "iPhone OS",
            Device.Kind.WEB: "Web",
        }
        return platform_map[self.kind]


class SearchIndex(models.Model):
    id: int
    model = models.CharField(max_length=64)
    object_id = models.BigIntegerField()
    vector = SearchVectorField()

    class Meta:
        indexes = [GinIndex(fields=["vector"]), models.Index(fields=["object_id"])]
        constraints = [models.UniqueConstraint(fields=["model", "object_id"], name="unique_model_object_id")]

    def __str__(self):
        return f"{self.model} - {self.object_id}"


class NotificationBatch(models.Model):
    id: int
    notifications = models.JSONField()
    recipient_type = models.CharField(max_length=10)
    recipients = ArrayField(models.CharField(max_length=128))
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey("auth.User", null=True, on_delete=models.SET_NULL)
    process_at = models.DateTimeField()
    processed = models.BooleanField(default=False)
    marketing = models.BooleanField(default=False)

    class Meta:
        indexes = [models.Index(fields=["processed"])]

    def __str__(self):
        return f"notificação [{self.recipient_type}] agendada para {self.process_at}"


class WhatsappTemplate(models.Model):
    id: int
    msgid = models.CharField(max_length=256, unique=True)
    content = models.TextField()
    type = models.CharField(max_length=64)
    default_values = models.JSONField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    inactive_reason = models.CharField(max_length=1024, blank=True, null=True)
    from_sender = models.CharField(max_length=64, blank=True, null=True)

    def to_dict_json(self) -> dict:
        return {
            "msgid": self.msgid,
            "content": self.content,
            "type": self.type,
            "is_active": self.is_active,
            "inactive_reason": self.inactive_reason,
            "from_sender": self.from_sender,
        }

    def render(self, context):
        try:
            return self.content.format(**context)
        except KeyError as e:
            raise ValidationError(f"Variável de template não encontrada: {e.args[0]}") from e

    def create_context(self, context):
        if self.default_values:
            context = {**self.default_values, **context}

        return {k: context[k] for k in self.iter_params()}

    def iter_params(self):
        for parsed in Formatter().parse(self.content):
            param = parsed[1]
            if param:
                yield param

    @property
    def from_number(self):
        return self.from_sender or "suporte"


class LandingPage(models.Model):
    id: int

    class ModeloTipoExpiracaoCupons(models.TextChoices):
        DAILY = "daily"
        HOURLY = "hourly"
        ON_AND_OFF_HOURS = "on_and_off_hours"

    codigo = models.CharField(max_length=256, unique=True)
    created_at = models.DateTimeField(default=now)
    updated_at = models.DateTimeField(auto_now=True)
    ativo = models.BooleanField(default=False)
    titulo = models.CharField(max_length=256, null=True)
    texto = models.TextField(null=True)
    pre_input = models.TextField(null=True)
    cta = models.TextField(max_length=128, null=True)
    link_cadastro = models.URLField(max_length=200, null=True)
    imagem = models.ImageField(upload_to="public/landing-page", max_length=256, null=True, storage=public_media_storage)
    cupoms = ArrayField(models.CharField(max_length=16), blank=True, null=True)
    cupom_start_date = models.DateField(null=True)
    cupom_start_time = models.TimeField(null=True, blank=True)
    expiring_method = models.CharField(
        max_length=16,
        choices=ModeloTipoExpiracaoCupons.choices,
        null=True,
        blank=True,
        default=ModeloTipoExpiracaoCupons.DAILY,
    )
    hidden_form = models.BooleanField(default=False)

    objects: SerializableManager = SerializableManager()

    @property
    def cupom_datetime(self):
        if self.cupom_start_date and self.cupom_start_time:
            return to_default_tz(datetime.combine(self.cupom_start_date, self.cupom_start_time))

    @property
    def cupom_index(self):
        index = -1
        expiring_method = self.expiring_method
        if expiring_method:
            if not self.cupom_start_date or not self.cupoms:
                return -1

            if expiring_method == self.ModeloTipoExpiracaoCupons.DAILY:
                index = max((today() - self.cupom_start_date).days, -1)
            if (
                expiring_method == self.ModeloTipoExpiracaoCupons.HOURLY
                or expiring_method == self.ModeloTipoExpiracaoCupons.ON_AND_OFF_HOURS
            ):
                if not self.cupom_start_time:
                    return -1
                current_time = to_default_tz(dateutils.now())
                lp_datetime = self.cupom_datetime
                if not lp_datetime:
                    return -1

                diff = current_time - lp_datetime
                index = max(int(diff.total_seconds() // 3600), -1)

                if expiring_method == self.ModeloTipoExpiracaoCupons.ON_AND_OFF_HOURS:
                    """
                    Esse método de expiração dos cupons trata de horas intercaladas.
                    Logo se existem n cupons e se passaram x < n horas desde o lp_datetime
                    sabemos que se x % 2 != 0 então estamos em uma hora "OFF".
                    Caso contrário, estamos em uma hora "ON" e o índice do cupom é x // 2
                    """
                    if index % 2 != 0:
                        return -1
                    index = index // 2

        if index >= len(self.cupoms):
            return -1
        return index


class BusChangeRequestRules(models.Model):
    """Regras para redução de frete em caso de mudança de tipo de Onibus.tipo"""

    id: int
    from_bus = models.TextField(max_length=32, default=None, null=False, choices=TIPOS_ONIBUS_CHOICES, db_column="from")
    to = models.TextField(max_length=32, default=None, null=False, choices=TIPOS_ONIBUS_CHOICES)
    reduction = models.DecimalField(
        max_digits=5, decimal_places=2, null=False, validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def apply_reduction(self, value):
        return value * (1 - (self.reduction / 100))


class CustosEstaduais(models.Model):
    id: int
    uf = models.CharField(max_length=2, unique=True)
    preco_diesel = models.DecimalField(max_digits=6, decimal_places=2, null=True)
    aliquota_simples = models.DecimalField(max_digits=6, decimal_places=2, null=True)
    ipva = models.DecimalField(max_digits=6, decimal_places=2, null=True)
    updated_at = models.DateTimeField(auto_now=True)

    def to_dict_json(self) -> dict:
        return {
            "uf": self.uf,
            "preco_diesel": self.preco_diesel,
            "aliquota_simples": self.aliquota_simples,
            "ipva": self.ipva,
            "updated_at": self.updated_at.isoformat(),
        }


class VeiculoValor(models.Model):
    class Categorias(models.TextChoices):
        BAIXO = "Baixo", "baixo"
        LD = "LD", "LD"
        DD = "DD", "LD"
        MICRO = "MICRO", "micro-onibus"
        VAN = "Van", "van"

    DEPRECIACAO_PRIMEIRO_ANO = D("0.15")
    DEPRECIACAO_APOS_UM_ANO = D("0.10")
    DEPRECIACAO_NONO_DECIMO_ANO = D("0.06")
    DEPRECIACAO_ANO_LIMITE = 10
    id: int
    eixos = models.IntegerField(default=2)
    categoria = models.CharField(max_length=15, choices=Categorias.choices, null=False)
    valor = models.DecimalField(max_digits=10, decimal_places=2, null=False, default=1000000)
    consumo = models.DecimalField(max_digits=5, decimal_places=2, null=False, default=5)
    seguro = models.DecimalField(max_digits=10, decimal_places=2, null=False, default=4500)
    updated_at = models.DateTimeField(auto_now=True)

    def valor_depreciado_por_idade(self, anos=0):
        valor = self.valor

        if anos == 0:
            return round(valor, 2)

        if anos > self.DEPRECIACAO_ANO_LIMITE:
            anos = self.DEPRECIACAO_ANO_LIMITE

        valor -= valor * self.DEPRECIACAO_PRIMEIRO_ANO
        for ano in range(anos - 1):
            if ano + 2 in [9, 10]:  # No nono e décimo ano, a depreciação é de 6%
                valor -= valor * self.DEPRECIACAO_NONO_DECIMO_ANO
                continue
            valor -= valor * self.DEPRECIACAO_APOS_UM_ANO

        return round(valor, 2)

    def valor_depreciacao(self, anos=0):
        if anos == 0:
            return 0

        valor_atual = self.valor_depreciado_por_idade(anos)
        valor_ano_anterior = self.valor_depreciado_por_idade(anos - 1)
        return valor_ano_anterior - valor_atual

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "eixos": self.eixos,
            "categoria": self.categoria,
            "valor": self.valor,
            "consumo": self.consumo,
            "seguro": self.seguro,
            "updated_at": self.updated_at.isoformat(),
        }

    class Meta:
        unique_together = ("eixos", "categoria")
        constraints = [
            models.CheckConstraint(check=models.Q(valor__gt=0), name="core_veiculovalor_valor_gt_0"),
            models.CheckConstraint(check=models.Q(consumo__gt=0), name="core_veiculovalor_consumo_gt_0"),
            models.CheckConstraint(check=models.Q(seguro__gt=0), name="core_veiculovalor_seguro_gt_0"),
        ]


RESSARCIMENTO_TYPE_CHOICES = [(c, c) for c in ["buser", "marketplace", "hibrido"]]


class ValorRessarcimento(models.Model):
    id: int
    index = models.CharField(max_length=4, default="0.0")
    key = models.CharField(max_length=256, unique=True)
    text = models.TextField()
    type = models.CharField(max_length=256, choices=RESSARCIMENTO_TYPE_CHOICES)
    porcento_rateio = models.IntegerField()
    value_real = models.DecimalField(max_digits=12, decimal_places=2, default=D(0))
    base_de_calculo = models.TextField(null=True, blank=True)
    tratativa = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    ocorrencia_key = models.CharField(max_length=256, default="")
    ocorrencia_text = models.TextField(null=True, blank=True, default="")
    voucher = models.BooleanField(default=False)
    code_cupom = models.CharField(max_length=256, default="")
    ressarcimento = models.BooleanField(default=False)
    custo_adicional = models.BooleanField(default=False)
    permissao = models.ForeignKey("auth.Permission", blank=True, null=True, on_delete=models.CASCADE)
    needs_comprovante = models.BooleanField(default=True)
    estornar_no_cancelamento = models.BooleanField(default=True)

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=~Q(ressarcimento=True, custo_adicional=True),
                name="core_valorressarcimento_ressarcimento_custoadicional_true",
            )
        ]

    def to_dict_json(self) -> dict:
        return {
            "index": self.index,
            "key": self.key,
            "text": self.text,
            "type": self.type,
            "porcento_rateio": self.porcento_rateio,
            "value_real": self.value_real,
            "base_de_calculo": self.base_de_calculo,
            "tratativa": self.tratativa,
            "locked": False if self.value_real == 0 and self.porcento_rateio == 0 else True,
            "locked_cupom": True if self.code_cupom != "" else False,
            "is_active": self.is_active,
            "ocorrencia_key": self.ocorrencia_key,
            "ocorrencia_text": self.ocorrencia_text,
            "voucher": self.voucher,
            "code_cupom": self.code_cupom,
            "ressarcimento": self.ressarcimento,
            "custo_adicional": self.custo_adicional,
            "permissao": self.permissao_id,
            "needs_comprovante": self.needs_comprovante,
            "estornar_no_cancelamento": self.estornar_no_cancelamento,
        }


class PassagemGratisSafelist(models.Model):
    id: int
    cpf = models.CharField(max_length=11, unique=True, db_index=True)
    due_date = models.DateTimeField(null=True, blank=True)
    campanha = models.CharField(max_length=256)
    permite_todos_modelos_venda = models.BooleanField(default=False)
    user = models.ForeignKey("auth.User", on_delete=models.CASCADE, null=True, blank=False)

    def clean(self):
        self.cpf = only_numbers(self.cpf)
        if len(self.cpf) != 11:
            raise ValidationError("CPF precisa ter 11 dígitos")

    def save(self, **kwargs):
        self.full_clean()
        super().save(**kwargs)


class AgenciaDeVenda(models.Model):
    objects: SerializableManager = SerializableManager()

    cnpj = models.CharField(max_length=14, unique=True, null=True)
    razao_social = models.CharField(max_length=256, blank=True, null=True)
    nickname = models.CharField(max_length=64, blank=True, null=True)


class PontoDeVenda(models.Model):
    class SituacaoRepasseDinheiro(models.TextChoices):
        ADIMPLENTE = "adimplente"
        INADIMPLENTE = "inadimplente"
        DESCONHECIDA = "desconhecida"

    id: int
    objects: SerializableManager = SerializableManager()

    cidade_id: int
    cidade = models.ForeignKey(Cidade, on_delete=models.CASCADE, null=True)
    mapurl = models.CharField(max_length=2048, null=True)
    ativo = models.BooleanField(default=True)
    phone = models.CharField(max_length=16)
    latitude = models.FloatField(blank=True, null=True)
    longitude = models.FloatField(blank=True, null=True)
    nickname = models.CharField(max_length=64, blank=True, null=True)
    endereco_referencia = models.CharField(max_length=140, blank=True, null=True)
    endereco_numero = models.CharField(max_length=32, blank=True, null=True)
    endereco_bairro = models.CharField(max_length=1024, blank=True, null=True)
    endereco_cep = models.CharField(max_length=8, blank=True, null=True)
    endereco_logradouro = models.CharField(max_length=256, blank=True, null=True)
    gestor_id: int | None
    gestor = models.ForeignKey(User, blank=True, null=True, related_name="pdv_gerenciados", on_delete=models.SET_NULL)
    email = models.CharField(max_length=256, null=True)
    cnpj = models.CharField(max_length=14, null=True)
    razao_social = models.CharField(max_length=256, null=True)
    public = models.BooleanField(default=True)
    tem_ponto_fisico = models.BooleanField(default=True, null=True, blank=True)
    maximo_divida_pdv_para_bloqueio = models.DecimalField(max_digits=12, decimal_places=2, default=D(1000.00))
    maximo_atraso_dias_para_bloqueio = models.IntegerField(default=7)
    vende_em_dinheiro = models.BooleanField(default=False)
    situacao_repasse_dinheiro = models.TextField(
        choices=SituacaoRepasseDinheiro.choices, default=SituacaoRepasseDinheiro.DESCONHECIDA
    )


def get_default_vigencia():
    return (today(), date.max)


class ConfiguracaoPontoDeVenda(models.Model):
    id: int
    ponto_de_venda = models.ForeignKey(PontoDeVenda, on_delete=models.CASCADE, null=True)
    taxa_comissao = models.DecimalField(
        decimal_places=2, max_digits=5, validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    vigencia = DateRangeField(default=get_default_vigencia)
    created_at = models.DateTimeField(null=True, blank=True)
    deleted_at = models.DateTimeField(null=True, blank=True)
    fromuser = models.ForeignKey(
        User, blank=True, null=True, related_name="configuracao_pdv_fromuser", on_delete=models.CASCADE
    )


class Survey(models.Model):
    class Area(models.TextChoices):
        MARKETING = "marketing"
        OPERATIONS = "operations"
        BUSER_EXPERIENCE = "buser_experience"
        LEGAL_AND_POLICE = "legal_and_police"
        PEOPLE = "people"
        PRODUCT = "product"
        NEW_BUSINESS = "new_business"
        FINANCE = "finance"
        TECHNOLOGY = "technology"
        AQUISICAO_E_CONVERSAO = "aquisicao_e_conversao"
        QUALIDADE = "qualidade"
        POS_VENDA_E_VIAGEM = "pos_venda_e_viagem"
        BUSER = "BUSER"
        GROWTH = "GROWTH"

    class SurveyCategory(models.TextChoices):
        NO_SHOW = "no_show"
        POST_TRAVEL = "post_travel"  # DEPRECATED
        POST_BOARDING = "post_boarding"
        NPS = "nps"  # transacional
        NPS_RELACIONAL = "nps_relacional"
        FEEDBACK_APP = "feedback_app"
        PAX_PROFILE = "pax_profile"
        CANCEL_TRAVEL = "cancel_travel"
        POS_VIAGEM = "pos_viagem"

    objects: SerializableManager = SerializableManager(
        select_related=["created_by"],
        prefetch_related=["questions"],
    )

    id: int
    area = models.CharField(max_length=50, choices=Area.choices)
    theme = models.TextField()
    subtheme = models.TextField(blank=True)
    description = models.TextField(blank=True)
    category = models.CharField(max_length=50, choices=SurveyCategory.choices)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey("auth.User", on_delete=models.DO_NOTHING, related_name="created_surveys")
    questions = models.ManyToManyField("core.SurveyQuestion", related_name="surveys")
    survey_slug = models.CharField(max_length=256, editable=False, null=True)
    idx = models.IntegerField(null=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["area", "theme", "category"],
                name="unique_active_theme_per_area_per_survey",
                condition=models.Q(is_active=True),
            ),
            models.UniqueConstraint(fields=["survey_slug"], name="unique_slug_per_survey"),
            models.UniqueConstraint(
                fields=["idx", "category"],
                name="unique_active_idx_per_category",
                condition=models.Q(is_active=True),
            ),
        ]

    def save(self, *args, **kwargs):
        self.survey_slug = slugify(f"{self.area}-{self.theme[:150]}")
        super(Survey, self).save(*args, **kwargs)

    def __str__(self) -> str:
        return f"Survey(id={self.id}, theme={self.theme}, category={self.category})"


class SurveyQuestion(models.Model):
    class QuestionCategory(models.TextChoices):
        SINGLE_CHOICE = "single_choice"
        MULTIPLE_CHOICE = "multiple_choice"
        TEXT = "text"
        FEEDBACK = "feedback"
        LIKE = "like"
        STAR_RATING = "star_rating"
        NPS = "nps"

    objects: SerializableManager = SerializableManager(
        select_related=["created_by"],
    )

    id: int
    title = models.TextField()
    subtitle = models.TextField(blank=True)
    options = ArrayField(models.TextField(), blank=True, default=list)
    category = models.TextField(choices=QuestionCategory.choices, default="text")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey("auth.User", on_delete=models.DO_NOTHING, related_name="created_questions")
    parent_question = models.ForeignKey(
        "self", on_delete=models.DO_NOTHING, null=True, blank=True, related_name="child_questions"
    )

    def __str__(self) -> str:
        return f"SurveyQuestion(id={self.id}, title={self.title}, category={self.category}, parent_id={self.parent_question_id})"


class SurveyResponse(models.Model):
    objects: SerializableManager = SerializableManager(
        select_related=["travel", "lead", "survey"],
        prefetch_related=["answer_set"],
    )

    id: int
    lead_id: int
    lead = models.ForeignKey("core.Lead", on_delete=models.DO_NOTHING, null=True)
    travel_id: int
    travel = models.ForeignKey("core.Travel", on_delete=models.DO_NOTHING, null=True)
    survey_id: int
    survey = models.ForeignKey("core.Survey", on_delete=models.DO_NOTHING)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    avg_rating = models.DecimalField(null=True, decimal_places=1, max_digits=3)

    class Meta:
        indexes = [
            models.Index(fields=["survey"]),
            models.Index(fields=["created_at"]),
            models.Index(fields=["-created_at"]),
        ]


class SurveyAnswer(models.Model):
    id: int
    survey_response_id: int
    survey_response = models.ForeignKey("core.SurveyResponse", on_delete=models.DO_NOTHING)
    survey_question_id: int
    survey_question = models.ForeignKey("core.SurveyQuestion", on_delete=models.DO_NOTHING)
    value = models.JSONField(blank=True, default=dict)


class CanalAtendimento(models.Model):
    id: int
    nome = models.CharField(max_length=64, unique=True)


class LicensePlateValidation(models.Model):
    class Status(models.TextChoices):
        APPROVED = "aprovado"
        PENDING = "pendente"
        REJECTED = "rejeitado"

    class RejectionReason(models.TextChoices):
        DIVERGENT = "Placa divergente"
        PICTURE_ERROR = "Erro na foto"

    id: int
    checkin_bus_id: int
    checkin_bus = models.ForeignKey("core.CheckinBus", on_delete=models.CASCADE)
    event_id: int
    event = models.ForeignKey("core.AlertaSeguranca", blank=True, null=True, on_delete=models.CASCADE)

    group_id: int
    group = models.ForeignKey("core.Grupo", on_delete=models.CASCADE, null=False, blank=False)
    status = models.TextField(default=Status.PENDING, choices=Status.choices)
    rejection_reason = models.TextField(choices=RejectionReason.choices, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    analyst_id: int
    analyst = models.ForeignKey("auth.User", on_delete=models.DO_NOTHING, null=True)
    license_plate = models.TextField(null=True)
    observation = models.TextField(null=True)

    objects: SerializableManager = SerializableManager()


class TemplateComunicacao(models.Model):
    id: int
    problem_description = models.TextField(null=False, blank=False)
    email = models.JSONField(null=False, blank=False)
    sms = models.JSONField(null=False, blank=False)
    push = models.JSONField(null=False, blank=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "problem_description": self.problem_description,
            "email": self.email,
            "sms": self.sms,
            "push": self.push,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
        }


class UserTrechoFavorito(models.Model):
    id: int
    user_id: int
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="trechos_favoritos")
    origem_id: int
    origem = models.ForeignKey("core.Cidade", on_delete=models.CASCADE, related_name="origem_trechos_favoritos")
    destino_id: int
    destino = models.ForeignKey("core.Cidade", on_delete=models.CASCADE, related_name="destino_trechos_favoritos")
    created_on = models.DateTimeField(auto_now_add=True)

    objects: SerializableManager = SerializableManager()
    objects_sqlextra = PostgresManager()

    class Meta:
        unique_together = ("user", "origem", "destino")


class CategoriaTuristica(models.Model):
    id: int
    objects: SerializableManager = SerializableManager()

    titulo = models.TextField(unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    ativo = models.BooleanField(default=True)
    icone = models.TextField(null=True, blank=True)
    cidades = models.ManyToManyField("core.Cidade", related_name="categorias")

    def __str__(self):
        return self.titulo


class AsyncTask(models.Model):
    class Status(models.TextChoices):
        PENDING = "PENDING"
        STARTED = "STARTED"
        SUCCESS = "SUCCESS"
        FAILURE = "FAILURE"

    id: int
    result = models.JSONField(null=True, blank=True, default=dict)
    endpoint = models.TextField(null=True, blank=True)
    input_data = models.JSONField(null=True, blank=True)
    celery_task_id = models.TextField(null=True, blank=True)
    correlation_id = models.TextField(null=True, blank=True)
    status = models.TextField(choices=Status.choices, default=Status.PENDING)
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    finished_at = models.DateTimeField(null=True, blank=True)
    created_by_id: int
    created_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.PROTECT)

    def failed(self):
        return self.status == self.Status.FAILURE

    def get_absolute_url(self):
        return reverse("staff:async-task", kwargs={"async_task_id": self.id})

    def as_dict(self):
        return {
            "id": self.pk,
            "status": self.status,
            "result": self.result,
            "created_at": self.created_at,
            "finished_at": self.finished_at,
        }
