import json
from datetime import datetime, timedelta
from decimal import Decimal as D
from enum import StrEnum
from functools import cached_property
from typing import TYPE_CHECKING, Any, ClassVar, Literal, TypedDict, cast

from django.contrib.auth.models import User
from django.contrib.postgres.fields import <PERSON>rrayField
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django.db.models import Count, J<PERSON><PERSON>ield, Prefetch, Q
from django_qserializer.serialization import SerializableManager, SerializableQuerySet
from simple_history.models import HistoricalRecords

from commons import dateutils, feature_flags, storage
from commons.dateutils import now, timedelta_to_milliseconds, to_default_tz, to_tz
from commons.django.indexes import UniqueIndex
from commons.django_model_utils import M
from commons.enum import ModeloOperacao, ModeloVenda
from commons.memoize import memoize
from commons.utils import hashint
from core.constants import (
    CLUSTER_CLASSES,
    FF_MARCACAO_ASSENTO_FRETAMENTO,
    TIPOS_ASSENTO_CHOICES,
    TIPOS_ASSENTO_ORDER_MAP,
)
from core.models_commons import Reputation
from core.models_company import Company, NotaFiscal, Onibus, PoltronaOnibus
from core.models_hibrido import AutorizacaoHibrido
from core.models_rota import (
    CAPACIDADE_MAP,
    FATOR_MAP,
    Checkpoint,
    Rota,
    RotaPrincipal,
    TrechoVendido,
    preco_tradicional,
)
from core.models_rotina import RotinaOnibus
from core.models_utils import ModelSimpleReprMixin
from core.service import checkpoint_svc

if TYPE_CHECKING:
    from django.db.models.manager import RelatedManager

    from pagamento_parceiro.models import AntecipacaoPagamento

GROUP_CHECKIN_STATUSES = [(c, c) for c in ["pending", "boarding", "going", "travel_done"]]
GROUP_CONFIRMING_STATUSES = [(c, c) for c in ["high", "medium", "very_low"]]
GROUP_CANCELED_REASONS = {
    # ROTAS REASON
    "NO_PEOPLE": {
        "nome": "Sem buseiros suficientes para realizar a viagem",
        "descricao": "infelizmente, sua viagem não atingiu o número mínimo de passageiros para ser realizada. É raro, mas acontece",
        "area": "risco",
    },
    "NO_COMPANY": {
        "nome": "Sem empresa para realizar viagem nas condições previstas (exceto consequência de problema Juridico)",
        "descricao": "rolou um imprevisto com a empresa parceira de fretamento. Seu caminho é nossa prioridade, então estamos avisando com antecedência",
    },
    "NO_COMPANY_LEGAL": {
        "nome": "Sem empresa para realizar viagem por consequência de problema Juridico",
        "descricao": "rolou um imprevisto com a empresa parceira de fretamento. Seu caminho é nossa prioridade, então estamos avisando com antecedência",
    },
    "LEGAL_PROBLEM": {
        "nome": "Suspensão da rota devido a um problema jurídico",
        "descricao": "tivemos problemas técnicos com o ônibus que realizaria sua viagem e não conseguimos substituí-lo a tempo",
    },
    "OFFERING_REDUCTION": {
        "nome": "Ajuste de oferta definitiva ou redução de frequência",
        "descricao": "por enquanto, essa rota vai sair do ar. Esperamos voltar em breve - com preços e opções melhores",
    },
    "STAFF": {
        "nome": (
            "Motivos internos não ligados a operação (teste, staff, criação errada de grupos, edição de modelo venda)"
        ),
        "descricao": "infelizmente, não vamos conseguir realizar sua viagem nessa data",
    },
    "EXTERNAL_FACTORS": {
        "nome": "Outros fatores externos não listados acima",
        "descricao": "essa rota vai sair do ar por um tempo. Esperamos voltar em breve - com preços e opções melhores",
    },
    # RISCO REASONS
    "COMPANY_PROBLEM": {
        "nome": "Parceiro teve um problema por culpa dele e não fará a viagem (SOMENTE USO DE RISCO)",
        "descricao": "tivemos problemas técnicos com o ônibus que realizaria sua viagem e não conseguimos substituí-lo a tempo",
    },
    "SUSPENDED_ROUTE": {
        "nome": "Rota suspensa por motivos externos (SOMENTE USO DE RISCO)",
        "descricao": "por enquanto, essa rota vai sair do ar. Esperamos voltar em breve - com preços e opções melhores",
    },
    "NATURAL_PHENOMENA_PROBLEM": {
        "nome": "Fenômenos Naturais",
        "descricao": "fenômenos que estão fora do nosso alcance impossibilitaram a realização da sua viagem",
    },
    "EDIT_LOCAL": {
        "nome": "Devido a alteração de local de embarque (SOMENTE USO DE RISCO)",
        "descricao": "não conseguiremos realizá-la nessa data",
    },
    "OTHER": {"nome": "Outro motivo (Precisa escrever com ajuda de alguém de comunicação)", "descricao": ""},
}


class ClosedReasons(StrEnum):
    PRICING = "Pricing"
    SEM_EMPRESA = "Sem empresa"
    FISCALIZACAO = "Fiscalização"
    SLOT_DESATENDIDO = "Slot desatendido"
    GRUPO_DESATENDIDO = "Grupo desatendido"
    OFERTA = "Garantir oferta nos demais trechos"
    SOLVER = "SOLVER RECOMENDOU O CANCELAMENTO DO GRUPO"
    FORA_DA_JANELA_DE_VENDA = "Grupo fora da janela de venda"
    CLEANUP_OLD_CLASSES = "Remanejamento - cleanup old classes"

    @classmethod
    def to_staff(cls, reason_type: Literal["grupo", "trecho_classe"] = "grupo") -> list[str]:
        if reason_type == "trecho_classe":
            trecho_classe_reasons = (cls.FISCALIZACAO, cls.OFERTA)
            return [cr.value for cr in trecho_classe_reasons]

        grupo_reasons = (
            cls.SLOT_DESATENDIDO,
            cls.GRUPO_DESATENDIDO,
            cls.PRICING,
            cls.FORA_DA_JANELA_DE_VENDA,
        )
        return [cr.value for cr in grupo_reasons]


class GrupoQuerySet(SerializableQuerySet):
    def withdriver(self, driver):
        qs = self.filter(Q(driver_one=driver) | Q(driver_two=driver)).order_by("datetime_ida")
        return qs

    def to_staff_serialize(self):
        return self.select_related(
            "rota",
            "rotina_onibus",
            "onibus__company",
            "reputation",
            "notafiscal",
            "notafiscal__company",
            "driver_one__profile",
            "driver_two__profile",
        ).prefetch_related(
            Prefetch(
                "travel_set__travelfeedback_set", queryset=M("travelfeedback").objects.all().order_by("-created_at")
            ),
            Prefetch("grupoclasse_set", queryset=GrupoClasse.objects.to_serialize()),
            Prefetch("company", queryset=Company.objects.to_serialize()),
            Prefetch(
                "trechoclasse_set",
                queryset=TrechoClasse.objects.select_related(
                    "closed_by",
                    "trecho_vendido__origem__cidade",
                    "trecho_vendido__destino__cidade",
                    "grupo_classe__closed_by",
                ).prefetch_related("price_manager__buckets"),
            ),
            Prefetch(
                "rota__trechos_vendidos",
                queryset=TrechoVendido.objects.select_related("origem__cidade", "destino__cidade"),
            ),
            Prefetch("rota__itinerario", queryset=Checkpoint.objects.select_related("local__cidade")),
            "onibus__classes",
            "trechoclasse_set__pedidoalteracaopreco_set",
        )

    def to_pay_serialize(self):
        return self.select_related("notafiscal", "notafiscal__company", "company")


class Grupo(ModelSimpleReprMixin, models.Model):
    """
    Representa uma perna de uma viagem, possívelmente com várias paradas
    e.g. Rio - Aparecida - Sâo Paulo.
        A ida é representada por um grupo, a volta por outro
    """

    ModeloVenda = ModeloVenda
    ModeloOperacao = ModeloOperacao

    class Status(models.TextChoices):
        PENDING = "pending"
        TRAVEL_CONFIRMED = "travel_confirmed"
        DONE = "done"
        CANCELED = "canceled"

    PAGO_POR_FRETE_MODELO_VENDA = (ModeloVenda.BUSER, ModeloVenda.HIBRIDO)
    FRETAMENTO_MODELO_VENDA = (ModeloVenda.BUSER, ModeloVenda.HIBRIDO)
    objects: SerializableManager = SerializableManager.from_queryset(GrupoQuerySet)()

    id: int
    if TYPE_CHECKING:
        trechoclasse_set: RelatedManager
        grupoclasse_set: RelatedManager
        travel_set: RelatedManager
        repassemarketplace_set: RelatedManager
        antecipacaopagamento_set = RelatedManager[AntecipacaoPagamento]()

    rota_id: int
    rota = models.ForeignKey(Rota, on_delete=models.CASCADE)

    # idealmente rotina_onibus não precisa existir e deve morrer algum dia
    rotina_onibus_id: int
    rotina_onibus = models.ForeignKey(RotinaOnibus, null=True, blank=True, on_delete=models.SET_NULL)
    onibus_id: int
    onibus = models.ForeignKey(Onibus, null=True, blank=True, on_delete=models.CASCADE)
    onibus_pos_bo = models.TextField(null=True, blank=True)
    company_id: int
    company = models.ForeignKey(Company, null=True, blank=True, on_delete=models.CASCADE)
    company_pos_bo = models.TextField(null=True, blank=True)
    tipo_pos_bo = models.TextField(null=True, blank=True)
    driver_one_id: int
    driver_one = models.ForeignKey(
        User, related_name="grupo_driver_one", null=True, blank=True, on_delete=models.CASCADE
    )
    driver_two_id: int
    driver_two = models.ForeignKey(
        User, related_name="grupo_driver_two", null=True, blank=True, on_delete=models.CASCADE
    )
    active_driver = models.IntegerField(null=True, blank=True)
    one_driver_exception = models.BooleanField(null=True, default=False)
    notafiscal_id: int
    notafiscal = models.ForeignKey(NotaFiscal, null=True, blank=True, on_delete=models.CASCADE)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    datetime_ida = models.DateTimeField()
    datetime_volta = models.DateTimeField(null=True, blank=True)  # todo: deprecated
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    canceled_at = models.DateTimeField(null=True)
    driver_updated_at = models.DateTimeField(auto_now=False, null=True, blank=True)
    status = models.CharField(max_length=32, default=Status.PENDING, choices=Status.choices)
    cancel_requested_at = models.DateTimeField(null=True)
    canceled_reason = models.CharField(max_length=100, null=True, blank=True)
    confirmed_at = models.DateTimeField(null=True)
    confirming_probability = models.CharField(max_length=32, default="very_low", choices=GROUP_CONFIRMING_STATUSES)
    valor_frete = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    nao_teve_frete = models.BooleanField(default=False)
    modelo_venda = models.CharField(max_length=15, choices=ModeloVenda.choices, default=ModeloVenda.BUSER)
    modelo_operacao = models.TextField(choices=ModeloOperacao.choices, default=ModeloOperacao.DEFAULT)
    valor_encomenda = models.DecimalField(max_digits=12, decimal_places=2, null=True)

    checkin_status = models.CharField(max_length=32, default="pending", choices=GROUP_CHECKIN_STATUSES)
    checkin_status_updated_at = models.DateTimeField(null=True, blank=True)
    checkpoint_idx = models.IntegerField(default=0)

    antt_preview_s3key = models.CharField(max_length=256, null=True, blank=True)
    antt_autorizacao = models.TextField(null=True, blank=True)  # todo: deprecated
    antt_number = models.CharField(null=True, blank=True, max_length=32)
    antt_placa = models.CharField(null=True, blank=True, max_length=32)
    antt_cnpj = models.CharField(null=True, blank=True, max_length=32)
    antt_grupo_volta = models.ForeignKey("self", null=True, blank=True, on_delete=models.CASCADE)

    artesp_preview = models.TextField(null=True, blank=True)
    artesp_number = models.CharField(max_length=256, null=True, blank=True)
    artesp_placa = models.CharField(null=True, blank=True, max_length=32)
    artesp_cnpj = models.CharField(null=True, blank=True, max_length=32)
    artesp_contrato = models.CharField(max_length=256, null=True, blank=True)
    artesp_empresa = models.CharField(max_length=256, null=True, blank=True)
    artesp_s3key = models.CharField(max_length=256, null=True, blank=True)

    driver_close_checkin_alert_sent = models.BooleanField(default=False)
    autorizacao_s3key = models.CharField(max_length=256, null=True, blank=True)
    datetime_checkin_open = models.DateTimeField(null=True, blank=True)
    datetime_checkin_close = models.DateTimeField(null=True, blank=True)
    datetime_travel_finished = models.DateTimeField(null=True, blank=True)
    problem_description = models.TextField(null=True, blank=True)
    observation = models.TextField(null=True, blank=True)
    ressarcido = models.BooleanField(default=False)
    ressarcimento_factor = models.DecimalField(max_digits=12, decimal_places=4, null=True, blank=True)
    ressarcimento_reason = models.TextField(null=True, blank=True)
    gerou_credito_cancelamento = models.BooleanField(null=True, blank=True)
    estornado_automaticamente = models.BooleanField(null=True, blank=True, default=False)
    reputation_id: int
    reputation = models.ForeignKey(Reputation, null=True, on_delete=models.CASCADE)
    checkpoints_datetime = models.TextField(null=True, blank=True)
    is_extra = models.BooleanField(default=False, null=True)
    motorista_descansado = JSONField(blank=True, null=True)
    contar_dia_parado = models.BooleanField(default=False, null=True)
    observacao_dia_parado = models.TextField(null=True, blank=True)
    percentual_repasse = models.DecimalField(max_digits=4, decimal_places=2, default=D(0))
    hidden_for_pax = models.BooleanField(default=False)
    area_pays_group = models.TextField(default="Comercial", null=True, blank=True)

    # Compatibilidade com o grupo rodoviária.
    rodoviaria_compativel = models.BooleanField(blank=True, null=True)

    autorizacao_hibrido_id: int
    # autorizacao usada para rodar no modelo hibrido
    autorizacao_hibrido = models.ForeignKey(AutorizacaoHibrido, blank=True, null=True, on_delete=models.SET_NULL)

    _duracao_total = models.DurationField(db_column="duracao_total", null=True, blank=True)
    # Lista de DuracaoDinamica.
    duracoes = ArrayField(models.IntegerField(), null=True)

    # taxa de serviço no checkout
    percentual_taxa_servico = models.DecimalField(max_digits=4, decimal_places=2, default=D(0), null=True)

    if TYPE_CHECKING:
        grupo_set = RelatedManager["Grupo"]()

    @property
    def duracao_total(self):
        return self._duracao_total or self.rota.duracao_total

    @duracao_total.setter
    def duracao_total(self, value):
        self._duracao_total = value

    class Meta:
        indexes = [
            models.Index(fields=["datetime_ida"]),
            models.Index(fields=["-datetime_ida"]),
            models.Index(fields=["updated_on"]),
            models.Index(fields=["canceled_at"], name="core_grupo_canceled_at_idx"),
        ]

    def save(self, *args, **kwargs):
        if not self.reputation_id:
            self.reputation = Reputation.get_or_create()
        super(Grupo, self).save(*args, **kwargs)

    @cached_property
    def grupo_cenario_safe(self):
        try:
            return self.grupocenario
        except GrupoCenario.DoesNotExist:
            return None

    @property
    def pode_estornar(self):
        if not self.is_paid():
            return False, "Grupo não está pago"
        if not self.last_op.value == self.valor_frete:
            return False, "Valor frete é diferente do valor pago ao operador"
        return True, ""

    def motoristas(self):
        if not self.driver_one_id and not self.driver_two_id:
            return "A DEFINIR"

        nomes = []
        if self.driver_one_id:
            nomes.append(self.driver_one.get_full_name())

        if self.driver_two_id:
            nomes.append(self.driver_two.get_full_name())

        return ", ".join(nomes)

    @property
    def billing_status(self):
        if self.modelo_venda == Grupo.ModeloVenda.BUSER and not self.contar_dia_parado:
            return self.notafiscal.status if self.notafiscal else "waiting_nota"
        if self.is_paid():
            return "paid"
        return "waiting_payment"

    @property
    def status_ptbr(self):
        status_dict = {
            "pending": "à confirmar",
            "canceled": "cancelado",
            "travel_confirmed": "confirmado",
            "done": "concluído",
        }

        return status_dict.get(self.status, self.status)

    @property
    def last_op(self):
        PAID = ["PAGAMENTO_VIAGEM", "PAGAMENTO_DIA_PARADO"]
        UNPAID = ["PAGAMENTO_VIAGEM_CANCELADO", "PAGAMENTO_DIA_PARADO_CANCELADO"]
        AccOps = M("CompanyAccountingOperation", appname="accounting")
        return (
            AccOps.objects.filter(grupo_id=self.id, account__company_id=self.company_id, source__in=PAID + UNPAID)
            .order_by("created_at")
            .last()
        )

    def is_paid(self):
        PAID = ["PAGAMENTO_VIAGEM", "PAGAMENTO_DIA_PARADO"]
        return self.last_op and self.last_op.source in PAID

    def checkin_ok(self):
        return self.checkin_status in {"closed", "travel_done"}

    def horario_embarque_driver(self):
        return self.datetime_ida - timedelta(minutes=45)  # TODO: verificar se isso ta sendo mostrado no timezone certo

    def feedback_info(self):
        if self.status == "done" and self.reputation is not None:
            return self.reputation.to_dict_json()
        return {}

    def feedbacks(self):
        try:
            return self._feedbacks
        except AttributeError:
            travels = self.travel_set.all()
            feedbacks = []
            for travel in travels:
                for feedback in travel.travelfeedback_set.all():
                    feedbacks.append(feedback.to_dict_json(anonimo=False))
            return feedbacks

    def nf_url(self):
        nota = self.notafiscal

        if nota and nota.s3key:
            return storage.storage_url(nota, "s3key")
        if nota and nota.xml_s3key:
            return storage.storage_url(nota, "xml_s3key")

        return None

    def nf_valor(self):
        nota = self.notafiscal

        return nota.valor if nota and nota.valor else None

    def autorizacao_url(self):
        return storage.storage_url(self, "autorizacao_s3key")

    @property
    def vagas(self):
        grupo_classes = self.grupoclasse_set.all()
        return sum(gc.vagas for gc in grupo_classes)

    def count_pessoas(self):
        trecho_classes = self.trechoclasse_set.all()
        return sum(tc.pessoas for tc in trecho_classes)

    def count_checkin(self):
        return (
            M("Passageiro")
            .objects.filter(travel__grupo=self, travel__status="pending", removed=False, checkin=True)
            .count()
        )

    def count_capacidade_onibus(self):
        grupo_classes = self.grupoclasse_set.all()
        return sum(gc.capacidade for gc in grupo_classes)

    def get_descontos(self):
        return M("CompanyAccountingOperation", appname="accounting").objects.filter(
            grupo=self, source__in=["DESCONTO_PAGAMENTO_VIAGEM", "DESCONTO_PAGAMENTO_VIAGEM_CANCELADO"]
        )

    def get_passageiros(self):
        return M("Passageiro").objects.filter(travel__grupo=self, travel__status="pending", removed=False)

    @property
    def is_buser(self):
        return self.modelo_venda == self.ModeloVenda.BUSER

    @property
    def is_marketplace(self):
        return self.modelo_venda == self.ModeloVenda.MARKETPLACE

    @property
    def is_hibrido(self):
        return self.modelo_venda == self.ModeloVenda.HIBRIDO

    @property
    def cancellation_deadline_hours(self):
        return 3 if self.is_marketplace or self.is_hibrido else 1

    @property
    def tem_pedagio(self):
        return hasattr(self, "pedagio")

    def get_gmv_atual(self) -> D:
        gmv = D(0)
        for travel in self.travel_set.all():
            if travel.status == "canceled" or travel.max_split_value is None:
                continue
            gmv += travel.gmv
        return gmv

    def add_pessoas_to_trechos_vendidos(self, map_trechoclasse, trechos_vendidos):
        if map_trechoclasse:
            for tv in trechos_vendidos:
                if tv["id"] in map_trechoclasse:
                    tv["pessoas"] = sum([tc["pessoas"] for tc in map_trechoclasse[tv["id"]].values()])

    def get_trechos_classes_map(self, with_buckets_availability=False):
        tcmap = {}
        trechos_classe = self.trechoclasse_set.all()
        checkin_count = {
            tc["id"]: tc["count"]
            for tc in trechos_classe.annotate(
                count=Count(
                    "travel__passageiro",
                    filter=Q(travel__passageiro__checkin=True, travel__passageiro__removed=False),
                )
            ).values("id", "count")
        }

        for tc in trechos_classe:
            cmap = tcmap.setdefault(tc.trecho_vendido_id, {})
            cmap[tc.grupo_classe_id] = tc.to_dict_json(staff=True, with_buckets_availability=with_buckets_availability)

            status_base = "APROVADO"
            if len(tc.pedidoalteracaopreco_set.all()):
                pedido = tc.pedidoalteracaopreco_set.latest("id")
                cmap[tc.grupo_classe_id]["pedidos_alteracao_preco"] = pedido.to_dict_json()
                if pedido.status == "PENDENTE":
                    status_base = "PENDENTE"

            cmap[tc.grupo_classe_id]["status"] = status_base
            cmap[tc.grupo_classe_id]["count_checkin"] = checkin_count.get(tc.id, 0)
        return tcmap

    def get_drivers(self):
        drivers = [self.driver_one, self.driver_two]
        drivers = [d for d in drivers if d]
        return drivers

    def need_2_drivers(self):
        if self.one_driver_exception:
            return False

        return self.rota.need_2_drivers(duracao_total=self.duracao_total)

    def to_dict_json_mensal(self):
        d = {
            "id": self.id,
            "observation": self.observation,
            "problem_description": self.problem_description,
        }
        try:
            d["autorizacao_grupo"] = self.autorizacao_grupo.to_dict_json()
        except AutorizacaoGrupo.DoesNotExist:
            d["autorizacao_grupo"] = ""

        return d

    def to_dict_json(self) -> dict:
        from core.serializers import serializer_grupo

        return serializer_grupo.to_dict_json(self)

    def eh_compativel_com_beneficio_viagem_gratis(self):
        return self.modelo_venda in (self.ModeloVenda.BUSER, self.ModeloVenda.HIBRIDO)

    def __str__(self):
        datetime_chegada = to_default_tz(self.datetime_ida + self.duracao_total)
        datetime_ida = to_default_tz(self.datetime_ida)
        dias_depois_da_ida = datetime_chegada.day - datetime_ida.day
        horario_chegada = datetime_chegada.strftime("%H:%M")
        return "%s @ %s - %s" % (
            self.rota,
            datetime_ida.strftime("%Y-%m-%d %H:%M"),
            horario_chegada if dias_depois_da_ida == 0 else "{0} (+{1})".format(horario_chegada, dias_depois_da_ida),
        )


_grupoclasse_serializer_args: dict[str, Any] = {"select_related": ["closed_by"]}


class GrupoCenario(models.Model):
    id: int
    grupo_id: int
    grupo = models.OneToOneField(Grupo, on_delete=models.CASCADE)

    eixo = models.TextField(null=False, blank=False)
    sentido = models.TextField(null=False, blank=False)
    distancia_km = models.PositiveSmallIntegerField(null=False)
    tipo_carro = models.TextField(null=False, blank=False)
    capacidade_total = models.PositiveSmallIntegerField(null=False)
    load_factor_curve = ArrayField(models.FloatField(), size=120, null=False, blank=False)
    dia_semana = models.PositiveSmallIntegerField(null=False)
    turno = models.TextField(null=False, blank=False)
    feriado = models.BooleanField(null=False)

    current_load_factor = models.FloatField(null=False)

    @cached_property
    def load_factors_dict(self):
        return dict(enumerate(self.load_factor_curve))

    @cached_property
    def ask(self) -> float:
        return self.distancia_km * self.capacidade_total


class GrupoClasseManager(SerializableManager):
    def __init__(self):
        super().__init__(**_grupoclasse_serializer_args)

    def get_queryset(self):
        qs = super().get_queryset()
        return qs.exclude(closed_reason=ClosedReasons.CLEANUP_OLD_CLASSES)


class GrupoClasse(models.Model):
    objects: GrupoClasseManager = GrupoClasseManager()
    objects_all: SerializableManager = SerializableManager(**_grupoclasse_serializer_args)

    id: int
    grupo_id: int
    grupo = models.ForeignKey(Grupo, on_delete=models.CASCADE)
    capacidade = models.IntegerField()
    seat_reservation = models.BooleanField(default=False)  # TODO: Deprecated (reservaassento)
    tipo_assento = models.CharField(max_length=64, blank=True, null=True, choices=TIPOS_ASSENTO_CHOICES)
    layout = models.TextField(default="[]")  # TODO: Deprecated (reservaassento)
    closed = models.BooleanField(default=False)
    closed_reason = models.TextField(null=True, blank=True)
    closed_by_id: int
    closed_by = models.ForeignKey(User, related_name="closed_by", null=True, blank=True, on_delete=models.CASCADE)
    closed_at = models.DateTimeField(null=True, blank=True)
    pessoas = models.IntegerField(default=0, null=True)

    @classmethod
    def create(
        cls,
        grupo,
        capacidade=None,
        tipo_assento=None,
        closed=False,
        closed_by_id=None,
        closed_reason=None,
        closed_at=None,
        save=True,
    ):
        tipo_assento = tipo_assento if tipo_assento in FATOR_MAP else "executivo"
        capacidade = capacidade or CAPACIDADE_MAP[tipo_assento]
        grupo_classe = cls(
            grupo=grupo,
            tipo_assento=tipo_assento,
            capacidade=capacidade,
            closed=closed,
            closed_by_id=closed_by_id,
            closed_reason=closed_reason,
            closed_at=closed_at,
        )
        if save:
            grupo_classe.save()
        return grupo_classe

    @property
    def vagas(self):
        return self.capacidade - self.pessoas

    def to_dict_json(self, *, staff=False, closed_details=True):
        d = {
            "id": self.id,
            "grupo_id": self.grupo_id,
            "vagas": self.vagas,
            "tipo_assento": self.tipo_assento,
            "closed": self.closed,
        }
        if closed_details:
            d.update(
                {
                    # TODO: apagar o closed_by que guarda o nome, usar o ID porque ID não é editável
                    # Eu posso colocar o nome "Vitinho", fazer alguma merda no sistema, e depois voltar
                    # o meu cadastro para o meu nome. Nos logs vai parecer que foi o Vitinho que fez a :poop:
                    "closed_by": self.closed_by and self.closed_by.get_full_name(),
                    "closed_by_id": self.closed_by_id,
                    "closed_reason": self.closed_reason,
                }
            )
        if staff:
            d["max_capacity"] = self.capacidade
            d["pessoas"] = self.pessoas
        return d

    @property
    def cluster(self) -> str:
        return CLUSTER_CLASSES[self.tipo_assento]

    @property
    def order_assento(self) -> int:
        return TIPOS_ASSENTO_ORDER_MAP[self.tipo_assento]


class PriceManager(models.Model):
    if TYPE_CHECKING:
        buckets: RelatedManager

    id: int
    min_pessoas = models.IntegerField(null=True, blank=True)
    max_pessoas = models.IntegerField(null=True, blank=True)
    value = models.DecimalField(max_digits=12, decimal_places=2)
    ref_value = models.DecimalField(max_digits=12, decimal_places=2)
    promo_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    expiration_days = models.IntegerField(null=True, blank=True)
    percentual_taxa_servico = models.DecimalField(max_digits=4, decimal_places=2, default=D(0), null=True)
    max_y = models.PositiveSmallIntegerField(default=1, null=True, blank=True)
    current_y = models.PositiveSmallIntegerField(default=0, null=True, blank=True)
    locked_at = models.DateTimeField(null=True, blank=True)
    locked_until = models.DateTimeField(null=True, blank=True)

    @property
    def is_locked(self) -> bool:
        return self.locked_until is not None and self.locked_until >= now()


class PriceBucket(models.Model):
    id: int
    idx = models.IntegerField()
    idy = models.PositiveSmallIntegerField(default=0, null=True, blank=True)
    tamanho = models.IntegerField()
    value = models.DecimalField(max_digits=12, decimal_places=2)
    ref_value = models.DecimalField(max_digits=12, decimal_places=2)
    expiration_days = models.IntegerField(null=True)
    price_manager_id: int
    price_manager = models.ForeignKey(
        PriceManager, on_delete=models.CASCADE, null=True, blank=True, related_name="buckets"
    )
    deleted = models.BooleanField(default=False)

    class Meta:
        ordering = ("idx", "idy")
        indexes = [
            UniqueIndex(fields=("price_manager_id", "idx", "idy")),
        ]

    def to_dict_json_old(self):
        dbucket = {
            "max_split_value": str(self.value),
            "tamanho": self.tamanho,
            "idy": 0 if self.idy is None else self.idy,
        }
        if self.expiration_days is not None and self.expiration_days >= 0:
            dbucket["expiration_days"] = self.expiration_days
        return dbucket


class PriceLogger(models.Model):
    class Reason(models.TextChoices):
        OVERFLOW = "Quantidade de pessoas superou o máximo"
        UNDERFLOW = "Quantidade de pessoas ficou abaixo do mínimo"
        EXPIRATION = "Viagem mais próxima do que expiration_days"
        UPDATE = "Ajuste de preço pelo administrador"

    class Type(models.TextChoices):
        CREATE_PRICE_BUCKET = "create_price_bucket"
        UPDATE_PRICE_BUCKET = "update_price_bucket"
        DELETE_PRICE_BUCKET = "delete_price_bucket"
        CREATE_PRICE_MANAGER = "create_price_manager"
        UPDATE_PRICE_MANAGER = "update_price_manager"

    reason = models.CharField(max_length=256, null=True, blank=True, choices=Reason.choices)
    type = models.CharField("tipo de log", max_length=64, choices=Type.choices)
    # 'create_price_bucket', 'update_price_bucket', 'delete_price_bucket'
    # 'create_price_manager', 'update_price_manager'

    id: int
    idx = models.IntegerField(null=True, blank=True)
    min_pessoas = models.IntegerField(null=True, blank=True)
    max_pessoas = models.IntegerField(null=True, blank=True)
    tamanho = models.IntegerField(null=True, blank=True)
    value = models.DecimalField(max_digits=12, decimal_places=2)
    promo_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    percentual_taxa_servico = models.DecimalField(max_digits=4, decimal_places=2, default=D(0), null=True)
    ref_value = models.DecimalField(max_digits=12, decimal_places=2)
    expiration_days = models.IntegerField(null=True, blank=True)
    price_manager_id: int
    price_manager = models.ForeignKey(PriceManager, on_delete=models.DO_NOTHING, null=True, blank=True, db_index=False)
    price_bucket_id: int
    price_bucket = models.ForeignKey(PriceBucket, on_delete=models.DO_NOTHING, null=True, blank=True, db_index=False)
    deleted = models.BooleanField(default=False, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    @classmethod
    def from_price_manager(cls, price_manager: PriceManager, op_type: str, reason: Reason):
        return cls(
            type=op_type,
            reason=reason,
            min_pessoas=price_manager.min_pessoas,
            max_pessoas=price_manager.max_pessoas,
            value=price_manager.value,
            ref_value=price_manager.ref_value,
            expiration_days=price_manager.expiration_days,
            promo_value=price_manager.promo_value,
            price_manager=price_manager,
        )

    @classmethod
    def from_price_bucket(cls, price_bucket: PriceBucket, op_type: str, reason: Reason):
        return cls(
            type=op_type,
            reason=reason,
            idx=price_bucket.idx,
            tamanho=price_bucket.tamanho,
            deleted=price_bucket.deleted,
            value=price_bucket.value,
            ref_value=price_bucket.ref_value,
            expiration_days=price_bucket.expiration_days,
            price_bucket=price_bucket,
            price_manager=price_bucket.price_manager,
        )


class ConfirmacaoInteligenteExecutionLogger(models.Model):
    id: int
    eixo = models.TextField()
    params = models.JSONField()
    input = models.JSONField()
    output = models.JSONField(blank=True, null=True)
    strategy = models.TextField(default="SOLUTION")
    error = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    versao_solver = models.TextField(null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["-created_at"]),
        ]


class ConfirmacaoInteligenteLogger(models.Model):
    id: int
    eixo = models.TextField(null=True, blank=True)
    grupo = models.ForeignKey(Grupo, on_delete=models.DO_NOTHING)
    rotina_onibus = models.ForeignKey(RotinaOnibus, null=True, blank=True, on_delete=models.DO_NOTHING)
    grupo_confirmado = models.BooleanField()
    pessoas_trechoclasse = models.JSONField(blank=True, null=True)
    pessoas_total = models.PositiveSmallIntegerField(
        null=False, blank=False, validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    pessoas_remanejadas = models.PositiveSmallIntegerField(
        null=False, blank=False, validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    pessoas_adicionadas = models.PositiveSmallIntegerField(
        null=False, blank=False, validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    pessoas_canceladas = models.PositiveSmallIntegerField(
        null=False, blank=False, validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    dummies_total = models.PositiveSmallIntegerField(
        null=False, blank=False, validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    dummies_remanejados = models.PositiveSmallIntegerField(
        null=False, blank=False, validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    dummies_adicionados = models.PositiveSmallIntegerField(
        null=False, blank=False, validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    dummies_cancelados = models.PositiveSmallIntegerField(
        null=False, blank=False, validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    gmv_real = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    gmv_previsto = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    forecast_trechoclasse = models.JSONField(blank=True, null=True)
    solver_threshold = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    valor_frete = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    additional_data = models.JSONField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    versao_solver = models.TextField(null=True, blank=True)
    solver_strategy = models.TextField(max_length=32)


class TrechoClasseExtra(TypedDict):
    mkp_servico: str
    mkp_extra: dict[str, Any]
    mkp_ota_config_id: int
    mkp_last_synced_at: str
    mkp_stopovers: list[dict] | None


_trechoclasse_serializer_args: dict[str, Any] = {
    "select_related": [
        "grupo__rota",
        "grupo__company",
        "grupo__onibus",
        "grupo__driver_one",
        "grupo__driver_two",
        "grupo_classe__grupo__rota",
    ],
    "prefetch_related": [
        models.Prefetch("trecho_vendido", queryset=TrechoVendido.objects.to_serialize()),
        "grupo_classe__trechoclasse_set",
        models.Prefetch(
            "grupo__rota__itinerario",
            queryset=Checkpoint.objects.to_serialize(),
        ),
        "closed_by",
        "pedidoalteracaopreco_set",
    ],
}


class TrechoClasseManager(SerializableManager):
    def __init__(self):
        super().__init__(**_trechoclasse_serializer_args)

    def get_queryset(self):
        qs = super().get_queryset()
        return qs.exclude(closed_reason=ClosedReasons.CLEANUP_OLD_CLASSES)


class TrechoClasse(models.Model):
    objects: TrechoClasseManager = TrechoClasseManager()
    objects_all: SerializableManager = SerializableManager(**_trechoclasse_serializer_args)

    id: int
    grupo_id: int
    grupo = models.ForeignKey(Grupo, on_delete=models.CASCADE)
    grupo_classe_id: int
    grupo_classe = models.ForeignKey(GrupoClasse, on_delete=models.CASCADE)
    trecho_vendido_id: int
    trecho_vendido = models.ForeignKey(TrechoVendido, on_delete=models.CASCADE)
    datetime_ida = models.DateTimeField(null=True)
    duracao_ida = models.DurationField(null=True)
    distancia_km = models.IntegerField(null=True)
    max_split_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    ref_split_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    """
    O ref_split_value_snapshot serve apenas para congelar o valor do ref_split_value quando o grupo vai para done.
    """
    ref_split_value_snapshot = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    split_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    pessoas = models.IntegerField(default=0)
    vagas = models.IntegerField(null=True)
    checkin_status = models.CharField(max_length=32, default="pending", choices=GROUP_CHECKIN_STATUSES)
    preco_rodoviaria = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)  # todo: deprecated
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    buckets = models.JSONField(blank=True, null=True)  # deprecated: usar get_active_buckets ou PriceBucket diretamente
    closed = models.BooleanField(default=False)
    closed_reason = models.TextField(null=True, blank=True)
    closed_by_id: int
    closed_by = models.ForeignKey(
        User, related_name="trechos_fechados", null=True, blank=True, on_delete=models.CASCADE
    )
    closed_at = models.DateTimeField(null=True, blank=True)
    price_manager_id: int
    price_manager = models.OneToOneField(PriceManager, models.SET_NULL, null=True, blank=True)
    extra = models.JSONField(default=dict)
    cenario_id: int | None
    cenario = models.ForeignKey("pricing.CenarioNew", models.SET_NULL, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=["trecho_vendido", "datetime_ida"]),
            models.Index(fields=["-datetime_ida"]),
        ]

    @classmethod
    def create(
        cls,
        trecho_vendido,
        grupo_classe,
        *,
        max_split_value=None,
        ref_split_value=None,
        price_manager=None,
        save=True,
        closed=False,
        closed_by_id=None,
        closed_reason=None,
        closed_at=None,
        distancia_km=None,
    ):
        from pricing.pricing_svc import calc_max_split_by_dist_v2

        grupo = grupo_classe.grupo
        if save and grupo.rota_id != trecho_vendido.rota_id:
            raise Exception("GrupoClasse e TrechoVendido de rotas diferentes.")
        distancia_km = distancia_km or trecho_vendido.distancia_total()
        cidade_origem = trecho_vendido.origem.cidade_id
        cidade_destino = trecho_vendido.destino.cidade_id

        if max_split_value is None:
            if grupo.modelo_venda == Grupo.ModeloVenda.MARKETPLACE:
                max_split_value = trecho_vendido.preco_rodoviaria
            else:
                max_split_value = calc_max_split_by_dist_v2(
                    distancia_km, grupo_classe.tipo_assento, cidade_origem, cidade_destino
                )
        if ref_split_value is None:
            ref_split_value = max_split_value
        trecho_classe = cls(
            grupo=grupo,
            grupo_classe=grupo_classe,
            trecho_vendido=trecho_vendido,
            max_split_value=max_split_value,
            ref_split_value=ref_split_value,
            price_manager=price_manager,
            vagas=grupo_classe.capacidade,
            closed=closed,
            closed_by_id=closed_by_id,
            closed_reason=closed_reason,
            closed_at=closed_at,
            distancia_km=distancia_km,
        )
        if save:
            trecho_classe.save()
        return trecho_classe

    @classmethod
    @memoize(timeout=60)
    def embarque_iniciado(cls, trecho_classe):
        return (
            M("Passageiro")
            .objects.filter(
                checkin=True,
                travel__grupo=trecho_classe.grupo,
                travel__trecho_classe__trecho_vendido__origem=trecho_classe.trecho_vendido.origem,
            )
            .exists()
        )

    def get_embarque_status(self):
        # 5 minutos depois da ida com embarque encerrado
        if self.datetime_ida < dateutils.now() - timedelta(minutes=5) and self.checkin_status in [
            "going",
            "travel_done",
        ]:
            return "EMBARQUE_ENCERRADO"
        # embarque iniciado
        if TrechoClasse.embarque_iniciado(self):
            # 5 minutos antes da ida
            if self.datetime_ida < dateutils.now() + timedelta(minutes=5):
                return "ULTIMA_CHAMADA"
            else:
                return "EMBARQUE_INICIADO"
        # 15 minutos antes da ida
        if self.datetime_ida < dateutils.now() + timedelta(minutes=15):
            return "EMBARQUE_PROXIMO"
        # 30 minutos antes da ida
        if self.datetime_ida < dateutils.now() + timedelta(minutes=30):
            return "VIAGEM_PROXIMA"

        return None

    def get_checkin_status(self, checkin_status: str = "checkin_open"):
        checkpoints_datetime = json.loads(self.grupo.checkpoints_datetime or "{}")
        trecho_index = str(checkpoint_svc.get_checkpoint_index(self.grupo.rota_id, self.trecho_vendido.origem_id))

        checkpoint_trecho = checkpoints_datetime.get(trecho_index, {})
        return checkpoint_trecho.get(checkin_status)

    def horario_embarque(self):
        return to_tz(self.datetime_ida - timedelta(minutes=30), self.origem_timezone())

    def origem_timezone(self):
        return self.trecho_vendido.origem.cidade.timezone

    def get_horario_partida(self):
        return to_tz(self.datetime_ida, self.origem_timezone())

    def get_horario_chegada(self):
        destino_tz = self.trecho_vendido.destino.cidade.timezone
        return (
            to_tz(self.datetime_ida + self.duracao_ida, destino_tz) if self.datetime_ida and self.duracao_ida else None
        )

    def get_horario_chegada_iso(self):
        horario_chegada = self.get_horario_chegada()
        return horario_chegada and horario_chegada.isoformat()

    def _bucket_ativo(self, real_time=False):
        result = getattr(self, "_bucket_ativo_cached", None)
        if result:
            return result

        from core.service import preco_svc

        self._bucket_ativo_cached = preco_svc.bucket_ativo(self, real_time=real_time)
        return self._bucket_ativo_cached

    def get_tipo_assento(self) -> str:
        return self.grupo_classe.tipo_assento

    @property
    def days_to_travel(self) -> int:
        datetime_ida = self.datetime_ida or self.grupo.datetime_ida
        return (datetime_ida.date() - dateutils.today()).days

    @property
    def current_load_factor(self) -> float:
        try:
            capacidade = self.pessoas + self.vagas
            return self.pessoas / capacidade
        except Exception:
            return 0.0

    @property
    def sentido(self) -> str:
        tv = self.trecho_vendido
        origem = tv.origem.cidade.slug or tv.origem.cidade.name
        destino = tv.destino.cidade.slug or tv.destino.cidade.name
        return f"{origem}>{destino}"

    @property
    def max_promo_value_bucket(self):
        return self._bucket_ativo(real_time=True).promo_value

    @property
    def ref_split_value_bucket(self) -> D:
        return self._bucket_ativo(real_time=True).ref_value

    @property
    def max_split_value_bucket(self) -> D:
        return self._bucket_ativo(real_time=True).value

    # isso aqui é pra se algum dia a gente quiser retornar a taxa de serviço na busca
    @property
    def percentual_taxa_servico(self) -> D:
        return self._bucket_ativo(real_time=True).percentual_taxa_servico

    @property
    def vagas_bucket(self):
        return self._bucket_ativo(real_time=True).vagas

    @property
    def typed_extra(self) -> TrechoClasseExtra:
        return TrechoClasseExtra(**self.extra)

    @property
    def extra_mkp_last_synced_at(self) -> None | datetime:
        if not (last_synced_at := self.extra.get("mkp_last_synced_at")):
            return
        return datetime.fromisoformat(last_synced_at)

    def to_dict_json_base(self):
        # depende apenas de:
        # * grupo
        # * grupo_classe
        # * trecho_vendido
        # * trecho_vendido.origem.cidade
        # * trecho_vendido.destino.cidade

        vagas = self.grupo_classe.vagas

        # Evita erro caso algum trecho classe não tenha tido as vagas calculadas
        if self.vagas is not None:
            vagas = self.vagas

        tc_json = {
            "id": self.id,
            "grupo_classe_id": self.grupo_classe_id,
            "grupo_classe_closed": self.grupo_classe.closed,
            "trecho_vendido_id": self.trecho_vendido_id,
            "tipo_assento": self.grupo_classe.tipo_assento,
            "datetime_ida": to_tz(self.datetime_ida, self.trecho_vendido.origem.cidade.timezone).isoformat(),
            "max_split_value": self.max_split_value,
            "ref_split_value": self.ref_split_value,
            "confirming_probability": self.grupo.confirming_probability,
            "status": self.grupo.status,
            "split_value": self.split_value,
            "preco_rodoviaria": preco_tradicional(self.trecho_vendido.preco_rodoviaria, self.grupo_classe.tipo_assento),
            "duracao_ida": timedelta_to_milliseconds(self.duracao_ida),
            "chegada_ida": self.get_horario_chegada_iso(),
            "modelo_venda": self.grupo.modelo_venda,
            "modelo_operacao": self.grupo.modelo_operacao,
            "company_id": self.grupo.company_id,
            "rotina_onibus_id": self.grupo.rotina_onibus_id,
            "vagas": vagas,
            "origem_id": self.trecho_vendido.origem_id,
            "destino_id": self.trecho_vendido.destino_id,
            "cancellation_deadline_hours": self.grupo.cancellation_deadline_hours,
            "closed": self.closed,
            "closed_reason": self.closed_reason,
            "closed_by": self.closed_by and self.closed_by.get_full_name(),
            "closed_by_user_id": self.closed_by and self.closed_by.id,
            "closed_at": self.closed_at and to_default_tz(self.closed_at),
            "extra_mkp_last_synced_at": self.typed_extra.get("mkp_last_synced_at"),
            "extra_mkp_servico": self.typed_extra.get("mkp_servico"),
            "extra_mkp_extra": self.typed_extra.get("mkp_extra"),
            "extra_mkp_stopovers": self.typed_extra.get("mkp_stopovers"),
            "extra_mkp_ota_config_id": self.typed_extra.get("mkp_ota_config_id"),
        }
        # TODO: estruturar em uma tabela
        if self.grupo.modelo_operacao == ModeloOperacao.POLTRONAS_ANTECIPADAS:
            tc_json["operacao_configs"] = {"modelo_atendimento": "buser"}
        return tc_json

    def get_price_manager(self) -> PriceManager:
        if not self.price_manager_id:
            return PriceManager(value=self.max_split_value, ref_value=self.ref_split_value)

        return cast(PriceManager, self.price_manager)

    def to_dict_json(
        self,
        staff=False,
        companyinfo=False,
        withitinerario=False,
        with_rateio=False,
        with_buckets_availability=False,
        with_marcacao_assento=False,
    ):
        from core.service.preco_svc import get_active_buckets

        d = self.grupo_classe.to_dict_json()
        d.update(self.trecho_vendido.to_dict_json(with_rateio=with_rateio))
        d.update(self.to_dict_json_base())
        if staff:
            buckets = get_active_buckets(self, add_available_key=with_buckets_availability)
            price_manager = self.get_price_manager()
            all_buckets = price_manager.buckets.all() if price_manager.pk else PriceBucket.objects.none()
            d.update(
                {
                    "pessoas": self.pessoas,
                    "buckets": buckets,
                    "price_manager": {
                        "current_y": price_manager.current_y,
                        "max_y": price_manager.max_y,
                        "all_buckets": [b.to_dict_json_old() for b in all_buckets if not b.deleted],
                    },
                    "hashed_trecho_classe_id": hashint(self.id),
                }
            )
        if companyinfo:
            d.update(
                {
                    "company_name": self.grupo.company.name if self.grupo.company else None,
                    "onibus_placa": self.grupo.onibus.placa if self.grupo.onibus else None,
                    "driver_name": self.grupo.motoristas(),
                }
            )
        if withitinerario:
            d["itinerario"] = [c.to_dict_json() for c in self.grupo.rota.get_itinerario()]
        if with_marcacao_assento:
            d["has_marcacao_assento"] = self.get_has_marcacao_assento()
        return d

    def get_has_marcacao_assento(self) -> bool:
        onibus = self.grupo.onibus if self.grupo else None
        is_onibus_enabled = (
            feature_flags.is_user_enabled(FF_MARCACAO_ASSENTO_FRETAMENTO, user_id=onibus.id) if onibus else False
        )
        return all([onibus, PoltronaOnibus.objects.filter(onibus=onibus).exists()]) if is_onibus_enabled else False


def get_pedido_alteracao_onibus_data_default(
    is_emergency=None,
    is_downgrade=None,
    is_overbooking=None,
    is_older_bus=None,
    is_out_of_time=None,
    frete_atual=None,
    frete_novo=None,
    frete_mudou=None,
    hour_conflict_reason=None,
    reason_frete_novo_error=None,
):
    return {
        "is_emergency": is_emergency,
        "is_downgrade": is_downgrade,
        "is_overbooking": is_overbooking,
        "is_older_bus": is_older_bus,
        "is_out_of_time": is_out_of_time,
        "hour_conflict_reason": hour_conflict_reason,
        "frete_atual": frete_atual,
        "frete_novo": frete_novo,
        "frete_mudou": frete_mudou,
        "reason_frete_novo_error": reason_frete_novo_error,
    }


class PedidoAlteracaoOnibus(models.Model):
    class Meta:
        get_latest_by = "created_at"

    class Status(models.TextChoices):
        APROVADO = "APROVADO"
        REJEITADO = "REJEITADO"
        PENDENTE = "PENDENTE"
        SOBRESCRITO = "SOBRESCRITO"

    class ReasonTypes(models.TextChoices):
        FALHA_MECANICA = "Falha mecânica"
        MANUTENCAO = "Manutenção"
        APREENSAO = "Apreensão"
        PLOTAGEM = "Plotagem"
        ESCALA_ERRADA = "Escala errada"
        PROBLEMA_NA_DOCUMENTACAO = "Problema na documentação"
        OUTRO = "Outro"

    objects: SerializableManager = SerializableManager()
    id: int
    grupo_id: int
    grupo = models.ForeignKey(Grupo, on_delete=models.CASCADE, related_name="pedidos_alteracao_onibus")
    status = models.TextField(choices=Status.choices, default=Status.PENDENTE)
    reason_type = models.TextField(choices=ReasonTypes.choices)
    reason_details = models.TextField(null=False)
    previous_bus_id: int
    previous_bus = models.ForeignKey(
        Onibus, null=True, on_delete=models.CASCADE, related_name="pedidos_alteracao_onibus_anterior"
    )
    next_bus_id: int
    next_bus = models.ForeignKey(
        Onibus, null=True, on_delete=models.CASCADE, related_name="pedidos_alteracao_onibus_proximo"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    created_by_id: int
    created_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name="pedidos_alteracao_onibus")
    updated_at = models.DateTimeField(auto_now=True)
    avaliado_by_id: int
    avaliado_by = models.ForeignKey(
        User, on_delete=models.PROTECT, related_name="avaliacoes_alteracao_onibus", null=True
    )
    avaliado_by_system = models.BooleanField(default=False)

    # Campos inseridos ao Julgar para se obter uma precisão melhor dos valores
    data = models.JSONField(default=get_pedido_alteracao_onibus_data_default)


class PedidoAlteracaoPreco(models.Model):
    class Status(models.TextChoices):
        APROVADO = "APROVADO"
        REJEITADO = "REJEITADO"
        PENDENTE = "PENDENTE"
        SOBRESCRITO = "SOBRESCRITO"

    objects: SerializableManager = SerializableManager()
    id: int
    trecho_classe_id: int
    trecho_classe = models.ForeignKey(TrechoClasse, null=True, on_delete=models.CASCADE)
    valor_novo = models.DecimalField(max_digits=12, decimal_places=2)
    valor_antigo = models.DecimalField(max_digits=12, decimal_places=2)
    created_by_id: int
    created_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name="pedidos_alteracao_preco")
    status = models.CharField(max_length=16, choices=Status.choices, default=Status.PENDENTE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    avaliado_by_id: int
    avaliado_by = models.ForeignKey(
        User, on_delete=models.PROTECT, related_name="avaliacoes_alteracao_preco", null=True
    )

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "trecho_classe": self.trecho_classe_id,
            "valor_novo": self.valor_novo,
            "valor_antigo": self.valor_antigo,
            "created_by": self.created_by_id,
            "status": self.status,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "avaliado_by": self.avaliado_by_id,
        }


class AjustePreco(models.Model):
    class Status(models.TextChoices):
        PROCESSANDO = "processando"
        SUCESSO = "sucesso"
        ERRO = "erro"

    id: int
    descricao = models.CharField(max_length=256)
    file_path = models.TextField(null=True, blank=True)
    created_on = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=32, choices=Status.choices, null=True, blank=True)
    erro = models.TextField(null=True, blank=True)
    task_metadata = models.JSONField(default=dict)
    submitted_by_id: int
    submitted_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.PROTECT)

    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=["-updated_at"]),
        ]

    def to_dict_json(self) -> dict:
        return {
            "id": self.id,
            "descricao": self.descricao,
            "file_path": storage.storage_url(self, "file_path"),
            "created_on": self.created_on,
            "status": self.status,
            "erro": self.erro,
        }

    @property
    def is_automatic(self) -> bool:
        return self.descricao == "ajuste de preço automático" or self.descricao.startswith("ajuste_automatico_parquet")


class PagamentoBO(models.Model):
    id: int
    objects: SerializableManager = SerializableManager()
    grupo_id: int
    grupo = models.ForeignKey(Grupo, related_name="+", on_delete=models.PROTECT)
    incidente_id: int
    incidente = models.ForeignKey("incidents.IncidenteDetalhado", on_delete=models.PROTECT, null=True)
    document_id: int
    document = models.ForeignKey("incidents.Document", on_delete=models.PROTECT, null=True)
    external_id = models.CharField(max_length=36, null=True)
    value = models.IntegerField(null=True)
    expiration_date = models.DateField(null=True)


class RestrictionTypes(models.TextChoices):
    ONIBUS_SUPORTADOS = "ONIBUS_SUPORTADOS", "Ônibus suportado"
    WEEKDAYS = "WEEKDAYS", "Dias de funcionamento"
    WORKING_HOURS = "WORKING_HOURS", "Horarios de funcionamento"
    MAX_EMBARQUE_SIMULTANEO = "MAX_EMBARQUE_SIMULTANEO", "Embarque simultâneo"


class RestrictionBypass(models.Model):
    id: int
    groups = models.ManyToManyField(Grupo, related_name="bypassed_restrictions")
    reason = models.TextField(null=False)
    restriction_types = ArrayField(models.CharField(max_length=32, choices=RestrictionTypes.choices))
    local_id: int
    local = models.ForeignKey("core.LocalEmbarque", on_delete=models.CASCADE, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    user_id: int

    class Meta:
        constraints = [
            models.CheckConstraint(
                name="%(app_label)s_%(class)s_restriction_types_valid",
                check=models.Q(restriction_types__contained_by=RestrictionTypes.values),
            )
        ]


class AutorizacaoGrupo(models.Model):
    class Status(models.TextChoices):
        APROVADO = "APROVADO"
        REJEITADO = "REJEITADO"
        PENDENTE = "PENDENTE"
        APROVADO_COM_PENDENCIAS = "APROVADO_COM_PENDENCIAS"
        CANCELADO = "CANCELADO"

    class Situacao(models.TextChoices):
        NENHUM_DOCUMENTO_ANEXO = "NENHUM_DOCUMENTO_ANEXO"
        LICENCA_VALIDA = "LICENCA_VALIDA"
        NAO_EH_LICENCA = "NAO_EH_LICENCA"
        LICENCA_OUTRA_VIAGEM = "LICENCA_OUTRA_VIAGEM"
        PLACA_APROVADA = "PLACA_APROVADA"
        PLACA_REJEITADA_PROBLEMAS_DOC = "PLACA_REJEITADA_PROBLEMAS_DOC"
        PLACA_REJEITADA_IRREGULAR = "PLACA_REJEITADA_IRREGULAR"

    class Reprovacoes(models.TextChoices):
        ORIGEM = "ORIGEM"
        DESTINO = "DESTINO"
        DATA_DA_VIAGEM = "DATA_DA_VIAGEM"
        HORA_DA_VIAGEM = "HORA_DA_VIAGEM"
        PLACA = "PLACA"
        CNPJ = "CNPJ"
        RAZAO_SOCIAL = "RAZAO_SOCIAL"
        MOTORISTA1 = "MOTORISTA1"
        MOTORISTA2 = "MOTORISTA2"
        UPLOAD_ATRASO = "UPLOAD_ATRASO"

    class TipoAgencia(models.TextChoices):
        ANTT = "ANTT"
        DERMG = "DERMG"
        ARTESP = "ARTESP"
        DETRORJ = "DETRORJ"
        OTHER = "OUTRO"

    id: int
    history = HistoricalRecords()
    comentario = models.TextField(null=True)
    uploaded_at = models.DateTimeField(default=now)
    avaliacao_updated_at = models.DateTimeField(null=True)
    grupo = models.OneToOneField(Grupo, on_delete=models.CASCADE, related_name="autorizacao_grupo")
    reprovacoes = ArrayField(models.CharField(max_length=22, choices=Reprovacoes.choices), default=list)
    situacao_licenca = models.TextField(max_length=37, null=True, choices=Situacao.choices)
    status = models.TextField(max_length=23, default=Status.PENDENTE, choices=Status.choices)
    avaliado_by = models.ForeignKey("auth.user", on_delete=models.SET_NULL, null=True)
    agency = models.TextField(choices=TipoAgencia.choices, null=True, blank=True, default=TipoAgencia.OTHER)
    authorization_number = models.TextField(max_length=24, null=True, blank=True)
    s3_key = models.CharField(max_length=256, null=True, blank=True)

    def to_dict_json(self) -> dict:
        d = {
            "comentario": self.comentario,
            "uploaded_at": self.uploaded_at,
            "avaliacao_updated_at": self.avaliacao_updated_at,
            "reprovacoes": self.reprovacoes,
            "situacao_licenca": self.situacao_licenca,
            "status": self.status,
            "agency": self.agency,
            "authorization_number": self.authorization_number,
        }
        return d


class AutorizacaoGrupoPassageiro(models.Model):
    id: int
    name = models.TextField()
    document_number = models.TextField(null=True, blank=True)
    autorizacao_grupo_id: int
    autorizacao_grupo = models.ForeignKey(AutorizacaoGrupo, on_delete=models.CASCADE)
    document_type = models.TextField(null=True, blank=True)


class MotoraFeedback(models.Model):
    class Problemas(models.TextChoices):
        APLICATIVO = "APLICATIVO"
        DURACAO_VIAGEM = "DURACAO_VIAGEM"
        PASSAGEIRO = "PASSAGEIRO"
        EMBARQUE = "EMBARQUE"
        DESEMBARQUE = "DESEMBARQUE"
        PARADA = "PARADA"
        POLTRONA = "POLTRONA"
        TRANSITO = "TRANSITO"
        SUPORTE = "SUPORTE"
        OUTROS = "OUTROS"

    id: int
    like = models.BooleanField(null=False)
    comentario = models.TextField(null=True)
    grupo_id: int
    grupo = models.ForeignKey(Grupo, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    problemas = ArrayField(models.CharField(max_length=20, choices=Problemas.choices), default=list)
    user_id: int
    user = models.ForeignKey(User, on_delete=models.CASCADE, db_constraint=False)

    class Meta:
        constraints = [models.UniqueConstraint(fields=["grupo", "user"], name="unique_motora_feedback")]


class EventoExtra(models.Model):
    """Modelo para registro dos dados do evento relacionado à solicitação de extra"""

    objects: ClassVar[SerializableManager] = SerializableManager()

    class EventoStatus(models.TextChoices):
        PENDING = "pending"
        DOING = "doing"
        DONE = "done"

    nome = models.CharField(max_length=100, null=False, blank=False)
    data_inicial = models.DateField(null=False, blank=False)
    data_final = models.DateField(null=False, blank=False)
    status = models.CharField(max_length=20, null=False, blank=False, choices=EventoStatus.choices)
    updated_by_id: int
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name="eventos_updated_by")
    created_at = models.DateTimeField(auto_now_add=True)
    history = HistoricalRecords()

    @property
    def _history_user(self):
        return self.updated_by

    @_history_user.setter
    def _history_user(self, value):
        self.updated_by = value

    class Meta:
        ordering = ["data_inicial"]


class EventoExtraSolicitacao(models.Model):
    """Modelo de registro do fluxo de solicitações de Extras do time de Rotas"""

    objects: ClassVar[SerializableManager] = SerializableManager()

    class Regional(models.TextChoices):
        MG = "MG"
        SP = "SP"

    class Prioridade(models.TextChoices):
        BAIXA = "baixa"
        MEDIA = "media"
        ALTA = "alta"

    id: int
    evento_extra_id: int
    evento_extra = models.ForeignKey(EventoExtra, on_delete=models.PROTECT)
    rota_principal_id: int
    rota_principal = models.ForeignKey(RotaPrincipal, on_delete=models.DO_NOTHING)
    rota_prevista = models.TextField(null=False, blank=False)
    regional = models.TextField(
        choices=Regional.choices,
        max_length=2,
        null=False,
        blank=False,
    )
    prioridade = models.TextField(
        choices=Prioridade.choices, help_text="Prioridade da solicitação. Menor valor tem maior prioridade"
    )
    tipos_assento = ArrayField(
        models.CharField(max_length=64, blank=True, null=True, choices=TIPOS_ASSENTO_CHOICES), blank=False, null=False
    )
    ticket_medio_estimado = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    breakeven_esperado = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)
    distancia_por_perna = models.IntegerField(null=True, blank=True)
    cash_in_gmv = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)

    # pendências
    is_fechado_rotas = models.BooleanField(null=False, blank=False, default=False)
    has_precificacao_inicial = models.BooleanField(null=False, blank=False, default=False)
    has_precificacao_final = models.BooleanField(null=False, blank=False, default=False)
    is_criado_staff = models.BooleanField(null=False, blank=False, default=False)

    updated_by_id: int
    updated_by = models.ForeignKey(User, on_delete=models.DO_NOTHING, related_name="solicitacoes_updated_by")
    created_at = models.DateTimeField(auto_now_add=True)
    history = HistoricalRecords()

    @property
    def _history_user(self):
        return self.updated_by

    @_history_user.setter
    def _history_user(self, value):
        self.updated_by = value


class EventoExtraNegociacao(models.Model):
    """Modelo de registro do fluxo de Negociação de Extras do time comercial"""

    objects: ClassVar[SerializableManager] = SerializableManager()

    evento_extra_id: int
    evento_extra = models.ForeignKey(EventoExtra, on_delete=models.PROTECT)
    solicitacao_extra_id: int
    solicitacao_extra = models.ForeignKey(EventoExtraSolicitacao, on_delete=models.CASCADE)
    gerente_comercial_id: int
    gerente_comercial = models.ForeignKey(User, on_delete=models.DO_NOTHING, related_name="negociacoes_kam")
    distancia_total = models.IntegerField()
    deslocamento = models.IntegerField()
    frete_total = models.DecimalField(max_digits=10, decimal_places=2, help_text="Valor total do frete total")
    frete_km = models.DecimalField(max_digits=10, decimal_places=2, help_text="Valor do frete por KM")
    cask = models.DecimalField(max_digits=10, decimal_places=2, help_text="Valor do cask")
    ticket_medio = models.DecimalField(max_digits=10, decimal_places=2, help_text="Valor do ticket médio")
    breakeven = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)
    resultado_max = models.DecimalField(max_digits=10, decimal_places=2, help_text="Resultado máximo")
    tipos_assento = ArrayField(
        models.CharField(max_length=64, blank=True, null=True, choices=TIPOS_ASSENTO_CHOICES), blank=False, null=False
    )
    capacidade = models.IntegerField()
    company_id: int
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, blank=True, null=True)
    onibus_id: int
    onibus = models.ForeignKey(Onibus, on_delete=models.DO_NOTHING, blank=True, null=True)

    # pendências
    has_empresa_escalada = models.BooleanField(null=False, blank=False, default=False)
    is_fechado_comercial = models.BooleanField(null=False, blank=False, default=False)
    has_contrato_assinado = models.BooleanField(null=False, blank=False, default=False)

    updated_by_id: int
    updated_by = models.ForeignKey(User, on_delete=models.DO_NOTHING, related_name="negociacoes_updated_by")
    created_at = models.DateTimeField(auto_now_add=True)
    history = HistoricalRecords()

    @property
    def _history_user(self):
        return self.updated_by

    @_history_user.setter
    def _history_user(self, value):
        self.updated_by = value


class EventoExtraSolicitacaoPerna(models.Model):
    """Modelo que representa a agenda das pernas.
    Uma perna pode ser entendida como um dos sentidos de uma viagem de ida e volta de um ônibus."""

    objects: ClassVar[SerializableManager] = SerializableManager()

    class Turno(models.TextChoices):
        MANHA = "manha"
        TARDE = "tarde"
        NOITE = "noite"
        MADRUGADA = "madrugada"

    solicitacao_extra_id: int
    solicitacao_extra = models.ForeignKey(EventoExtraSolicitacao, on_delete=models.CASCADE)
    sentido = models.TextField(max_length=255, blank=False, null=False)
    turno = models.TextField(choices=Turno.choices)
    rota_id: int
    rota = models.ForeignKey(Rota, on_delete=models.CASCADE, blank=True, null=True)
    data = models.DateField(blank=True, null=True)
    hora = models.TimeField(blank=True, null=True)

    updated_by_id: int
    updated_by = models.ForeignKey(User, on_delete=models.DO_NOTHING, related_name="perna_updated_by")
    created_at = models.DateTimeField(auto_now_add=True)
    history = HistoricalRecords()
