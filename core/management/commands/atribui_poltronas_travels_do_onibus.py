import logging
from collections import defaultdict

from django.core.management import BaseCommand
from django.db.models import Prefetch

from commons.dateutils import now
from core.models_grupo import Grupo
from core.models_travel import Passageiro, Travel
from core.service.selecao_assento.fretamento import FretamentoSeatController

buserlogger = logging.getLogger("buserlogger")


class Command(BaseCommand):
    help = "Atribui poltrona de fretamento aos pax que já compraram para determinado ônibus. Vamos executar esse comando conforme formos liberando as placas em prod para a marcação de assentos."

    def add_arguments(self, parser):
        parser.add_argument("--onibus_id", help="Id do onibus", required=True)

    def handle(self, onibus_id, **kwargs) -> None:
        _now = now()

        travels = (
            Travel.objects.select_related("trecho_classe", "grupo")
            .prefetch_related(
                Prefetch("passageiro_set", queryset=Passageiro.objects.filter(removed=False)),
            )
            .filter(
                grupo__onibus_id=onibus_id,
                grupo__modelo_venda=Grupo.ModeloVenda.BUSER,
                trecho_classe__datetime_ida__gte=_now,
            )
            .exclude(status=Travel.Status.CANCELED)
            .order_by("-count_seats", "trecho_classe__datetime_ida")
            .distinct()
        )
        travels_by_trecho_classe = defaultdict(list)
        for travel in travels:
            travels_by_trecho_classe[travel.trecho_classe].append(travel)

        for trecho_classe, travels_list in travels_by_trecho_classe.items():
            seat_controller = FretamentoSeatController(trecho_classe)
            buserlogger.info(
                "Encontradas %s travels para atribuir poltronas no trecho_classe %s",
                len(travels_list),
                trecho_classe.id,
            )
            for travel in travels_list:
                passageiros = travel.passageiro_set.all()
                if not passageiros:
                    continue

                if seat_controller.has_marcacao_assento():
                    seat_controller.atribui_poltronas(list(passageiros))
