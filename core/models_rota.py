from datetime import date, timedelta
from decimal import Decimal as D
from typing import TYPE_CHECKING, ClassVar

from django.contrib.auth.models import User
from django.contrib.postgres.fields import ArrayField
from django.db import models
from django.db.models.query import QuerySet
from django_qserializer.serialization import SerializableManager
from simple_history.models import HistoricalRecords

from commons.dateutils import now, timedelta_to_milliseconds, to_default_tz
from commons.django.constraints import UniqueConstraint
from commons.enum import ModeloVenda
from commons.memoize import memoize_local
from commons.price_helper import TICKET_KM_MAP, calc_max_split_by_dist
from commons.storage import private_media_storage
from core.models_commons import Cidade
from core.models_company import TIPOS_ONIBUS_CHOICES, Company
from core.models_utils import ModelSimpleReprMixin
from core.service import globalsettings_svc
from marketplace.utils import itree_slug

if TYPE_CHECKING:
    from django.db.models.manager import RelatedManager

    from core.models_parada import CheckpointParada


FATOR_MIN_SPLIT_VALUE = D("0.94")
DAYS_CHOICE = [(1, "Seg"), (2, "Ter"), (3, "Qua"), (4, "Qui"), (5, "Sex"), (6, "Sáb"), (7, "Dom")]

HUMANIZE_WEEKDAY = {1: "Segunda", 2: "Terça", 3: "Quarta", 4: "Quinta", 5: "Sexta", 6: "Sábado", 7: "Domingo"}


class LocalEmbarque(ModelSimpleReprMixin, models.Model):
    class Classification(models.TextChoices):
        POSTO_DE_GASOLINA = "posto_de_gasolina"
        TERMINAL_RODOVIARIO = "terminal_rodoviario"
        RESTAURANTE = "restaurante"  # deprecated
        PONTO_DE_ONIBUS = "ponto_de_onibus"  # deprecated
        ESTABELECIMENTO_COMERCIAL = "estabelecimento_comercial"
        PONTO_DE_RUA = "ponto_de_rua"
        SHOPPING = "shopping"  # deprecated
        HOTEL = "hotel"  # deprecated
        ESTACIONAMENTO = "estacionamento"
        OUTRO = "outro"  # deprecated

    class Features(models.TextChoices):
        COBERTURA = "cobertura"
        BANCOS = "bancos"
        METRO = "metro"
        ALIMENTACAO = "alimentacao"
        BANHEIROS = "banheiros"
        ILUMINACAO = "iluminacao"
        CAMERAS = "cameras"
        CENTRO = "centro"
        RODOVIARIA = "rodoviaria"

    class CategoriaDoPonto(models.TextChoices):
        PREMIUM = "premium"
        LOW_COST = "low_cost"

    class TipoDeAcesso(models.TextChoices):
        PUBLICO = "publico"
        PRIVADO = "privado"

    objects: ClassVar[SerializableManager] = SerializableManager(
        select_related=["cidade"],
    )

    id: int
    gestor_id: int  # deprecated
    gestor = models.ForeignKey(User, related_name="pontos_geridos", null=True, on_delete=models.SET_NULL)  # deprecated
    cidade_id: int
    cidade = models.ForeignKey(Cidade, on_delete=models.CASCADE)
    endereco = models.CharField(max_length=2048)
    endereco_slug = models.CharField(max_length=256, editable=False, null=True)
    description = models.TextField(blank=True, null=True)
    description_ao = models.TextField(blank=True, null=True)  # Descrição do PDE para grupos de assentos ociosos
    mapurl = models.CharField(max_length=2048)
    original_mapurl = models.CharField(blank=True, null=True, max_length=2048)
    street_view_mapurl = models.TextField(null=True, blank=True)
    ativo = models.BooleanField(default=True)
    latitude = models.FloatField(blank=True, null=True)
    longitude = models.FloatField(blank=True, null=True)
    nickname = models.CharField(max_length=64, blank=True, null=True)
    endereco_referencia = models.CharField(max_length=140, blank=True, null=True)  # deprecated
    endereco_numero = models.CharField(max_length=32, blank=True, null=True)
    endereco_bairro = models.CharField(max_length=1024, blank=True, null=True)
    endereco_cep = models.CharField(max_length=8, blank=True, null=True)
    endereco_logradouro = models.CharField(max_length=256, blank=True, null=True)
    bairro_slug = models.CharField(max_length=256, editable=False, null=True)
    nickname_slug = models.CharField(max_length=256, editable=False, null=True)
    start_time = models.TimeField(null=True)  # deprecated, use Weekdays
    end_time = models.TimeField(null=True)  # deprecated, use Weekdays
    max_embarque_simultaneo = models.IntegerField(null=True, default=None)
    requires_auth = models.BooleanField(default=False)  # deprecated
    desembarque_rapido = models.BooleanField(default=False)
    classification = models.CharField(max_length=32, null=True, choices=Classification.choices)
    slug = models.SlugField(max_length=128, null=True)
    features = ArrayField(models.CharField(max_length=64, choices=Features.choices), blank=True, null=True)
    contingency_point_id: int  # deprecated
    contingency_point = models.ForeignKey(
        "self", blank=True, null=True, related_name="contingencia", on_delete=models.SET_NULL
    )  # deprecated
    modelos_venda = ArrayField(models.CharField(max_length=64), blank=True, null=True)
    aceita_embarque_ao = models.BooleanField(default=False, blank=True, null=True)
    uso_do_local = ArrayField(models.CharField(max_length=64), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    impede_bypass = models.BooleanField(default=False, blank=True, null=True)
    categoria_do_ponto = models.CharField(max_length=32, choices=CategoriaDoPonto.choices, null=True, blank=True)
    tipo_de_acesso = models.CharField(max_length=32, choices=TipoDeAcesso.choices, null=True, blank=True)
    zona_maxima_de_restricao = models.BooleanField(default=False, null=True, blank=True)
    max_minutos_permanencia = models.IntegerField(default=30, null=True, blank=True)

    # alguns pontos possuem entradas diferentes para motoristas e.g. Vila Guilherme
    driver_description = models.TextField(blank=True, null=True)
    driver_endereco_numero = models.CharField(max_length=32, blank=True, null=True)
    driver_endereco_logradouro = models.CharField(max_length=256, blank=True, null=True)

    dias_proibidos = ArrayField(models.DateField(), null=True, blank=True)

    if TYPE_CHECKING:
        weekdays: "RelatedManager[Weekdays]"

    def __str__(self):
        id_tag = f"#{self.id}: " if self.id else ""
        desc = f" - {self.description[:30]}" if self.description else ""
        return f"{id_tag}{self.cidade.name}{desc}"

    class Meta:
        ordering = ("cidade__slug", "endereco")
        indexes = [models.Index(fields=["slug"], name="localembarque_slug_idx")]

    @property
    def endereco_completo(self):
        if not self.endereco_logradouro or not self.endereco_bairro:
            return self.endereco
        numero = ", %s" % self.endereco_numero if self.endereco_numero else ""
        return "%s%s - %s" % (self.endereco_logradouro, numero, self.endereco_bairro)

    @property
    def coords(self):
        return self.latitude, self.longitude

    @property
    def coords_map(self):
        return {"latitude": self.latitude, "longitude": self.longitude}

    @property
    def itree_slug(self):
        if not self.slug or not self.cidade.slug:
            return
        return itree_slug(self.cidade.slug, self.slug)

    def to_dict_json(self) -> dict:
        d = self.cidade.to_dict_json()
        d["cidade_id"] = self.cidade_id
        if self.start_time:
            d["start_time"] = self.start_time.strftime("%H:%M")
        if self.end_time:
            d["end_time"] = self.end_time.strftime("%H:%M")

        d.update(
            {
                "id": self.id,
                "slug_local": self.slug,
                "nickname": self.nickname,
                "nickname_slug": self.nickname_slug,
                "endereco": self.endereco_completo,
                "endereco_slug": self.endereco_slug,
                "endereco_logradouro": self.endereco_logradouro,
                "endereco_numero": self.endereco_numero,
                "endereco_bairro": self.endereco_bairro,
                "bairro_slug": self.bairro_slug,
                "endereco_cep": self.endereco_cep,
                "description": self.description,
                "description_ao": self.description_ao,
                "aceita_embarque_ao": self.aceita_embarque_ao,
                "mapurl": self.mapurl,
                "street_view_url": self.street_view_mapurl,
                "ativo": self.ativo,
                "max_embarque_simultaneo": self.max_embarque_simultaneo,
                "max_minutos_permanencia": self.max_minutos_permanencia,
                "coords": {"latitude": self.latitude, "longitude": self.longitude},
                "features": self.features,
                "modelos_venda": self.modelos_venda,
                "uso_do_local": self.uso_do_local,
                "desembarque_rapido": self.desembarque_rapido,
            }
        )
        return d


class LocalEmbarqueInternalDetails(models.Model):
    class StatusFlag(models.TextChoices):
        VERDE = "verde"
        AMARELO = "amarelo"
        VERMELHO = "vermelho"
        AZUL = "azul"

    id: int
    local_id: int
    local = models.OneToOneField(LocalEmbarque, on_delete=models.CASCADE, related_name="detalhes_internos")
    status_flag = models.CharField(max_length=16, null=True, choices=StatusFlag.choices)
    reports = models.TextField(blank=True, null=True)
    observations = models.TextField(blank=True, null=True)

    # contato externo responsável pelo ponto
    contato_nome = models.CharField(max_length=64, blank=True, null=True)
    contato_email = models.CharField(max_length=64, blank=True, null=True)
    contato_telefone = models.CharField(max_length=16, blank=True, null=True)


class LocalEmbarqueDistancia(ModelSimpleReprMixin, models.Model):
    id: int
    origem = models.ForeignKey(LocalEmbarque, on_delete=models.CASCADE, null=False, blank=False, related_name="+")
    destino = models.ForeignKey(LocalEmbarque, on_delete=models.CASCADE, null=False, blank=False, related_name="+")
    distancia = models.FloatField(null=False, blank=False)
    tempo_min = models.FloatField(null=False, blank=False)

    class Meta:
        unique_together = ("origem", "destino")


class HistoricoAlteracaoEmbarque(models.Model):
    id: int
    objects: ClassVar[SerializableManager] = SerializableManager()

    grupo_novo = models.ForeignKey("core.Grupo", related_name="grupo_novo", on_delete=models.PROTECT)
    grupo_antigo = models.ForeignKey("core.Grupo", related_name="grupo_antigo", on_delete=models.PROTECT)
    local_novo = models.ForeignKey(LocalEmbarque, related_name="local_novo", on_delete=models.PROTECT)
    local_antigo = models.ForeignKey(LocalEmbarque, related_name="local_antigo", on_delete=models.PROTECT)
    user = models.ForeignKey(User, on_delete=models.PROTECT)
    created_at = models.DateTimeField(auto_now_add=True)


class OnibusSuportado(models.Model):
    id: int
    local = models.ForeignKey(LocalEmbarque, on_delete=models.CASCADE, related_name="onibus_suportados")
    tipo = models.CharField(choices=TIPOS_ONIBUS_CHOICES, max_length=32, null=False)


class GruposLocalEmbarque(models.Model):
    id: int
    count_date = models.DateField()
    count = models.IntegerField(null=True, default=0)
    local = models.OneToOneField(LocalEmbarque, on_delete=models.CASCADE, related_name="grupos_futuros")


class Weekdays(models.Model):
    objects: ClassVar[SerializableManager] = SerializableManager()

    local = models.ForeignKey(LocalEmbarque, on_delete=models.CASCADE, related_name="weekdays")
    dia = models.IntegerField(choices=DAYS_CHOICE, null=False)
    start_time = models.TimeField(null=True)
    end_time = models.TimeField(null=True)
    max_minutos_permanencia = models.IntegerField(default=30, null=True, blank=True)
    max_embarque_simultaneo = models.IntegerField(default=0, null=True, blank=True)


class LocalRetiradaMarketplace(models.Model):
    id: int
    objects: ClassVar[SerializableManager] = SerializableManager()

    class LocalRetirada(models.TextChoices):
        GUICHE = "guiche"
        MOTORISTA = "motorista"
        OUTRO = "outro"

    local_embarque = models.ForeignKey(LocalEmbarque, on_delete=models.CASCADE)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    tipo = models.CharField(max_length=15, choices=LocalRetirada.choices, null=False)
    descricao = models.TextField(null=True, blank=True)
    descricao_pax = models.TextField(null=True, blank=True)

    def __str__(self):
        return f"{self.local_embarque} - {self.company}"

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=["local_embarque", "company"], name="unique_local_retirada_marketplace")
        ]


class DateRules(models.Model):
    name = models.CharField(max_length=64)
    rules = models.TextField(default="{}")

    def __str__(self):
        return self.name


class TipoOnibus(models.Model):
    name = models.CharField(max_length=64)
    layout = models.TextField(default="[]")

    def __str__(self):
        return self.name


HORARIOS_DEFAULT = ",".join(["{h}:00,{h}:30".format(h=h) for h in range(19, 23)]) + ",23:00"


class Rota(ModelSimpleReprMixin, models.Model):
    class Tipos(models.TextChoices):
        BUSER = "buser"
        MARKETPLACE = "marketplace"

    id: int
    ativo = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    distancia_total = models.IntegerField(null=True, blank=True)
    duracao_total = models.DurationField(null=True, blank=True)
    ufs_intermediarios = models.CharField(max_length=128, null=True, blank=True)
    origem_id: int
    origem = models.ForeignKey(LocalEmbarque, related_name="origem", on_delete=models.CASCADE)
    destino_id: int
    destino = models.ForeignKey(LocalEmbarque, related_name="destino", on_delete=models.CASCADE)
    pedagio_por_eixo = models.DecimalField(max_digits=8, decimal_places=2, null=True)
    pedagio_por_eixo_fds = models.DecimalField(max_digits=8, decimal_places=2, null=True)
    pedagio_updated_at = models.DateTimeField(blank=True, null=True, auto_now_add=True)
    pedagio_updated_by = models.TextField(null=True, blank=True)
    tipo = models.TextField(default=Tipos.BUSER, choices=Tipos.choices, db_index=True)

    if TYPE_CHECKING:
        trechos_vendidos = RelatedManager["TrechoVendido"]()
        itinerario = RelatedManager["Checkpoint"]()
        checkpointparada_set = RelatedManager[CheckpointParada]()

    class Meta:
        indexes = [
            models.Index(fields=["ativo"]),
            models.Index(fields=["updated_at"]),
        ]

    def get_trechos_vendidos(self):
        return self.trechos_vendidos.all()

    def get_trechos_vendidos_ids_ori_dest(self) -> list[dict]:
        return [
            {
                "id": tv.id,
                "origem": {"sigla": tv.origem.cidade.sigla, "nickname": tv.origem.nickname},
                "destino": {"sigla": tv.destino.cidade.sigla, "nickname": tv.destino.nickname},
                "secundario": tv.secundario,
            }
            for tv in self.get_trechos_vendidos()
        ]

    def get_itinerario(self):
        return self.itinerario.all()

    def get_paradas(self):
        return self.checkpointparada_set.all()

    def need_2_drivers(self, duracao_total) -> bool:
        max_distance_allowed_one_driver = globalsettings_svc.get("max_distance_allowed_one_driver")
        max_duration_allowed_one_driver = timedelta(hours=7, minutes=20)

        if not max_distance_allowed_one_driver:
            raise Exception("Distancia maxima permitida para apenas um motorista não cadastrada")
        return self.distancia_total > max_distance_allowed_one_driver or duracao_total > max_duration_allowed_one_driver

    def to_dict_json(self, with_rateio=False) -> dict:
        return {
            "id": self.id,
            "ativo": self.ativo,
            # TODO_REFATORACAO_ROTAS: Talvez esse uso de itinerário use as durações.
            "itinerario": [c.to_dict_json() for c in self.get_itinerario()],
            "trechos_vendidos": [tv.to_dict_json(with_rateio=with_rateio) for tv in self.get_trechos_vendidos()],
            "ufs_intermediarios": self.ufs_intermediarios or "",
            "duracao_total": timedelta_to_milliseconds(self.duracao_total),
            "distancia_total": self.distancia_total,
            "pedagio_por_eixo": self.pedagio_por_eixo,
        }

    def interestadual(self) -> bool:
        """
        UFs intermediários não é tão confiável. É um campo de texto cadastrado na mão.
        Quando o campo tem alguma coisa, a rota é interestadual mesmo, quando não tem,
        precisa conferir as cidades do itinerário que é mais preciso.

        Checar primeiro o ufs_intermediarios evita chamar o get_cidades sempre, que é mais pesado.
        """
        from core.service.itinerario_svc import get_cidades

        if self.ufs_intermediarios is not None and self.ufs_intermediarios != "":
            return True

        ufs = {cidade["uf"] for cidade in get_cidades(self.id)}
        return len(ufs) >= 2

    def __str__(self) -> str:
        checkpoints = list(self.get_itinerario())
        return "-".join([c.local.cidade.sigla for c in checkpoints])


class RotaPrincipal(models.Model):
    objects: ClassVar[SerializableManager] = SerializableManager()

    eixo = models.CharField(max_length=64, unique=True)
    cidades_trajeto = models.ManyToManyField("core.Cidade", related_name="rotas_principais", blank=True)


class Checkpoint(models.Model):
    objects: ClassVar[SerializableManager] = SerializableManager(
        select_related=["local__cidade"],
    )

    id: int
    rota_id: int
    rota = models.ForeignKey(Rota, related_name="itinerario", on_delete=models.CASCADE)
    local_id: int
    local = models.ForeignKey(LocalEmbarque, on_delete=models.CASCADE)
    idx = models.IntegerField()
    distancia_km = models.IntegerField(null=True, blank=True)
    duracao = models.DurationField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    tempo_embarque = models.DurationField(default=timedelta(minutes=20))
    is_conexao = models.BooleanField(default=False, null=True)

    class Meta:
        unique_together = (
            "rota",
            "idx",
        )
        ordering = (
            "rota",
            "idx",
        )

    def to_dict_json(self, local=True) -> dict:
        d = {
            "id": self.id,
            "local_id": self.local_id,
            "distancia_km": self.distancia_km,
            "duracao": timedelta_to_milliseconds(self.duracao) or 0,
            "tempo_embarque": timedelta_to_milliseconds(self.tempo_embarque),
            "idx": self.idx,
            "apenas_parada": False,
            "is_conexao": self.is_conexao,
        }
        if local:
            d["local"] = self.local.to_dict_json()
        return d


@memoize_local(timeout=24 * 60 * 60)
def _nome_origem_destino(origem_id, destino_id):
    bulk = dict(LocalEmbarque.objects.values_list("pk", "cidade__name").filter(pk__in=[origem_id, destino_id]))
    return bulk[origem_id], bulk[destino_id]


class TrechoVendido(ModelSimpleReprMixin, models.Model):
    objects: ClassVar[SerializableManager] = SerializableManager(
        select_related=["rota", "origem__cidade", "destino__cidade"],
        prefetch_related=[
            models.Prefetch(
                "rota__itinerario",
                queryset=Checkpoint.objects.to_serialize(),
            ),
        ],
    )

    id: int
    rota_id: int
    rota = models.ForeignKey(Rota, related_name="trechos_vendidos", on_delete=models.CASCADE)
    origem_id: int
    origem = models.ForeignKey(LocalEmbarque, related_name="trechos_vendidos_origem", on_delete=models.CASCADE)
    destino_id: int
    destino = models.ForeignKey(LocalEmbarque, related_name="trechos_vendidos_destino", on_delete=models.CASCADE)
    preco_rodoviaria = models.DecimalField(max_digits=12, decimal_places=2, default=D("100.00"))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    secundario = models.BooleanField(default=False, null=True)

    class Meta:
        indexes = [
            models.Index(fields=["updated_at"]),
        ]

    def rateio_map(self) -> dict:
        return {tipo_assento: calc_max_split(self.preco_rodoviaria, tipo_assento) for tipo_assento in FATOR_MAP}

    def rateio_map_dist(self, itinerario: list[dict] | None = None) -> dict:
        distancia_trecho = self.distancia_total(itinerario)
        return {tipo_assento: calc_max_split_by_dist(distancia_trecho, tipo_assento) for tipo_assento in TICKET_KM_MAP}

    @property
    def _nome_origem_destino(self):
        """
        Retorna uma tupla `(cidade__origem__name, cidade_destino__name)` de cache
        local.

        Não use isso. Existe para uma otimização de fluxo existente, mas que precisa morrer.
        """
        return _nome_origem_destino(self.origem_id, self.destino_id)

    def distancia_total(self, itinerario: list[dict] | None = None) -> int:
        from core.service.itinerario_svc import get_itinerario_trecho_vendido

        if itinerario is None:
            itinerario = get_itinerario_trecho_vendido(self.id)
        distancia = 0
        pode_somar = False
        for chk in itinerario:
            if pode_somar:
                distancia += chk["distancia_km"] or 0
            if chk["local_id"] == self.origem_id:
                pode_somar = True
            if chk["local_id"] == self.destino_id:
                break
        return distancia

    def to_dict_json(self, pontos: bool = True, with_rateio: bool = False) -> dict:
        d = {
            "id": self.id,
            "rota_id": self.rota_id,
            "preco_rodoviaria": self.preco_rodoviaria,
            # TODO: tech-debt (squad Receita) isso é pra manter a compatilidade, mas será removido
            "origem": {"id": self.origem_id},
            "destino": {"id": self.destino_id},
            "origem_id": self.origem_id,
            "destino_id": self.destino_id,
            "secundario": self.secundario,
        }
        if pontos:
            d.update(
                {
                    "origem": self.origem.to_dict_json(),
                    "destino": self.destino.to_dict_json(),
                }
            )
        if with_rateio:
            d["rateio_sugerido_map"] = self.rateio_map()
        return d

    def __str__(self):
        return "-".join([self.origem.cidade.sigla, self.destino.cidade.sigla])


class TrechoImportante(models.Model):
    id: int
    origem_id: int
    origem = models.ForeignKey(Cidade, related_name="trecho_importante_origem", on_delete=models.CASCADE)
    destino_id: int
    destino = models.ForeignKey(Cidade, related_name="trecho_importante_destino", on_delete=models.CASCADE)

    class Meta:
        unique_together = ("origem", "destino")

    def to_dict_json(self) -> dict:
        d = {"id": self.id, "origem": self.origem.to_dict_json(), "destino": self.destino.to_dict_json()}
        return d


def calc_max_split(preco_rodoviaria, tipo_assento):  # TODO: aqui provavelmente nao eh o melhor lugar pra isso
    fator = FATOR_MAP.get(tipo_assento, FATOR_RATEIO)
    max_split_value = round(fator * preco_rodoviaria)
    max_split_value = max_split_value - max_split_value % 10 - 1 if max_split_value % 10 < 2 else max_split_value
    return D(max_split_value)


def preco_tradicional(preco_base, tipo_assento):  # TODO: aqui provavelmente nao eh o melhor lugar pra isso
    fator = FATOR_MAP_TRADICIONAL.get(tipo_assento, 1)
    return (preco_base or D(0)) * fator


def add_classes_individuais(classes_dict):
    classes_dict.update({f"{k} individual": v for k, v in classes_dict.items()})


FATOR_RATEIO = D("0.65")
FATOR_MAP = {
    "carro": FATOR_RATEIO * D("0.6"),  # .988
    "van": FATOR_RATEIO * D("0.66"),  # .988
    "convencional": FATOR_RATEIO,
    "executivo": FATOR_RATEIO,
    "semi leito": FATOR_RATEIO * D("1.10"),  # .715
    "leito": FATOR_RATEIO * D("1.36"),  # .884
    "leito cama": FATOR_RATEIO * D("1.52"),  # .988
    "cama premium": FATOR_RATEIO * D("1.75"),  # .988
}

add_classes_individuais(FATOR_MAP)
FATOR_MAP_TRADICIONAL = {
    "carro": D("0.6"),
    "van": D("0.66"),
    "convencional": D("1.0"),
    "executivo": D("1.0"),
    "semi leito": D("1.12"),
    "leito": D("1.45"),
    "leito cama": D("2.10"),
    "cama premium": D("2.42"),
}
add_classes_individuais(FATOR_MAP_TRADICIONAL)
CAPACIDADE_MAP = {
    "convencional": 42,
    "executivo": 42,
    "semi leito": 42,
    "leito": 24,
    "leito cama": 18,
    "cama premium": 18,
    "carro": 4,
    "van": 15,
}
add_classes_individuais(CAPACIDADE_MAP)
NIVEL_ASSENTO_MAP = {
    "carro": 0.3,
    "van": 0.6,
    "convencional": 1,
    "executivo": 1,
    "semi leito": 2,
    "leito": 3,
    "leito cama": 4,
    "cama premium": 5,
}
add_classes_individuais(NIVEL_ASSENTO_MAP)


# Legado, ninguém mais usa isso aqui.
class AreaDeControle(models.Model):
    id: int
    golsat_id = models.IntegerField()
    description = models.CharField(max_length=100)
    extended_description = models.CharField(max_length=255)
    status = models.CharField(max_length=20)
    # Verde / Amarelo / Azul
    category = models.CharField(max_length=30)

    latitude = models.FloatField()
    longitude = models.FloatField()

    geo_type = models.CharField(max_length=30)
    geo_coordinates = models.TextField()
    geo_bbox = models.TextField()

    class Meta:
        constraints = [UniqueConstraint(fields=["golsat_id"], name="unique_golsat_id")]


class DuracaoDinamica(models.Model):
    """
    A duração dinâmica define os tempos de forma diferente dos checkpoints.

    O `checkpoint.duracao` é o tempo entre o checkpoint anterior e o checkpoint.

    Já o `duracao_dinamica.duracao` define a duração entre o checkpoint e o
    próximo checkpoint.

    Isso é assim porque a decisão de uma duração dinâmica é sempre baseada no
    horário de partida do checkpoint, portanto manter o comportamento dos
    checkpoints faria ter a configuração para um checkpoint em duas durações
    distintas.
    """

    class TipoDia(models.TextChoices):
        DEFAULT = "DEFAULT"
        SEXTA_NOITE = "SEXTA_NOITE"
        SEGUNDA_MANHA = "SEGUNDA_MANHA"
        # TODO: FERIADO, FIM_DE_SEMANA, etc.

    id: int
    # Apenas uma das foreign keys será preenchida, checkpoint OU parada
    checkpoint_id: int
    checkpoint = models.ForeignKey("core.Checkpoint", on_delete=models.PROTECT, null=True)
    parada_id: int
    parada = models.ForeignKey("core.CheckpointParada", on_delete=models.PROTECT, null=True)

    tipo = models.TextField(choices=TipoDia.choices)
    hora_tzsp = models.TextField(null=True)
    dow_tzsp = models.TextField(null=True)
    duracao = models.DurationField()

    # TODO_REFATORACAO_ROTAS: Nome ruim considerando que pode ser tempo_parada também.
    tempo_embarque = models.DurationField()

    inicio_vigencia = models.DateField(auto_now_add=True, null=True)
    fim_vigencia = models.DateField(default=date(9999, 12, 31), null=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)

    def __repr__(self):
        return (
            f"<{self.__class__.__name__} id={self.id} checkpoint={self.checkpoint_id}"
            f" parada={self.parada_id} tipo={self.tipo}"
            f" duracao={self.duracao} tempo_embarque={self.tempo_embarque}>"
        )


class ExtratoPedagio(models.Model):
    doc = models.FileField(upload_to="private/pedagio", storage=private_media_storage)
    tag = models.TextField(null=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=False)
    created_at = models.DateTimeField(auto_now_add=True, null=True)


class Pedagio(models.Model):
    id: int

    class Status(models.TextChoices):
        EM_ANALISE = "em_analise"
        AGUARDANDO = "aguardando"
        PAGO = "pago"

    grupo_id: int
    grupo = models.OneToOneField("core.Grupo", null=True, on_delete=models.SET_NULL)
    valor_sugerido = models.DecimalField(max_digits=7, decimal_places=2)
    valor_parceiro = models.DecimalField(max_digits=7, decimal_places=2)
    status = models.CharField(null=False, choices=Status.choices, max_length=256)
    extrato_mes = models.ManyToManyField(ExtratoPedagio, related_name="pedagios")
    extrato_viagem_id: int
    extrato_viagem = models.ForeignKey(
        ExtratoPedagio, related_name="extratos_viagem", on_delete=models.SET_NULL, null=True
    )
    analista_revisor_id: int
    analista_revisor = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=False)
    obs_revisao = models.CharField(max_length=256)
    history = HistoricalRecords()

    objects: ClassVar[SerializableManager] = SerializableManager()


class Conexao(models.Model):
    objects: ClassVar[SerializableManager] = SerializableManager()

    id: int
    deleted_at = models.DateTimeField(null=True, blank=True)
    cidade_origem_id: int
    cidade_origem = models.ForeignKey("core.Cidade", on_delete=models.PROTECT, related_name="conexoes_origem")
    cidade_destino_id: int
    cidade_destino = models.ForeignKey("core.Cidade", on_delete=models.PROTECT, related_name="conexoes_destino")

    exibir_busca_sem_data = models.BooleanField(
        default=True,
        help_text="Caso a flag seja False essa conexão só será retornada em busca com data",
    )
    trecho_conexao_set: QuerySet

    def delete(self, using=None, keep_parents=False):
        agora = to_default_tz(now())
        self.deleted_at = agora
        trechos_conexao = self.trechos_conexao.all()
        trechos_conexao.update(deleted_at=agora)
        self.save(update_fields=["deleted_at"])


class TrechoConexao(models.Model):
    objects: ClassVar[SerializableManager] = SerializableManager()

    id: int
    idx = models.IntegerField()

    min_time_between_conexoes = models.DurationField(
        default=timedelta(seconds=0),
        help_text="Indica o tempo mínimo entre a chegada desse trecho e a partida do próximo, sempre começamos a "
        "preencher esse valor do primeiro trecho (idx 0) e o último trecho pode/deve ter esse valor como "
        "00:00 pois não teremos trechos depois dele.",
    )

    max_time_between_conexoes = models.DurationField(
        default=timedelta(seconds=0),
        help_text="Indica o tempo máximo entre a chegada desse trecho e a partida do próximo, sempre começamos a "
        "preencher esse valor do primeiro trecho (idx 0) e o último trecho pode/deve ter esse valor como "
        "00:00 pois não teremos trechos depois dele.",
    )

    conexao_id: int
    conexao = models.ForeignKey(Conexao, on_delete=models.PROTECT, related_name="trechos_conexao")

    origem_id: int
    origem = models.ForeignKey(Cidade, on_delete=models.PROTECT, related_name="trechos_conexao_origem")

    destino_id: int
    destino = models.ForeignKey(Cidade, on_delete=models.PROTECT, related_name="trechos_conexao_destino")

    conexao_em_mesmo_local = models.BooleanField(
        default=False, help_text="indica que a chegada da perna atual é a partida da próxima"
    )

    company_id: int
    company = models.ForeignKey(Company, on_delete=models.PROTECT, null=True, blank=True)

    modelo_venda = models.TextField(choices=ModeloVenda.choices, null=True)

    deleted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ("idx",)
