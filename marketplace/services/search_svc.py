import datetime
import itertools
import logging

from django.utils import timezone
from memoize import memoize

from commons.dateutils import to_tz
from core.models_company import Company
from core.models_grupo import TrechoClasse
from core.models_rota import LocalEmbarque
from integrations import get_client
from integrations.marketplace_client import MarketplaceClient, SearchResponseMarketplace
from marketplace.services import create_group_svc
from search_result.adapter.cidade_adapter import CidadeAdapter
from search_result.adapter.local_embarque_adapter import LocalEmbarqueAdapter
from search_result.service.search_svc import get_local_by_slug_with_memoize, resolve_slugs

logger = logging.getLogger(__name__)


@memoize(timeout=24 * 60 * 60)
def get_timezone_by_slug(slug) -> str:
    local_origem: CidadeAdapter | LocalEmbarqueAdapter | None = get_local_by_slug_with_memoize(slug)
    assert local_origem is not None
    return local_origem.timezone


def search_marketplace(
    origem: str, destino: str, data: datetime.date, timeout: float | None = None
) -> SearchResponseMarketplace | None:
    client: MarketplaceClient = get_client("marketplace")
    try:
        response = client.search(origem, destino, data, timeout)
        logger.info(f"[HOT_UPDATE] {origem=}, {destino=}, data={data.isoformat()} found {len(response.items)} results")
        # TODO: Esse filtro idealmente deve ser feito do lado do marketplace, porém ainda falta a info de timezone do lado de lá
        timezone_origem = get_timezone_by_slug(origem)
        for item in response.items:
            if item.travels is None:
                continue
            item.travels = [
                item
                for item in item.travels
                if to_tz(datetime.datetime.fromisoformat(item.departure_at), timezone_origem) > timezone.now()
            ]
        return response
    except client.exceptions.ClientError as ex:
        logger.warning("busca_quente_marketplace error (%s, %s, data=%s)", origem, destino, data, exc_info=ex)


def search(origem: str, destino: str, data: datetime.date, fetch_db_data=True) -> list[TrechoClasse]:
    origem, destino = resolve_slugs(origem, destino)

    def _fetch_db_data(trechos: list[TrechoClasse]) -> list[TrechoClasse]:
        slugs = set()
        cnpjs = set()
        for trecho in trechos:
            slugs.add(trecho.trecho_vendido.origem.slug)
            slugs.add(trecho.trecho_vendido.destino.slug)
            assert trecho.grupo.company
            cnpjs.add(trecho.grupo.company.cnpj)

        locais = {
            local.slug: local
            for local in LocalEmbarque.objects.filter(slug__in=slugs, ativo=True)
            .distinct("slug")
            .order_by("slug", "id")
        }
        empresas = {
            empresa.cnpj: empresa
            for empresa in Company.objects.filter(cnpj__in=cnpjs, is_enabled=True)
            .distinct("cnpj")
            .order_by("cnpj", "id")
        }

        trechos_with_fetched_data = []
        for trecho in trechos:
            try:
                origem = locais[trecho.trecho_vendido.origem.slug]
                destino = locais[trecho.trecho_vendido.destino.slug]
                assert trecho.grupo.company
                empresa = empresas[trecho.grupo.company.cnpj]
            except KeyError:
                continue

            trecho.trecho_vendido.rota._checkpoints[0].local = origem  # type: ignore
            trecho.trecho_vendido.rota._checkpoints[1].local = destino  # type: ignore
            trecho.trecho_vendido.origem = origem
            trecho.trecho_vendido.destino = destino
            trecho.grupo.company = empresa
            trechos_with_fetched_data.append(trecho)

        return trechos_with_fetched_data

    marketplace_search_response: SearchResponseMarketplace | None = search_marketplace(
        origem, destino, data, timeout=1.5
    )

    if not marketplace_search_response:
        return []

    trechos = list(
        itertools.chain.from_iterable(
            [create_group_svc.to_trecho_classe(travel) for travel in item.travels or []]
            for item in marketplace_search_response.items
        )
    )

    if fetch_db_data:
        trechos = _fetch_db_data(trechos)

    return trechos
