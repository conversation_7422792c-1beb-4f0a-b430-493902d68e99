import logging
import threading
from dataclasses import dataclass
from datetime import datetime

import requests
from django.conf import settings
from requests.adapters import HTTPA<PERSON>pter
from tenacity import retry
from tenacity.retry import retry_if_exception_type
from tenacity.stop import stop_after_attempt
from tenacity.wait import wait_random
from urllib3 import Retry

from core.models_driving import SmartCamAlarm

buserlogger = logging.getLogger("buserlogger")

DEFAULT_PAGE_SIZE = 1_000

SASCAR_STATUS_MAP = {
    0: SmartCamAlarm.Types.PERDA_VIDEO,
    1: SmartCamAlarm.Types.LENTE_COBERTA,
    56002: SmartCamAlarm.Types.CELULAR,
    56004: SmartCamAlarm.Types.DISTRACAO,
    56006: SmartCamAlarm.Types.COLISAO_FRONTAL,
    56010: SmartCamAlarm.Types.BOCEJO,
    56011: SmartCamAlarm.Types.COLISAO_PEDESTRE,
    56016: SmartCamAlarm.Types.CINTO,
    56000: SmartCamAlarm.Types.OLHO_FECHADO,
    105: SmartCamAlarm.Types.PERDA_GPS,
    56009: SmartCamAlarm.Types.TRASLADO,
    56001: SmartCamAlarm.Types.SEM_MOTORISTA,
    3: SmartCamAlarm.Types.ANORMALIDADE_DE_MEMORIA,
    38: SmartCamAlarm.Types.DESLIGAMENTO_ILEGAL,
    56005: SmartCamAlarm.Types.SAIDA_DE_FAIXA,
    56003: SmartCamAlarm.Types.FUMANTE,
    # SASCAR TABLET TELEMETRIA
    210002: SmartCamAlarm.Types.EXCESSO_VELOCIDADE_PISTA_SECA,
    210003: SmartCamAlarm.Types.EXCESSO_VELOCIDADE_PISTA_MOLHADA,
    210004: SmartCamAlarm.Types.EXCESSO_VELOCIDADE_ROTOGRAMA_PISTA_SECA,
    210006: SmartCamAlarm.Types.FREADA_BRUSCA,
    210007: SmartCamAlarm.Types.FORCA_G_LATERAL_FORTE,
    210008: SmartCamAlarm.Types.FORCA_G_LATERAL_MEDIA,
    210009: SmartCamAlarm.Types.ACELERACAO_BRUSCA,
    210010: SmartCamAlarm.Types.EXCESSO_VELOCIDADE_ROTOGRAMA_PISTA_MOLHADA,
}


@dataclass
class RetrieveTokenRequest:
    identify_code: str
    secret: str
    effective_time_ms: int

    @property
    def __dict__(self):
        return {
            "identifyCode": self.identify_code,
            "secret": self.secret,
            "effectiveTime": self.effective_time_ms,
        }


@dataclass
class RetrievetokenResponse:
    token: str
    expire_time: datetime

    @property
    def __dict__(self):
        return {
            "token": self.token,
            "expireTime": self.expire_time,
        }


class InvalidResponse(RuntimeError): ...


class InvalidUsernameOrPasswordException(RuntimeError): ...


class RetrieveTokenRequestRequiredException(RuntimeError): ...


class RPCSystemCallException(RuntimeError): ...


_exceptions_types = {
    11006: RPCSystemCallException,  # Deu ruim do lado deles
}


class SmartCamClient:
    def __init__(self, timeout: int, session: requests.Session, retry: Retry | None = Retry(total=3, backoff_factor=1)):
        super().__init__()
        self.timeout = timeout
        self.session = session
        self.session.verify = False  # settings.BASE_DIR / "certs/sascar-com-br-chain.pem" (TODO: voltar isso aqui)
        self.base_url = settings.SMARTCAM_URL
        self._auth_token = None
        self._retrieve_token = None
        self._configure_retry(retry)

    def _configure_retry(self, retry: Retry | None):
        retry_adapter = HTTPAdapter(max_retries=retry)
        self.session.mount("http://", retry_adapter)
        self.session.mount("https://", retry_adapter)

    def _include_token_to_headers(self, kwargs):
        headers = kwargs.get("headers", {})
        if self._auth_token:
            headers["token"] = self._auth_token
            kwargs["headers"] = headers

    @retry(
        retry=retry_if_exception_type(RPCSystemCallException),
        wait=wait_random(1, 5),
        stop=stop_after_attempt(5),
    )
    def _api_request(self, url: str, method: str = "GET", retry=0, **kwargs):
        response = self.session.request(method, url, timeout=self.timeout, **kwargs)
        response.raise_for_status()
        returned_data = response.json()
        # code 3800 significa que o token é inválido
        if returned_data["code"] == 3800 and retry < 3:
            self.retrieve_token()
            self._include_token_to_headers(kwargs)
            return self._api_request(url=url, method=method, retry=retry + 1, **kwargs)

        if not returned_data["success"]:
            exc_type = _exceptions_types.get(returned_data["code"], RuntimeError)
            raise exc_type(f"[{returned_data['code']}] {returned_data['message']}")

        return returned_data

    def set_token(self, token, retrieve_token):
        self._auth_token = token
        self._retrieve_token = vars(retrieve_token)

    def get(self, endpoint, **kwargs):
        url = f"{self.base_url}/{endpoint}"
        self._include_token_to_headers(kwargs)
        return self._api_request(url=url, method="GET", **kwargs)

    def post(self, endpoint, **kwargs):
        url = f"{self.base_url}/{endpoint}"
        self._include_token_to_headers(kwargs)
        return self._api_request(url=url, method="POST", **kwargs)

    def retrieve_token(self, request: RetrieveTokenRequest = None):
        if request is None and not hasattr(self, "_retrieve_token"):
            raise RetrieveTokenRequestRequiredException()
        if request:
            self._retrieve_token = vars(request)
        response = self.session.request(
            "POST",
            f"{self.base_url}/openapi/auth",
            json=self._retrieve_token,
        )
        response.raise_for_status()
        returned_data = response.json()
        if not returned_data["success"]:
            if "Login fail" in returned_data["message"] or "Username/password error" in returned_data["message"]:
                raise InvalidUsernameOrPasswordException(f"[{returned_data['code']}] {returned_data['message']}")
            raise InvalidResponse(f"[{returned_data['code']}] {returned_data['message']}")

        self._auth_token = returned_data["data"]["token"]
        return RetrievetokenResponse(
            self._auth_token,
            datetime.fromtimestamp(returned_data["data"]["expireTime"] / 1000.0),
        )

    def _page_iter(self, url: str, json_data: dict | None = None, params: dict | None = None):
        page = 1
        while True:
            if json_data is not None:
                json_data["page"] = page
            if params is not None:
                params["page"] = page
            returned_data = self.get(url, json=json_data, params=params)
            data = returned_data["data"]
            if not data or not data.get("list"):
                break
            for item in data["list"]:
                yield item
            page += 1

    def get_vehicles(self, page_size: int = DEFAULT_PAGE_SIZE):
        return self._page_iter("openapi/vehicle/page", json_data={"pageSize": page_size})

    def get_devices(self):
        returned_data = self.get("openapi/device/page")
        for device in returned_data["data"]:
            yield device

    def get_evidences(self, device_ids: list[str], start_time: int, end_time: int, page_size: int = DEFAULT_PAGE_SIZE):
        # não da pra passar os 3 evidenceState de uma única vez e ele é obrigatório
        yield from self._page_iter(
            "openapi/evidence/page",
            json_data={
                "pageSize": page_size,
                "evidenceState": "WAITING",
                "uniqueIds": ",".join(device_ids),
                "startTime": start_time,
                "endTime": end_time,
            },
        )
        yield from self._page_iter(
            "openapi/evidence/page",
            json_data={
                "pageSize": page_size,
                "evidenceState": "LOADING",
                "uniqueIds": ",".join(device_ids),
                "startTime": start_time,
                "endTime": end_time,
            },
        )
        yield from self._page_iter(
            "openapi/evidence/page",
            json_data={
                "pageSize": page_size,
                "evidenceState": "SUCCESS",
                "uniqueIds": ",".join(device_ids),
                "startTime": start_time,
                "endTime": end_time,
            },
        )

    def get_evidence(self, evidence_id: int):
        return self.get("openapi/evidence/detail", json={"evidenceId": evidence_id})["data"]

    def get_evidence_file(self, evidence_id: int, file_type: str):
        return self.get("openapi/evidence/file", json={"evidenceId": evidence_id, "fileTypes": file_type})["data"]

    def get_alarms_realtime(
        self,
        unique_ids: list[str],
        start_time: int,
        end_time: int,
        vehicle_ids: list[str] | None = None,
        evidence_state: str | None = "3",
        page_size: int = DEFAULT_PAGE_SIZE,
    ):
        before = int(
            (datetime.fromtimestamp(end_time / 1000) - datetime.fromtimestamp(start_time / 1000)).total_seconds()
            * 1_000
        )
        params = {
            "before": before,
            "startTime": start_time,
            "endTime": end_time,
            "pageSize": page_size,
            "evidenceState": evidence_state,
            "uniqueIds": ",".join(unique_ids),
        }
        if vehicle_ids:
            params["vehicleIds"] = ",".join(vehicle_ids)

        items = list(
            self._page_iter(
                "openapi/alarm/realtime",
                params=params,
            )
        )

        if all(isinstance(item, dict) for item in items):
            items = _parse_sascar_type(items)

        return items


def _parse_sascar_type(items):
    for i in items:
        try:
            i["type"] = SASCAR_STATUS_MAP[i["alarmType"]]
        except KeyError:
            i["type"] = i["alarmType"]
            buserlogger.info("smartcam_alarm: tipo desconhecido {}".format(i["alarmType"]))

        i.pop("alarmType")
    return items


_local = threading.local()


def get_smartcam_client(session=None):
    if not hasattr(_local, "smartcam_client"):
        if session is None:
            session = requests.Session()
        _local.smartcam_client = SmartCamClient(timeout=settings.SMARTCAM_TIMEOUT, session=session)
    return _local.smartcam_client
