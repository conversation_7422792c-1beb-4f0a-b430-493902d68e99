import type { PageMeta } from '#app'
import type { RouteRecordRaw } from 'vue-router'

const notMigrated = { redirectNuxt2ByMeta: true }

const noopComponent = () =>
  import('~/pages/Noop.vue').then((r) => r.default || r)

type RouteMetaTaskbar = Partial<{
  hide: boolean // Esconde taskbar e padding superior
  custom: boolean // Esconde a taskbar mas deixa o padding superior
  title: string
  transparent: boolean
  onlyEssentialLinks: boolean
  hideNotLoggedInMenu: boolean
  onlyCheckoutLinks: boolean
}>

interface RouteMeta extends PageMeta {
  squad?: string
  taskbar?: RouteMetaTaskbar
  darkMode?: boolean
  hideFooter?: boolean
  hideNavBottom?: boolean
  hideOnScrollDown?: boolean
  prefetch?: Array<string | { name: string }>
  keepTravel?: boolean
  isPromo?: boolean
  showItineraryMap?: boolean
  redirectNuxt2ByMeta?: boolean
  redirectNuxt2ByCookie?: string
}

const meta = {
  narrowLoggedUser: {
    layout: 'narrow',
    middleware: 'logged-user'
  },
  ajuda: {
    taskbar: { title: 'Ajuda' },
    squad: 'pvv'
  },
  chatRenderer: {
    hideFooter: true,
    taskbar: {
      hide: true
    }
  },
  mgmLead: {
    hideFooter: true,
    squad: 'pvv',
    taskbar: { title: 'Close Friends', custom: true },
    hideNavBottom: true
  },
  completarCadastro: {
    hideFooter: true,
    squad: 'conversao',
    layout: 'narrow',
    middleware: 'logged-user'
  },
  feriados: { squad: 'conversao' },
  notificacoes: {
    taskbar: { title: 'Notificações' },
    hideFooter: true,
    squad: 'pvv',
    layout: 'narrow',
    middleware: 'logged-user'
  },
  profileOptions: {
    taskbar: { title: 'Minha conta' },
    hideFooter: true,
    squad: 'pvv',
    layout: 'narrow',
    middleware: 'logged-user'
  },
  sugerirViagem: {
    taskbar: { title: 'Sugira uma viagem' },
    hideFooter: true,
    hideNavBottom: true,
    squad: 'conversao'
  },
  fidelidade: {
    taskbar: { title: 'Programa de fidelidade' },
    hideFooter: true,
    hideOnScrollDown: false,
    squad: 'pvv',
    middleware: 'logged-user',
    layout: 'narrow'
  },
  formasPagamento: {
    taskbar: { title: 'Formas de pagamento' },
    hideFooter: true,
    squad: 'pvv',
    middleware: 'logged-user',
    layout: 'narrow'
  },
  confirmarTelefoneViaLink: {
    hideFooter: true
  },
  seguroAdicional: {
    hideFooter: true,
    hideOnScrollDown: true,
    hideNavBottom: false
  },
  leadOptOut: {
    hideFooter: true,
    hideOnScrollDown: true
  },
  viagem: {
    taskbar: { title: 'Detalhes da viagem' },
    hideFooter: true,
    squad: 'pvv',
    keepTravel: true
  },
  problemasBagagem: {
    hideFooter: true,
    layout: 'narrow',
    keepTravel: true
  },
  viajantes: {
    taskbar: { custom: true },
    hideFooter: true,
    squad: 'pvv',
    layout: 'narrow',
    middleware: 'logged-user'
  },
  error: {
    taskbar: { hideNotLoggedInMenu: true },
    hideFooter: true,
    layout: 'narrow'
  },
  confirmLeadOptOut: { hideFooter: true, hideOnScrollDown: true },
  manageLinkedUsers: {
    taskbar: { title: 'Compartilhamento de viagens' },
    hideFooter: true,
    squad: 'pvv',
    layout: 'narrow',
    middleware: 'logged-user'
  },
  pesquisaRelacional: {
    hideFooter: true,
    hideOnScrollDown: true,
    squad: 'pvv',
    middleware: 'logged-user'
  },
  pesquisaPosEmbarque: {
    hideFooter: true,
    hideOnScrollDown: true,
    squad: 'pvv'
  },
  pesquisaNps: {
    hideFooter: true,
    hideOnScrollDown: true,
    squad: 'pvv',
    taskbar: {
      title: 'Pesquisa NPS'
    },
    middleware: 'logged-user'
  },
  festival: { squad: 'conversao' },
  editProfile: {
    hideFooter: true,
    middleware: 'logged-user',
    squad: 'pvv',
    layout: 'narrow'
  },
  searchPromoOrigin: {
    taskbar: { title: 'Promoções' },
    squad: 'aquisicao',
    isPromo: true
  },
  editProfileDados: {
    taskbar: { title: 'Editar conta' }
  },
  editProfileMarketing: {
    taskbar: { title: 'Notificações de marketing' }
  },
  profile: {
    taskbar: { title: 'Perfil' },
    hideFooter: true,
    squad: 'pvv',
    layout: 'narrow',
    middleware: 'logged-user'
  },
  carteira: {
    taskbar: { title: 'Carteira' },
    hideFooter: true,
    squad: 'pvv',
    layout: 'narrow',
    middleware: 'logged-user'
  },
  pesquisaViagem: {
    hideFooter: true,
    hideOnScrollDown: true,
    squad: 'pvv'
  },
  promocoes: {
    taskbar: { custom: true },
    hideFooter: true,
    squad: 'conversao',
    middleware: 'logged-user'
  },
  verificarCadastro: {
    hideFooter: true,
    squad: 'conversao',
    middleware: 'logged-user'
  },
  // Revendedor
  dashboardPerformance: {
    taskbar: { title: 'Performance de vendas' },
    hideFooter: true,
    hideOnScrollDown: true,
    middleware: ['logged-user', 'revendedor-user']
  },
  vendasRevendedor: {
    taskbar: { title: 'Vendas Realizadas' },
    hideFooter: true,
    middleware: ['logged-user', 'revendedor-user']
  },
  consultarPax: {
    taskbar: { title: 'Consultar Pax' },
    hideFooter: true,
    middleware: ['logged-user', 'revendedor-user']
  },
  revendedorCupons: {
    taskbar: { title: 'Cupons do Revendedor' },
    hideFooter: true,
    middleware: ['logged-user', 'revendedor-user']
  },
  revendedorCarteira: {
    taskbar: { title: 'Minha carteira - Revendedor' },
    hideFooter: true,
    middleware: ['logged-user', 'revendedor-user']
  }
} satisfies Record<string, RouteMeta>

const routes: Array<RouteRecordRaw> = [
  // URLs Promocionais
  { path: '/convite/:invitecode', redirect: '/' },
  {
    path: '/onibus/sao-paulo-sp/ate-50-reais',
    name: 'urlPromocionalSP',
    component: noopComponent,
    meta: notMigrated
  },

  { path: '/', name: 'home', component: noopComponent, meta: notMigrated },

  // Viagens e Checkout
  {
    path: '/onibus',
    name: 'rotas',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/onibus/para/:destino',
    name: 'searchDestinationPageV1',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/onibus/de/descubra/:cidadeSlug?',
    name: 'descubra',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/onibus/promo/de/:origem?',
    name: 'promoOrigin',
    props: true,
    component: () => import('~/pages/promo-origin/promo-origin.vue'),
    meta: meta.searchPromoOrigin
  },
  {
    path: '/onibus/promo/para/:destino',
    name: 'promoDestination',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/onibus/:origem/:destino',
    name: 'searchPageV1',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/parceria/:nickname/:origem?/:destino?',
    name: 'parceria',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/onibus/festival/:slugFestival/:origem?/:destino?',
    name: 'searchPageFestival',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/onibus/:origem',
    name: 'searchOriginPageV1',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/onibus/:weekDay/:origem/:destino',
    name: 'searchPageWeekDay',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/reserva/grupos/:idIda/:idVolta?',
    name: 'checkout',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/reserva/bpe/:reservationCode',
    name: 'bpe',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/reserva/bpe-status/:passagemRodoviariaId',
    name: 'bpeEmContingencia',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/reserva/viagens/:id/alterar/:step?',
    name: 'alterarReserva',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/reserva/:status/:travelIdaId/:travelVoltaId?',
    name: 'reserva',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/link-pagamento/:code',
    name: 'linkPagamento',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/pagamento-pendente',
    name: 'pagamentoDivida',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/nova-busca/:origem?/:destino?',
    name: 'novaBusca',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/checkout/addon/:travel',
    name: 'checkoutAddon',
    component: noopComponent,
    meta: notMigrated
  },
  // Promoções & Aquisições
  {
    path: '/promo',
    name: 'promo-novo',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/black-friday/:origem?/:destino?',
    name: 'blackFriday',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/aniversario/:origem?/:destino?',
    name: 'aniversarioBuser',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/feriado/:origem?',
    name: 'feriado',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/feriados/:code',
    name: 'feriados',
    props: true,
    component: () => import('~/pages/feriados/feriados-page.vue'),
    meta: meta.feriados
  },
  {
    path: '/festival/:slugFestival?',
    name: 'festival',
    component: () => import('~/pages/festival/festival.vue'),
    meta: meta.festival,
    props: true
  },
  {
    path: '/promo/:code',
    name: 'promocaoCode-novo',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/first-lead-page',
    name: 'firstLeadPage',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/convite',
    name: 'mgmLeadPage',
    component: () => import('~/pages/mgm-lead/mgm-lead-page.vue'),
    meta: meta.mgmLead
  },
  {
    path: '/conheca/:code?',
    name: 'conheca',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/destinos-de-inverno',
    name: 'destinosInverno',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/fidelidade',
    component: () => import('~/pages/user/fidelidade/fidelidade.vue'),
    meta: meta.fidelidade,
    children: [
      {
        path: '',
        name: 'fidelidade',
        components: {
          default: () =>
            import(
              '~/pages/user/fidelidade/lista-fidelidade/lista-fidelidade.vue'
            ),
          skeleton: () =>
            import(
              '~/pages/user/fidelidade/lista-fidelidade/lista-fidelidade-skeleton.vue'
            )
        }
      },
      {
        path: ':id',
        name: 'detalhesFidelidade',
        components: {
          default: () =>
            import(
              '~/pages/user/fidelidade/detalhes-fidelidade/detalhes-fidelidade.vue'
            ),
          skeleton: () =>
            import(
              '~/pages/user/fidelidade/detalhes-fidelidade/detalhes-fidelidade-skeleton.vue'
            )
        },
        props: {
          default: true,
          skeleton: false
        }
      }
    ]
  },
  // Suporte
  {
    path: '/chat',
    name: 'chat',
    component: () => import('~/pages/chat.vue')
  },
  {
    path: '/chat-renderer-mobile-version',
    name: 'chat-renderer',
    component: () => import('~/pages/chat-renderer.vue'),
    meta: meta.chatRenderer
  },
  {
    path: '/ajuda',
    component: () => import('~/pages/ajuda/ajuda.vue'),
    children: [
      {
        name: 'ajuda',
        path: '',
        component: () => import('~/pages/ajuda/topics/topics.vue')
      },
      {
        name: 'ajudaTopic',
        path: ':topicSlug',
        props: true,
        component: () => import('~/pages/ajuda/topic/topic.vue')
      },
      {
        name: 'ajudaQuestion',
        path: ':topicSlug/:questionSlug',
        props: true,
        component: () => import('~/pages/ajuda/question/question.vue')
      }
    ],
    meta: meta.ajuda
  },
  {
    path: '/fale-conosco',
    name: 'faleConosco',
    component: () => import('~/pages/fale-conosco/fale-conosco.vue'),
    meta: { squad: 'efox' }
  },

  // Autenticação
  {
    path: '/auth/confirmar-telefone/:origin/:token',
    name: 'confirmarTelefoneViaLink',
    component: () => import('~/pages/auth/confirmar-telefone-via-link.vue'),
    meta: meta.confirmarTelefoneViaLink,
    props: true
  },
  {
    path: '/auth/redefinir-senha',
    name: 'redefinirSenha',
    component: () => import('~/pages/auth/redefinir-senha/redefinir-senha.vue'),
    meta: { hideFooter: true }
  },
  {
    path: '/auth/:step?/:origin?',
    name: 'login',
    component: noopComponent,
    meta: notMigrated
  },

  // Institucional
  {
    path: '/sul',
    name: 'sobreSul',
    component: () => import('~/pages/campanhas/campanha-sul.vue')
  },
  {
    path: '/sobre',
    name: 'sobre',
    component: () => import('~/pages/sobre.vue')
  },
  {
    path: '/pix',
    name: 'pix',
    component: () => import('~/pages/campanhas/pix.vue')
  },
  {
    path: '/imprensa',
    name: 'imprensa',
    component: () => import('~/pages/imprensa.vue')
  },
  {
    path: '/empresas',
    name: 'empresas',
    component: () => import('~/pages/empresas.vue')
  },
  {
    path: '/empresas/:empresaSlug',
    name: 'empresa',
    props: true,
    component: () => import('~/pages/empresa.vue')
  },
  {
    path: '/whatsapp',
    name: 'whatsapp',
    component: () => import('~/pages/whatsapp.vue')
  },
  {
    path: '/noticias',
    name: 'noticias',
    component: () => import('~/pages/noticias.vue')
  },
  {
    path: '/carreiras',
    name: 'carreiras',
    component: () => import('~/pages/carreiras.vue'),
    meta: {
      taskbar: { hide: true },
      hideNavBottom: true
    }
  },
  {
    path: '/motoristas',
    name: 'motoristas',
    component: () => import('~/pages/motoristas.vue')
  },
  {
    path: '/parceiros',
    name: 'parceiros',
    component: () => import('~/pages/parceiros.vue'),
    meta: {
      taskbar: { hide: true },
      hideNavBottom: true
    }
  },
  {
    path: '/sobre/sustentabilidade',
    name: 'sustentabilidade',
    component: () => import('~/pages/sustentabilidade.vue')
  },
  {
    path: '/sobre/:textSlug',
    name: 'termosEPoliticas',
    props: true,
    component: () => import('~/pages/termos-e-politicas.vue')
  },

  // Conteúdo & SEO
  {
    path: '/destinos',
    name: 'destinos',
    component: () => import('~/pages/destinos.vue')
  },
  {
    path: '/destinos/pontos-turisticos',
    name: 'pontosTuristicos',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/destinos/pontos-turisticos/:uf([a-z][a-z])',
    name: 'pontosTuristicosEstado',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/destinos/pontos-turisticos/:uf([a-z][a-z])/:cidadeSlug',
    name: 'pontosTuristicosCidade',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/destinos/pontos-turisticos/:uf([a-z][a-z])/:cidadeSlug/todos',
    name: 'pontosTuristicosCidadeTodos',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/destinos/pontos-turisticos/:uf([a-z][a-z])/:cidadeSlug/:pontoSlug',
    name: 'pontoTuristico',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/destinos/pontos-turisticos/:categorySlug',
    name: 'pontosTuristicosCategoriaNacional',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/destinos/pontos-turisticos/:categorySlug/:uf([a-z][a-z])',
    name: 'pontosTuristicosCategoriaEstado',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/destinos/pontos-turisticos/:categorySlug/:uf([a-z][a-z])/:cidadeSlug',
    name: 'pontosTuristicosCategoriaCidade',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/destinos/pontos-turisticos/:categorySlug/:subcategorySlug',
    name: 'pontosTuristicosSubcategoriaNacional',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/destinos/pontos-turisticos/:categorySlug/:subcategorySlug/:uf([a-z][a-z])',
    name: 'pontosTuristicosSubcategoriaEstado',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/destinos/pontos-turisticos/:categorySlug/:subcategorySlug/:uf([a-z][a-z])/:cidadeSlug',
    name: 'pontosTuristicosSubcategoriaCidade',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/destinos/:cidadeSlug',
    name: 'destino',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/pontos',
    name: 'pontosDeEmbarque',
    component: () => import('~/pages/pontos-de-embarque.vue')
  },
  {
    path: '/agencias-autorizadas',
    name: 'agenciasAutorizadas',
    component: () =>
      import('~/pages/agencias-autorizadas/agencias-autorizadas.vue')
  },
  {
    path: '/pontos/:uf/:cidadeSlug',
    name: 'pontosDeEmbarqueCidade',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/pontos/:uf/:cidadeSlug/:bairroSlug/:nicknameSlug',
    name: 'pontoDeEmbarque',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/glossario',
    name: 'glossario',
    component: () => import('~/pages/glossario/glossario.vue')
  },
  {
    path: '/glossario/:glossarioItemSlug(.*)',
    name: 'glossarioItem',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/search-redirect',
    name: 'searchRedirect',
    component: () => import('~/pages/search-redirect/search-redirect.vue')
  },

  // Área do usuário
  {
    path: '/perfil',
    name: 'profile',
    component: () => import('~/pages/user/profile/profile.vue'),
    meta: meta.profile
  },
  {
    path: '/perfil/opcoes',
    name: 'profileOptions',
    component: () => import('~/pages/user/profile/profile-options.vue'),
    meta: meta.profileOptions
  },
  {
    path: '/perfil/editar',
    component: () => import('~/pages/user/profile/edit-profile.vue'),
    meta: meta.editProfile,
    children: [
      {
        path: '',
        name: 'editProfileDados',
        component: () =>
          import(
            '~/pages/user/profile/edit-profile-dados/edit-profile-dados.vue'
          ),
        meta: meta.editProfileDados
      },
      {
        path: 'marketing',
        name: 'editProfileMarketing',
        component: () =>
          import(
            '~/pages/user/profile/edit-profile-marketing/edit-profile-marketing.vue'
          ),
        meta: meta.editProfileMarketing
      }
    ]
  },
  {
    path: '/perfil/carteira',
    name: 'carteira',
    component: () => import('~/pages/user/carteira.vue'),
    meta: meta.carteira
  },
  {
    path: '/perfil/formas-pagamento',
    name: 'formasPagamento',
    component: () => import('~/pages/user/formas-pagamento.vue'),
    meta: meta.formasPagamento
  },
  {
    path: '/perfil/notificacoes',
    name: 'notificacoes',
    component: () => import('~/pages/notificacoes.vue'),
    meta: meta.notificacoes
  },
  {
    path: '/perfil/viajantes',
    name: 'viajantes',
    component: () => import('~/pages/viajantes.vue'),
    meta: meta.viajantes
  },
  {
    path: '/perfil/compartilhamento-de-dados',
    name: 'manageLinkedUsers',
    component: () =>
      import('~/pages/user/lista-linked-users/lista-linked-users.vue'),
    meta: meta.manageLinkedUsers
  },
  {
    path: '/perfil/promocoes',
    name: 'promocoes',
    component: () => import('~/pages/user/promocoes.vue'),
    meta: meta.promocoes
  },
  {
    path: '/perfil/sugerir-viagem',
    name: 'sugerirViagem',
    component: () => import('~/pages/sugerir-viagem/sugerir-viagem.vue'),
    meta: meta.sugerirViagem
  },
  {
    path: '/perfil/viagens',
    name: 'viagens',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/perfil/linked_user/:token',
    name: 'confirmLinkedUser',
    component: () =>
      import('~/pages/user/confirm-linked-user/confirm-linked-user.vue'),
    meta: { hideFooter: true, middleware: 'logged-user' },
    props: true
  },
  {
    path: '/perfil/viagens/:id',
    props: true,
    component: () => import('~/pages/user/viagem/viagem.vue'),
    meta: meta.viagem,
    children: [
      { path: '', name: 'viagem', component: noopComponent, meta: notMigrated },
      {
        path: 'nao-reconheco-essa-viagem',
        name: 'optOutTravel',
        component: () =>
          import('~/pages/user/viagem/opt-out-travel/opt-out-travel.vue'),
        meta: meta.narrowLoggedUser
      },
      {
        path: 'desvincular-travel',
        name: 'desvincularTravel',
        props: true,
        component: () =>
          import('~/pages/user/viagem/opt-out-travel/desvincular-travel.vue'),
        meta: meta.narrowLoggedUser
      },
      {
        path: 'alteracao-na-reserva',
        name: 'infoAlteracoes',
        component: () =>
          import('~/pages/user/viagem/info-alteracoes/info-alteracoes.vue'),
        meta: meta.narrowLoggedUser
      },
      {
        path: 'ajuda/perdi-um-item',
        name: 'perdiUmItem',
        component: () =>
          import('~/pages/user/lost-item-on-bus/lost-item-on-bus.vue'),
        meta: meta.narrowLoggedUser
      },
      {
        path: 'ajuda/problemas-com-a-bagagem',
        name: 'problemasComABagagem',
        component: () =>
          import('~/pages/user/problemas-bagagem/problemas-bagagem.vue'),
        meta: meta.problemasBagagem
      },
      {
        path: 'ajuda/nao-consegui-viajar',
        name: 'viagemNoShow',
        component: () =>
          import('~/pages/user/viagem-no-show/viagem-no-show.vue'),
        meta: meta.narrowLoggedUser
      },
      {
        path: 'ajuda/rodoviaria',
        name: 'ajudaMarketplace',
        component: () =>
          import('~/pages/user/viagem/ajuda/marketplace/list/list.vue'),
        meta: meta.narrowLoggedUser
      },
      {
        path: 'ajuda/rodoviaria/atraso-onibus',
        name: 'atrasoOnibusMarketplace',
        component: () =>
          import(
            '~/pages/user/viagem/ajuda/marketplace/atraso-onibus/atraso-onibus.vue'
          ),
        meta: meta.narrowLoggedUser
      },
      {
        path: 'ajuda/rodoviaria/cancelar-viagem',
        name: 'cancelarViagemMarketplace',
        component: () =>
          import(
            '~/pages/user/viagem/ajuda/marketplace/cancelar-viagem/cancelar-viagem.vue'
          ),
        meta: meta.narrowLoggedUser
      },
      {
        path: 'ajuda/rodoviaria/nao-consegui-viajar',
        name: 'naoConseguiViajarMarketplace',
        component: () =>
          import(
            '~/pages/user/viagem/ajuda/marketplace/nao-consegui-viajar/nao-consegui-viajar.vue'
          ),
        meta: meta.narrowLoggedUser
      },
      {
        path: 'ajuda/rodoviaria/acidente',
        name: 'marketplaceAcidente',
        component: () =>
          import('~/pages/user/viagem/ajuda/marketplace/acidente/acidente.vue'),
        meta: meta.narrowLoggedUser
      },
      {
        path: 'ajuda/rodoviaria/reembolso',
        name: 'ajudaReembolsoMarketplace',
        component: () =>
          import(
            '~/pages/user/viagem/ajuda/marketplace/reembolso/reembolso.vue'
          ),
        meta: meta.narrowLoggedUser
      },
      {
        path: 'ajuda/rodoviaria/falha-mecanica',
        name: 'falhaMecanicaMarketplace',
        component: () =>
          import(
            '~/pages/user/viagem/ajuda/marketplace/falha-mecanica/falha-mecanica.vue'
          ),
        meta: meta.narrowLoggedUser
      },
      {
        path: 'ajuda/rodoviaria/empresa-rodoviaria',
        name: 'empresaRodoviariaMarketplace',
        component: noopComponent,
        meta: notMigrated
      },
      {
        path: 'ajuda/rodoviaria/documentos-embarque',
        name: 'documentosEmbarqueMarketplace',
        component: noopComponent,
        meta: notMigrated
      },
      {
        path: 'cancelar/',
        name: 'cancelarReserva',
        component: noopComponent,
        meta: notMigrated
      },
      {
        path: 'cancelada',
        name: 'reservaCancelada',
        component: noopComponent,
        meta: notMigrated
      }
    ]
  },
  {
    path: '/perfil/verificar-cadastro',
    name: 'verificarCadastro',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/perfil/completar-cadastro',
    name: 'completarCadastro',
    component: () => import('~/pages/user/completar-cadastro.vue'),
    meta: meta.completarCadastro
  },
  {
    path: '/perfil/close-friends',
    name: 'MGMListing',
    component: () => import('~/pages/user/mgm/listagem.vue'),
    meta: {
      hideFooter: true,
      middleware: 'logged-user',
      layout: 'narrow'
    }
  },
  {
    path: '/perfil/pesquisa-relacional',
    name: 'pesquisaRelacional',
    component: () =>
      import('~/pages/user/pesquisa-relacional/pesquisa-relacional.vue'),
    meta: meta.pesquisaRelacional
  },
  {
    path: '/viagem/:travel_id/pesquisa-viagem',
    name: 'pesquisaViagem',
    props: true,
    component: () => import('~/pages/user/pesquisa-viagem/pesquisa-viagem.vue'),
    meta: meta.pesquisaViagem
  },
  {
    path: '/viagem/:travelId/pesquisa-nps',
    name: 'pesquisaNps',
    component: () => import('~/pages/user/pesquisa-nps/pesquisa-nps.vue'),
    meta: meta.pesquisaNps,
    props: true
  },
  {
    path: '/perfil/lead-opt-out',
    name: 'leadOptOut',
    component: () => import('~/pages/user/lead-opt-out/lead-opt-out.vue'),
    meta: meta.leadOptOut
  },
  {
    path: '/perfil/confirm-lead-opt-out/:token',
    name: 'confirmLeadOptOut',
    component: () =>
      import('~/pages/user/lead-opt-out/confirm-lead-opt-out.vue'),
    meta: meta.confirmLeadOptOut,
    props: true
  },
  {
    path: '/perfil/retrospectiva', // alterado no nuxt2 para não ter header
    name: 'retrospectiva',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/seguro-adicional/:reserva',
    name: 'seguroAdicional',
    component: () => import('~/pages/seguro-adicional/seguro-adicional.vue'),
    meta: meta.seguroAdicional,
    props: true
  },

  // Páginas relacionadas a viagem do pax, mas que não precisam de autenticação
  {
    path: '/viagem/acompanhar/:reservationCode/:departureDate',
    name: 'acompanharViagem',
    component: noopComponent,
    meta: notMigrated
  },
  {
    path: '/viagem/:travel_id/pesquisa-pos-embarque/:lead_id',
    name: 'pesquisaPosEmbarque',
    component: () =>
      import('~/pages/user/pesquisa-pos-embarque/pesquisa-pos-embarque.vue'),
    meta: meta.pesquisaPosEmbarque
  },
  {
    path: '/viagem/:travel_id/pesquisa-pos-embarque/:lead_id/:copywriting_id',
    name: 'pesquisaPosEmbarqueCopywriting',
    component: () =>
      import('~/pages/user/pesquisa-pos-embarque/pesquisa-pos-embarque.vue'),
    meta: meta.pesquisaPosEmbarque
  },
  {
    path: '/upgrade-poltrona/:travel_id',
    name: 'upgradePoltrona',
    component: noopComponent,
    meta: notMigrated
  },
  // Revendedor
  {
    path: '/revendedor/dashboard/:kind',
    name: 'dashboardPerformance',
    component: () =>
      import(
        '~/pages/revendedor/dashboard-performance/dashboard-performance.vue'
      ),
    meta: meta.dashboardPerformance
  },
  {
    path: '/reserva/vendas',
    name: 'vendasRevendedor',
    component: () => import('~/pages/revendedor/vendas/vendas-revendedor.vue'),
    meta: meta.vendasRevendedor
  },
  {
    path: '/consultar-pax',
    name: 'consultarPax',
    component: () =>
      import('~/pages/revendedor/consulta-pax/consultar-pax.vue'),
    meta: meta.consultarPax
  },
  {
    path: '/revendedor/cupom',
    name: 'RevendedorCupons',
    component: () => import('~/pages/revendedor/cupom/revendedor-cupons.vue'),
    meta: meta.revendedorCupons
  },
  {
    path: '/revendedor/carteira',
    name: 'RevendedorCarteira',
    component: () =>
      import('~/pages/revendedor/carteira/revendedor-carteira.vue'),
    meta: meta.revendedorCarteira
  },
  // Erro global
  {
    path: '/erro',
    name: 'error',
    component: () => import('~/pages/error.vue'),
    meta: meta.error
  }
]

export default routes
