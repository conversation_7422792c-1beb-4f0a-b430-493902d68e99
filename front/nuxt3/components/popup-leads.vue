<template>
  <ada-modal
    :model-value="true"
    class="popup-leads"
    max-width="400px"
    paddless
    @close="close"
  >
    <div class="pl-container">
      <div class="pl-img-container">
        <img
          class="pl-image"
          src="~/assets/images/home/<USER>"
          alt="Viagens para qualquer destino com descontos."
        />
      </div>
      <div class="pl-form-container">
        <div class="pl-form">
          <ada-input
            ref="phoneRef"
            v-model="phone"
            :start-icon="faMagnifyingGlass"
            mask="(00) 00000-0000"
            placeholder="Digite o número do celular"
            label="Celular"
            inputmode="cel"
            class="mb-3"
            maxlength="15"
            :rules="[rules.required, rules.phoneMobileBR]"
            autofocus
          />
          <ada-input
            v-model="emailForm"
            label="Email"
            placeholder="Digite seu email"
            :rules="[rules.email]"
            maxlength="100"
            inputmode="email"
            outline
            class="mb-2"
          />
        </div>
        <div class="mt-2">
          <ada-button class="pl-button" @click="onSaveLead">
            Ativar agora
          </ada-button>
        </div>
        <div class="mb-3 mt-2">
          <ada-button class="pl-button" block transparent @click="close">
            Não quero desconto
          </ada-button>
        </div>
      </div>
    </div>
  </ada-modal>
</template>

<script setup>
import { useCookie, useRoute, useRouter } from '#app'
import { faMagnifyingGlass } from '@fortawesome/pro-light-svg-icons'
import { ref } from 'vue'
import { saveLead, salvaPromoLeadAnon } from '~api/lead.js'
import cookiehelper from '~/helpers/cookiehelper.js'
import EventBus from '~/helpers/eventbus.js'
import { required, email, phoneMobileBR } from '~/helpers/rules.js'

const emit = defineEmits(['close'])

const phoneRef = ref(null)
const phone = ref('')
const emailForm = ref('')
const rules = { required, email, phoneMobileBR }
const cupom = ref('20OFFSOAGORA')
const route = useRoute()
const router = useRouter()

const pushToken = useCookie('pushtoken')
const apnsPushToken = useCookie('apnstoken')

async function onSaveLead() {
  if (!phoneRef.value.validate()) {
    return
  }
  const lead = {
    phone: phone.value,
    email: emailForm.value?.toLowerCase(),
    url: route.fullPath,
    attributes: [
      { key: 'tag', value: `popup-leads-${cupom.value}-${route.name}` }
    ],
    apns_pushtoken: apnsPushToken.value,
    pushtoken: pushToken.value
  }
  EventBus.$emit('popup-lead-cadastrou', lead)
  EventBus.$emit('lead-gerado', { source: 'popup-lead' })
  await saveLead(lead)
  cookiehelper.setItem('promo', cupom.value)
  salvaPromoLeadAnon(cupom.value)

  close()
  router.push({
    name: 'promocaoCode-novo',
    params: {
      code: cupom.value
    }
  })
}
function close() {
  emit('close')
}
</script>

<style lang="scss" scoped>
.popup-leads {
  :deep(.ada-card) {
    background-color: transparent;
    overflow-y: hidden;
  }

  .pl-container {
    .pl-img-container {
      width: 100%;

      .pl-image {
        width: 100%;
        vertical-align: middle;
      }
    }

    .pl-form-container {
      border-bottom-left-radius: $border-radius-lg;
      border-bottom-right-radius: $border-radius-lg;
      background-color: white;
      display: flex;
      flex-direction: column;
      align-items: center;

      .pl-form {
        width: 80%;
        display: flex;
        flex-direction: column;
        padding-top: $spacing-3;

        :deep(.i-label) {
          font-size: 16px;
        }

        @media (min-width: $screen-tablet-min) {
          display: flex;
          align-items: flex-end;
        }
      }
    }
  }
}
</style>
