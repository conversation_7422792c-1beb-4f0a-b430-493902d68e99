<template>
  <ada-card max-width="820">
    <div>
      <p class="text-sm mt-2 color-dark d-flex ai-start">
        O pagamento do boleto pode levar até 2 dias úteis para constar em nosso
        sistema.
      </p>

      <div v-if="parcelas && parcelas.length" class="mt-2">
        <!-- Table Header -->
        <div class="header d-flex fd-row jc-between ai-center fw-bold">
          <p class="info-large text-sm">Ação</p>
          <p class="info-casual text-sm">ID</p>
          <p class="info-casual text-sm">Valor</p>
          <p class="info-casual text-sm">Vencimento</p>
          <p class="info-casual hide-mobile-and-down text-sm">ID Pagamento</p>
        </div>
        <ada-divider class="mb-1" color="grey" />

        <div
          v-for="(parcela, index) in parcelas"
          :key="`row-${parcela.id}${index}`"
        >
          <!-- Table Rows -->
          <div class="d-flex fd-row jc-between ai-center py-1 row-type">
            <div class="info-large">
              <ada-button
                color="primary"
                size="small"
                class="ml-2 btn-small"
                block
                :href="parcela.boleto_url"
                target="_blank"
              >
                Pagar
              </ada-button>
            </div>
            <p class="info-casual text-sm">{{ parcela.id }}</p>
            <p class="info-casual text-sm">{{ real(parcela.value) }}</p>
            <p class="info-casual text-sm">
              {{ dayjs(parcela.boleto_expiration_date).format('DD/MM/YYYY') }}
            </p>
            <p class="info-casual mr-1 text-sm hide-mobile-and-down">
              {{ parcela.old_pagamento_id }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </ada-card>
</template>

<script setup>
import dayjs from 'dayjs'
import { real } from '~/helpers/formatters.js'

defineOptions({
  name: 'ParcelasPendentes'
})

defineProps({
  parcelas: {
    type: Array,
    default: () => []
  }
})
</script>

<style lang="scss" scoped>
.header {
  padding: 10px;
  background-color: rgb(245 245 245);
  border: 1px solid rgb(128 128 128 / 36.3%);
}

.row-type {
  border: 1px solid rgb(128 128 128 / 36.3%);
  margin-bottom: 5px;
  background-color: rgb(250 250 250);
}

.info-casual {
  width: 15vw;

  @media (max-width: $screen-tablet-max) {
    width: 22vw !important;
  }
}

.info-large {
  width: 12vw;

  @media (max-width: $screen-tablet-max) {
    width: 10vw !important;
  }
}

.btn-small {
  text-transform: none;

  @media (width <= 450px) {
    font-size: 13px !important;
    min-width: 50px !important;
    margin: 0;
  }
}

.hide-mobile-and-down {
  @media (max-width: $screen-tablet-max) {
    display: none;
  }
}
</style>
