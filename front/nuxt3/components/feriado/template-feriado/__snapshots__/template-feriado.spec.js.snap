// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Feriado > should render page Feriado 1`] = `
"<div data-v-56ec8f3d="" class="feriado" origem="sao-paulo-sp">
  <div data-v-56ec8f3d="" class="f-content">
    <div data-v-56ec8f3d="" class="ada-container">
      <!-- @slot Conteúdo do container -->
      <header data-v-56ec8f3d="" class="f-header">
        <div data-v-56ec8f3d="" class="ada-image" style="width: 100%; height: 100%; --object-fit: cover;">
          <!--v-if-->
          <!-- @slot Imagem renderizada --><img data-v-56ec8f3d="" class="fh-img" src="background-header-corpus-christi.png">
        </div>
        <div data-v-71a180d9="" data-v-56ec8f3d="" class="ada-card ac-rounded ac-elevation-close ac-straight feriado-search-box f-search-box">
          <!--v-if-->
          <!-- @slot Conteúdo do card -->
          <h2 data-v-71a180d9="" class="title-md">Vai viajar no feriado de Corpus Christi?</h2>
          <p data-v-71a180d9="" class="text-sm">Feriado emendado ou bate-volta, nesse feriado de Corpus Christi a Buser te leva para onde você quiser.</p>
          <div data-v-71a180d9="" class="remote-search force-mobile">
            <form autocomplete="off">
              <div class="rs-cities"><span class="select-city"><div class="ada-float ada-select"><div class="f-reference"><!-- @slot Elemento de referência para o posicionamento do balão flutuante --><div id="origem" class="ada-field s-input rsc-city is-origem ada-input"><label for="origem">Saindo de</label><div class="fc-outlined f-container" style="height: 44px;" data-testid="container"><!-- @slot Espaço inicial --><!-- @slot Espaço inicial --><!-- Espaço incial --><fa-stub icon="[object Object]" class="color-grey ml-2"></fa-stub><!-- @slot Campo de formulário --><input id="origem" placeholder="Cidade de origem" aria-expanded="false" role="combobox" aria-haspopup="listbox" class="i-field"><!-- @slot Espaço final --><!--v-if--><!--v-if--><!-- Barra de carregamento --><!--v-if--></div><!--v-if--></div></div><!--v-if--></div></span><button class="is-button b-color-primary b-transparent b-icon b-rounded b-small ada-button rsc-swap-btn" type="button" aria-label="Inverter origem e destino">
                  <!--v-if-->
                  <!-- @slot Conteúdo renderizado no button -->
                  <fa-stub class="rscsb-desktop" icon="[object Object]"></fa-stub>
                  <fa-stub class="rscsb-mobile" icon="[object Object]"></fa-stub>
                </button><span class="select-city"><div class="ada-float ada-select"><div class="f-reference"><!-- @slot Elemento de referência para o posicionamento do balão flutuante --><div id="destino" class="ada-field s-input rsc-city is-destino ada-input"><label for="destino">Indo para</label><div class="fc-outlined f-container" style="height: 44px;" data-testid="container"><!-- @slot Espaço inicial --><!-- @slot Espaço inicial --><!-- Espaço incial --><fa-stub icon="[object Object]" class="color-grey ml-2"></fa-stub><!-- @slot Campo de formulário --><input id="destino" placeholder="Cidade de destino" aria-expanded="false" role="combobox" aria-haspopup="listbox" class="i-field"><!-- @slot Espaço final --><!--v-if--><!--v-if--><!-- Barra de carregamento --><!--v-if--></div><!--v-if--></div></div><!--v-if--></div></span></div>
              <div class="rs-dates">
                <div class="ada-datepicker">
                  <!-- close on focus  -->
                  <div tabindex="0"></div>
                  <div class="ada-field ada-input"><label for="ada-input-36">Data de ida</label>
                    <div class="fc-outlined f-container" style="height: 44px;" data-testid="container">
                      <!-- @slot Espaço inicial -->
                      <!-- @slot Espaço inicial -->
                      <!-- Espaço inicial -->
                      <fa-stub icon="[object Object]" class="color-grey ml-2"></fa-stub><!-- @slot Campo de formulário --><input id="ada-input-36" placeholder="Ida" readonly="" class="i-field"><!-- @slot Espaço final -->
                      <!--v-if-->
                      <!--v-if-->
                      <!-- Barra de carregamento -->
                      <!--v-if-->
                    </div>
                    <!--v-if-->
                  </div><span></span>
                </div>
                <div class="ada-datepicker">
                  <!-- close on focus  -->
                  <div tabindex="0"></div>
                  <div class="ada-field ada-input"><label for="ada-input-44">Data de volta</label>
                    <div class="fc-outlined f-container" style="height: 44px;" data-testid="container">
                      <!-- @slot Espaço inicial -->
                      <!-- @slot Espaço inicial -->
                      <!-- Espaço inicial -->
                      <fa-stub icon="[object Object]" class="color-grey ml-2"></fa-stub><!-- @slot Campo de formulário --><input id="ada-input-44" placeholder="Volta" readonly="" class="i-field"><!-- @slot Espaço final -->
                      <!--v-if-->
                      <!--v-if-->
                      <!-- Barra de carregamento -->
                      <!--v-if-->
                    </div>
                    <!--v-if-->
                  </div><span></span>
                </div>
              </div><button class="is-button b-color-primary b-block ada-button rs-submit" type="submit" aria-label="Buscar">
                <!--v-if-->
                <!-- @slot Conteúdo renderizado no button -->
                <fa-stub icon="[object Object]" class="rss-icon"></fa-stub><span class="rss-text">Buscar</span>
              </button>
            </form>
            <!--v-if-->
          </div>
        </div>
        <div data-v-9bdbc7d9="" data-v-56ec8f3d="" class="search-change-location f-change-location">
          <p data-v-9bdbc7d9="" class="scl-content"> <button data-v-9bdbc7d9="" class="is-link b-small l-small ada-button fw-600" type="button">
              <!--v-if-->
              <!-- @slot Conteúdo renderizado no button -->Selecione uma cidade
            </button>
            <fa-stub data-v-9bdbc7d9="" class="sclc-expand-icon" icon="[object Object]"></fa-stub>
          </p><!-- Não trocar pelo \`<popup>\` pois irá cortar o menu flutuante do <select-city>  -->
          <!--v-if-->
        </div>
      </header>
    </div>
    <div data-v-ac6751af="" data-v-56ec8f3d="" class="mb-7">
      <div data-v-6f7f40e0="" data-v-ac6751af="" class="menor-preco-skeleton">
        <div data-v-6f7f40e0="" class="ada-container mps-top">
          <!-- @slot Conteúdo do container -->
          <div data-v-6f7f40e0="" class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
            <div style="width: 100%; max-width: 100%; border-radius: 1px;" class="is-title-md fw-600 s-item" data-name="title-md" data-testid="skeleton-item">
              <!-- @slot Espaço para aninhamento de skeletons -->
            </div>
          </div>
          <div data-v-6f7f40e0="" class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
            <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 47px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
              <!-- @slot Espaço para aninhamento de skeletons -->
            </div>
          </div>
        </div>
        <div data-v-6f7f40e0="" class="ada-container px-0">
          <!-- @slot Conteúdo do container -->
          <section data-v-6f7f40e0="" class="ada-carousel">
            <ul style="--columns-xs: 1; --columns-sm: 1; --columns-md: 2; --columns-lg: 3; --columns-xl: 3;" class="c-container" tabindex="0">
              <!-- @slot Lista de componentes \`ada-carousel-item\` -->
              <li data-v-6f7f40e0="" class="ada-carousel-item mr-2">
                <!-- @slot Conteúdo do item -->
                <div data-v-6f7f40e0="" class="mb-3 d-flex ai-center">
                  <div data-v-6f7f40e0="" class="ada-skeleton w-auto" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 86px; max-width: 100%; border-radius: 8px; height: 86px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                  <div data-v-6f7f40e0="" class="ada-skeleton ml-2" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 14px; --length: 15ch;" class="has-length s-item" data-name="text-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                </div>
                <div data-v-6f7f40e0="" class="mb-3 d-flex ai-center">
                  <div data-v-6f7f40e0="" class="ada-skeleton w-auto" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 86px; max-width: 100%; border-radius: 8px; height: 86px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                  <div data-v-6f7f40e0="" class="ada-skeleton ml-2" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 14px; --length: 15ch;" class="has-length s-item" data-name="text-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                </div>
                <div data-v-6f7f40e0="" class="mb-3 d-flex ai-center">
                  <div data-v-6f7f40e0="" class="ada-skeleton w-auto" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 86px; max-width: 100%; border-radius: 8px; height: 86px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                  <div data-v-6f7f40e0="" class="ada-skeleton ml-2" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 14px; --length: 15ch;" class="has-length s-item" data-name="text-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                </div>
              </li>
              <li data-v-6f7f40e0="" class="ada-carousel-item mr-2">
                <!-- @slot Conteúdo do item -->
                <div data-v-6f7f40e0="" class="mb-3 d-flex ai-center">
                  <div data-v-6f7f40e0="" class="ada-skeleton w-auto" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 86px; max-width: 100%; border-radius: 8px; height: 86px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                  <div data-v-6f7f40e0="" class="ada-skeleton ml-2" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 14px; --length: 15ch;" class="has-length s-item" data-name="text-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                </div>
                <div data-v-6f7f40e0="" class="mb-3 d-flex ai-center">
                  <div data-v-6f7f40e0="" class="ada-skeleton w-auto" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 86px; max-width: 100%; border-radius: 8px; height: 86px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                  <div data-v-6f7f40e0="" class="ada-skeleton ml-2" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 14px; --length: 15ch;" class="has-length s-item" data-name="text-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                </div>
                <div data-v-6f7f40e0="" class="mb-3 d-flex ai-center">
                  <div data-v-6f7f40e0="" class="ada-skeleton w-auto" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 86px; max-width: 100%; border-radius: 8px; height: 86px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                  <div data-v-6f7f40e0="" class="ada-skeleton ml-2" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 14px; --length: 15ch;" class="has-length s-item" data-name="text-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                </div>
              </li>
              <li data-v-6f7f40e0="" class="ada-carousel-item mr-2">
                <!-- @slot Conteúdo do item -->
                <div data-v-6f7f40e0="" class="mb-3 d-flex ai-center">
                  <div data-v-6f7f40e0="" class="ada-skeleton w-auto" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 86px; max-width: 100%; border-radius: 8px; height: 86px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                  <div data-v-6f7f40e0="" class="ada-skeleton ml-2" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 14px; --length: 15ch;" class="has-length s-item" data-name="text-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                </div>
                <div data-v-6f7f40e0="" class="mb-3 d-flex ai-center">
                  <div data-v-6f7f40e0="" class="ada-skeleton w-auto" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 86px; max-width: 100%; border-radius: 8px; height: 86px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                  <div data-v-6f7f40e0="" class="ada-skeleton ml-2" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 14px; --length: 15ch;" class="has-length s-item" data-name="text-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                </div>
                <div data-v-6f7f40e0="" class="mb-3 d-flex ai-center">
                  <div data-v-6f7f40e0="" class="ada-skeleton w-auto" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 86px; max-width: 100%; border-radius: 8px; height: 86px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                  <div data-v-6f7f40e0="" class="ada-skeleton ml-2" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 14px; --length: 15ch;" class="has-length s-item" data-name="text-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                </div>
              </li>
            </ul>
            <!--
      @slot Botão navegação anterior
      @binding {Object} on - Evento de click
    -->
            <!--v-if-->
            <!--
      @slot Botão navegação próximo
      @binding {Object} on - Evento de click
     -->
            <!--v-if-->
          </section>
        </div>
      </div>
    </div>
    <div data-v-072cc83d="" data-v-56ec8f3d="" class="ada-container secao-oferta-trechos">
      <!-- @slot Conteúdo do container -->
      <div data-v-072cc83d="" class="ada-skeleton sotc-title" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
        <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 18ch;" class="is-title-md fw-600 has-length s-item" data-name="title-md" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
      </div>
      <div data-v-072cc83d="" class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
        <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 12ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
        <div style="width: 8px; max-width: 100%; border-radius: 1px; height: 8px; visibility: hidden;" class="s-item" data-name="spacer" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
      </div>
      <div data-v-072cc83d="" class="ada-skeleton sot-cards" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 24px;">
        <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 100%; padding: 16px; min-height: 168px;" class="s-item" data-name="card" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
        <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 100%; padding: 16px; min-height: 168px;" class="s-item" data-name="card" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
        <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 100%; padding: 16px; min-height: 168px;" class="s-item" data-name="card" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
      </div>
    </div>
    <lazy-hydrate-stub data-v-56ec8f3d="" when-visible="">
      <div data-v-56ec8f3d="" class="ada-container mt-6">
        <!-- @slot Conteúdo do container -->
        <div data-v-beffd9cd="" data-v-832a7cd9="" data-v-56ec8f3d="" class="secao-destinos-base" loading="true">
          <section data-v-beffd9cd="" class="sdb-section">
            <div data-v-beffd9cd="" class="ada-container sdbc-header px-0">
              <!-- @slot Conteúdo do container -->
              <h3 data-v-beffd9cd="" class="sdbch-title title-sm">Destinos recomendados</h3>
              <div data-v-285969a1="" data-v-beffd9cd="" class="ada-toggleable descubra-filter-categories sdbch-categories" style="display: none;">
                <!-- @slot Lista de componentes \`ada-toggleable-item\` -->
                <div data-v-285969a1="" class="ada-toggleable-item">
                  <!--
      @slot Conteúdo do ada-toggleable-item.

      @binding \`isActive\` - Estado do \`ada-toggleable-item\`
      @binding \`toggle\`- Função para alternar o estado do \`ada-toggleable-item\`
     --><button data-v-285969a1="" class="ada-chip c-primary c-selected c-rounded c-small" type="button">
                    <!-- @slot Espaço inicial -->
                    <fa-stub icon="[object Object]"></fa-stub><span class="c-content"><!-- @slot Conteúdo principal -->Todos<span class="cc-caption"><!-- @slot Legenda auxiliar --></span></span><!-- @slot Espaço final -->
                  </button>
                </div>
                <div data-v-285969a1="" class="ada-toggleable-item">
                  <!--
      @slot Conteúdo do ada-toggleable-item.

      @binding \`isActive\` - Estado do \`ada-toggleable-item\`
      @binding \`toggle\`- Função para alternar o estado do \`ada-toggleable-item\`
     --><button data-v-285969a1="" class="ada-chip c-primary c-rounded c-small" type="button">
                    <!-- @slot Espaço inicial -->
                    <fa-stub icon="[object Object]"></fa-stub><span class="c-content"><!-- @slot Conteúdo principal -->Menor preço<span class="cc-caption"><!-- @slot Legenda auxiliar --></span></span><!-- @slot Espaço final -->
                  </button>
                </div>
                <div data-v-285969a1="" class="ada-toggleable-item">
                  <!--
      @slot Conteúdo do ada-toggleable-item.

      @binding \`isActive\` - Estado do \`ada-toggleable-item\`
      @binding \`toggle\`- Função para alternar o estado do \`ada-toggleable-item\`
     --><button data-v-285969a1="" class="ada-chip c-primary c-rounded c-small" type="button">
                    <!-- @slot Espaço inicial -->
                    <fa-stub icon="[object Object]"></fa-stub><span class="c-content"><!-- @slot Conteúdo principal -->Bate e volta<span class="cc-caption"><!-- @slot Legenda auxiliar --></span></span><!-- @slot Espaço final -->
                  </button>
                </div>
              </div>
            </div>
            <div data-v-beffd9cd="" class="">
              <div data-v-832a7cd9="" class="secao-destinos-feriado">
                <div data-v-832a7cd9="">
                  <div class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                    <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 180px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                  <div class="ada-skeleton mt-1" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 16px; --length: 20ch;" class="has-length s-item" data-name="text-md" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                </div>
                <div data-v-832a7cd9="">
                  <div class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                    <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 180px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                  <div class="ada-skeleton mt-1" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 16px; --length: 20ch;" class="has-length s-item" data-name="text-md" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                </div>
                <div data-v-832a7cd9="">
                  <div class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                    <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 180px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                  <div class="ada-skeleton mt-1" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                    <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 16px; --length: 20ch;" class="has-length s-item" data-name="text-md" data-testid="skeleton-item">
                      <!-- @slot Espaço para aninhamento de skeletons -->
                    </div>
                  </div>
                </div>
              </div>
              <!--v-if-->
            </div>
          </section>
          <div data-v-beffd9cd="" class="ada-container sdb-button px-0">
            <!-- @slot Conteúdo do container -->
            <router-link-stub data-v-beffd9cd="" class="is-button b-color-primary b-outlined ada-button sdb-button" to="[object Object]">
              <!--v-if-->
              <!-- @slot Conteúdo renderizado no button -->Descubra <fa-stub data-v-beffd9cd="" icon="[object Object]" class="ml-1"></fa-stub>
            </router-link-stub>
          </div>
        </div>
      </div>
    </lazy-hydrate-stub>
  </div>
  <div data-v-56ec8f3d="" class="ada-container mb-6">
    <!-- @slot Conteúdo do container -->
    <section data-v-56ec8f3d="" class="f-section-1">
      <div data-v-56ec8f3d="" class="mt-6 ta-center">
        <div data-v-56ec8f3d="" class="ada-image fs1-img" style="width: 100%; height: 100%; --object-fit: cover;">
          <!--v-if-->
          <!-- @slot Imagem renderizada --><img data-v-56ec8f3d="" src="background-header-corpus-christi.png">
        </div>
        <h3 data-v-56ec8f3d="" class="title-lg">Viaje com a Buser no feriado</h3>
      </div>
      <div data-v-56ec8f3d="" class="title text">
        <ul data-v-56ec8f3d="" class="f-info-buser">
          <h4 data-v-56ec8f3d="" class="color-brand">Ofertas Exclusivas da Buser</h4><!-- eslint-disable-next-line vue/no-v-html -->
          <li data-v-56ec8f3d="">Aproveite as melhores promoções em viagens de ônibus com a Buser! Com nosso sistema exclusivo de cupons de desconto, você pode garantir uma viagem barata e econômica para o seu próximo destino.<br>Além disso, a Buser preza pela segurança dos passageiros, proporcionando uma viagem com desconto sem abrir mão da qualidade e do conforto.</li>
        </ul>
      </div>
      <div data-v-56ec8f3d="" class="title text">
        <ul data-v-56ec8f3d="" class="f-info-buser">
          <h4 data-v-56ec8f3d="" class="color-brand">Economize Enquanto Viaja</h4><!-- eslint-disable-next-line vue/no-v-html -->
          <li data-v-56ec8f3d="">A Buser está revolucionando a forma como você viaja de ônibus, oferecendo promoções imperdíveis para viagens com desconto.<br>Não se preocupe com gastos excessivos, pois a Buser garante que você possa desfrutar de uma viagem barata e confortável. Nossa prioridade é a segurança dos passageiros, e é por isso que proporcionamos não apenas uma viagem com desconto, mas também um ambiente seguro e protegido para sua jornada.</li>
        </ul>
      </div>
    </section>
    <lazy-hydrate-stub data-v-56ec8f3d="" when-visible="">
      <div data-v-beffd9cd="" data-v-56ec8f3d="" class="secao-destinos-base" center-button-exit="true">
        <section data-v-beffd9cd="" class="sdb-section">
          <div data-v-beffd9cd="" class="ada-container sdbc-header px-0">
            <!-- @slot Conteúdo do container -->
            <h3 data-v-beffd9cd="" class="sdbch-title title-sm">Destino em alta</h3>
            <div data-v-285969a1="" data-v-beffd9cd="" class="ada-toggleable descubra-filter-categories sdbch-categories">
              <!-- @slot Lista de componentes \`ada-toggleable-item\` -->
              <div data-v-285969a1="" class="ada-toggleable-item">
                <!--
      @slot Conteúdo do ada-toggleable-item.

      @binding \`isActive\` - Estado do \`ada-toggleable-item\`
      @binding \`toggle\`- Função para alternar o estado do \`ada-toggleable-item\`
     --><button data-v-285969a1="" class="ada-chip c-primary c-rounded c-small" type="button">
                  <!-- @slot Espaço inicial -->
                  <fa-stub icon="[object Object]"></fa-stub><span class="c-content"><!-- @slot Conteúdo principal -->Recomendados<span class="cc-caption"><!-- @slot Legenda auxiliar --></span></span><!-- @slot Espaço final -->
                </button>
              </div>
              <div data-v-285969a1="" class="ada-toggleable-item">
                <!--
      @slot Conteúdo do ada-toggleable-item.

      @binding \`isActive\` - Estado do \`ada-toggleable-item\`
      @binding \`toggle\`- Função para alternar o estado do \`ada-toggleable-item\`
     --><button data-v-285969a1="" class="ada-chip c-primary c-rounded c-small" type="button">
                  <!-- @slot Espaço inicial -->
                  <fa-stub icon="[object Object]"></fa-stub><span class="c-content"><!-- @slot Conteúdo principal -->Menor preço<span class="cc-caption"><!-- @slot Legenda auxiliar --></span></span><!-- @slot Espaço final -->
                </button>
              </div>
              <div data-v-285969a1="" class="ada-toggleable-item">
                <!--
      @slot Conteúdo do ada-toggleable-item.

      @binding \`isActive\` - Estado do \`ada-toggleable-item\`
      @binding \`toggle\`- Função para alternar o estado do \`ada-toggleable-item\`
     --><button data-v-285969a1="" class="ada-chip c-primary c-rounded c-small" type="button">
                  <!-- @slot Espaço inicial -->
                  <fa-stub icon="[object Object]"></fa-stub><span class="c-content"><!-- @slot Conteúdo principal -->Bate e volta<span class="cc-caption"><!-- @slot Legenda auxiliar --></span></span><!-- @slot Espaço final -->
                </button>
              </div>
            </div>
          </div>
          <div data-v-beffd9cd="" class="">
            <div data-v-56ec8f3d="" class="f-destinos-em-alta">
              <div data-v-56ec8f3d="">
                <div class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 180px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
                <div class="ada-skeleton mt-1" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 16px; --length: 20ch;" class="has-length s-item" data-name="text-md" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
              </div>
              <div data-v-56ec8f3d="">
                <div class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 180px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
                <div class="ada-skeleton mt-1" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 16px; --length: 20ch;" class="has-length s-item" data-name="text-md" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
              </div>
              <div data-v-56ec8f3d="">
                <div class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 180px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
                <div class="ada-skeleton mt-1" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 16px; --length: 20ch;" class="has-length s-item" data-name="text-md" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
              </div>
              <div data-v-56ec8f3d="">
                <div class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 180px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
                <div class="ada-skeleton mt-1" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 16px; --length: 20ch;" class="has-length s-item" data-name="text-md" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
              </div>
              <div data-v-56ec8f3d="">
                <div class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 180px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
                <div class="ada-skeleton mt-1" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 16px; --length: 20ch;" class="has-length s-item" data-name="text-md" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
              </div>
              <div data-v-56ec8f3d="">
                <div class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 180px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
                <div class="ada-skeleton mt-1" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 16px; --length: 20ch;" class="has-length s-item" data-name="text-md" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
              </div>
              <div data-v-56ec8f3d="">
                <div class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 180px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
                <div class="ada-skeleton mt-1" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 16px; --length: 20ch;" class="has-length s-item" data-name="text-md" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
              </div>
              <div data-v-56ec8f3d="">
                <div class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 180px; padding: 16px;" class="s-item" data-name="card" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
                <div class="ada-skeleton mt-1" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 8px;">
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 10ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                  <div style="width: 100%; max-width: 100%; border-radius: 1px; height: 16px; --length: 20ch;" class="has-length s-item" data-name="text-md" data-testid="skeleton-item">
                    <!-- @slot Espaço para aninhamento de skeletons -->
                  </div>
                </div>
              </div>
            </div>
            <!--v-if-->
          </div>
        </section>
        <div data-v-beffd9cd="" class="ada-container sdb-button px-0">
          <!-- @slot Conteúdo do container -->
          <router-link-stub data-v-beffd9cd="" class="is-button b-color-primary b-outlined ada-button sdb-button" to="[object Object]">
            <!--v-if-->
            <!-- @slot Conteúdo renderizado no button -->Veja outros destinos <fa-stub data-v-beffd9cd="" icon="[object Object]" class="ml-1"></fa-stub>
          </router-link-stub>
        </div>
      </div>
    </lazy-hydrate-stub>
    <div data-v-72ba71fc="" data-v-56ec8f3d="" class="ada-container mt-6 p-0">
      <!-- @slot Conteúdo do container -->
      <div data-v-72ba71fc="" class="ada-card ac-outlined ac-rounded ac-soft descubra-destinos">
        <!--v-if-->
        <!-- @slot Conteúdo do card -->
        <div data-v-72ba71fc="" class="ada-image ai-rounded dd-image" style="width: 100%; height: 100%; --object-fit: cover;">
          <!--v-if-->
          <!-- @slot Imagem renderizada --><img data-v-72ba71fc="" src="/assets/images/feriado/outros-destinos.png" alt="Curtindo o feriado: partida de futebol na praia ao entardecer.">
        </div>
        <div data-v-72ba71fc="">
          <h3 data-v-72ba71fc="" class="title-md mb-2">Vários destinos para você conhecer</h3>
          <p data-v-72ba71fc="" class="mb-2"> Conheça todos os grupos de viagem formados na Buser, sua próxima viagem está aqui! </p>
          <router-link-stub data-v-72ba71fc="" class="is-button b-color-primary ada-button" to="[object Object]">
            <!--v-if-->
            <!-- @slot Conteúdo renderizado no button --> Explore outros destinos
          </router-link-stub>
        </div>
      </div>
    </div>
    <section data-v-56ec8f3d="" class="f-new-destinations">
      <h3 data-v-56ec8f3d="" class="mb-4"> Reserve sua viagem com a Buser e viaje para novos destinos </h3>
      <ul data-v-56ec8f3d="" class="fnd-infos">
        <li data-v-56ec8f3d="" class="title text">
          <h4 data-v-56ec8f3d="" class="mb-half color-brand">Sua reserva na sua mão</h4><!-- eslint-disable-next-line vue/no-v-html -->
          <p data-v-56ec8f3d="">Gerencie suas viagens e tire dúvidas sobre os eu embarque direto no app. Altere ou remarque sua viagem com flexibilidade.</p>
        </li>
        <li data-v-56ec8f3d="" class="title text">
          <h4 data-v-56ec8f3d="" class="mb-half color-brand">Viagem de ônibus</h4><!-- eslint-disable-next-line vue/no-v-html -->
          <p data-v-56ec8f3d="">Você encontra viagens de fretamento e passagem de ônibus para viajar dia 30 de Maio com preços baixos e destinos variados.</p>
        </li>
        <li data-v-56ec8f3d="" class="title text">
          <h4 data-v-56ec8f3d="" class="mb-half color-brand">Explore novos destinos</h4><!-- eslint-disable-next-line vue/no-v-html -->
          <p data-v-56ec8f3d="">Encontre novos destinos com preços imperdíveis. Crie novas histórias com experiências inesquecíveis viajando pelo Brasil. Descubra seu próximo destino.</p>
        </li>
      </ul>
    </section>
  </div>
</div>"
`;
