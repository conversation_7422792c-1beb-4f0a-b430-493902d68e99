import { mount } from '@vue/test-utils'
import pagination from './pagination.vue'

const mountPagination = (props) => {
  return mount(pagination, {
    propsData: props
  })
}

describe('pagination', () => {
  it('should render correctly', () => {
    const wrapper = mountPagination({ actualPage: 1, totalPages: 2 })
    expect(wrapper.html()).toMatchSnapshot()
  })

  it('should disable prev if its the first page', () => {
    const wrapper = mountPagination({ actualPage: 1, totalPages: 2 })
    const prev = wrapper.find('.p-button-prev')

    expect(prev.exists()).toBe(true)
    expect(prev.attributes().disabled).toBeDefined()
  })

  it('should disable next if its the last page', () => {
    const wrapper = mountPagination({ actualPage: 2, totalPages: 2 })
    const next = wrapper.find('.p-button-next')

    expect(next.exists()).toBe(true)
    expect(next.attributes().disabled).toBeDefined()
  })

  it("should disable both buttons if there's only one page", () => {
    const wrapper = mountPagination({ actualPage: 1, totalPages: 1 })
    const next = wrapper.find('.p-button-next')
    const prev = wrapper.find('.p-button-prev')

    expect(next.exists()).toBe(true)
    expect(prev.exists()).toBe(true)
    expect(next.attributes().disabled).toBeDefined()
    expect(prev.attributes().disabled).toBeDefined()
  })

  it('should enable both buttons if actualPage its neither the last or first page', () => {
    const wrapper = mountPagination({ actualPage: 3, totalPages: 5 })
    const next = wrapper.find('.p-button-next')
    const prev = wrapper.find('.p-button-prev')

    expect(next.exists()).toBe(true)
    expect(prev.exists()).toBe(true)
    expect(next.attributes().disabled).toBeUndefined()
    expect(prev.attributes().disabled).toBeUndefined()
  })
})
