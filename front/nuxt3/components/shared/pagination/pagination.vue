<template>
  <nav class="pagination">
    <slot name="prev">
      <ada-button
        :disabled="actualPage <= 1"
        class="p-button-prev"
        @click="$emit('prev')"
      >
        Anterior
      </ada-button>
    </slot>

    <p class="px-2">Página {{ actualPage }} de {{ totalPages }}</p>

    <slot name="next">
      <ada-button
        :disabled="actualPage >= totalPages"
        class="p-button-next"
        @click="$emit('next')"
      >
        Próximo
      </ada-button>
    </slot>
  </nav>
</template>

<script>
export default {
  name: 'Pagination',
  props: {
    actualPage: {
      type: Number,
      required: true
    },
    totalPages: {
      type: Number,
      required: true
    }
  },
  emits: ['prev', 'next']
}
</script>

<style lang="scss" scoped>
.pagination {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
