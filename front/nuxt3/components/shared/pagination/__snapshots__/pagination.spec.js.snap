// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`pagination > should render correctly 1`] = `
"<nav data-v-6b0833d5="" class="pagination"><button data-v-6b0833d5="" class="is-button b-color-primary b-disabled ada-button p-button-prev" type="button" disabled="">
    <!--v-if-->
    <!-- @slot Conteúdo renderizado no button --> Anterior
  </button>
  <p data-v-6b0833d5="" class="px-2">Página 1 de 2</p><button data-v-6b0833d5="" class="is-button b-color-primary ada-button p-button-next" type="button">
    <!--v-if-->
    <!-- @slot Conteúdo renderizado no button --> Próximo
  </button>
</nav>"
`;
