<template>
  <component :is="Tag" v-once class="html-view" />
</template>

<script setup>
import { h } from 'vue'

const props = defineProps({
  html: {
    type: String,
    required: true
  },
  element: {
    type: String,
    default: 'div'
  }
})

// https://github.com/vuejs/core/issues/12520#issuecomment-2921196085
const Tag = h(props.element, { innerHTML: props.html })
</script>

<style lang="scss">
.html-view {
  &,
  * {
    font-size: 1rem;
    line-height: 24px;
  }

  p {
    margin-bottom: $spacing-1;
  }

  ul,
  ol {
    display: block;
    margin: 1em 0;
    padding-left: $spacing-5;
  }

  ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }

  li {
    display: list-item;
  }

  img {
    max-width: 100%;
  }
}
</style>
