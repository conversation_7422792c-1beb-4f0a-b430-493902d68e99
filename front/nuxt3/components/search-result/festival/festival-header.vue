<template>
  <header class="festival-header">
    <div class="fh-container" :style="imagesVars">
      <ada-container class="my-4">
        <p class="title-xs">
          {{ festival.headerTexts.auxTitle }}
        </p>
        <h1 class="title-md my-1">
          {{ festival.headerTexts.title }}
        </h1>
        <h2 class="text-md fw-400 hide-tablet">
          {{ festival.headerTexts.subtitle }}
        </h2>
      </ada-container>
    </div>

    <ada-container class="fh-container-searchbox">
      <remote-search-box
        page-result-name="searchPageFestival"
        :extra-params="extraParams"
      />
    </ada-container>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Festival } from '~/assets/js/festivais.ts'
import remoteSearchBox from '~/components/shared/remote-search-box.vue'
const props = withDefaults(
  defineProps<{
    festival: Festival
    extraParams: object
  }>(),
  {
    extraParams: () => ({})
  }
)

const imagesVars = computed(() => {
  const base: {
    '--bg-mobile': string
    '--bg-desktop': string
    'background-blend-mode'?: string
    'background-color'?: string
  } = {
    '--bg-mobile': `url(${props.festival.imageBackgroundMobile})`,
    '--bg-desktop': `url(${props.festival.imageBackground})`
  }

  base['background-blend-mode'] = 'multiply'
  base['background-color'] = '#0000004D'

  return base
})
</script>

<style lang="scss" scoped>
.festival-header {
  position: relative;
  height: 380px;
  display: flex;
  flex-direction: column;
  align-items: center;

  @media (min-width: $screen-tablet-min) {
    height: 260px;
  }

  .fh-container {
    align-self: normal;
    color: $color-white;
    height: 212px;
    background-size: cover;
    background-repeat: no-repeat;
    background-image: var(--bg-mobile);

    @media (min-width: $screen-tablet-min) {
      background-image: var(--bg-desktop);
    }

    .fhc-main {
      justify-content: center;
      display: flex;
      max-height: 160px;
      align-items: center;

      @media (min-width: $screen-tablet-min) {
        gap: $spacing-4;
      }

      .fhcm-text {
        letter-spacing: 0;
        align-self: center;
        align-content: center;
      }
    }
  }

  .fh-container-searchbox {
    height: min-content;
    position: absolute;
    bottom: 0;
  }
}
</style>
