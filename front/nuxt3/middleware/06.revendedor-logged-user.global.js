import { defineNuxtRouteMiddleware, navigateTo, useRuntimeConfig } from '#app'
import { useUserProfile } from '~/composables/user-profile.js'
import { useRevendedorStore } from '~/stores/revendedor.js'
import { useSessionStore } from '~/stores/session.js'

export default defineNuxtRouteMiddleware((to) => {
  const revendedorStore = useRevendedorStore()
  const isRevendedorSite = revendedorStore.isRevendedorSite

  if (!isRevendedorSite || to.name === 'login') return

  const sessionStore = useSessionStore()
  if (!sessionStore.loggedIn) {
    const config = useRuntimeConfig()
    return navigateTo(
      `${config.public.nuxt2BaseUrl}/auth?${new URLSearchParams({ next: to.path }).toString()}`,
      { external: true }
    )
  }

  if (!revendedorStore.isRevendedor) {
    useUserProfile().handleLogout()
  }
})
