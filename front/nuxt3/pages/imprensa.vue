<template>
  <div class="imprensa">
    <page-header
      height="300px"
      subtitle-class="mt-3 mb-1 fw-bold"
      :background="background"
    >
      <template #breadcrumbs>
        <ada-breadcrumbs :items="breadcrumbs" />
      </template>

      <div class="pb-3">
        <h1 class="title-lg pt-5 pb-2">Sala de imprensa</h1>
        <p>
          <PERSON><PERSON> as novidades da Buser e veja como entrar em contato com a gente.
        </p>
      </div>
    </page-header>

    <main class="i-content">
      <section
        v-if="pressReleases?.items?.length"
        ref="releases"
        class="ic-section ic-releases"
      >
        <ada-container>
          <h2 class="title-md mb-5">Press Releases</h2>

          <div class="icr-content">
            <ada-card
              v-for="release in pressReleases.items"
              :key="release.id"
              outlined
              rounded
              class="i-card"
            >
              <ada-image height="191px">
                <a
                  :href="release.link"
                  target="_blank"
                  rel="noopener external nofollow"
                  class="ic-image-link"
                >
                  <img
                    v-lazy-load
                    :data-src="getImageUrl(release)"
                    :alt="getImageAlt(release)"
                    width="312"
                    height="191"
                    class="ic-image"
                  />
                </a>
              </ada-image>

              <a
                :href="release.link"
                target="_blank"
                rel="noopener external nofollow"
                class="ic-content"
              >
                <span class="fw-bold mb-1">
                  {{ release.title }}
                </span>

                <html-view
                  class="text-md"
                  element="span"
                  :html="release.description"
                />
              </a>
            </ada-card>
          </div>

          <pagination
            v-if="pressReleases.count_pages > 1"
            class="mt-2"
            :actual-page="currentPage"
            :total-pages="pressReleases.count_pages"
          >
            <template #prev>
              <ada-button :to="previousPageRoute" :disabled="!hasPreviousPage">
                Anterior
              </ada-button>
            </template>
            <template #next>
              <ada-button :to="nextPageRoute" :disabled="!hasNextPage">
                Próxima
              </ada-button>
            </template>
          </pagination>
        </ada-container>
      </section>

      <section class="bg-grey-lightest ic-section ic-images">
        <ada-container>
          <div class="mb-5">
            <h2 class="title-md mb-1">Banco de imagens</h2>
            <p>
              Acesse nosso Guia da Marca e faça download dos logos, fotos e
              vídeos institucionais.
            </p>
          </div>

          <div class="ici-content">
            <ada-card outlined rounded class="i-card">
              <ada-image height="191px">
                <img
                  v-lazy-load
                  data-src="~/assets/images/buser-logo-fundo-rosa.png"
                  alt="Logo da Buser"
                  width="312"
                  height="191"
                  class="ic-image"
                />
              </ada-image>

              <div class="p-3">
                <p>
                  Confira nossas diretrizes e orientações de aplicação da marca
                </p>
                <ada-button
                  outlined
                  block
                  href="https://drive.google.com/drive/folders/1OQrukANRM4pwd-zAiF2DSRYatwq1njSV"
                  target="_blank"
                  rel="noopener external nofollow"
                  class="mt-3"
                >
                  Guia de marca e logos
                </ada-button>
              </div>
            </ada-card>

            <ada-card outlined rounded class="i-card">
              <ada-image height="191px">
                <img
                  v-lazy-load
                  data-src="~/assets/images/imprensa/onibus.png"
                  alt="Ônibus da Buser"
                  width="312"
                  height="191"
                  class="ic-image"
                />
              </ada-image>

              <div class="p-3">
                <p>
                  Veja opções de imagens e vídeos para ilustrar seu conteúdo
                </p>
                <ada-button
                  outlined
                  block
                  href="https://drive.google.com/drive/folders/1uMdQrQfu9GwmeHYrgpcjho4J8YPERgqb"
                  target="_blank"
                  rel="noopener external nofollow"
                  class="mt-3"
                >
                  Fotos e vídeos institucionais
                </ada-button>
              </div>
            </ada-card>

            <ada-card outlined rounded class="i-card">
              <ada-image height="191px">
                <img
                  v-lazy-load
                  data-src="~/assets/images/imprensa/app.png"
                  alt="Pessoa utilizando o aplicativo da Buser"
                  width="312"
                  height="191"
                  class="ic-image"
                />
              </ada-image>

              <div class="p-3">
                <p>Conheça nosso aplicativo e baixe fotos em alta resolução</p>
                <ada-button
                  outlined
                  block
                  href="https://drive.google.com/drive/folders/16FWSU5mqcZdr0ujP94Y9oLVwmv8IjS_W"
                  target="_blank"
                  rel="noopener external nofollow"
                  class="mt-3"
                >
                  Imagens do app Buser
                </ada-button>
              </div>
            </ada-card>
          </div>
        </ada-container>
      </section>

      <section class="ic-section ic-section--pink">
        <ada-container class="ic-contact">
          <ada-image class="icc-image" object-fit="contain">
            <img
              v-lazy-load
              data-src="~/assets/images/imprensa/pessoas.png"
              alt="Funcionários da Buser no ônibus"
              width="402"
              height="406"
            />
          </ada-image>

          <div class="icc-text">
            <div class="mb-1">
              <h2 class="title-md color-white mb-1">Quer falar com a gente?</h2>
              <p class="fw-bold color-white">
                Veja abaixo qual área pode te auxiliar
              </p>
            </div>

            <p class="color-white">
              Se você é jornalista, entre em contato com a nossa equipe de PR em
              <a
                class="fw-bold color-white"
                href="mailto:<EMAIL>"
              >
                <EMAIL>
              </a>
            </p>

            <p class="color-white">
              Para dúvidas sobre patrocínios, eventos e publicidade, contate a
              equipe de Marketing em
              <a
                class="fw-bold color-white"
                href="mailto:<EMAIL>"
              >
                <EMAIL>
              </a>
            </p>

            <p class="color-white">
              Se você é cliente e está precisando de ajuda, acesse a página
              <router-link :to="{ name: 'ajuda' }" class="fw-bold color-white">
                https://www.buser.com.br/ajuda
              </router-link>
              para verificar os canais de atendimento
            </p>
          </div>
        </ada-container>
      </section>

      <section class="bg-grey-lightest ic-section">
        <midia-section />
      </section>

      <section class="ic-section ic-about">
        <ada-container>
          <div class="mb-5">
            <h2 class="title-md mb-1">Sobre a Buser</h2>
            <p class="mb-1">
              A Buser nasceu para abrir caminhos, evoluir com a tecnologia e
              levar as pessoas pra frente.
            </p>
          </div>

          <div class="ica-content">
            <div class="icac-display-card">
              <p>+10M</p>
              <p>Clientes cadastrados</p>
            </div>
            <div class="icac-display-card">
              <p>945</p>
              <p>Cidades conectadas</p>
            </div>
            <div class="icac-display-card">
              <p>+300</p>
              <p>Empresas parceiras</p>
            </div>
            <div class="icac-display-card">
              <p>1.342</p>
              <p>Ônibus parceiros</p>
            </div>
          </div>
        </ada-container>
      </section>
    </main>
  </div>
</template>

<script setup>
import { useRoute, useHead, useAsyncData } from '#app'
import { ref, computed, watch } from 'vue'
import { listPressReleases } from '~api/content.js'
import buserLogo from '~/assets/images/buser-logo-fundo-rosa.png'
import headerBackground from '~/assets/images/header/onibus-ponte-1.png'
import metahelper from '~/helpers/metahelper.js'

const route = useRoute()
const currentPage = computed(() => {
  return +route.query?.page || 1
})

const { data: pressReleases, refresh } = await useAsyncData(
  'pressReleases',
  async () => {
    try {
      const paginator = {
        page: currentPage.value,
        rowsPerPage: 3
      }

      const { items, count_items, count_pages } = await listPressReleases({
        paginator
      })

      return {
        items,
        count_items,
        count_pages
      }
    } catch (e) {
      console.error(e)
    }
  }
)

const defaultReleaseImage = {
  url: buserLogo,
  alt: 'Logo da Buser'
}

const getImageUrl = (release) => {
  return release.image?.url || defaultReleaseImage.url
}

const getImageAlt = (release) => {
  return release.image?.descricao || defaultReleaseImage.alt
}

const hasNextPage = computed(() => {
  return currentPage.value < pressReleases.value?.count_pages
})

const hasPreviousPage = computed(() => {
  return currentPage.value > 1
})

const nextPageRoute = computed(() => {
  return {
    name: 'imprensa',
    query: {
      page: currentPage.value + 1
    }
  }
})

const previousPageRoute = computed(() => {
  return {
    name: 'imprensa',
    query: {
      page: currentPage.value - 1
    }
  }
})

const releases = ref(null)

watch(
  () => route.query,
  () => {
    scrollTo(releases.value)
    refresh()
  }
)

const scrollTo = (child) => {
  if (!child || !child.$el) {
    return
  }

  const el = child.$el
  setTimeout(() => {
    el.scrollIntoView({ behavior: 'smooth' })
  })
}

const breadcrumbs = [
  {
    route: { name: 'home' },
    text: 'Home'
  },
  {
    route: { name: 'imprensa' },
    text: 'Imprensa'
  }
]

const background = {
  url: headerBackground,
  alt: 'Ponte com Ônibus da Buser andando'
}

useHead(
  metahelper.generateMetaTags({
    url:
      'https://www.buser.com.br/imprensa' +
      (currentPage.value > 1 ? `?page=${currentPage.value}` : ''),
    title: 'Sala de Imprensa',
    description:
      'Fique por dentro das principais novidades da Buser por meio da Sala de Imprensa! Aproveite também e veja por onde entrar em contato conosco para assuntos afins.'
  })
)
</script>

<style lang="scss" scoped>
.imprensa {
  .i-content {
    .ic-section {
      padding: $spacing-5 0;

      @media (min-width: $screen-tablet-min) {
        padding: $spacing-10 0;
      }

      &--pink {
        background-color: $color-brand;
      }
    }

    .ic-contact {
      display: flex;
      align-items: center;
      flex-direction: column;
      gap: $spacing-4;

      @media (min-width: $screen-tablet-min) {
        flex-direction: row;
        gap: $spacing-10;
      }

      .icc-image {
        max-width: 402px;
      }

      .icc-text {
        display: flex;
        flex-direction: column;
        gap: $spacing-3;
      }
    }

    .ic-about {
      .ica-content {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: $spacing-2;

        .icac-display-card {
          width: 100%;
          padding: $spacing-3;
          border-radius: $border-radius-md;
          background-color: $color-brand;

          p {
            color: $color-white;

            &:first-child {
              font-size: 32px;
              font-weight: bold;
            }
          }
        }
      }
    }

    .ic-images {
      .ici-content {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(312px, 1fr));
        gap: $spacing-4;
      }
    }

    .ic-releases {
      .icr-content {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(312px, 1fr));
        gap: $spacing-4;
      }
    }
  }

  .i-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    border-radius: $border-radius-md;
    overflow: hidden;
    flex-grow: 1;

    .ic-image {
      object-position: left;
    }

    .ic-image-link {
      width: 100%;
      height: 100%;
    }

    .ic-content {
      color: $color-grey-dark;
      padding: $spacing-3;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }
}
</style>
