<template>
  <div data-sentry-mask>
    <h1 class="title-md mb-3 hide-tablet">Editar conta</h1>

    <ada-card outlined rounded class="mb-3 o-hidden">
      <ada-list>
        <ada-list-item
          title="Nome completo"
          :subtitle="userData.name"
          :end-icon="faPen"
          @click="editProfile('nome')"
        />

        <ada-list-item
          :title="getPendingTitle('Email', emailPending)"
          :subtitle="userData.email"
          :end-icon="faPen"
          @click="editProfile('email')"
        />

        <ada-list-item
          title="Celular"
          :subtitle="oldPhoneFormatted || emptyValue"
          :end-icon="faPen"
          @click="editProfile('celular')"
        />

        <ada-list-item
          title="Data de nascimento"
          :subtitle="birthdayFormatted || emptyValue"
          :end-icon="birthdayFormatted ? faPen : faPlus"
          @click="editProfile('nascimento')"
        />

        <ada-list-item
          title="CPF"
          :subtitle="cpfFormatted || emptyValue"
          :end-icon="canEditCpf ? faPlus : faLock"
          :disabled="!canEditCpf"
          @click="editProfile('cpf')"
        />

        <ada-list-item
          title="CEP"
          :subtitle="cepFormatted || emptyValue"
          :end-icon="cepFormatted ? faPen : faPlus"
          @click="editProfile('cep')"
        />
      </ada-list>
    </ada-card>

    <ada-card outlined rounded class="mb-3 o-hidden">
      <ada-list>
        <ada-list-item
          title="Contato de emergência"
          :subtitle="userData.emergency_contact.name || emptyValue"
          :start-icon="faLightEmergencyOn"
          :end-icon="userData.emergency_contact.name ? faPen : faPlus"
          start-icon-color="red"
          end-icon-color="brand"
          @click="editProfile('contato_emergencia')"
        />
      </ada-list>
    </ada-card>

    <ada-card outlined rounded class="p-2 mb-3 bg-grey-lightest">
      <h2 class="title-xs mb-1">Desativar conta</h2>
      <p>
        Seu perfil será desativado. Você não receberá mais comunicações da Buser
        e não será capaz de acessar informações de viagens passadas. Você pode
        reativar sua conta a qualquer momento acessando-a novamente.
      </p>

      <ada-button color="red" class="mt-1" outlined small @click="showPopup">
        Desativar minha conta Buser
      </ada-button>
    </ada-card>

    <ada-accordion>
      <ada-accordion-item paddless>
        <template #title="{ isActive, toggle }">
          <div class="d-flex jc-between ai-center" @click="toggle">
            <p class="px-2 text-sm">Opções avançadas</p>
            <ada-button class="w-auto ml-1" transparent small>
              <fa :icon="isActive ? faChevronUp : faChevronDown" />
            </ada-button>
          </div>
        </template>

        <div class="p-2">
          <h2 class="title-xs mb-1">Exclusão dos dados</h2>
          <div v-if="isDriver">
            <p class="mb-1">
              Prezado(a), Agradecemos pelo seu contato e confirmamos que a conta
              vinculada ao seu e-mail já foi devidamente inativada pelo nosso
              time. Destacamos, contudo, que, conforme previsto em nossa
              Política de Privacidade e em conformidade com a Lei Geral de
              Proteção de Dados (LGPD), alguns dados relacionados às viagens
              realizadas por você precisarão ser mantidos por períodos
              específicos para:
            </p>

            <ul class="mb-1">
              <li>
                - Cumprimento de obrigações legais e regulatórias do setor de
                transportes, fiscais e tributárias.
              </li>
              <li>
                - Preservação em caso de eventual necessidade de exercício
                regular de direitos.
              </li>
            </ul>

            <p class="mb-1">
              Esta retenção de dados é realizada de forma segura e restrita,
              apenas para as finalidades legais necessárias, sendo o acesso aos
              dados limitado apenas ao pessoal autorizado e estritamente para os
              fins legais mencionados. Lembramos que o cadastro em nossa
              plataforma é realizado pela empresa na qual você trabalha, razão
              pela qual podem existir dados que estejam sob guarda direta desta.
              A exclusão de tais dados deve ser solicitada diretamente à
              referida empresa. Em caso de dúvidas adicionais sobre seus dados
              pessoais na Buser, seguimos à disposição através do e-mail
              <EMAIL>. Atenciosamente, Time Buser
            </p>
          </div>
          <p v-else class="mb-1">
            Removeremos definitivamente todas as suas informações em até 48
            horas, sem possibilidade de recuperar sua conta e histórico na
            Buser. Ao selecionar esta opção, enviaremos uma mensagem de
            conclusão no seu e-mail.
          </p>

          <p>
            Para mais informações acesse nossa
            <a href="/sobre/politica-de-privacidade" target="_blank">
              Política de Privacidade.
            </a>
          </p>

          <ada-button
            color="red"
            class="mt-1"
            small
            @click="showDeleteAccountPopup"
          >
            Deletar minha conta
          </ada-button>
        </div>
      </ada-accordion-item>
    </ada-accordion>

    <popup-desativar-conta
      v-if="openPopup"
      :loading="inactivatingAccount"
      :error="inactivateError"
      @close="closePopup"
      @inactivate-account="handleAccountInactivation"
    />

    <popup-deletar-conta
      v-if="openDeletarContaPopup"
      :loading="deletingAccount"
      :error="deleteError"
      @close="closeDeleteAccountPopup"
      @delete-account="handleAccountDeletion"
    />

    <popup
      v-model="visible"
      :modal-options="{ maxWidth: '500px' }"
      :title="`Atualizar ${popupTitle[field]}`"
    >
      <edit-profile-form :field="field" @close="close" />
    </popup>
  </div>
</template>

<script>
import { defineNuxtComponent } from '#app'
import {
  faPen,
  faPlus,
  faLock,
  faLightEmergencyOn
} from '@fortawesome/pro-regular-svg-icons'
import { faChevronDown, faChevronUp } from '@fortawesome/pro-solid-svg-icons'
import { mapState, mapActions } from 'pinia'
import { useChallengeV3 } from 'vue-recaptcha'
import { verifyEmailChange } from '~api/user.js'
import authhelper from '~/helpers/authhelper.js'
import {
  phone,
  dateFormat,
  DATE_FORMATS,
  cpf,
  cep
} from '~/helpers/formatters.js'
import metahelper from '~/helpers/metahelper.js'
import { useSessionStore } from '~/stores/session.js'
import { useToastStore } from '~/stores/toast.js'
import popup from '~/components/shared/popup/popup.vue'
import editProfileForm from '~/components/user/edit-profile/edit-profile-form.vue'
import popupDeletarConta from '~/components/user/popup-deletar-conta/popup-deletar-conta.vue'
import popupDesativarConta from '~/components/user/popup-desativar-conta/popup-desativar-conta.vue'

const FIELD_EMPTY_VALUE = 'Não informado'

const popupTitle = {
  nome: 'nome',
  cep: 'CEP',
  email: 'email',
  celular: 'celular',
  nascimento: 'data de nascimento',
  cpf: 'CPF',
  contato_emergencia: 'contato de emergência'
}

export default defineNuxtComponent({
  name: 'EditProfileDados',
  components: {
    popup,
    popupDesativarConta,
    popupDeletarConta,
    editProfileForm
  },
  setup() {
    return {
      recaptcha: useChallengeV3('delete_account')
    }
  },
  data() {
    return {
      inactivateError: null,
      openPopup: false,
      emailPending: false,
      popupTitle,
      userData: {
        name: '',
        email: '',
        phone: '',
        oldPhone: '',
        birth_date: '',
        cep: '',
        cpf: '',
        emergency_contact: {
          name: '',
          phone: '',
          relationship: ''
        }
      },
      field: '',
      visible: false,
      inactivatingAccount: false,
      deleteError: null,
      deletingAccount: false,
      openDeletarContaPopup: false,
      error_types: {
        money_or_travel:
          'Você ainda possui mais de R$ 10 em sua conta e/ou viagens pendentes. Faça a retirada de seu dinheiro e cancele suas viagens antes de desativar sua conta.',
        in_debt:
          'Você não pode desativar sua conta pois possui saldo negativo na carteira. Entre em contato com nosso suporte para resolver este problema.'
      },
      emptyValue: FIELD_EMPTY_VALUE,
      faChevronDown,
      faChevronUp,
      faPen,
      faPlus,
      faLock,
      faLightEmergencyOn
    }
  },
  head() {
    return {
      ...metahelper.generateMetaTags({
        title: 'Editar conta',
        robots: 'noindex, nofollow'
      })
    }
  },
  computed: {
    ...mapState(useSessionStore, ['user']),
    oldPhoneFormatted() {
      return phone(this.userData.oldPhone)
    },
    birthdayFormatted() {
      return dateFormat(
        DATE_FORMATS.extendeddaymonthyear,
        this.userData.birth_date
      )
    },
    isDriver() {
      return this.user.permissions.DRIVER
    },
    cpfFormatted() {
      return cpf(this.userData.cpf)
    },
    cepFormatted() {
      return cep(this.userData.cep)
    },
    canEditCpf() {
      return this.userData.cpf === ''
    }
  },
  mounted() {
    this.fetchEmailPending()
  },
  created() {
    this.setUserData()
  },
  methods: {
    ...mapActions(useToastStore, { openToast: 'open' }),
    ...mapActions(useSessionStore, ['fetchUserSession']),
    showPopup() {
      this.openPopup = true
    },
    closePopup() {
      this.openPopup = false
    },
    getPendingTitle(title, pending) {
      if (!pending) return title
      return `${title} (Confirmação pendente)`
    },
    async handleAccountInactivation(reason) {
      this.inactivateError = null
      this.inactivatingAccount = true

      try {
        const res = await authhelper.inactivateAccount(reason)

        if (!res?.restriction) {
          await this.fetchUserSession()

          this.$router.push({
            name: 'login',
            params: {
              step: 'entrar',
              origin: 'via-conta-desativada'
            }
          })
        }

        this.inactivateError = this.error_types[res?.restriction]
      } catch (error) {
        this.openToast({
          message: `Erro inesperado ao realizar operação: ${error.message}`,
          type: 'error',
          error
        })
        throw error
      }
      this.inactivatingAccount = false
    },
    editProfile(type) {
      this.field = type
      this.visible = true
    },
    async fetchEmailPending() {
      try {
        const res = await verifyEmailChange()
        if (res?.new_email) {
          this.emailPending = true
          this.userData.email = res.new_email
        }
      } catch (error) {
        this.openToast({
          message: `Erro inesperado ao realizar operação: ${error.message}`,
          type: 'error',
          error
        })
        throw error
      }
    },
    close({ key, value }) {
      this.userData[key] = value
      this.visible = false
    },
    showDeleteAccountPopup() {
      this.openDeletarContaPopup = true
    },
    closeDeleteAccountPopup() {
      this.openDeletarContaPopup = false
    },
    async handleAccountDeletion() {
      this.deletingAccount = true
      this.deleteError = null

      try {
        const grtoken = await this.recaptcha.execute()
        const res = await authhelper.deleteAccountAutomatic(grtoken)

        if (!res?.restriction) {
          this.openToast({
            message: 'Enviamos um e-mail confirmando a deleção dos seus dados.',
            type: 'success'
          })
          await this.fetchUserSession()

          this.$router.push({ name: 'home' })
        } else if (res?.restriction === 'can_automatic_delete_account') {
          this.openToast({
            message: 'Conta deletada com sucesso.',
            type: 'success'
          })
          await this.fetchUserSession()

          this.$router.push({ name: 'home' })
        }

        this.deleteError = this.error_types[res?.restriction]
      } catch (error) {
        this.openToast({
          message: `Erro inesperado ao realizar operação: ${error.message}`,
          type: 'error',
          error
        })
        throw error
      }
      this.deletingAccount = false
    },
    setUserData() {
      this.userData.email = this.user.email || ''
      this.userData.name = this.user.name || ''
      this.userData.phone = this.user.cell_phone || ''
      this.userData.oldPhone = this.user.cell_phone || ''
      this.userData.birth_date = this.user.birth_date || ''
      this.userData.cep = this.user.cep || ''
      this.userData.cpf = this.user.cpf || ''
      this.userData.emergency_contact = { ...this.user.emergency_contact }
    }
  }
})
</script>
