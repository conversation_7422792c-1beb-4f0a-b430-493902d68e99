<template>
  <div class="promo-origin">
    <ada-container class="po-header">
      <div class="poh-bg p-4">
        <div>
          <ada-skeleton v-if="!personalizedTitle" layout="text-md(40)" />
          <h1 v-else class="title-md fw-bold text-white">
            {{ personalizedTitle }}
          </h1>
          <p class="text-md text-white">
            Promoções e viagens de ônibus com preços imperdíveis.
          </p>
        </div>

        <search-change-location
          class="search-change-location"
          :loading="loading"
          :cidade="selectedCity"
          @city-changed="changedCity"
        />
      </div>
    </ada-container>

    <secao-ofertas-similares-skeleton v-if="loading" class="mt-4" />
    <secao-ofertas-similares
      v-else-if="ofertasSimilares?.ofertas?.length"
      class="mt-4"
      source="promo-origin"
      :ofertas="ofertasSimilares?.ofertas"
      :origem="ofertasSimilares?.origem"
      :destino="ofertasSimilares?.destino"
    />
    <ada-container v-else class="my-2">
      <empty-results-card :origin="selectedCity" @city-changed="changedCity" />
    </ada-container>

    <secao-destinos-com-desconto-skeleton v-if="loading" class="mt-4" />
    <secao-destinos-com-desconto
      v-else-if="selectedCity"
      class="mt-4"
      :origem="selectedCity"
      :destinos="destinosComDesconto"
    />

    <secao-meus-cupons-skeleton v-if="loading" class="mt-4" />
    <secao-meus-cupons v-else-if="cupons" class="mt-4" :cupons="cupons" />

    <seo-bundle class="mt-3" :components="['cupons-carrossel']" />

    <descubra-destinos class="mt-5" :params="{ cidadeSlug: origem }" />

    <promo-seo-section v-if="!isApp && !loggedIn" class="mt-5" />
  </div>
</template>

<script setup lang="ts">
import { useRouter, useHead, useAsyncData } from '#app'
import { storeToRefs } from 'pinia'
import { ref, onMounted, computed } from 'vue'
import { getCupomLead } from '~api/promo.js'
import { search, searchOrigem, trechoPromo, searchPlaces } from '~api/search.js'
import EventBus from '~/helpers/eventbus.js'
import metahelper, {
  limitMetaTitle,
  limitMetaDescription
} from '~/helpers/metahelper.js'
import { fetchSimilarCarts } from '~/helpers/similar-itens.js'
import { useAbandonedCartStore } from '~/stores/abandoned-cart.js'
import { useBuscasRecentesStore } from '~/stores/buscas-recentes.js'
import { useGeolocationStore } from '~/stores/geolocation.js'
import { useSessionStore } from '~/stores/session.js'
import { useSettingsStore } from '~/stores/settings.js'
import descubraDestinos from '~/components/shared/descubra-destinos/descubra-destinos.vue'
import emptyResultsCard from '~/components/shared/empty-results-card.vue'
import searchChangeLocation from '~/components/shared/search-change-location/search-change-location.vue'
import promoSeoSection from '~/components/promocao/promo-seo-section.vue'
import secaoDestinosComDescontoSkeleton from '~/components/promocao/secao-destinos-com-desconto/secao-destinos-com-desconto-skeleton.vue'
import secaoDestinosComDesconto from '~/components/promocao/secao-destinos-com-desconto/secao-destinos-com-desconto.vue'
import secaoMeusCuponsSkeleton from '~/components/promocao/secao-meus-cupons/secao-meus-cupons-skeleton.vue'
import secaoMeusCupons from '~/components/promocao/secao-meus-cupons/secao-meus-cupons.vue'
import secaoOfertasSimilaresSkeleton from '~/components/promocao/secao-ofertas-similares/secao-ofertas-similares-skeleton.vue'
import secaoOfertasSimilares from '~/components/promocao/secao-ofertas-similares/secao-ofertas-similares.vue'
import type { Place, Grupo } from '~/components/search-result/grupos/types.ts'

const props = defineProps<{
  origem?: string
}>()

const changedCity = (newLocation: { slug: string }) => {
  if (!newLocation || props.origem === newLocation.slug) {
    return
  }

  useRouter().replace({ params: { origem: newLocation.slug } })
}

const loading = ref(true)
const selectedCity = ref<
  | {
      name: string
      uf: string
      slug: string
    }
  | undefined
>(undefined)

const settingsStore = useSettingsStore()
const { isApp } = storeToRefs(settingsStore)

const sessionStore = useSessionStore()
const { user, loggedIn } = storeToRefs(sessionStore)

const personalizedTitle = computed(() =>
  loggedIn.value && user.value
    ? `Pra você, ${user.value.first_name}`
    : 'Viagem de ônibus no precinho'
)

const cupons = ref<{ valido: boolean; usado: boolean }[] | undefined>(undefined)

type OfertasSimilares = {
  origem: Place
  destino: Place
  ofertas: Grupo[]
  tipo: string
}

const ofertasSimilares = ref<OfertasSimilares | undefined>(undefined)
const destinosComDesconto = ref([])

if (props.origem) {
  try {
    const { data: cities } = await useAsyncData(async () =>
      searchPlaces(props.origem)
    )
    const { origem: city } = cities.value

    if (city) {
      selectedCity.value = {
        name: city.city,
        uf: city.state,
        ...city
      }

      const url = `https://www.buser.com.br/onibus/promo/de/${selectedCity.value?.slug || ''}`
      const title = selectedCity.value
        ? `Promoções viagens de ônibus - ${selectedCity.value?.name}`
        : 'Promoções viagens de ônibus'
      const description = selectedCity.value
        ? `Viagens de ônibus saindo de ${selectedCity.value?.name} em promoção, mais baratas que a passagem na rodoviária. Selecione o destino e aproveite os melhores preços disponíveis.`
        : 'Viagens de ônibus em promoção, mais baratas que a passagem na rodoviária. Selecione o destino e aproveite os melhores preços disponíveis.'
      const robots = selectedCity.value ? '' : 'noindex, nofollow'

      useHead(
        metahelper.generateMetaTags({
          url,
          title: limitMetaTitle(title),
          description: limitMetaDescription(description),
          brand: 'Buser',
          robots
        })
      )
    }
  } catch (err) {
    console.error(err)
  }
}

onMounted(async () => {
  const abandonedCartStore = useAbandonedCartStore()
  const buscasRecentesStore = useBuscasRecentesStore()
  await Promise.allSettled([
    sessionStore.fetchUserSession(),
    abandonedCartStore.initAbandonedCart(),
    buscasRecentesStore.initBuscasRecentes()
  ])

  const geolocationStore = useGeolocationStore()
  const isInvalidCity = props.origem && !selectedCity.value
  if (!props.origem || isInvalidCity) {
    try {
      await geolocationStore.fetchLocationFromApi()
    } catch (e) {
      console.error(e)
      return
    } finally {
      const position = geolocationStore.currentPosition
      const newLocation = {
        slug: position ? position['slug'] : 'sao-paulo-sp'
      }
      changedCity(newLocation)
    }
  }

  const ofertas = (await getOfertasSimilares()) as OfertasSimilares
  if (ofertas && ofertas.origem && ofertas.destino && ofertas.ofertas?.length) {
    ofertasSimilares.value = ofertas
  }

  try {
    if (loggedIn.value && user.value) {
      const { cupons: cuponsLead } = (await getCupomLead()) as {
        cupons: { valido: boolean; usado: boolean }[]
      }
      cupons.value = cuponsLead
        .filter((cupom) => cupom.valido && !cupom.usado)
        .slice(0, 2)
    }
  } catch (error) {
    console.error(error)
  }

  const searchOrigemResult = await searchOrigem({
    origemSlug: props.origem || 'sao-paulo-sp',
    limit: 4,
    bucketizado: false,
    promo: false
  })
  destinosComDesconto.value = searchOrigemResult?.saindo_de ?? []

  EventBus.$emit('acessou-promo-origin', {
    origem: props.origem,
    tipoOfertasSimilares: ofertas?.tipo,
    qtdCupons: cupons.value?.length ?? 0,
    version: 'v2'
  })

  loading.value = false
})

type Cart = {
  ida: Grupo
  volta: Grupo
}

const getGruposFromAbandonedCart = async (abandonedCart: Cart) => {
  const origem = abandonedCart.ida?.origem
  const destino = abandonedCart.ida?.destino
  if (!origem?.name || !destino?.name) {
    return null
  }

  const ofertas = await fetchSimilarCarts(abandonedCart)

  return { ofertas, origem, destino, tipo: 'abandoned-cart' }
}

const getGruposFromGroups = async ({
  groupsIda,
  groupsVolta,
  tipo
}: {
  groupsIda: Grupo[]
  groupsVolta: Grupo[] | undefined
  tipo: string
}) => {
  const origem = groupsIda?.at(0)?.origem
  const destino = groupsIda?.at(0)?.destino
  if (!origem || !destino || !origem.slug || !destino.slug) {
    return null
  }

  const bestGroupsIda = groupsIda
    .sort((fst, snd) => fst.max_split_value - snd.max_split_value)
    .slice(0, 3)
  const bestGroupsVolta =
    groupsVolta
      ?.sort((fst, snd) => fst.max_split_value - snd.max_split_value)
      .slice(0, 3) || []

  const trechoPromosIda = await trechoPromo({
    origemSlug: origem.slug,
    destinoSlug: destino.slug
  })

  const ofertas = bestGroupsIda.map((groupIda, idx) => {
    const isTrechoPromoIda = trechoPromosIda.trechos.includes(groupIda.id)
    return {
      ida: groupIda,
      volta: bestGroupsVolta.at(idx),
      tipo,
      trechoPromoDiscount: isTrechoPromoIda ? trechoPromosIda.discount : 0
      // LATER: e a volta paizão?
    }
  })

  return {
    ofertas,
    origem: origem,
    destino: destino
  }
}

const getGruposFromBuscaRecente = async ({
  origem,
  destino,
  dataIda,
  dataVolta
}: {
  origem: Place
  destino: Place
  dataIda: Date
  dataVolta: Date
}) => {
  if (!origem?.slug || !destino?.slug) {
    return null
  }

  const searchResultIdaPromise = search({
    origemSlug: origem.slug,
    destinoSlug: destino.slug,
    ...(dataIda && { departureDate: dataIda }),
    weekDay: null
  })
  const searchResultVoltaPromise = dataVolta
    ? search({
        origemSlug: destino.slug,
        destinoSlug: origem.slug,
        departureDate: dataVolta,
        weekDay: null
      })
    : Promise.resolve(null)

  const results = await Promise.allSettled([
    searchResultIdaPromise,
    searchResultVoltaPromise
  ])
  const [groupsIda, groupsVolta] = results.map((results) => {
    if (results.status !== 'fulfilled') {
      return null
    }

    return results.value?.groups_by_date?.reduce(
      (groups: Grupo[], groupsByDate: { grupos: Grupo[] }) => {
        return groups.concat(groupsByDate.grupos)
      },
      []
    )
  })

  return await getGruposFromGroups({
    groupsIda,
    groupsVolta,
    tipo: 'busca-recente'
  })
}

const getGruposFromOrigem = async (origemSlug: string) => {
  if (!origemSlug) {
    return null
  }

  const searchOrigemResult = await searchOrigem({
    origemSlug,
    limit: 1,
    bucketizado: false,
    promo: undefined
  })
  const bestDestino = searchOrigemResult?.saindo_de?.at(0)
  if (!bestDestino?.slug) {
    return null
  }

  const searchResult = await search({
    origemSlug,
    destinoSlug: bestDestino.slug,
    departureDate: undefined,
    weekDay: undefined
  })
  const groups = searchResult?.groups_by_date?.reduce(
    (groups: object[], groupsByDate: { grupos: object[] }) => {
      return groups.concat(groupsByDate.grupos)
    },
    []
  )

  return await getGruposFromGroups({
    groupsIda: groups,
    tipo: 'from-origem',
    groupsVolta: undefined
  })
}

const getOfertasSimilares = async () => {
  const abandonedCartStore = useAbandonedCartStore()
  const { hasAbandonedCart, cart: abandonedCart } =
    storeToRefs(abandonedCartStore)

  const buscasRecentesStore = useBuscasRecentesStore()
  const { buscas, quantidadeBuscas } = storeToRefs(buscasRecentesStore)

  const ofertaSources = [
    async () =>
      hasAbandonedCart.value && abandonedCart.value
        ? await getGruposFromAbandonedCart(abandonedCart.value)
        : null,
    async () =>
      quantidadeBuscas.value >= 1
        ? await getGruposFromBuscaRecente(buscas.value[0])
        : null,
    async () => await getGruposFromOrigem(props.origem || 'sao-paulo-sp')
  ]

  for (const sourceFunc of ofertaSources) {
    const oferta = await sourceFunc()
    if (oferta) {
      return oferta
    }
  }
}
</script>

<style lang="scss" scoped>
@keyframes pohbg-gradient {
  0% {
    background-position: 100% 50%;
  }

  50% {
    background-position: 0% 50%;
  }

  100% {
    background-position: 100% 50%;
  }
}

.promo-origin {
  margin-top: $spacing-2;
  padding-bottom: $spacing-4;

  .po-header {
    .poh-bg {
      width: 100%;
      height: 100%;
      gap: $spacing-2;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background: linear-gradient(-45deg, #e6006a, #ff62a6, #ff10a7, #e6006a);
      background-size: 400% 400%;
      animation: pohbg-gradient 15s ease infinite;
      border-radius: $border-radius-md;

      @media (min-width: $screen-tablet-min) {
        flex-direction: row;
        gap: 0;
      }

      .search-change-location {
        align-self: flex-start;

        @media (min-width: $screen-tablet-min) {
          align-self: flex-end;
        }
      }
    }
  }
}
</style>
