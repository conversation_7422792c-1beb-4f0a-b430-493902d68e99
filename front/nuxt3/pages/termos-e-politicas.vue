<template>
  <div class="termos-e-politicas">
    <template v-if="text">
      <page-header :title="text.titulo" :subtitle="textSubtitle">
        <template #breadcrumbs>
          <ada-breadcrumbs :items="breadcrumbs" />
        </template>
      </page-header>

      <ada-container class="py-6">
        <html-view :html="text.html" class="t-html" />
      </ada-container>
    </template>

    <ada-container v-else class="t-empty-case">
      <img
        v-lazy-load
        class="tec-icon"
        data-src="~/assets/images/search/sad-bus.svg"
        alt="Ícone de um ônibus"
        width="55"
        height="55"
      />

      <div>
        <p class="mb-1">Política ou termo não encontrado.</p>
        <a href="/sobre"> Conheça sobre a Buser. </a>
      </div>
    </ada-container>

    <ada-container v-if="otherTexts.length" class="pb-6">
      <h2 class="title-md pb-2"><PERSON><PERSON> tamb<PERSON></h2>

      <ul class="t-other-texts">
        <li v-for="(otherText, index) in otherTexts" :key="otherText.id">
          <a :href="`/sobre/${otherText.slug}`" :title="otherText.titulo">
            {{ otherText.titulo }}
          </a>
          <span v-if="index !== otherTexts.length - 1" class="mr-1">,</span>
          <span v-else>.</span>
        </li>
      </ul>
    </ada-container>
  </div>
</template>

<script setup>
import { useAsyncData, useHead } from '#app'
import { computed } from 'vue'
import { getTextos } from '~api/content.js'
import { dateFormat, DATE_FORMATS } from '~/helpers/formatters.js'
import metahelper from '~/helpers/metahelper.js'
import htmlView from '~/components/shared/html-view.vue'
import pageHeader from '~/components/shared/page-header/page-header.vue'

const props = defineProps({
  textSlug: {
    type: String,
    required: true
  }
})

const { data: texts } = await useAsyncData('texts', async () => {
  try {
    return await getTextos()
  } catch (e) {
    console.error(e)
    return []
  }
})

const text = computed(() =>
  texts.value.find((text) => text.slug === props.textSlug)
)

const textSubtitle = computed(() => {
  const date = dateFormat(DATE_FORMATS.date, text.value.updated_at)
  return `Última modificação: ${date} `
})

const otherTexts = computed(() =>
  texts.value.filter((text) => text.slug !== props.textSlug)
)

const breadcrumbs = computed(() => [
  {
    route: { name: 'home' },
    text: 'Home'
  },
  {
    route: { name: 'sobre' },
    text: 'Sobre'
  },
  {
    route: { name: 'termosEPoliticas', params: { textSlug: props.textSlug } },
    text: text.value.titulo
  }
])

useHead(
  metahelper.generateMetaTags({
    title: text.value ? text.value.titulo : 'Termo ou Política não encontrada',
    description: text.value
      ? `Leia e conheça ${text.value.titulo} da Buser.`
      : 'Leia e conheça os termos e políticas da Buser.',
    url: `https://www.buser.com.br/sobre/${props.textSlug}`
  })
)
</script>

<style lang="scss">
.termos-e-politicas {
  .t-html {
    h3 {
      font-size: 24px;
      font-weight: bold;
      margin-top: $spacing-4;
      margin-bottom: $spacing-2;
    }
  }
}
</style>

<style lang="scss" scoped>
.termos-e-politicas {
  .t-empty-case {
    height: 230px;
    display: flex;
    align-items: center;
    justify-content: center;

    .tec-icon {
      margin-right: $spacing-3;
    }
  }

  .t-other-texts {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
}
</style>
