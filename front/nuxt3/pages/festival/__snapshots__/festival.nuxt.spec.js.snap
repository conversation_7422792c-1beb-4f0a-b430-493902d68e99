// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Festival > should renders properly 1`] = `
"<div id="test-wrapper">
  <div class="festival-template">
    <header data-v-a6167dc1="" class="festival-header">
      <div data-v-a6167dc1="" class="fh-container" style="--bg-mobile: url(/assets/images/festival/background-pop-festival-mobile.webp); --bg-desktop: url(/assets/images/festival/background-pop-festival.webp); background-blend-mode: multiply; background-color: #0000004D;">
        <div data-v-a6167dc1="" class="ada-container my-4">
          <!-- @slot Conteúdo do container -->
          <p data-v-a6167dc1="" class="title-xs">Mother Monster no Rio!</p>
          <h1 data-v-a6167dc1="" class="title-md my-1">Brasil não fique mais devasted, tá na hora do show!</h1>
          <h2 data-v-a6167dc1="" class="text-md fw-400 hide-tablet">Reserve sua viagem com segurança, praticidade e bora aproveitar</h2>
        </div>
      </div>
      <div data-v-a6167dc1="" class="ada-container fh-container-searchbox">
        <!-- @slot Conteúdo do container -->
        <div data-v-7da75efd="" data-v-a6167dc1="" class="ada-card ac-outlined ac-rounded ac-elevation-close ac-soft remote-search-box">
          <!--v-if-->
          <!-- @slot Conteúdo do card -->
          <div data-v-7da75efd="" class="remote-search">
            <form autocomplete="off">
              <div class="rs-cities"><span class="select-city"><div class="ada-float ada-select"><div class="f-reference"><!-- @slot Elemento de referência para o posicionamento do balão flutuante --><div id="origem" class="ada-field s-input rsc-city is-origem ada-input"><label for="origem">Saindo de</label><div class="fc-outlined f-container" style="height: 44px;" data-testid="container"><!-- @slot Espaço inicial --><!-- @slot Espaço inicial --><!-- Espaço incial --><fa-stub icon="[object Object]" class="color-grey ml-2"></fa-stub><!-- @slot Campo de formulário --><input id="origem" placeholder="Cidade de origem" aria-expanded="false" role="combobox" aria-haspopup="listbox" class="i-field"><!-- @slot Espaço final --><!--v-if--><!--v-if--><!-- Barra de carregamento --><!--v-if--></div><!--v-if--></div></div><!--v-if--></div></span><button class="is-button b-color-primary b-transparent b-icon b-rounded b-small ada-button rsc-swap-btn" type="button" aria-label="Inverter origem e destino">
                  <!--v-if-->
                  <!-- @slot Conteúdo renderizado no button -->
                  <fa-stub class="rscsb-desktop" icon="[object Object]"></fa-stub>
                  <fa-stub class="rscsb-mobile" icon="[object Object]"></fa-stub>
                </button><span class="select-city"><div class="ada-float ada-select"><div class="f-reference"><!-- @slot Elemento de referência para o posicionamento do balão flutuante --><div id="destino" class="ada-field s-input rsc-city is-destino ada-input"><label for="destino">Indo para</label><div class="fc-outlined f-container" style="height: 44px;" data-testid="container"><!-- @slot Espaço inicial --><!-- @slot Espaço inicial --><!-- Espaço incial --><fa-stub icon="[object Object]" class="color-grey ml-2"></fa-stub><!-- @slot Campo de formulário --><input id="destino" placeholder="Cidade de destino" aria-expanded="false" role="combobox" aria-haspopup="listbox" class="i-field"><!-- @slot Espaço final --><!--v-if--><!--v-if--><!-- Barra de carregamento --><!--v-if--></div><!--v-if--></div></div><!--v-if--></div></span></div>
              <div class="rs-dates">
                <div class="ada-datepicker">
                  <!-- close on focus  -->
                  <div tabindex="0"></div>
                  <div class="ada-field ada-input"><label for="ada-input-41">Data de ida</label>
                    <div class="fc-outlined f-container" style="height: 44px;" data-testid="container">
                      <!-- @slot Espaço inicial -->
                      <!-- @slot Espaço inicial -->
                      <!-- Espaço inicial -->
                      <fa-stub icon="[object Object]" class="color-grey ml-2"></fa-stub><!-- @slot Campo de formulário --><input id="ada-input-41" placeholder="Ida" readonly="" class="i-field"><!-- @slot Espaço final -->
                      <!--v-if-->
                      <!--v-if-->
                      <!-- Barra de carregamento -->
                      <!--v-if-->
                    </div>
                    <!--v-if-->
                  </div><!-- MOBILE -->
                  <!-- DESKTOP -->
                  <div class="ada-float d-block">
                    <div class="f-reference">
                      <!-- @slot Elemento de referência para o posicionamento do balão flutuante -->
                    </div>
                    <!--v-if-->
                  </div>
                </div>
                <div class="ada-datepicker">
                  <!-- close on focus  -->
                  <div tabindex="0"></div>
                  <div class="ada-field ada-input"><label for="ada-input-49">Data de volta</label>
                    <div class="fc-outlined f-container" style="height: 44px;" data-testid="container">
                      <!-- @slot Espaço inicial -->
                      <!-- @slot Espaço inicial -->
                      <!-- Espaço inicial -->
                      <fa-stub icon="[object Object]" class="color-grey ml-2"></fa-stub><!-- @slot Campo de formulário --><input id="ada-input-49" placeholder="Volta" readonly="" class="i-field"><!-- @slot Espaço final -->
                      <!--v-if-->
                      <!--v-if-->
                      <!-- Barra de carregamento -->
                      <!--v-if-->
                    </div>
                    <!--v-if-->
                  </div><!-- MOBILE -->
                  <!-- DESKTOP -->
                  <div class="ada-float d-block">
                    <div class="f-reference">
                      <!-- @slot Elemento de referência para o posicionamento do balão flutuante -->
                    </div>
                    <!--v-if-->
                  </div>
                </div>
              </div><button class="is-button b-color-primary b-block ada-button rs-submit" type="submit" aria-label="Buscar">
                <!--v-if-->
                <!-- @slot Conteúdo renderizado no button -->
                <fa-stub icon="[object Object]" class="rss-icon"></fa-stub><span class="rss-text show-tablet">Buscar</span>
              </button>
            </form>
            <!--v-if-->
          </div>
        </div>
      </div>
    </header>
    <div data-v-072cc83d="" class="ada-container secao-oferta-trechos mt-5">
      <!-- @slot Conteúdo do container -->
      <div data-v-072cc83d="" class="ada-skeleton sotc-title" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
        <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 18ch;" class="is-title-md fw-600 has-length s-item" data-name="title-md" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
      </div>
      <div data-v-072cc83d="" class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
        <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 12ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
        <div style="width: 8px; max-width: 100%; border-radius: 1px; height: 8px; visibility: hidden;" class="s-item" data-name="spacer" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
      </div>
      <div data-v-072cc83d="" class="ada-skeleton sot-cards" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 24px;">
        <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 100%; padding: 16px; min-height: 168px;" class="s-item" data-name="card" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
        <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 100%; padding: 16px; min-height: 168px;" class="s-item" data-name="card" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
        <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 100%; padding: 16px; min-height: 168px;" class="s-item" data-name="card" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
      </div>
    </div>
    <div data-v-b5519df0="" class="ada-container valores-buser px-0 py-5" valores="[object Object],[object Object],[object Object]">
      <!-- @slot Conteúdo do container -->
      <div data-v-b5519df0="" class="vb-head">
        <p class="text-md">Aqui é sem letra miúdas</p>
        <h2 class="title-sm">É só você vivendo o seu caminho</h2>
      </div>
      <section style="transform: translate3d(0px,20px,0px); opacity: 0;" data-v-b5519df0="" class="ada-carousel" initial="[object Object]" visibleonce="[object Object]">
        <ul style="--columns-xs: 1; --columns-sm: 1; --columns-md: 2; --columns-lg: 2; --columns-xl: 3;" class="c-container" tabindex="0">
          <!-- @slot Lista de componentes \`ada-carousel-item\` -->
          <li data-v-b5519df0="" class="ada-carousel-item">
            <!-- @slot Conteúdo do item -->
            <div data-v-b5519df0="" class="ada-card ac-outlined ac-elevation-close ac-straight vb-card">
              <!--v-if-->
              <!-- @slot Conteúdo do card -->
              <div data-v-b5519df0="" class="vbc-icon mb-4">
                <fa-stub data-v-b5519df0="" icon="[object Object]" size="lg" class="color-purple-dark"></fa-stub>
              </div>
              <h3 data-v-b5519df0="" class="title-sm mb-2 color-brand">Mais seguro</h3>
              <p data-v-b5519df0="">Empresas e motoristas treinados e certificados pela ANTT. Ônibus com tecnologias exclusivas, como câmeras de fadiga e telemetria (GPS) e suporte 24 horas em todos os nossos canais.</p>
            </div>
          </li>
          <li data-v-b5519df0="" class="ada-carousel-item">
            <!-- @slot Conteúdo do item -->
            <div data-v-b5519df0="" class="ada-card ac-outlined ac-elevation-close ac-straight vb-card">
              <!--v-if-->
              <!-- @slot Conteúdo do card -->
              <div data-v-b5519df0="" class="vbc-icon mb-4">
                <fa-stub data-v-b5519df0="" icon="[object Object]" size="lg" class="color-purple-dark"></fa-stub>
              </div>
              <h3 data-v-b5519df0="" class="title-sm mb-2 color-brand">Prático como deve ser</h3>
              <p data-v-b5519df0="">Viajar com a praticidade de comprar pelo app, sem pegar filas. No Fretamento, dá até pra acompanhar o embarque em tempo real. Não dá pra viajar de outro jeito.</p>
            </div>
          </li>
          <li data-v-b5519df0="" class="ada-carousel-item">
            <!-- @slot Conteúdo do item -->
            <div data-v-b5519df0="" class="ada-card ac-outlined ac-elevation-close ac-straight vb-card">
              <!--v-if-->
              <!-- @slot Conteúdo do card -->
              <div data-v-b5519df0="" class="vbc-icon mb-4">
                <fa-stub data-v-b5519df0="" icon="[object Object]" size="lg" class="color-purple-dark"></fa-stub>
              </div>
              <h3 data-v-b5519df0="" class="title-sm mb-2 color-brand">Barato e com qualidade</h3>
              <p data-v-b5519df0="">Viagens de ônibus por até menos da metade do preço das passagens de rodoviária. Diversas opções de destino com qualidade e preço justo!</p>
            </div>
          </li>
        </ul>
        <!--
      @slot Botão navegação anterior
      @binding {Object} on - Evento de click
    -->
        <!--v-if-->
        <!--
      @slot Botão navegação próximo
      @binding {Object} on - Evento de click
     -->
        <!--v-if-->
      </section>
    </div>
  </div>
</div>"
`;

exports[`Festival > should renders properly 2`] = `
"<div id="test-wrapper">
  <div class="festival-template">
    <header data-v-a6167dc1="" class="festival-header">
      <div data-v-a6167dc1="" class="fh-container" style="--bg-mobile: url(/assets/images/landing-page/pessoas-em-show-croped.webp); --bg-desktop: url(/assets/images/landing-page/pessoas-em-show-croped.webp); background-blend-mode: multiply; background-color: #0000004D;">
        <div data-v-a6167dc1="" class="ada-container my-4">
          <!-- @slot Conteúdo do container -->
          <p data-v-a6167dc1="" class="title-xs">Vai viajar pro festival?</p>
          <h1 data-v-a6167dc1="" class="title-md my-1">Escolha a trilha sonora, o caminho é com a gente.</h1>
          <h2 data-v-a6167dc1="" class="text-md fw-400 hide-tablet">Com a Buser, o show começa na estrada. Aqui você viaja com mais conforto, preço justo e suporte 24h. Bora embarcar?</h2>
        </div>
      </div>
      <div data-v-a6167dc1="" class="ada-container fh-container-searchbox">
        <!-- @slot Conteúdo do container -->
        <div data-v-7da75efd="" data-v-a6167dc1="" class="ada-card ac-outlined ac-rounded ac-elevation-close ac-soft remote-search-box">
          <!--v-if-->
          <!-- @slot Conteúdo do card -->
          <div data-v-7da75efd="" class="remote-search">
            <form autocomplete="off">
              <div class="rs-cities"><span class="select-city"><div class="ada-float ada-select"><div class="f-reference"><!-- @slot Elemento de referência para o posicionamento do balão flutuante --><div id="origem" class="ada-field s-input rsc-city is-origem ada-input"><label for="origem">Saindo de</label><div class="fc-outlined f-container" style="height: 44px;" data-testid="container"><!-- @slot Espaço inicial --><!-- @slot Espaço inicial --><!-- Espaço incial --><fa-stub icon="[object Object]" class="color-grey ml-2"></fa-stub><!-- @slot Campo de formulário --><input id="origem" placeholder="Cidade de origem" aria-expanded="false" role="combobox" aria-haspopup="listbox" class="i-field"><!-- @slot Espaço final --><!--v-if--><!--v-if--><!-- Barra de carregamento --><!--v-if--></div><!--v-if--></div></div><!--v-if--></div></span><button class="is-button b-color-primary b-transparent b-icon b-rounded b-small ada-button rsc-swap-btn" type="button" aria-label="Inverter origem e destino">
                  <!--v-if-->
                  <!-- @slot Conteúdo renderizado no button -->
                  <fa-stub class="rscsb-desktop" icon="[object Object]"></fa-stub>
                  <fa-stub class="rscsb-mobile" icon="[object Object]"></fa-stub>
                </button><span class="select-city"><div class="ada-float ada-select"><div class="f-reference"><!-- @slot Elemento de referência para o posicionamento do balão flutuante --><div id="destino" class="ada-field s-input rsc-city is-destino ada-input"><label for="destino">Indo para</label><div class="fc-outlined f-container" style="height: 44px;" data-testid="container"><!-- @slot Espaço inicial --><!-- @slot Espaço inicial --><!-- Espaço incial --><fa-stub icon="[object Object]" class="color-grey ml-2"></fa-stub><!-- @slot Campo de formulário --><input id="destino" placeholder="Cidade de destino" aria-expanded="false" role="combobox" aria-haspopup="listbox" class="i-field"><!-- @slot Espaço final --><!--v-if--><!--v-if--><!-- Barra de carregamento --><!--v-if--></div><!--v-if--></div></div><!--v-if--></div></span></div>
              <div class="rs-dates">
                <div class="ada-datepicker">
                  <!-- close on focus  -->
                  <div tabindex="0"></div>
                  <div class="ada-field ada-input"><label for="ada-input-122">Data de ida</label>
                    <div class="fc-outlined f-container" style="height: 44px;" data-testid="container">
                      <!-- @slot Espaço inicial -->
                      <!-- @slot Espaço inicial -->
                      <!-- Espaço inicial -->
                      <fa-stub icon="[object Object]" class="color-grey ml-2"></fa-stub><!-- @slot Campo de formulário --><input id="ada-input-122" placeholder="Ida" readonly="" class="i-field"><!-- @slot Espaço final -->
                      <!--v-if-->
                      <!--v-if-->
                      <!-- Barra de carregamento -->
                      <!--v-if-->
                    </div>
                    <!--v-if-->
                  </div><!-- MOBILE -->
                  <!-- DESKTOP -->
                  <div class="ada-float d-block">
                    <div class="f-reference">
                      <!-- @slot Elemento de referência para o posicionamento do balão flutuante -->
                    </div>
                    <!--v-if-->
                  </div>
                </div>
                <div class="ada-datepicker">
                  <!-- close on focus  -->
                  <div tabindex="0"></div>
                  <div class="ada-field ada-input"><label for="ada-input-130">Data de volta</label>
                    <div class="fc-outlined f-container" style="height: 44px;" data-testid="container">
                      <!-- @slot Espaço inicial -->
                      <!-- @slot Espaço inicial -->
                      <!-- Espaço inicial -->
                      <fa-stub icon="[object Object]" class="color-grey ml-2"></fa-stub><!-- @slot Campo de formulário --><input id="ada-input-130" placeholder="Volta" readonly="" class="i-field"><!-- @slot Espaço final -->
                      <!--v-if-->
                      <!--v-if-->
                      <!-- Barra de carregamento -->
                      <!--v-if-->
                    </div>
                    <!--v-if-->
                  </div><!-- MOBILE -->
                  <!-- DESKTOP -->
                  <div class="ada-float d-block">
                    <div class="f-reference">
                      <!-- @slot Elemento de referência para o posicionamento do balão flutuante -->
                    </div>
                    <!--v-if-->
                  </div>
                </div>
              </div><button class="is-button b-color-primary b-block ada-button rs-submit" type="submit" aria-label="Buscar">
                <!--v-if-->
                <!-- @slot Conteúdo renderizado no button -->
                <fa-stub icon="[object Object]" class="rss-icon"></fa-stub><span class="rss-text show-tablet">Buscar</span>
              </button>
            </form>
            <!--v-if-->
          </div>
        </div>
      </div>
    </header>
    <div data-v-072cc83d="" class="ada-container secao-oferta-trechos mt-5">
      <!-- @slot Conteúdo do container -->
      <div data-v-072cc83d="" class="ada-skeleton sotc-title" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
        <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 18ch;" class="is-title-md fw-600 has-length s-item" data-name="title-md" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
      </div>
      <div data-v-072cc83d="" class="ada-skeleton" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 0px;">
        <div style="width: 100%; max-width: 100%; border-radius: 1px; --length: 12ch;" class="is-title-sm fw-600 has-length s-item" data-name="title-sm" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
        <div style="width: 8px; max-width: 100%; border-radius: 1px; height: 8px; visibility: hidden;" class="s-item" data-name="spacer" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
      </div>
      <div data-v-072cc83d="" class="ada-skeleton sot-cards" role="status" aria-busy="true" aria-label="Carregando conteúdo" tabindex="0" style="align-items: start; justify-content: start; gap: 24px;">
        <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 100%; padding: 16px; min-height: 168px;" class="s-item" data-name="card" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
        <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 100%; padding: 16px; min-height: 168px;" class="s-item" data-name="card" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
        <div style="width: 100%; max-width: 100%; border-radius: 8px; height: 100%; padding: 16px; min-height: 168px;" class="s-item" data-name="card" data-testid="skeleton-item">
          <!-- @slot Espaço para aninhamento de skeletons -->
        </div>
      </div>
    </div>
    <div data-v-b5519df0="" class="ada-container valores-buser px-0 py-5" valores="[object Object],[object Object],[object Object]">
      <!-- @slot Conteúdo do container -->
      <div data-v-b5519df0="" class="vb-head">
        <p class="text-md">Aqui é sem letra miúdas</p>
        <h2 class="title-sm">É só você vivendo o seu caminho</h2>
      </div>
      <section style="transform: translate3d(0px,20px,0px); opacity: 0;" data-v-b5519df0="" class="ada-carousel" initial="[object Object]" visibleonce="[object Object]">
        <ul style="--columns-xs: 1; --columns-sm: 1; --columns-md: 2; --columns-lg: 2; --columns-xl: 3;" class="c-container" tabindex="0">
          <!-- @slot Lista de componentes \`ada-carousel-item\` -->
          <li data-v-b5519df0="" class="ada-carousel-item">
            <!-- @slot Conteúdo do item -->
            <div data-v-b5519df0="" class="ada-card ac-outlined ac-elevation-close ac-straight vb-card">
              <!--v-if-->
              <!-- @slot Conteúdo do card -->
              <div data-v-b5519df0="" class="vbc-icon mb-4">
                <fa-stub data-v-b5519df0="" icon="[object Object]" size="lg" class="color-purple-dark"></fa-stub>
              </div>
              <h3 data-v-b5519df0="" class="title-sm mb-2 color-brand">Mais seguro</h3>
              <p data-v-b5519df0="">Empresas e motoristas treinados e certificados pela ANTT. Ônibus com tecnologias exclusivas, como câmeras de fadiga e telemetria (GPS) e suporte 24 horas em todos os nossos canais.</p>
            </div>
          </li>
          <li data-v-b5519df0="" class="ada-carousel-item">
            <!-- @slot Conteúdo do item -->
            <div data-v-b5519df0="" class="ada-card ac-outlined ac-elevation-close ac-straight vb-card">
              <!--v-if-->
              <!-- @slot Conteúdo do card -->
              <div data-v-b5519df0="" class="vbc-icon mb-4">
                <fa-stub data-v-b5519df0="" icon="[object Object]" size="lg" class="color-purple-dark"></fa-stub>
              </div>
              <h3 data-v-b5519df0="" class="title-sm mb-2 color-brand">Prático como deve ser</h3>
              <p data-v-b5519df0="">Viajar com a praticidade de comprar pelo app, sem pegar filas. No Fretamento, dá até pra acompanhar o embarque em tempo real. Não dá pra viajar de outro jeito.</p>
            </div>
          </li>
          <li data-v-b5519df0="" class="ada-carousel-item">
            <!-- @slot Conteúdo do item -->
            <div data-v-b5519df0="" class="ada-card ac-outlined ac-elevation-close ac-straight vb-card">
              <!--v-if-->
              <!-- @slot Conteúdo do card -->
              <div data-v-b5519df0="" class="vbc-icon mb-4">
                <fa-stub data-v-b5519df0="" icon="[object Object]" size="lg" class="color-purple-dark"></fa-stub>
              </div>
              <h3 data-v-b5519df0="" class="title-sm mb-2 color-brand">Barato e com qualidade</h3>
              <p data-v-b5519df0="">Viagens de ônibus por até menos da metade do preço das passagens de rodoviária. Diversas opções de destino com qualidade e preço justo!</p>
            </div>
          </li>
        </ul>
        <!--
      @slot Botão navegação anterior
      @binding {Object} on - Evento de click
    -->
        <!--v-if-->
        <!--
      @slot Botão navegação próximo
      @binding {Object} on - Evento de click
     -->
        <!--v-if-->
      </section>
    </div>
  </div>
</div>"
`;
