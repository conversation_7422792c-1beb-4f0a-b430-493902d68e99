import { renderSuspended } from '@nuxt/test-utils/runtime'
import { createTestingPinia } from '@pinia/testing'
import { screen } from '@testing-library/vue'
import flushPromises from 'flush-promises'
import { createRouter, createMemoryHistory } from 'vue-router'
import api from '~api'
import { festivais } from '~/assets/js/festivais.ts'
import festival from './festival.vue'
import routes from '~/app/routes.ts'

const router = createRouter({ routes, history: createMemoryHistory() })

const testinPinia = createTestingPinia()
vi.mock('~api', () => ({
  default: {
    passenger: {
      getMelhoresViagensNoTrecho: vi.fn()
    }
  }
}))

vi.mock('~/helpers/eventbus.js', () => ({
  default: {
    $emit: vi.fn()
  }
}))
const renderFestival = (festivalSlug) => {
  return renderSuspended(festival, {
    props: {
      slugFestival: festivalSlug
    },
    stubs: ['festival-header'],
    global: {
      plugins: [router, testinPinia]
    }
  })
}
describe('Festival', () => {
  api.passenger.getMelhoresViagensNoTrecho.mockResolvedValue({
    trechos: [{ price: 100 }]
  })
  it.each(Object.entries(festivais))(
    'should renders properly',
    async (slugFestival, festival) => {
      const wrapper = await renderFestival(slugFestival)
      await flushPromises()
      expect(wrapper.html()).toMatchSnapshot()
      expect(
        await screen.findByText(festival.headerTexts.title)
      ).toBeInTheDocument()
      expect(
        await screen.findByText(festival.headerTexts.subtitle)
      ).toBeInTheDocument()
      expect(
        await screen.findByText(festival.headerTexts.auxTitle)
      ).toBeInTheDocument()
    }
  )
})
