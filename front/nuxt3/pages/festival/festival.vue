<template>
  <div class="festival-template">
    <festival-header
      v-once
      :festival="currentFestival"
      :extra-params="{ slugFestival: slugFestival }"
    />

    <secao-oferta-trechos-skeleton v-if="loading" class="mt-5" />

    <secao-oferta-trechos
      v-else-if="ofertas?.trechos"
      class="mt-5"
      title="As melhores ofertas para sua viagem"
      :trecho-em-oferta="ofertas"
    />
    <valores-buser :valores="VALORES.loggedIn" class="py-5">
      <template #head>
        <p class="text-md">Aqui é sem letra miúdas</p>
        <h2 class="title-sm">É só você vivendo o seu caminho</h2>
      </template>
    </valores-buser>
  </div>
</template>

<script setup lang="ts">
import { useRoute, navigateTo, useHead } from '#app'
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import { onMounted, ref } from 'vue'
import api from '~api'
import { searchPlaces } from '~api/search.js'
import { VALORES } from '~/assets/js/atributos-dinamicos.ts'
import { festivais } from '~/assets/js/festivais.ts'
import type { Festival } from '~/assets/js/festivais.ts'
import EventBus from '~/helpers/eventbus.js'
import metahelper from '~/helpers/metahelper.js'
import { useFavoritePlacesStore } from '~/stores/favorite-places.js'
import { useGeolocationStore } from '~/stores/geolocation.js'
import { useSearchboxStore } from '~/stores/searchbox.js'
import valoresBuser from '~/components/home/<USER>'
import secaoOfertaTrechosSkeleton from '~/components/promocao/secao-oferta-trechos/secao-oferta-trechos-skeleton.vue'
import secaoOfertaTrechos from '~/components/promocao/secao-oferta-trechos/secao-oferta-trechos.vue'
import festivalHeader from '~/components/search-result/festival/festival-header.vue'
import type { Place, Grupo } from '~/components/search-result/grupos/types.ts'
import type { Ref } from 'vue'

const props = defineProps<{
  slugFestival: string
}>()

const loading = ref(true)
const ofertas: Ref<{ trechos?: Grupo[] } | null> = ref(null)
const currentFestival: Ref<Festival> = ref(festivais.rock)
const geolocationStore = useGeolocationStore()
const favoritePlacesStore = useFavoritePlacesStore()
const searchboxStore = useSearchboxStore()

const {
  toCity,
  fromCity
}: { toCity: Ref<Place | null>; fromCity: Ref<Place | null> } =
  storeToRefs(searchboxStore)
const { currentPosition }: { currentPosition: Ref<Place | null> } =
  storeToRefs(geolocationStore)

const { fetchFavoritePlaces } = favoritePlacesStore
const { fetchLocationFromApi } = geolocationStore

function fetchCurrentFestival(): void {
  const _currentFestival = festivais[props.slugFestival]
  const route = useRoute()
  if (!_currentFestival) {
    route.params.slugFestival = 'rock'
    navigateTo({ ...route })
    return
  }
  currentFestival.value = _currentFestival
}

fetchCurrentFestival()

onMounted(async () => {
  await Promise.allSettled([fetchFavoritePlaces(), fetchLocationFromApi()])

  const origemSlug = currentPosition.value?.slug || 'sao-paulo-sp'
  const destinoSlug = 'rio-de-janeiro-rj'

  await Promise.allSettled([
    fetchOfertas(origemSlug, destinoSlug),
    assingOriginAndDestination(origemSlug, destinoSlug)
  ])

  EventBus.$emit('acessou-festival', {
    origem: currentPosition.value,
    name: props.slugFestival
  })

  loading.value = false
})

function head() {
  const preload: { as: string; imagesrcset: string } = {
    as: 'image',
    imagesrcset: `${currentFestival.value.imageBackgroundMobile} 1x, ${currentFestival.value.imageBackground} 2x`
  }

  return metahelper.generateMetaTags({
    title: 'Buser - Viagem para festival',
    description:
      'Buser, o app do ônibus. Aqui você viaja com mais conforto, preço justo e suporte 24h. Bora embarcar?',
    url: `www.buser.com.br/festival/${props.slugFestival}`,
    linkPreload: preload
  })
}

useHead(head())

async function fetchOfertas(
  origemSlug: string,
  destinoSlug: string | null | undefined
): Promise<void> {
  const payload: {
    origem: string
    destino?: string | null
    datetimeIda?: string
    datetimeFim?: string | null
    limit?: number
  } = {
    origem: origemSlug,
    destino: destinoSlug,
    datetimeFim: null,
    limit: 3
  }
  const now = dayjs()
  if (now.isSameOrBefore(dayjs('2025-05-02'), 'day')) {
    payload.datetimeIda = '2025-05-02'
  }

  try {
    // @ts-expect-error: Unreachable code error
    ofertas.value = await api.passenger.getMelhoresViagensNoTrecho(payload)
  } catch (error) {
    console.error(error)
  }
}

async function assingOriginAndDestination(
  origemSlug: string,
  destinoSlug: string
): Promise<void> {
  try {
    const { origem, destino } = await searchPlaces(origemSlug, destinoSlug)
    fromCity.value = origem
    toCity.value = destino
  } catch (error) {
    console.error(error)
  }
}
</script>
