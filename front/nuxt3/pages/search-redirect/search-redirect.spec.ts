import { mount } from '@vue/test-utils'
import { expect, vi, test } from 'vitest'
import searchRedirect from '~/pages/search-redirect/search-redirect.vue'

const mocks = vi.hoisted(() => {
  let routeQueryMock: undefined | Record<string, string>
  return {
    navigateTo: vi.fn(),
    routeQuery: routeQueryMock
  }
})
vi.mock('#app', async () => ({ navigateTo: mocks.navigateTo }))

vi.mock('vue-router', () => ({ useRoute: () => ({ query: mocks.routeQuery }) }))

test('Redireciona para home se não existir query', async () => {
  try {
    mount(searchRedirect)
  } catch (e) {
    expect(mocks.navigateTo).toBeCalledWith('/', {
      external: true,
      redirectCode: 302
    })
  }
})

test('Redireciona para home se estiver faltando algum dado', async () => {
  mocks.routeQuery = {
    from_ticketing_stop_time_id: '["sao-paulo-sp"]',
    to_ticketing_stop_time_id: '["belo-horizonte-mg"]',
    service_date: '["20250706"]'
  }

  mount(searchRedirect)
  expect(mocks.navigateTo).toBeCalledWith('/', {
    external: true,
    redirectCode: 302
  })
})

test('Redireciona para a search', async () => {
  mocks.routeQuery = {
    from_ticketing_stop_time_id: '["sao-paulo-sp"]',
    to_ticketing_stop_time_id: '["belo-horizonte-mg"]',
    service_date: '["20250706"]',
    ticketing_trip_id: '["gyTRiz"]'
  }

  mount(searchRedirect)
  const expectedArgs = [
    '/onibus/sao-paulo-sp/belo-horizonte-mg/?ida=2025-07-06&idGrupoDestaque=gyTRiz',
    { external: true, redirectCode: 302 }
  ]
  expect(mocks.navigateTo).toBeCalledWith(...expectedArgs)
})
