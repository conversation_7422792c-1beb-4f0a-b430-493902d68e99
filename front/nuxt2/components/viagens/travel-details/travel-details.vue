<template>
  <div v-if="hasTravel" class="travel-details pb-4">
    <detalhes-reembolso
      v-if="showSensitiveInfo && mostraNovoComprovanteCancelamento && isPrintingReembolso && !isTravelCompartilhada"
      :travel="travel"
      :extrato="extrato"
      class="td-print-reembolso"
    />
    <div :class="{ 'td-no-print': isPrintingReembolso }">
      <travel-historic
        :show-sensitive-info="showSensitiveInfo"
        :historico="historico"
        :loading="loadingTravelStatuses"
        :current-event="currentEvent"
        class="mb-2"
      />

      <pagamento-pendente
        v-if="!isCanceledOrFinished && pagamentoPendenteData && !isTravelCompartilhada"
        :pagamento="pagamentoPendenteData"
        :tipo="tipoPagamentoPendente"
        class="mb-2"
      />

      <ada-card class="p-2 mb-2" outlined rounded>
        <resumo-viagem
          :grupo="travel.grupo"
          :inactive="isCanceledOrFinished"
          :bus-arrival-info="busArrivalInfo"
          :current-event="currentEvent"
        />
      </ada-card>
      <desembarque-rapido-alert v-if="shouldShowAlertDesembarqueRapido" class="mb-2" />
      <actions-travel-revendedor v-if="isRevendedor" :can-be-canceled="canBeCanceled" @cancel-travel="onCancelTravel" />
      <retail-media-banner v-if="showRetailMediaTelaViagem" />

      <addon-carousel
        v-if="!isRevendedor && habilitarUpgrade && hasUpgradeAvailable && !embarqueProximo"
        :upgrade-info="upgradeInfo"
        class="mb-2"
      />

      <template v-if="!isRevendedor && sobeComponenteSeguranca">
        <h2 class="title-sm mb-1 pt-1">
          Opções de segurança
        </h2>

        <ada-card class="mb-2 o-hidden" outlined rounded>
          <opcoes-seguranca @onclose="close" />
        </ada-card>
      </template>

      <card-alteracoes v-if="!isRevendedor && hasAlteracoes" :travel="travel" class="mb-2" />

      <alerta-imprimir-bpe v-if="hasBpe" :reservation-code-ida="travel.reservation_code" class="mb-2" />

      <local-retirada-marketplace
        v-if="
          isMarketplaceOrHibrido && isViagemPendente && !loadingBpe && !hasBpe
        "
        :grupo-ida="travel.grupo"
        class="mb-2"
      />

      <ada-alert
        v-if="!isRevendedor && canRemarcarGratis && isMarketplace && !isTravelCompartilhada"
        type="warning"
        :icon="faClock"
        class="mb-2"
      >
        <p class="text-sm">
          {{ textoRemarcarGratuitamente }}
        </p>
        <p>
          <router-link
            :to="{
              name: 'alterarReserva',
              params: {
                isTravelRemarcadaGratis: true,
                id: travel.id,
                isRedirect: true
              }
            }"
          >
            Remarcar de graça
          </router-link>
        </p>
      </ada-alert>

      <travel-alerts
        v-if="!isMarketplace"
        :travel="travel"
        :can-ask-noshow-cupom="canAskNoshowCupom"
        :current-event="currentEvent"
        :datetime-ida="travel.grupo.datetime_ida"
        :bus-arrival-info="busArrivalInfo"
        :noshow-coupon-validation-error="noshowCupomValidationError"
        :alteracoes-travel="alteracoesTravel"
        :can-remarcar-gratis="canRemarcarGratis"
        :is-travel-compartilhada="isTravelCompartilhada"
        class="mb-2"
        @print="print"
      />

      <travel-info :travel="travel" :is-marketplace="isMarketplace" class="mb-2" />
      <card-bagagens
        v-if="!isMarketplaceOrHibrido"
        :bagagem-adicional="extrato?.quantidade_bagagem_adicional"
        :should-show-alert-desembarque-rapido="shouldShowAlertDesembarqueRapido"
        :arrivals="arrivals"
        class="mb-2"
      />

      <ada-alert v-if="isMarketplace" class="mb-2" type="info" hide-icon>
        <p class="text-sm">
          O Buser Passagens é um programa de revenda de passagens de ônibus de linhas rodoviárias.
          <span v-html="textoAtendimentoMarketplace" />.
        </p>
      </ada-alert>

      <addon-carousel
        v-if="!isRevendedor && habilitarUpgrade && hasUpgradeAvailable && embarqueProximo"
        :upgrade-info="upgradeInfo"
        class="mb-2"
      />

      <itinerario :travel="travel" class="mb-2" />

      <card-seguro-adicional
        v-if="extrato?.valor_seguro_extra > 0 && travel?.reservation_code"
        :reserva="travel.reservation_code"
        class="mb-2"
      />

      <boarding-documents v-if="isViagemPendente" :travel="travel" :has-bpe="hasBpe" class="mb-2" />

      <addon-marcacao-assento
        v-if="!isRevendedor && habilitarUpgrade && hasMarcacaoAssento"
        :upgrade-info="upgradeInfo"
        class="mb-2"
      />

      <card-buser-premium v-if="isBuserPremium" class="mb-2" />

      <h2 class="title-sm mb-1 pt-1">
        {{ passengersTitle }}
      </h2>

      <router-link
        v-if="!isRevendedor && !isTravelCompartilhada && !isMarketplaceOrHibrido && canEditPassengers && showSensitiveInfo"
        :to="{ name: 'viajantes' }"
        class="d-block mb-1"
      >
        Editar dados dos passageiros
      </router-link>

      <passageiros
        :travel="travel"
        :can-remove-passenger="canBeCanceled && showSensitiveInfo && !isTravelCompartilhada"
        :is-marketplace="isMarketplace"
        :show-sensitive-info="showSensitiveInfo"
        class="mb-2"
        @remove-passenger="onRemovePassenger"
      />

      <template v-if="showSensitiveInfo && !isTravelCompartilhada">
        <h2 class="title-sm mb-1 pt-1">
          Extrato
        </h2>

        <travel-extrato :travel="travel" :is-canceled="isCanceled" :taxa-cancelamento="taxaCancelamento" class="mb-2">
          <ada-button v-if="mostraNovoComprovanteCancelamento" link class="mt-2" @click="print">
            Imprimir comprovante de cancelamento
          </ada-button>
        </travel-extrato>
      </template>
      <card-hoteis-destino v-if="!showRetailMediaTelaViagem" :destiny-name="travel.grupo.destino.name" source-page="detalhes-viagem" class="mb-1" />
      <template v-if="!isRevendedor && !sobeComponenteSeguranca">
        <h2 class="title-sm mb-1 pt-1">
          Opções de segurança
        </h2>
        <ada-card class="mb-2 o-hidden" outlined rounded>
          <opcoes-seguranca @onclose="close" />
        </ada-card>
      </template>

      <template v-if="!isRevendedor && showSensitiveInfo">
        <h2 class="title-sm mb-1 pt-1">
          Ajuda 24h
        </h2>
        <travel-faq
          :can-ask-noshow-cupom="canAskNoshowCupom"
          :is-travel-compartilhada="isTravelCompartilhada"
          :can-remarcar-gratis="canRemarcarGratis"
          class="mb-2"
          @cancel-travel="onCancelTravel"
          @click-chat-type="chatClick"
        />
      </template>

      <template v-if="!showSensitiveInfo && !loggedIn">
        <h3 class="title-xs mb-1">
          Não encontrou o que queria?
        </h3>

        <ada-card class="p-2" outlined rounded>
          <p>
            <router-link :to="{ name: 'login' }">
              Faça o login e acesse sua conta
            </router-link>
            para mais detalhes.
          </p>
        </ada-card>
      </template>
    </div>

    <popup-remove-passenger
      v-if="popupRemovePassengerConfirmationVisible"
      :passenger="passengerToRemove"
      :loading="removingPassenger"
      :is-dinheiro="isDinheiro"
      :taxa-cancelamento="taxaCancelamento"
      :last-passenger="isLastPassenger"
      @confirm="removePassenger"
      @close="closePopupRemovePassenger"
    />

    <popup-error-help
      v-if="popupErrorHelpVisible"
      :error="rodoviariaErrorResponse.error"
      :help="rodoviariaErrorResponse.help"
      :type="rodoviariaErrorResponse.type"
      @close="closePopupErrorHelp"
      @click-chat-type="chatClick"
    />
  </div>
</template>

<script>
import { faClock } from '@fortawesome/pro-regular-svg-icons'
import dayjs from 'dayjs'
import { mapState, mapActions } from 'pinia'
import { removePassenger, canAskNoshowCupom } from '~api/travel.js'
import EventBus from '~/helpers/eventbus.js'
import { pluralize } from '~/helpers/formatters.js'
import { isMarketplace, isMarketplaceOrHibrido, groupsHaveBuserPremium } from '~/helpers/grouphelper.js'
import { montarMensagemRemarcacao } from '~/helpers/remarcargratuitamentehelper.js'
import { canBeCanceled } from '~/helpers/travelhelper.js'
import { useRevendedorStore } from '~/stores/revendedor.js'
import { useSessionStore } from '~/stores/session.js'
import { useSettingsStore } from '~/stores/settings.js'
import { useToastStore } from '~/stores/toast.js'
import { useViagemStore } from '~/stores/viagem.js'
import alertaImprimirBpe from '~/components/shared/alerta-imprimir-bpe/alerta-imprimir-bpe.vue'
import localRetiradaMarketplace from '~/components/shared/local-retirada-marketplace/local-retirada-marketplace.vue'
import opcoesSeguranca from '~/components/shared/opcoes-seguranca/opcoes-seguranca.vue'
import popupErrorHelp from '~/components/shared/popup-error-help/popup-error-help.vue'
import resumoViagem from '~/components/shared/resumo-viagem/resumo-viagem.vue'
import addonCarousel from '~/components/addon/addon-carousel/addon-carousel.vue'
import addonMarcacaoAssento from '~/components/addon/addon-marcacao-assento/addon-marcacao-assento.vue'
import cardHoteisDestino from '~/components/parceria/decolar/card-hoteis-destino.vue'
import detalhesReembolso from '~/components/reserva/cancelar-reserva/reembolso/detalhes-reembolso/detalhes-reembolso.vue'
import boardingDocuments from '~/components/viagens/boarding-documents/boarding-documents.vue'
import cardAlteracoes from '~/components/viagens/card-alteracoes/card-alteracoes.vue'
import cardBagagens from '~/components/viagens/card-bagagens/card-bagagens.vue'
import CardBuserPremium from '~/components/viagens/card-buser-premium/card-buser-premium.vue'
import cardSeguroAdicional from '~/components/viagens/card-seguro-adicional/card-seguro-adicional.vue'
import itinerario from '~/components/viagens/itinerario.vue'
import passageiros from '~/components/viagens/passageiros/passageiros.vue'
import popupRemovePassenger from '~/components/viagens/popup-remove-passenger.vue'
import travelAlerts from '~/components/viagens/travel-alerts/travel-alerts.vue'
import retailMediaBanner from '~/components/viagens/travel-details/retail-media/retail-media.vue'
import travelExtrato from '~/components/viagens/travel-extrato/travel-extrato.vue'
import travelFaq from '~/components/viagens/travel-faq/travel-faq.vue'
import travelHistoric from '~/components/viagens/travel-historic/travel-historic.vue'
import travelInfo from '~/components/viagens/travel-info/travel-info.vue'
import pagamentoPendente from '~/components/viagens/travel-pagamento-pendente/travel-pagamento-pendente.vue'
const allowedStatusesForRemarcacaoGratis = ['viagem_confirmada', 'embarque_proximo', 'onibus_em_transito']

export default {
  props: {
    travel: {
      type: Object,
      required: true
    },
    showSensitiveInfo: {
      type: Boolean,
      default: false
    },
    busArrivalInfo: {
      type: Object,
      default: () => ({})
    },
    currentEvent: {
      type: Object,
      required: true
    }
  },
  components: {
    alertaImprimirBpe,
    localRetiradaMarketplace,
    travelInfo,
    itinerario,
    passageiros,
    popupRemovePassenger,
    popupErrorHelp,
    travelHistoric,
    resumoViagem,
    travelAlerts,
    detalhesReembolso,
    travelFaq,
    cardHoteisDestino,
    travelExtrato,
    boardingDocuments,
    cardAlteracoes,
    opcoesSeguranca,
    cardSeguroAdicional,
    cardBagagens,
    addonCarousel,
    pagamentoPendente,
    retailMediaBanner,
    CardBuserPremium,
    desembarqueRapidoAlert: () => import('~/components/shared/desembarque-rapido-alert.vue'),
    addonMarcacaoAssento,
    actionsTravelRevendedor: () => import('~/components/revendedor/actions-travel-revendedor.vue')
  },
  emits: ['reload'],
  data() {
    return {
      popupRemovePassengerConfirmationVisible: false,
      passengerToRemove: {},
      removingPassenger: false,
      isPrintingReembolso: false,
      rodoviariaErrorResponse: {},
      popupErrorHelpVisible: false,
      askNoshowCupom: false,
      noshowCupomValidationError: '',
      alteracoesTravel: {},
      upgradeInfo: {},
      faClock
    }
  },
  computed: {
    ...mapState(useSessionStore, ['loggedIn', 'user']),
    ...mapState(useRevendedorStore, ['isRevendedor']),
    ...mapState(useViagemStore, [
      'extrato',
      'travelStatuses',
      'taxaCancelamento',
      'loadingTravelStatuses',
      'hasBpe',
      'loadingBpe',
      'alteracoesInbox'
    ]),
    ...mapState(useSettingsStore, ['permiteBotaoEmergencia']),
    ...mapState(useSessionStore, ['user']),
    isBuserUser() {
      return this.user?.email.includes('@buser.com.br')
    },
    arrivals() {
      let itinerario = this.travel?.grupo?.itinerario || []

      if (!itinerario.length) return itinerario

      return itinerario.slice(0, itinerario.length - 1).map(itinerario => itinerario.local.nickname)
    },
    shouldShowAlertDesembarqueRapido() {
      return this.travel.grupo.destino?.desembarque_rapido
    },
    sobeComponenteSeguranca() {
      const now = dayjs()
      if (this.isMobile && this.travel.grupo.modelo_venda === 'buser' && this.permiteBotaoEmergencia) {
        if (this.travel.grupo.checkin_status === 'going' || this.travel.grupo.checkin_status === 'boarding') {
          return true
        }
        return this.travel.grupo.checkin_status === 'travel_done' && dayjs(this.travel.grupo.checkin_status_updated_at).add('30', 'minutes') >= now && dayjs(this.travel.grupo.checkin_status_updated_at) <= now
      }
      return false
    },
    isTravelCompartilhada() {
      for (const pax of this.travel.passengers) {
        if (pax.linked_user === this.user?.id) {
          return true
        }
      }
      return false
    },
    busArrivalDatetime() {
      return this.busArrivalInfo?.arrival_datetime
    },
    hasTravel() {
      return !!this.travel?.id
    },
    canBeCanceled() {
      return canBeCanceled(this.travel)
    },
    isWaitingPayment() {
      return this.travel.payment?.status === 'waiting_payment'
    },
    isCanceled() {
      return this.travel.status === 'canceled' || ['viagem_cancelada', 'reserva_cancelada'].includes(this.currentEvent.status)
    },
    isCanceledOrFinished() {
      return ['done', 'canceled', 'noshow'].includes(this.travel.status)
    },
    isViagemPendente() {
      return ['pending', 'travel_confirmed', 'closed'].includes(
        this.travel.status
      )
    },
    showRetailMediaTelaViagem() {
      const isViagemConfirmada = this.currentEvent.status === 'viagem_confirmada'
      const isEmbarqueRealizado = this.currentEvent.status === 'embarque_realizado'
      const isDuranteAViagem = dayjs(this.travel.grupo.datetime_ida).isBefore(dayjs())
      const isAte1HoraAntesDaViagem = dayjs().isBefore(dayjs(this.travel.grupo.datetime_ida).subtract(1, 'hour'))
      return (isDuranteAViagem && isViagemConfirmada) || isEmbarqueRealizado || isAte1HoraAntesDaViagem
    },
    isMarketplace() {
      return isMarketplace(this.travel.grupo)
    },
    isMarketplaceOrHibrido() {
      return isMarketplaceOrHibrido(this.travel.grupo)
    },
    pagamentoPendenteTravel() {
      return this.travel.payment?.status === 'waiting_payment'
    },
    pagamentoPendenteUpsell() {
      return this.upgradeInfo?.pagamento?.status === 'waiting_payment'
    },
    pagamentoPendenteData() {
      if (!this.pagamentoPendenteTravel && !this.pagamentoPendenteUpsell) {
        return
      }
      const pagamentoTravel = this.travel.payment
      const pagamentoUpsell = this.upgradeInfo?.pagamento
      return this.pagamentoPendenteTravel ? pagamentoTravel : pagamentoUpsell
    },
    tipoPagamentoPendente() {
      return this.pagamentoPendenteTravel ? 'reserva' : 'upsell'
    },
    isDinheiro() {
      return this.travel.payment?.method === 'dinheiro'
    },
    historico() {
      return this.travelStatuses?.historico_viagem || []
    },
    mostraNovoComprovanteCancelamento() {
      return (
        this.isCanceled && // apenas reservas que foram canceladas
        !!this.extrato?.refunds?.length &&
        !['PIX_UNPAID', 'BOLETO_UNPAID'].includes(this.travel?.canceled_reason)
      )
    },
    canAskNoshowCupom() {
      return this.askNoshowCupom
    },
    empresa() {
      return this.travel.grupo?.company_name
    },
    hasAlteracoes() {
      return this.alteracoesInbox.length > 0
    },
    passengersTitle() {
      return pluralize(this.travel.passengers?.length, 'Passageiro', 'Passageiros', true)
    },
    canRemarcarGratis() {
      if (!this.permiteRemarcacaoGratuita) {
        return false
      }
      const now = dayjs()
      const passouPrevisaoChegada = now.diff(dayjs(this.busArrivalDatetime), 'minutes') > 0
      if (this.alteracoesTravel?.pode_remarcar_gratis && allowedStatusesForRemarcacaoGratis.includes(this.currentEvent.status)) {
        const motivos_alteracoes = ['atraso', 'downgrade', 'alteracao_local_embarque', 'alteracao_local_desembarque', 'alteracao_horario_embarque', 'alteracao_duracao_ida']
        if (this.alteracoesTravel?.motivos.includes('atraso')) {
          return !passouPrevisaoChegada
        } else if (this.alteracoesTravel?.motivos.some(motivo => motivos_alteracoes.includes(motivo))) {
          return now.diff(dayjs(this.travel.grupo.datetime_ida), 'minutes') < 0
        }
      }
      return false
    },
    textoRemarcarGratuitamente() {
      return montarMensagemRemarcacao(this.alteracoesTravel)
    },
    canEditPassengers() {
      const travelDatetimeIda = dayjs(this.travel.grupo.datetime_ida)
      return dayjs().isBefore(travelDatetimeIda.add(-3, 'hours')) && !this.isCanceledOrFinished
    },
    embarqueProximo() {
      const travelDatetimeIda = dayjs(this.travel.grupo.datetime_ida)
      return dayjs().isAfter(travelDatetimeIda.add(-2, 'hours')) && !this.isCanceledOrFinished
    },
    isLastPassenger() {
      return this.travel?.passengers.filter(p => !p.removed).length === 1
    },
    hasUpgradeAvailable() {
      return this.upgradeInfo?.assento?.upgrade_available || this.upgradeInfo?.bagagem?.upgrade_available || this.upgradeInfo?.seguro?.upgrade_available
    },
    habilitarUpgrade() {
      return !this.isCanceledOrFinished && this.showSensitiveInfo && !this.isTravelCompartilhada
    },
    hasMarcacaoAssento() {
      return this.upgradeInfo?.marcacao_assento?.upgrade_available && this.isBuserUser
    },
    isBuserPremium() {
      return groupsHaveBuserPremium([this.travel.grupo])
    },
    textoAtendimentoMarketplace() {
      let texto = `A operação após a venda da passagem neste modelo é de responsabilidade da empresa <strong>${this.empresa}</strong>`
      if (this.travel.grupo.operacao_configs?.modelo_atendimento === 'buser') {
        texto += ' e o atendimento é de responsabilidade da Buser'
      }
      return texto
    }
  },
  async mounted() {
    this.alteracoesTravel = await this.verificaPodeCancelarGratuitamente()
    if (!this.hasTravel) return

    if (this.showSensitiveInfo) {
      if (this.isMarketplaceOrHibrido) {
        this.loadHasBpe()
      }
      if (!this.taxaCancelamento) {
        this.loadTaxaCancelamento(this.travel.id)
      }
      if (dayjs().isAfter(this.travel.grupo.datetime_ida)) {
        this.fetchNoshowCupom()
      }
      if (this.habilitarUpgrade) {
        this.upgradeInformation()
      }
    }

    if (this.isViagemPendente) {
      this.loadAlteracoesInbox()
    }
  },
  methods: {
    ...mapActions(useToastStore, { openToast: 'open' }),
    ...mapActions(useViagemStore, ['loadUpgradeInfo', 'loadHasBpe', 'loadAlteracoesInbox', 'loadTaxaCancelamento', 'loadCanRemarcarGratis']),
    ...mapState(useSettingsStore, ['permiteRemarcacaoGratuita']),
    async upgradeInformation() {
      this.upgradeInfo = await this.loadUpgradeInfo()
      if (this.upgradeInfo) {
        this.upgradeInfo.travelId = this.travel?.id
      }
    },
    close() {
      // o lint ficou no meu pé pq emiti um evento e nao usei nesse componente (mas preciso no outro)
    },
    print() {
      window.onafterprint = () => {
        this.isPrintingReembolso = false
      }

      this.isPrintingReembolso = true

      // o Nexttick aqui é para garantir que o componente foi renderizado antes de abrir a tela de impressão
      this.$nextTick(() => {
        window.print()
      })
    },
    onCancelTravel(incentivo = null) {
      this.$router.push({
        name: 'cancelarReserva',
        params: {
          id: this.travel.id,
          step: 'motivo',
          incentivo
        }
      })
    },
    onRemovePassenger(passenger) {
      if (this.canBeCanceled && !this.isWaitingPayment) {
        this.passengerToRemove = JSON.parse(JSON.stringify(passenger))
        this.popupRemovePassengerConfirmationVisible = true
      } else {
        this.openToast({
          message:
            'Não é possível remover passageiros, pois esta reserva possui um pagamento pendente.',
          type: 'error'
        })
      }
    },
    closePopupRemovePassenger() {
      this.passengerToRemove = {}
      this.popupRemovePassengerConfirmationVisible = false
    },
    async removePassenger() {
      try {
        this.removingPassenger = true
        const response = await removePassenger(
          this.passengerToRemove.pid
        )

        if (response.error) {
          if (response.help) {
            this.handleRodoviariaErrorResponse(response)
            this.closePopupRemovePassenger()
            return
          }

          throw new Error(response.error)
        }
        this.$set(this.passengerToRemove, 'removed', true)
        this.closePopupRemovePassenger()
        this.reload()
      } catch (error) {
        this.openToast({
          message: error.message,
          type: 'error',
          error
        })
      } finally {
        this.removingPassenger = false
      }
    },
    async fetchNoshowCupom() {
      try {
        const response = await canAskNoshowCupom(this.travel.id)

        this.askNoshowCupom = response.can_ask_noshow_cupom
        // O back retorna um atributo no payload informando o tipo de erro de validação
        this.noshowCupomValidationError = response?.validation_error || ''
      } catch (error) {
        this.askNoshowCupom = false
      }
    },
    handleRodoviariaErrorResponse(response) {
      this.rodoviariaErrorResponse = response
      this.popupErrorHelpVisible = true
    },
    closePopupErrorHelp() {
      this.rodoviariaErrorResponse = {}
      this.popupErrorHelpVisible = false
    },
    chatClick(params) {
      const { id, grupo: { origem, destino } } = this.travel
      const trecho = `${origem.slug}=${destino.slug}`
      EventBus.$emit('opened-help-chat',
        { ...params, viagem: { id, trecho } }
      )
    },
    reload() {
      this.$emit('reload')
    },
    async verificaPodeCancelarGratuitamente() {
      if (this.loggedIn && this.permiteRemarcacaoGratuita) {
        return await this.loadCanRemarcarGratis()
      }
      return {}
    }
  }
}
</script>

<style lang="scss">
.travel-details {
  .td-print-reembolso {
    display: none;
  }
}

@media print {
  @page {
    size: 21cm 29.7cm;
    margin: $spacing-3;
  }

  .travel-details {
    .td-print-reembolso {
      display: flex !important;
      justify-content: center;
      align-items: center;
    }

    .td-no-print {
      display: none !important;
    }
  }

  .taskbar,
  .nav-bottom,
  .no-print {
    display: none;
  }
}
</style>
