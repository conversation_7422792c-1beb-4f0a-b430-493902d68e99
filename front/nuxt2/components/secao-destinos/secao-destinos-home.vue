<template>
  <ada-container class="px-0">
    <secao-destinos-base
      :origin="origem"
      :filter-categories="categories"
      :empty-results="destinations.length === 0 && !loading"
      :hide-filters="hideFilters"
      :show-header-skeleton="loading"
      exit-button-text="Ver mais destinos"
      title="Descubra novos destinos"
      :tag="currentTag"
      @city-changed="onCityChange"
      @tag-changed="onChangeTag"
    >
      <template #cards>
        <ada-carousel
          v-if="layout === 'classic'"
          class="mb-3"
          columns-xl="4"
          columns-lg="4"
          columns-md="3"
          columns-sm="2"
          columns-xs="1"
        >
          <template v-if="loading">
            <ada-carousel-item v-for="x in 4" :key="x">
              <card-destination-skeleton v-for="i in 2" :key="i" />
            </ada-carousel-item>
          </template>
          <template v-else-if="agrupados.length > 0">
            <ada-carousel-item v-for="destinos in agrupados" :key="destinos[0].id">
              <card-destination
                v-for="destino in destinos"
                :key="destino.id"
                class="mb-2"
                :destination="destino"
                :origin="origem"
                price-inside-img
              />
            </ada-carousel-item>
          </template>
        </ada-carousel>

        <new-secao-destinos-home
          v-else-if="layout === 'new'"
          class="pb-2"
          :origin="origem"
          :destinos="destinations"
          :loading="loading"
        />
      </template>
    </secao-destinos-base>
  </ada-container>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { computed, onMounted, Ref, ref } from 'vue'
import { listDescubraDestinos } from '~api/place.js'
import { useGeolocationStore } from '~/stores/geolocation.js'
import cardDestinationSkeleton from '~/components/shared/card-destination/card-destination-skeleton.vue'
import cardDestination from '~/components/shared/card-destination/card-destination.vue'
import { CategoryType } from '~/components/descubra/descubra-filter-categories/categories.ts'
import { Place } from '~/components/search-result/grupos/types.ts'
import secaoDestinosBase from '~/components/secao-destinos/secao-destinos-base.vue'
import newSecaoDestinosHome from './new-secao-destinos-home.vue'

type LayoutType = 'classic' | 'new'

const props = withDefaults(defineProps<{ origemSlug?: string, defaultTag?: string, layout?: LayoutType }>(), {
  layout: 'classic',
  defaultTag: ''
})
const categories = ref([CategoryType.DESTINOS_NO_SUL, CategoryType.MENOR_PRECO, CategoryType.BATE_E_VOLTA])
const destinations = ref<Array<Place>>([])
const loading = ref(true)
const origem = ref(undefined)
const geolocationStore = useGeolocationStore()
const { currentPosition }: { currentPosition: Ref<Place | undefined> } = storeToRefs(geolocationStore)
const currentTag = ref(props.defaultTag)

onMounted(async() => {
  let slug = props.origemSlug
  if (!slug) {
    slug = currentPosition.value?.slug || 'sao-paulo-sp'
  }

  await fetchData(slug, currentTag.value)
  loading.value = false
})

async function fetchData(slug: string, tag: string) {
  loading.value = true
  currentTag.value = tag

  if (props.origemSlug) {
    slug = props.origemSlug
  }

  const quantidadeDeDestinos = props.layout === 'classic' ? 8 : 5
  const lastImageFeatured = props.layout === 'new'

  const promiseDestinos = [listDescubraDestinos(slug, tag, 1, quantidadeDeDestinos, lastImageFeatured)]

  if (tag === 'destinos no sul') {
    promiseDestinos.push(fallbackTagDestinosNoSul(slug, quantidadeDeDestinos, lastImageFeatured))
  }

  const response = await Promise.allSettled(promiseDestinos)
  const [principal, fallback] = response.map(x => x.value)

  if (principal?.destinationCities.length > 0) {
    const { originCity, destinationCities } = principal
    destinations.value = destinationCities
    origem.value = originCity
    loading.value = false
    return
  }

  destinations.value = fallback?.destinationCities || []
  origem.value = fallback?.originCity || {}
  categories.value[0] = CategoryType.PARA_VOCE
  loading.value = false
}

async function fallbackTagDestinosNoSul(slug: string, quantidadeDeDestinos: number, lastImageFeatured: boolean) {
  return await listDescubraDestinos(slug, '', 1, quantidadeDeDestinos, lastImageFeatured)
}

const onChangeTag = (tag: string) => {
  if (tag === currentTag.value) return

  const slug = origem.value?.slug
  if (!slug) {
    return
  }

  fetchData(slug, tag)
}
const onCityChange = ({ slug }) => {
  if (!slug || origem.value === slug) {
    return
  }

  fetchData(slug, '')
}

const agrupados = computed(() => {
  const acc = []

  for (let i = 0; i < destinations.value.length; i += 2) {
    const fatia = destinations.value.slice(i, i + 2)

    acc.push(fatia)
  }

  return acc
})

const hideFilters = computed(() => {
  return destinations.value.length === 0 && !loading.value
})
</script>
