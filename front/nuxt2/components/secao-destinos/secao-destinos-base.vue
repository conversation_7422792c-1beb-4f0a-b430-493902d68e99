<template>
  <div class="secao-destinos-base">
    <section class="sdb-section">
      <secao-destinos-base-header-skeleton v-if="showHeaderSkeleton" />
      <ada-container
        v-else
        class="sdbc-header"
        :class="{ 'px-0': removePaddingHeader }"
      >
        <h3 class="sdbch-title title-sm">
          {{ title }}
        </h3>
        <descubra-filter-categories
          v-show="!hideFilters"
          :value="tag"
          class="sdbch-categories"
          :base-categories="filterCategories"
          @change="onTagChange"
        />
      </ada-container>

      <div :class="{ 'empty-results': emptyResults }">
        <slot name="cards" />
        <empty-results-card v-if="emptyResults" :origin="origin" @city-changed="$emit('city-changed', $event)" />
      </div>
    </section>

    <ada-container class="sdb-button" :class="{ 'px-0': removePaddingBtn }">
      <ada-button
        class="sdb-button"
        outlined
        :to="{
          name: 'descubra',
          params: { cidadeSlug: origin?.slug },
          query: { tag: tag }
        }"
      >
        {{ exitButtonText }}
        <fa :icon="faArrowRight" class="ml-1" />
      </ada-button>
    </ada-container>
  </div>
</template>

<script setup lang="ts">
import { faArrowRight } from '@fortawesome/pro-regular-svg-icons'
import emptyResultsCard from '~/components/shared/empty-results-card.vue'
import { type CategoryType } from '~/components/descubra/descubra-filter-categories/categories.ts'
import descubraFilterCategories from '~/components/descubra/descubra-filter-categories/descubra-filter-categories.vue'
import { Place } from '~/components/search-result/grupos/types.ts'
import secaoDestinosBaseHeaderSkeleton from '~/components/secao-destinos/secao-destinos-base-header-skeleton.vue'

const emit = defineEmits(['city-changed', 'tag-changed'])

defineProps<{
  title?: string,
  filterCategories?: Array<CategoryType>,
  origin?: Place,
  emptyResults?: Boolean,
  hideFilters?: Boolean,
  showHeaderSkeleton?: Boolean,
  exitButtonText?: string,
  removePaddingHeader?: Boolean,
  removePaddingBtn?: Boolean,
  tag?: string,
}>()

const onTagChange = (tagEvent: string) => {
  emit('tag-changed', tagEvent)
}

</script>

<style lang="scss" scoped>
.secao-destinos-base {
  .sdb-section {
    margin-top: $spacing-6;
    display: flex;
    flex-direction: column;

    .sdbc-header {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: $spacing-2;
      margin-bottom: $spacing-2;

      @media (min-width: $screen-tablet-min) {
        flex-direction: row;
        align-items: center;
      }

      .sdbch-categories {
        flex: 0 0 min-content;
        margin-bottom: $spacing-1;
      }

    }

    .empty-results {
      margin-bottom: $spacing-2;
    }
  }

  .sdb-button {
    display: flex;
    min-width: 260px;
    justify-content: center;
  }
}
</style>
