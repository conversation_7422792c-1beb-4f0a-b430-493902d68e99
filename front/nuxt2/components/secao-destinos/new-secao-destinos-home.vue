<template>
  <ada-container class="section-destinations px-0">
    <template v-if="$props.loading">
      <div class="cd-multi-cards">
        <span v-for="i in 4" :key="i">
          <ada-skeleton
            :styles="{ minWidth: '257px', minHeight: '247px', borderRadius: '16px' }"
          />
          <ada-skeleton layout="text-md(15)" />
        </span>
      </div>

      <div class="cd-destaque skeleton">
        <ada-skeleton class="h-100 hide-tablet" :styles="{ width: '100%', height: '100%', borderRadius: '16px' }" />
        <ada-skeleton layout="text-md(15)" />
      </div>
    </template>

    <template v-else-if="destinos.length > 0">
      <div class="cd-multi-cards">
        <card-destino
          v-for="(destino, index) in destinos.slice(0, 4)"
          :key="destino.slug"
          v-motion
          v-bind="slideUp({ delay: index * 100 })"
          :destino="destino"
          :origin-slug="origin?.slug || ''"
          width-img="257px"
          height-img="247px"
        />
      </div>

      <card-destino
        v-if="destinos.length >= 4"
        v-motion
        v-bind="slideUp({ delay: 4 * 100 })"
        class="cd-destaque hide-tablet"
        :destino="destinos[destinos.length - 1] || {}"
        :origin-slug="origin?.slug || ''"
        width-img="100%"
        height-img="100%"
        large-content
      />
    </template>
  </ada-container>
</template>

<script setup lang="ts">
import { slideUp } from '~/helpers/motionhelper.js'
import { Place } from '~/components/search-result/grupos/types.ts'
import cardDestino from '~/components/secao-destinos/card-destino.vue'
import { Destino } from '~/components/secao-destinos/types.ts'

defineProps<{
  destinos: Array<Destino>,
  origin?: Place,
  loading: boolean
}>()
</script>

<style scoped lang="scss">
.section-destinations {
  display: grid;

  @media (min-width: $screen-tablet-min) {
    grid-template: 'multi destaque' auto / min-content 1fr;
    grid-column-gap: $spacing-2;
    padding-inline: $spacing-2 !important;
  }

  .cd-multi-cards {
    grid-area: multi;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-2;

    @media (max-width: $screen-tablet-max) {
      mask-image: linear-gradient(90deg,transparent,#000 $spacing-2,#000 calc(100% - #{$spacing-2}),transparent);
      scroll-behavior: smooth;
      scroll-snap-type: x mandatory;
      scroll-padding: 0 $spacing-2;
      padding: 0 $spacing-2;
      overflow: auto;
      scrollbar-width: none;
      overflow-y: hidden;
    }
  }

  .cd-destaque {
    padding-bottom: $spacing-4;
    grid-area: destaque;

    &.skeleton {
      padding-bottom: $spacing-3;
    }
  }
}
</style>
