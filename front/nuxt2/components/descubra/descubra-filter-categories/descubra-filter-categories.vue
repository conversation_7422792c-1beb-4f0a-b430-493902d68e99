<template>
  <ada-toggleable ref="container" :value="selectedValue" class="descubra-filter-categories" required @input="onChange">
    <ada-toggleable-item
      v-for="categoria in categorias"
      v-slot="{ isActive, toggle }"
      :key="categoria.id"
      :value="categoria.value"
    >
      <ada-chip :ref="categoria.value" :icon="getIcon(categoria.icone)" :selected="isActive" small @click="toggle">
        {{ categoria.titulo }}
      </ada-chip>
    </ada-toggleable-item>
  </ada-toggleable>
</template>

<script>
import {
  faBus,
  faMoneyBillWave,
  faBoltLightning,
  faIslandTropical,
  faBenchTree,
  faMountains,
  faBuildings,
  faVolleyballBall,
  faStar
} from '@fortawesome/pro-regular-svg-icons'
import { MapCategory, CategoryType } from './categories.ts'

const ICONS = {
  'money-bill-wave': faMoneyBillWave,
  'bolt-lightning': faBoltLightning,
  'island-tropical': faIslandTropical,
  'bench-tree': faBenchTree,
  mountains: faMountains,
  buildings: faBuildings,
  volleyball: faVolleyballBall,
  star: faStar
}

export default {
  props: {
    value: {
      type: String
    },
    filterCategories: {
      type: Array,
      default: () => []
    },
    baseCategories: {
      type: Array,
      default: () => [CategoryType.TODOS, CategoryType.MENOR_PRECO, CategoryType.BATE_E_VOLTA]
    }
  },
  emits: ['change'],
  computed: {
    selectedValue() {
      return this.categorias.some(({ value }) => value === this.value)
        ? this.value
        : 'todos'
    },
    categorias() {
      const categorias = this.baseCategories.map((categoria) => {
        return MapCategory[categoria]
      })

      return categorias.concat(this.filterCategories)
    }
  },
  mounted() {
    const container = this.$refs.container
    const refSelectedValue = this.$refs[this.selectedValue] ? this.$refs[this.selectedValue][0] : null

    if (!(refSelectedValue && typeof container?.$el?.scrollTo === 'function')) return

    // 16 é o gap entre os items + padding do container
    const spacingOffset = 16
    const leftOffset = refSelectedValue.$el.offsetLeft - spacingOffset
    container.$el.scrollTo({ left: leftOffset, behavior: 'smooth' })
  },
  methods: {
    onChange(category) {
      const invalids = ['todos', 'recomendados', 'para-voce']

      this.$emit('change', invalids.includes(category) ? '' : category)
    },
    getIcon(icon) {
      return ICONS[icon] || faBus
    }
  }
}
</script>

<style lang="scss" scoped>
.descubra-filter-categories {
  $item-spacing: 2px;

  display: flex;
  padding: $item-spacing;
  flex-wrap: nowrap;
  overflow-x: auto;
  gap: $spacing-1;
  padding-bottom: $spacing-half;

  @media (min-width: $screen-tablet-min) {
    margin: 0;
    width: 100%;
  }
}
</style>
