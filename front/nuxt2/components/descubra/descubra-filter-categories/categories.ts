import { faBus, faMoneyBillWave, faBoltLightning, faStar, faWandMagicSparkles } from '@fortawesome/pro-regular-svg-icons'

export enum CategoryType {
  TODOS,
  MENOR_PRECO,
  BATE_E_VOLTA,
  RECOMENDADOS,
  PARA_VOCE,
  DESTINOS_NO_SUL
}

interface Category {
  titulo: string;
  value: string;
  icone: object;
}

export const MapCategory: Record<CategoryType, Category> = {
  [CategoryType.TODOS]: {
    titulo: 'Todos',
    value: 'todos',
    icone: faBus
  },
  [CategoryType.MENOR_PRECO]: {
    titulo: 'Menor preço',
    value: 'menor-preco',
    icone: faMoneyBillWave
  },
  [CategoryType.BATE_E_VOLTA]: {
    titulo: 'Bate e volta',
    value: 'bate-volta',
    icone: faBoltLightning
  },
  [CategoryType.RECOMENDADOS]: {
    titulo: 'Recomendados',
    value: 'recomendados',
    icone: faStar
  },
  [CategoryType.PARA_VOCE]: {
    titulo: 'Para você',
    value: 'para-voce',
    icone: faWandMagicSparkles
  },
  [CategoryType.DESTINOS_NO_SUL]: {
    titulo: 'Destinos no sul',
    value: 'destinos no sul',
    icone: faBus
  }
}
