<template>
  <div class="checkout">
    <ada-container full-width-mobile class="c-container">
      <div class="cc-content">
        <div class="ccc-column">
          <div ref="payment">
            <checkout-payment-methods
              v-if="upsellInfo && !isLoadingAddonsInfo"
              ref="paymentMethodsSection"
              class="cccc-checkout-card"
              :is-item-adicional="true"
              @purchase="purchase"
            />
          </div>

          <checkout-bagagem-adicional
            v-if="bagagemAdicionalLiberada"
            ref="bagagemAdicional"
            class="mb-2"
            :valor-bagagem-adicional="upsellInfo?.bagagem?.price"
            :max-bagagens="upsellInfo?.bagagem?.max_quantity"
            @add-bagagem-extra="updateBagagemAdicional"
          />

          <checkout-poltronas
            v-if="showCheckoutPoltronas"
            class="cccc-checkout-card"
            :layout-onibus-ida="layoutOnibusIda"
            :passengers="paxSemMarcacaoAssento"
            :grupo-ida="grupos[0]"
            @update-poltronas="updatePoltronasSelected"
          />
        </div>

        <div class="ccc-column">
          <checkout-group
            :grupos="grupos"
            title="Viagem de ida"
            :is-conexao="grupos.length > 1"
            :loading="!grupos"
            class="cccc-checkout-card is-date-ida"
          />

          <checkout-seguro-extra
            v-if="mostrarSeguroExtra"
            ref="seguroExtra"
            class="cccc-checkout-card"
            @toggle-seguro-extra="updateSeguroExtra"
            @birthdays-updated="updatePassengersBirthdays"
          />
          <div class="cccc-checkout-card ">
            <checkout-statement />
          </div>

          <div ref="acceptance">
            <checkout-terms class="cccc-checkout-card" @toggle-termos-de-uso="handleAceitouCheckoutTerms" />
          </div>

          <ada-alert v-if="reservationData?.user?.email && aceitouCheckoutTerms" type="info" hide-icon class="mb-2">
            <p class="cccc-termos-alert">
              Sua reserva será ligada ao email <strong>{{ reservationData.user.email }}</strong>
            </p>
          </ada-alert>

          <ada-button block :loading="processingPurchase || isLoadingStatement" class="cccc-pay" @click="purchase">
            {{ processingPurchase ? 'Processando...' : 'Confirmar e pagar' }}
          </ada-button>

          <checkout-beneficios :grupos-ida="grupos" />
        </div>
      </div>
    </ada-container>

    <popup
      v-model="hasStatementError"
      :modal-options="{ maxWidth: '520px' }"
      :persistent="persistentPopup"
      :title="errorTitle"
    >
      <checkout-error :statement="completeStatement" :idas="grupos" />
    </popup>

    <popup
      v-model="hasRodoviariaError"
      :modal-options="{ maxWidth: '520px' }"
      :persistent="true"
      :title="rodoviariaErrorTitle"
    >
      <checkout-error :statement="erroRodoviaria" :idas="grupos" />
    </popup>

    <popup v-model="hasPaymentErrorAfterPurchase" :modal-options="{ maxWidth: '520px' }" :persistent="false">
      <checkout-rejected-error
        ref="RejectedPopup"
        :statement="completeStatement"
        @retype-cc="retypeCreditCard"
        @confirm-and-pay="purchaseWithUpdatedCreditCardInfo"
        @credit-card-selected="showSelectCreditCardPopup"
        @authorized="openCurrentCreditCard"
      />
    </popup>

    <popup-reserva-in-progress v-if="hasInProgressPurchase" />

    <input id="CS-SessionId" ref="clearSaleInput" type="hidden" value="">
  </div>
</template>

<script>
import { mapActions, mapState, mapWritableState } from 'pinia'
import { $axios } from '~/api/utils/axios.js'
import { purchaseItensAdicionais as purchaseApi } from '~api/checkout.js'
import { checkUpsells, getTravel, checkPaymentsMethodsAllowed } from '~api/travel.js'
import sentryhelper from '~sentryhelper'
import ajaxhandler from '~/helpers/ajaxhandler.js'
import { REJECTED_POPUP_ERRORS } from '~/helpers/checkouthelper.js'
import { injetaScriptClearsale } from '~/helpers/clearsale.js'
import EventBus from '~/helpers/eventbus.js'
import passengerHelper from '~/helpers/passengerhelper.js'
import PAYMENT_ERROR_MAP from '~/helpers/payment-error.js'
import { useReservationStore } from '~/stores/reservation.js'
import { useStatementStore } from '~/stores/statement.js'
import { useToastStore } from '~/stores/toast.js'
import checkoutGroup from '~/components/shared/checkout-group.vue'
import popup from '~/components/shared/popup/popup.vue'
import checkoutBagagemAdicional from '~/components/checkout/checkout-bagagem/checkout-bagagem-adicional.vue'
import checkoutBeneficios from '~/components/checkout/checkout-beneficios/checkout-beneficios.vue'
import checkoutError from '~/components/checkout/checkout-error/checkout-error.vue'
import checkoutPaymentMethods from '~/components/checkout/checkout-payment-methods/checkout-payment-methods.vue'
import checkoutPoltronas from '~/components/checkout/checkout-poltronas/checkout-poltronas.vue'
import checkoutRejectedError from '~/components/checkout/checkout-rejected-error/checkout-rejected-error.vue'
import checkoutSeguroExtra from '~/components/checkout/checkout-seguro-extra/checkout-seguro-extra.vue'
import checkoutStatement from '~/components/checkout/checkout-statement/checkout-statement.vue'
import checkoutTerms from '~/components/checkout/checkout-terms/checkout-terms.vue'
import popupReservaInProgress from '~/components/checkout/popup-reserva-in-progress/popup-reserva-in-progress.vue'

export default {
  name: 'CheckoutAddon',
  components: {
    popup,
    checkoutStatement,
    checkoutSeguroExtra,
    checkoutBeneficios,
    checkoutPaymentMethods,
    checkoutTerms,
    checkoutError,
    checkoutBagagemAdicional,
    checkoutRejectedError,
    checkoutGroup,
    popupReservaInProgress,
    checkoutPoltronas
  },
  data() {
    return {
      processingPurchase: false,
      hasStatementError: false,
      aceitouCheckoutTerms: false,
      persistentPopup: true,
      hasPaymentErrorAfterPurchase: false,
      errorTitle: '',
      hasRodoviariaError: false,
      rodoviariaErrorTitle: '',
      erroRodoviaria: {},
      hasInProgressPurchase: false,
      upsellInfo: null,
      grupos: [],
      quantBagagemExtra: 0,
      passengersBirthdays: {},
      isLoadingAddonsInfo: false,
      saldoReais: 0
    }
  },
  props: {
    lastPage: {
      default: null,
      type: String
    }
  },
  emits: ['set-group', 'purchase-finished'],
  async mounted() {
    this.travelId = this.$route?.params?.travel
    this.isLoadingAddonsInfo = true
    try {
      this.upsellInfo = await checkUpsells(this.travelId)
    } catch (error) {
      EventBus.$emit('sem-upsell-disponivel')
      this.openToast({ message: error, type: 'error', error })
      this.toTravelPage()
    }

    this.initReservation()
    this.travel = await getTravel(this.travelId, null, true)
    if (this.travel?.has_marcacao_assento && this.travel?.grupo) {
      this.travel.grupo.has_marcacao_assento = true
      try {
        this.$emit('set-group', this.travel.grupo)
      } catch (error) {
        sentryhelper.captureException(error)
      }
    }
    this.grupos.push(this.travel.grupo)

    if (!this.upsellInfo?.seguro?.upgrade_available && !this.upsellInfo?.bagagem?.upgrade_available) {
      this.toTravelPage()
    }

    this.reservationData = {
      passengers: this.travel?.passengers.filter(p => !p.removed),
      possiblePassengers: this.travel?.passengers.filter(p => !p.removed).map(p => ({ ...p, selected: true })),
      valorSeguroExtra: this.upsellInfo?.seguro?.price || null,
      isItemAdicional: true,
      pagamento_total: 0,
      extrato_item_adicional: [],
      parcelamentoOptions: [],
      payment: null,
      bagagemAdicional: 0,
      acceptance: false,
      user: {},
      paymentMethod: null,
      poltronasMapIda: {},
      valoresMarcacaoAssento: this.upsellInfo?.marcacao_assento?.price_by_trecho_classe || null
    }

    await this.generateExtrato()
    injetaScriptClearsale()
    const data = {
      seguro: this.upsellInfo?.seguro?.upgrade_available,
      bagagem: this.upsellInfo?.bagagem?.upgrade_available,
      marcacao_assento: this.upsellInfo?.marcacao_assento?.upgrade_available,
      upgrade_poltrona: this.upsellInfo?.assento?.upgrade_available,
      upgrade_poltrona_price: this.upsellInfo?.assento?.diff_value,
      travel_id: this.travelId
    }
    EventBus.$emit('acessou-checkout-adicional', data)
    this.isLoadingAddonsInfo = false
  },
  methods: {
    ...mapActions(useToastStore, { openToast: 'open' }),
    ...mapActions(useStatementStore, ['updateStatement']),
    ...mapActions(useReservationStore, ['initReservation']),
    async updatePaymentsInfo() {
      const paymentResponse = await checkPaymentsMethodsAllowed(this.travelId,
        this.reservationData?.payment?.bank_identification_number,
        this.valorTotal)
      this.reservationData = {
        ...this.reservationData,
        ...paymentResponse
      }
      this.saldoReais = paymentResponse?.saldo_reais || 0
    },
    retypeCreditCard() {
      this.hasPaymentErrorAfterPurchase = false
      this.$refs.paymentMethodsSection.retypeCurrentCreditCard()
    },
    openCurrentCreditCard() {
      EventBus.$emit('clicou-btn-ja-autorizei', {
        isItemAdicional: true
      })
      this.hasPaymentErrorAfterPurchase = false
      this.$refs.paymentMethodsSection.regerarHash(false)
    },
    async updateBagagemAdicional(quantBagagemExtra) {
      EventBus.$emit('add-bagagem-adicional', {
        quantidade: quantBagagemExtra
      })
      this.quantBagagemExtra = quantBagagemExtra
      await this.generateExtrato()
    },
    async updateSeguroExtra(seguroExtraValue) {
      EventBus.$emit('atualizou-seguro-extra', {
        valorBase: this.valorTotal,
        valorAcrescimo: seguroExtraValue,
        marcado: this.reservationData?.seguroExtra,
        porcentagem: this.reservationData?.addonsPriceConfig?.seguro_extra?.percentage,
        isItemAdicional: true
      })
      await this.generateExtrato()
    },
    async handleUpdateStatement() {
      try {
        await this.updateStatement({
          ...this.reservationData,
          groups: this.groupsMap,
          email: this.reservationData?.user?.email,
          deviceToken: this.deviceToken,
          isItemAdicional: true
        }, this.$cookies, this.$route?.query)
      } catch (error) {
        this.openToast({ message: error, type: 'error', error })
      } finally {
        this.hasStatementError = !this.completeStatement || !!this.completeStatement?.error
      }
    },
    async updatePaymentValues() {
      const pagamentoTotal = this.reservationData?.pagamento_total || 0
      this.completeStatement.pagamento_total = pagamentoTotal
      await this.handleUpdateStatement()
      if (!this.reservationData.payment || !this.isUserInfoValid) return

      if (this.isCreditCard) {
        this.reservationData.payment.value = this.parcelamentoOptionsCartaoSelecionado[this.reservationData.payment.parcelaCount - 1]?.total_parcelado || 0
        this.reservationData.payment.net_value = pagamentoTotal
        return
      }

      this.reservationData.payment.value = pagamentoTotal
      this.reservationData.payment.net_value = pagamentoTotal
    },
    updatePassengersBirthdays(birthdays) {
      this.passengersBirthdays = birthdays
    },
    createReservationPayload() {
      // Payload
      function _getPassengerBirthday(ctx, passenger) {
        // Atualiza a data de nascimento dos passageiros sem data de nascimento, caso o seguro extra esteja selecionado

        if (!ctx.reservationData.seguroExtra) {
          return
        }

        const uniqueId = passengerHelper.getUniqueID(passenger)
        return ctx.passengersBirthdays[uniqueId]
      }
      const passengers = this.reservationData.passengers.map(p => ({
        id: p.id,
        pid: p.pid,
        name: p.name,
        social_name: p.social_name,
        cpf: p.cpf,
        rg_number: p.rg_number,
        rg_orgao: p.rg_orgao,
        tipo_documento: p.tipo_documento,
        needs_cadeirinha: p.needs_cadeirinha,
        birthday: p.birthday || _getPassengerBirthday(this, p),
        phone: p.phone,
        tipos_deficiencia: p.tipos_deficiencia
      }))

      const payload = {
        travel_id: this.travelId,
        passengers,
        groups: this.groupsMap,
        seguro_extra: this.reservationData.seguroExtra,
        quantidade_bagagem_adicional: this.reservationData.bagagemAdicional,
        valor_bagagem_adicional: this.reservationData.valorBagagemAdicional,
        acceptance: this.reservationData.acceptance,
        pagamento_total: this.valorTotal || 0,
        valor_total: this.reservationData.valorTotal,
        tabid: $axios.defaults.headers.common.tabid,
        reserva_assentos: this.reservationData.poltronasMapIda
      }

      if (this.isCreditCard) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { card, validade, cvv, ...paymentInfo } = this.reservationData.payment
        payload.payment = paymentInfo
        payload.payment.cs_session_id = this.$refs.clearSaleInput.value
      } else {
        payload.payment = this.reservationData.payment
      }

      return payload
    },
    purchaseWithUpdatedCreditCardInfo(updatedFields) {
      EventBus.$emit('viu-popup-adjustable-errors-cc-reject', {
        isItemAdicional: true
      })
      this.$refs.paymentMethodsSection.updateCurrentCreditCard(updatedFields)
      this.hasPaymentErrorAfterPurchase = false
    },
    showSelectCreditCardPopup() {
      this.hasPaymentErrorAfterPurchase = false
      this.$refs.paymentMethodsSection.showSelectCreditCardPopup = true
    },
    async purchase() {
      if (this.processingPurchase) {
        return
      }

      const payload = this.createReservationPayload()

      let validatePayload = this.validatePayload(payload)

      if (this.userPrecisaPagar && this.isCreditCard && !this.paymentHasHash) {
        validatePayload = false
        const needsNewHash = this.completeStatement?.errorCode === 'cc_rejected_bad_filled_security_code' || this.completeStatement?.errorCode === 'bad_filled_card_code'
        this.$refs.paymentMethodsSection.regerarHash(needsNewHash)
      }

      if (!validatePayload) {
        this.processingPurchase = false
        return
      }

      this.showInProgressPurchaseMessage()
      let purchase = null

      try {
        purchase = await purchaseApi(payload)
      } catch (error) {
        ajaxhandler.onUnhandledRejection(error, this.$pinia)
        this.processingPurchase = false
        this.hideInProgessMessage()
        return
      }

      if (purchase?.error === 'Pagamento diferente do esperado') {
        await this.handleUpdateStatement()
        this.openToast({
          message: 'Pagamento diferente do esperado',
          type: 'error'
        })
      }

      this.hideInProgessMessage()
      return this.handleReservation(purchase)
    },
    handleReservation(reservationResult) {
      if (reservationResult.error) {
        this.handleStatementError(reservationResult)
        return
      }
      this.openToast({
        message: `${reservationResult.message}`
      })

      this.emiteEventosDeCompra()

      // usado apenas para não desbloquear as poltronas na saida da navegação; não deve atrapalhar o fluxo
      try {
        this.$emit('purchase-finished')
      } catch (error) {
        sentryhelper.captureException(error)
      }

      this.toTravelPage(this.reservationData?.payment?.payment_method === 'pix')
    },
    emiteEventosDeCompra() {
      if (this.reservationData.bagagemAdicional) {
        EventBus.$emit('solicitou-item-adicional', {
          type: 'bagagem',
          gmv: this.reservationData.bagagemAdicional * this.upsellInfo.bagagem.price,
          qtd: this.reservationData.bagagemAdicional,
          lastPage: this.lastPage

        })
      }

      if (this.reservationData.seguroExtra) {
        EventBus.$emit('solicitou-item-adicional', {
          type: 'seguro',
          gmv: this.reservationData.valorSeguroExtra,
          qtd: this.selectedPassengers.length
        })
      }
    },
    handleStatementError(reservationResult) {
      const { error, title, payment_method } = reservationResult
      this.errorTitle = title
      if (this.userPrecisaPagar && this.isCreditCard && this.paymentHasHash) {
        this.reservationData.payment.card_hash = null
      }
      this.processingPurchase = false

      if (PAYMENT_ERROR_MAP[error]) {
        this.hasPaymentErrorAfterPurchase = true
        this.completeStatement.error = PAYMENT_ERROR_MAP[error].error
        this.completeStatement.errorCode = error
        this.completeStatement.title = PAYMENT_ERROR_MAP[error].title
        this.completeStatement.paymentMethod = this.paymentMethod

        return
      }

      const hasRejectedPaymentError = REJECTED_POPUP_ERRORS.includes(error)

      if (hasRejectedPaymentError) {
        this.hasPaymentErrorAfterPurchase = hasRejectedPaymentError
        this.completeStatement.error = error
        this.completeStatement.errorCode = error
        this.completeStatement.title = title
        this.completeStatement.paymentMethod = payment_method
        return
      }

      this.openToast({
        message: `${error}`,
        type: 'error'
      })
    },
    validatePayload(payload) {
      let valid = true
      let refName = ''
      const validations = {
        passengers: (valid) => (this.validationErrors.passengers = valid),
        acceptance: (valid) => (this.validationErrors.acceptance = valid)
      }

      if (this.userPrecisaPagar) {
        validations.payment = (valid) => (this.validationErrors.payment = valid)
      }

      for (const prop in validations) {
        let validProp = Array.isArray(payload[prop]) ? !!payload[prop].length : !!payload[prop]

        if (validProp && prop === 'payment') {
          // Se o método é cartão de crédito e não envia o card_hash o payload não é válido
          if (payload[prop].payment_method === 'credit_card' && !(payload[prop].card_hash)) {
            validProp = false
          }
        }

        if (!validProp) {
          refName = refName || prop
          valid = false

          if (prop === 'email' || prop === 'name' || prop === 'phone') {
            refName = 'user'
          }
        }

        validations[prop](!validProp)
      }

      // Regra específica para validar a data de nascimento no seguro extra
      if (payload.seguro_extra) {
        const temPaxSemDataNascimento = payload.passengers.some(p => !p.birthday)
        if (temPaxSemDataNascimento) {
          this.validationErrors.seguroExtra = true
          refName = refName || 'seguroExtra'
          valid = false
        }
      }

      this.scrollTo(refName)
      return valid
    },
    scrollTo(refName) {
      if (refName === '' || !this.$andromeda.breakpoint.smAndDown) return

      const element = this.$refs[refName]
      setTimeout(() => {
        element.scrollIntoView({ behavior: 'smooth' })
      })
    },
    handleAceitouCheckoutTerms(isAccepted) {
      this.aceitouCheckoutTerms = isAccepted
    },
    showInProgressPurchaseMessage() {
      this.hasInProgressPurchase = true
      this.processingPurchase = true
    },
    hideInProgessMessage() {
      this.hasInProgressPurchase = false
    },
    toTravelPage(isPixPayment = false) {
      if (!this.travelId) {
        return this.$router.push({ name: 'home' })
      }
      const query = {}
      if (isPixPayment) {
        query.openPixPayment = true
      }
      this.$router.push({ name: 'viagem', params: { id: this.travelId }, query })
    },
    async generateExtrato() {
      let total = 0
      const extrato = []
      if (this.reservationData.seguroExtra) {
        total += this.upsellInfo.seguro.price * this.selectedPassengers.length
        extrato.push({
          title: 'Seguro',
          subtitle: `${this.selectedPassengers.length} passageiro` + (this.selectedPassengers.length > 1 ? 's' : ''),
          value: this.upsellInfo.seguro.price * this.selectedPassengers.length
        })
      }
      if (this.quantBagagemExtra > 0) {
        total += this.upsellInfo.bagagem.price * this.quantBagagemExtra
        extrato.push({
          title: 'Bagagem Adicional',
          subtitle: `${this.quantBagagemExtra} unidade` + (this.quantBagagemExtra > 1 ? 's' : ''),
          value: this.upsellInfo.bagagem.price * this.quantBagagemExtra
        })
      }
      let poltronasSelecionadas = Object.keys(this.reservationData.poltronasMapIda).length
      if (poltronasSelecionadas > 0) {
        total += this.reservationData.valoresMarcacaoAssento[this.travel?.grupo?.id] * poltronasSelecionadas
        extrato.push({
          title: 'Seleção poltrona',
          subtitle: `${poltronasSelecionadas} poltrona` + (poltronasSelecionadas > 1 ? 's' : ''),
          value: this.reservationData.valoresMarcacaoAssento[this.travel?.grupo?.id] * poltronasSelecionadas
        })
      }
      let valorTotal = null
      if (extrato.length) {
        valorTotal = total
        if (this.saldoReais) {
          const saldoUtilizado = this.saldoReais > total ? total : this.saldoReais
          extrato.push({
            title: 'Saldo em Reais',
            value: -saldoUtilizado
          })
          total -= saldoUtilizado
        }
        extrato.push({
          title: 'Total itens adicionais',
          value: total
        })
      }
      this.reservationData.extrato_item_adicional = extrato
      this.reservationData.pagamento_total = extrato.length ? total : null
      this.reservationData.valorTotal = valorTotal
      await this.updatePaymentsInfo()
      await this.updatePaymentValues()
    },
    async updatePoltronasSelected() {
      await this.generateExtrato()
    }
  },
  computed: {
    ...mapState(useStatementStore, ['completeStatement', 'parcelamentoOptions', 'isLoadingStatement']),
    ...mapState(useReservationStore, ['layoutOnibusIda']),
    ...mapWritableState(useReservationStore, ['reservationData', 'validationErrors', 'selectedPassengers']),
    valorTotal() {
      return this.reservationData?.pagamento_total || null
    },
    deviceToken() {
      return this.$cookies.get('apnstoken') || this.$cookies.get('pushtoken')
    },
    isCreditCard() {
      return this.reservationData.payment?.payment_method === 'credit_card'
    },
    paymentHasHash() {
      return !!this.reservationData.payment?.card_hash
    },
    paymentMethod() {
      return this.reservationData.payment?.payment_method
    },
    groupsMap() {
      return this.grupos.length ? this.grupos.map(g => g.id) : []
    },
    bagagemAdicionalLiberada() {
      return this.upsellInfo?.bagagem?.upgrade_available && this.upsellInfo?.bagagem?.price > 0
    },
    mostrarSeguroExtra() {
      return this.upsellInfo?.seguro?.upgrade_available
    },
    userPrecisaPagar() {
      return this.valorTotal >= 2
    },
    parcelamentoOptionsCartaoSelecionado() {
      return this.parcelamentoOptions.find(item => item.bin === this.reservationData.binCartaoSelecionado)?.options || []
    },
    showCheckoutPoltronas() {
      return this.upsellInfo?.marcacao_assento?.upgrade_available && this.paxSemMarcacaoAssento.length > 0
    },
    paxSemMarcacaoAssento() {
      return this.selectedPassengers.filter(pax => !this.upsellInfo?.marcacao_assento?.pax_com_poltronas_compradas?.includes(pax.pid))
    }
  },
  beforeUnmount() {
    this.reservationData = {}
    this.updateStatement()
  }
}
</script>

<style lang="scss" scoped>
.checkout {
  $column-width: 432px;
  $column-gap: $spacing-3;

  min-height: calc(100vh - #{$taskbar-height-mobile});
  padding-top: $spacing-2;

  @media (min-width: $screen-tablet-min) {
    min-height: calc(100vh - #{$taskbar-height-desktop});
    padding-bottom: $spacing-2;
  }

  .c-container {
    max-width: $screen-desktop-min;

    .cc-content {
      display: flex;
      flex-direction: column;

      @media (min-width: $screen-tablet-min) {
        flex-direction: row;
        justify-content: center;
      }

      .ccc-column {
        display: contents; // Ignora este wrapper no mobile

        @media (min-width: $screen-tablet-min) {
          display: block;
          flex: 1;

          &:first-of-type {
            margin-right: $column-gap;
          }
        }

        .cccc-checkout-card {
          margin-bottom: $spacing-2;

          &.is-date-ida {
            order: -2;
          }
        }

        .cccc-termos-alert {
          font-size: $font-size-sm;
          word-break: break-all;
        }

        .cccc-pay {
          @media (max-width: $screen-tablet-max) {
            border-radius: 0;
          }
        }
      }
    }
  }
}
</style>
