<template>
  <ada-accordion
    class="grupo"
    :class="{ disabled: !canReservar, 'is-recomendacao': !!props.recomendacao }"
    :style="cssVars && cssVars"
  >
    <ada-accordion-item>
      <template #title="{ isActive, toggle }">
        <div
          v-if="props.recomendacao"
          class="g-titulo-recomendacao"
        >
          <fa :icon="props.recomendacao.icon" />
          <strong class="ml-1">
            {{ props.recomendacao.title }}
          </strong>
        </div>
        <div class="g-info" @click="handleCardClick(toggle)">
          <div
            class="gi-header"
            data-testid="grupo-card-conexao"
          >
            <div class="gih-top">
              <div :class="{'giht-top-checkbox': isRevendedor}">
                <ada-checkbox v-if="isRevendedor" :value="hasSelected(props.grupo)" @input="onSelect" @click.stop />
                <company-label :grupo="props.grupo" />
              </div>
              <aviso-vagas-restantes v-if="canReservar" :grupo="props.grupo" class="show-tablet" small />
            </div>

            <itinerario-resumido class="gih-itinerario-resumido" :grupo="props.grupo" is-conexao />

            <div class="gih-body">
              <ul
                class="gihb-poltrona text-sm"
              >
                <span class="gihbp-label">
                  Poltrona:
                </span>
                <li v-for="con in props.grupo.conexoes" :key="con.id" class="gihbp-assento">
                  {{ labels[con.tipo_assento] }}
                </li>
              </ul>
              <div class="gihb-oferta">
                <div class="gihbo-preco">
                  <preco
                    class="gihbo-valor"
                    :preco-antigo="precoAntigo"
                    :preco-atual="precoAtual"
                    :can-reservar="canReservar"
                  />

                  <p v-if="showParcelamento" class="text-sm w-100 ws-nowrap">
                    {{ textoParcelamento }}
                  </p>
                </div>

                <aviso-vagas-restantes v-if="canReservar" :grupo="props.grupo" class="hide-tablet" small is-wrapper />
              </div>

              <ada-button
                v-if="!props.readOnly"
                class="gihb-selection"
                color="primary"
                :disabled="!canReservar"
                @click.capture.stop="reservar"
              >
                Selecionar
              </ada-button>
            </div>
          </div>
          <div class="gi-actions" :class="{ active: isActive, disabled: !canReservar }">
            <div class="gia-expand">
              <template v-if="canReservar">
                <div class="giae-badges">
                  <badge-grupo v-if="badgeDestaque" :grupo="grupo" :key-badge="badgeDestaque" />
                  <ada-badge
                    small
                    color="blue-light"
                    class="fw-normal"
                  >
                    <fa-sprite
                      icon="far-fa-bus"
                      sprite="pontos-turisticos"
                      class="mr-half"
                    /> {{ props.grupo.conexoes.length - 1 }} Conexão
                  </ada-badge>
                </div>
                <div class="giae-detalhes caption color-blue-dark">
                  <fa class="giaed-icon" :icon="faChevronDown" />
                  <span>
                    {{ isActive ? 'Esconder detalhes' : 'Ver detalhes' }}
                  </span>
                </div>
              </template>

              <div v-else class="pr-2 caption color-grey">
                Vagas esgotadas
              </div>
            </div>
          </div>
        </div>
      </template>

      <template v-if="canReservar">
        <accordion-content-conexao
          v-if="accordionContentRendered"
          data-testid="accordion-content-conexao"
          :grupo="props.grupo"
          :read-only="props.readOnly"
          @reservar="reservar"
        />
      </template>
    </ada-accordion-item>
  </ada-accordion>
</template>

<script lang="ts" setup>
import { faChevronDown } from '@fortawesome/pro-solid-svg-icons'
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import { ref, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router/composables'
import dateEventInfo from '~/helpers/date-event-info.js'
import EventBus from '~/helpers/eventbus.js'
import { dateFormat, DATE_FORMATS, real } from '~/helpers/formatters.js'
import { labels } from '~/helpers/tipoAssento.js'
import { FILTRO_KEYS } from '~/stores/filtro.js'
import { useRevendedorStore } from '~/stores/revendedor.js'
import { useSearchStore } from '~/stores/search.js'
import avisoVagasRestantes from '~/components/shared/aviso-vagas-restantes/aviso-vagas-restantes.vue'
import companyLabel from '~/components/search-result/grupos/grupo/company-label.vue'
import itinerarioResumido from '~/components/search-result/grupos/grupo/itinerario-resumido.vue'
import preco from '~/components/search-result/grupos/grupo/preco/preco.vue'
import { GrupoConexao } from '~/components/search-result/grupos/types.ts'
import accordionContentConexao from '~/components/search-result/shared/accordion-content-conexao/accordion-content-conexao.vue'
import BadgeGrupo from '~/components/search-result/shared/badge-grupo/badge-grupo.vue'

type Recomendacao = {
  title: string,
  icon: string,
  textColor: string,
  backgroundColor: string,
  borderColor: string
}
const props = defineProps<{
  grupo: GrupoConexao,
  readOnly?: boolean,
  isGrupoDaMadrugada?: boolean,
  recomendacao?: Recomendacao,
}>()

let accordionContentRendered = ref(false)

const searchStore = useSearchStore()
const route = useRoute()
const router = useRouter()
const { next, isVolta, badgesByDate } = searchStore

const { toggleGrupoSelecionado, isRevendedor } = useRevendedorStore()
const { hasSelected } = storeToRefs(useRevendedorStore())

// COMPUTEDS
const precoAntigo = computed(() => {
  const maxValue = Math.max(props.grupo.max_split_value, props.grupo.ref_split_value)

  if (maxValue > props.grupo.max_split_value) return maxValue

  return 0
})

const showBestPrice = computed(() => {
  return badgesByDate.best_price_by_date && badgesByDate.best_price_by_date[props.grupo.id]
})

const showFaster = computed(() => {
  return badgesByDate.faster_by_date && badgesByDate.faster_by_date[props.grupo.id]
})

const badgeDestaque = computed(() => {
  if (showBestPrice.value) return 'menor-preco'
  if (showFaster.value) return 'mais-rapido'
  return null
})

const cssVars = computed(() => {
  if (!props.recomendacao) return { }

  return {
    '--recomendacao-text-color': props.recomendacao.textColor,
    '--recomendacao-border-color': props.recomendacao.borderColor,
    '--recomendacao-background-color': props.recomendacao.backgroundColor
  }
})

const precoAtual = computed(() => {
  return props.grupo.max_split_value
})

const hasPrecoPromocional = computed(() => {
  const somaMaxSplitValue = props.grupo.conexoes.reduce((acc, con) => acc + con.max_split_value, 0)
  const somaSplitValue = props.grupo.conexoes.reduce((acc, con) => acc + con.ref_split_value, 0)

  return somaMaxSplitValue < somaSplitValue
})

const showParcelamento = computed(() => {
  if (!props.grupo?.parcelamento) return false
  return canReservar.value
})

const textoParcelamento = computed(() => {
  if (!showParcelamento.value) return
  const { valor_por_parcela, quantidade_de_parcelas } = props.grupo.parcelamento
  return `${quantidade_de_parcelas}x ${real(valor_por_parcela)}`
})

const canReservar = computed(() => {
  if (props.readOnly) return true

  const hasClosed = props.grupo.conexoes.some(con => con.closed)

  const hasVagas = props.grupo.vagas > 0

  const allAvailable = props.grupo.conexoes.every(con => con.status in { pending: 1, travel_confirmed: 1 })

  return !hasClosed && hasVagas && allAvailable && isBeforeDatetimeIda.value
})

const isBeforeDatetimeIda = computed(() => {
  return dayjs().isBefore(props.grupo.datetime_ida)
})

const isBuscaComVolta = computed(() => {
  return route.query.volta
})

// METHODS
const reservar = async() => {
  const amplitudeParams = {
    grupos: props.grupo,
    difDataDeAgora: dateEventInfo.difDataDeAgora(props.grupo.datetime_ida),
    has_parcelamento: showParcelamento.value,
    isGrupoDaMadrugada: props.isGrupoDaMadrugada,
    isPrecoPromocional: hasPrecoPromocional.value,
    dataIda: dateFormat(DATE_FORMATS.dbdatetime, props.grupo.datetime_ida),
    preco: props.grupo.max_split_value
  }
  EventBus.$emit('selecionou-grupo', amplitudeParams)
  if (isBuscaComVolta.value && !isVolta(route)) {
    // Quando o usuário seleciona grupo na ida e vai pra volta, não podemos repassar os filtros pra busca de volta
    const cleanedRouteQuery = {}
    let hasChangedQueryParams = false
    for (const key in route.query) {
      if (!FILTRO_KEYS.includes(key)) {
        cleanedRouteQuery[key] = route.query[key]
      } else {
        hasChangedQueryParams = true
      }
    }
    if (hasChangedQueryParams) {
      router.replace({ query: cleanedRouteQuery, params: { isTravelRemarcadaGratis: route.params.isTravelRemarcadaGratis } })
    }
  }
  await next(props.grupo, route)
}

const handleCardClick = (toggle) => {
  if (!canReservar.value) return
  accordionContentRendered.value = true
  nextTick(toggle)
}

const onSelect = () => {
  toggleGrupoSelecionado(props.grupo, precoAntigo.value, precoAtual.value, isVolta(route))
}

</script>

<style lang="scss" scoped>

.grupo {
  position: relative;
  overflow: hidden;
  border-radius: $border-radius-md;
  background-color: $color-white;
  border-top: 1px solid $color-grey-light;
  border-bottom: 1px solid $color-grey-light;
  box-sizing: border-box;
  box-shadow: 0 2px 3px rgb(0 0 0 / 8%);

  @media (min-width: $screen-desktop-min) {
    margin-left: 0;
    margin-right: 0;
  }

  &.disabled {
    .g-info {
      cursor: default;
    }
  }

  &.is-recomendacao {
    border: 2px solid var(--recomendacao-border-color);
    border-radius: $border-radius-md;

    @media (max-width: $screen-desktop-max) {
      margin-inline: (-$spacing-1);
    }
  }

  .g-titulo-recomendacao {
    display: flex;
    align-items: center;
    padding: $spacing-1 $spacing-2;
    color: var(--recomendacao-text-color);
    font-weight: $font-weight-medium;
    background-color: var(--recomendacao-background-color);
    border-top-right-radius: $border-radius-md;
    border-top-left-radius: $border-radius-md;
    line-height: 1;

    @media (min-width: $screen-desktop-min) {
      padding-block: $spacing-one-half;
    }
  }

  .g-info {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-between;

    .gi-header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: $spacing-2;
      gap: $spacing-2;

      @media (min-width: $screen-desktop-min) {
        align-items: center;
        flex-wrap: nowrap;
        padding: $spacing-2;
        gap: $spacing-4;
      }

      &:hover {
        cursor: pointer;
      }

      .gih-top {
        width: 100%;
        display: flex;
        align-self: flex-start;
        justify-content: space-between;
        align-items: center;
        gap: $spacing-half;

        @media (min-width: $screen-desktop-min) {
          width: 140px;
          flex-direction: column-reverse;
          align-items: flex-start;
          gap: $spacing-1;
        }

        .giht-top-checkbox {
          display: flex;
          align-items: center;
        }
      }

      .gih-itinerario-resumido {
        @media (min-width: $screen-desktop-min) {
          flex: 1;
          margin-left: $spacing-1;
        }
      }

      .gih-body {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        @media (min-width: $screen-desktop-min) {
          gap: $spacing-1;
          flex: 1;
        }

        .gihb-poltrona {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          list-style-position: inside;

          .gihbp-label {
            color: $color-grey;
            font-size: $font-size-xs;
            font-weight: $font-weight-bold;
          }

          .gihbp-assento {
            counter-increment: count 1;
            font-size: $font-size-xs;
            font-weight: 500;

            &::marker {
              content: counter(count) '°';
              white-space: nowrap;
            }
          }
        }

        .gihb-oferta {
          display: flex;
          flex-direction: column;
          gap: $spacing-half;
          text-align: right;
          flex: 1;
          margin-right: $spacing-1;

          @media (min-width: $screen-tablet-min) {
            flex: 1 0 93px;
          }

          .gihbo-preco {
            align-self: flex-end;
            display: flex;
            flex-direction: column;
            align-items: center;

            @media (min-width: $screen-desktop-min) {
              flex: 0 0 auto;
              align-items: flex-end;
            }

            .gihbo-valor {
              align-items: flex-end;
            }
          }
        }

        .gihb-selection {
          flex: 0;

          @media (max-width: $screen-phone-max) {
            padding: 0 16px !important;
            height: 34px !important;
            font-size: 12px !important;
          }
        }
      }
    }

    .gi-actions {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: $spacing-1;
      background-color: $color-grey-lightest;
      border-top: 1px solid $color-grey-light;
      padding: $spacing-1 $spacing-2;
      line-height: 1;

      &.active {
        .gia-expand {
          .giae-detalhes {
            .giaed-icon {
              transform: rotate(180deg);
            }
          }
        }
      }

      &.disabled {
        .gia-expand {
          justify-content: flex-end;
          cursor: default;
        }
      }

      .gia-expand {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        width: 100%;
        min-height: $spacing-3;

        .giae-detalhes {
          text-align: end;
          line-height: 1;
          flex-grow: 1;

          @media (min-width: $screen-tablet-min) {
            padding-right: $spacing-1;
          }

          .giaed-icon {
            transition: transform 0.2s ease-in-out;
            color: $color-blue-dark;
            margin-right: $spacing-half;
          }
        }

        .giae-badges {
          display: flex;
          gap: $spacing-half;
        }
      }
    }
  }

}
</style>
