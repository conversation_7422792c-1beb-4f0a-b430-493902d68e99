import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import userEvent from '@testing-library/user-event'
import { render, screen } from '@testing-library/vue'
import { groupsFactory } from '~/api/mock/data/db_groups.js'
import { real } from '~/helpers/formatters.js'
import { useSearchStore } from '~/stores/search.js'
import grupoCard from '~/components/search-result/grupos/grupo.vue'

const $route = {
  name: 'searchPageV1',
  query: {
    ida: '2023-09-08'
  },
  params: {
    origem: 'sao-paulo-sp',
    destino: 'rio-de-janeiro-rj'
  },
  meta: {}
}

const renderGrupoCard = (props) => {
  return render(grupoCard, {
    props,
    mocks: {
      $route
    },
    stubs: ['share-travel']
  })
}

describe('grupo card', () => {
  const group = groupsFactory()[1]

  beforeEach(() => {
    createTestingPinia({
      stubActions: false
    })
  })

  it('should be correctly iterative', async() => {
    const searchStore = useSearchStore()
    renderGrupoCard({ grupo: group })

    const cardGrupo = screen.getByTestId('grupo-card')
    const selectButton = screen.getAllByRole('button', { name: 'Selecionar' })[0]
    expect(screen.getByText('R$ 110,00')).toBeVisible()
    expect(screen.getByText('3x R$ 36,00')).toBeVisible()
    expect(screen.getByText('Poltrona:')).toBeVisible()
    expect(screen.getByText('Micro-ônibus: Executivo')).toBeVisible()

    await userEvent.click(cardGrupo)
    expect(await screen.findByTestId('grupo-hidden-area')).toBeVisible()
    expect(searchStore.next).not.toHaveBeenCalled()

    await userEvent.click(selectButton)
    expect(searchStore.next).toHaveBeenCalled()
  })

  it('should not be iterative when disabled', async() => {
    const searchStore = useSearchStore()
    renderGrupoCard({ grupo: { ...group, closed: true } })
    const selectButton = screen.getAllByRole('button', { name: 'Selecionar' })[0]
    const cardGrupo = screen.getByTestId('grupo-card')

    await userEvent.click(cardGrupo)
    await userEvent.click(selectButton)

    expect(screen.queryByTestId('grupo-hidden-area')).not.toBeInTheDocument()
    expect(selectButton).toBeDisabled()
    expect(searchStore.next).not.toHaveBeenCalled()
  })

  it('should render with Buser Premium tag', async() => {
    const grupo = {
      ...group,
      company_id: 168,
      company_name: 'Nena',
      tipo_assento: 'cama premium',
      modelo_venda: 'buser'
    }

    renderGrupoCard({ grupo })
    const cardGrupo = screen.getByTestId('grupo-card')
    await userEvent.click(cardGrupo)

    const premiumElements = await screen.findByText('Premium')
    expect(premiumElements).toBeVisible()
  })

  it('should render group with parcelamento', () => {
    const searchStore = useSearchStore()
    searchStore.badgesByDate = {
      promotional_price_new_route: null
    }
    const { quantidade_de_parcelas, valor_por_parcela } = group.parcelamento
    const text = `${quantidade_de_parcelas}x ${real(valor_por_parcela)}`

    renderGrupoCard({ grupo: group })

    expect(screen.getByText(text)).toBeInTheDocument()
  })

  it('should not render with parcelamento, promotional value', () => {
    const searchStore = useSearchStore()
    searchStore.activePromo = {
      availableGroups: {
        fjkl12: {
          promocao: {
            promotional_value: 90
          }
        }
      }
    }
    const { quantidade_de_parcelas, valor_por_parcela } = group.parcelamento
    const text = `${quantidade_de_parcelas}x ${real(valor_por_parcela)}`

    renderGrupoCard({ grupo: group })

    expect(screen.queryByText(text)).not.toBeInTheDocument()
  })

  it('should not render with parcelamento não pode reservar', () => {
    group.closed = true
    const { quantidade_de_parcelas, valor_por_parcela } = group.parcelamento
    const text = `${quantidade_de_parcelas}x ${real(valor_por_parcela)}`
    renderGrupoCard({ grupo: group })

    expect(screen.queryByText(text)).not.toBeInTheDocument()
    group.closed = false
  })

  it('should display preco cortado when with promo', () => {
    const searchStore = useSearchStore()
    searchStore.activePromo = {
      availableGroups: {
        fjkl12: {
          promocao: {
            promotional_value: 90
          }
        }
      }
    }

    renderGrupoCard({ grupo: group })

    expect(screen.getByText('R$ 90,00')).toBeInTheDocument()
  })

  it('should display conexao', () => {
    const group_com_conexao = { ...group }
    group_com_conexao.itinerario[2].is_conexao = true
    renderGrupoCard({ grupo: group_com_conexao })

    expect(screen.getByTestId('grupo-card')).toBeVisible()
  })
})
