<template>
  <ada-accordion
    class="grupo"
    :style="cssVars && cssVars"
    :class="{
      disabled: !canReservar,
      'is-recomendacao': !!recomendacao
    }"
  >
    <ada-accordion-item>
      <template #title="{ isActive, toggle }">
        <div v-if="recomendacao" class="g-titulo-recomendacao">
          <fa :icon="recomendacao.icon" />
          <strong class="ml-1">
            {{ recomendacao.title }}
          </strong>
        </div>

        <div class="g-info" @click="handleCardClick(toggle)">
          <div class="gi-header" data-testid="grupo-card">
            <p v-if="recomendacao && isBuscaSemdata" class="gih-dia">
              {{ datetimeIdaWeekdayFormatted }}
            </p>

            <div class="gih-top">
              <ada-badge v-if="isRevendedor && taxaDeServico" color="grey-light" class="fw-500 mt-2">
                Taxa: {{ taxaDeServico }}
              </ada-badge>
              <div :class="{ 'giht-top-checkbox': isRevendedor }">
                <ada-checkbox v-if="isRevendedor" :value="hasSelected(grupo)" @input="onSelect" @click.stop />
                <company-label :grupo="grupo" />
              </div>
              <aviso-vagas-restantes v-if="canReservar" :grupo="grupo" class="show-tablet" small />
            </div>
            <itinerario-resumido
              class="gih-itinerario-resumido"
              :grupo="grupo"
              :is-recomendacao="!!recomendacao"
              :should-show-tooltip-desembarque-rapido="shouldShowDesembarqueRapido"
              :show-dia-da-semana="isRevendedor"
            />

            <div class="gih-body">
              <poltrona
                class="gihb-poltrona"
                :tipo-assento="grupo.tipo_assento"
                :tipo-veiculo="grupo.tipo_veiculo"
                :tem-banheiro="grupo.tem_banheiro"
                :has-marcacao-assento="false"
                :show-info="grupo.tipo_assento !== 'carro'"
                caption-label
                hide-icon
              />
              <div class="gihb-oferta">
                <div class="gihbo-preco">
                  <preco
                    v-if="!hidePreco"
                    class="gihbo-valor"
                    :preco-antigo="precoAntigo"
                    :preco-atual="precoAtual"
                    :can-reservar="canReservar"
                    :zera-preco="grupo.zera_preco"
                    :loading="grupo.updatingPrice"
                  />

                  <p v-if="showParcelamento" class="text-sm w-100 ws-nowrap">
                    {{ textoParcelamento }}
                  </p>
                </div>

                <aviso-vagas-restantes
                  v-if="canReservar"
                  :grupo="grupo"
                  :is-revendedor="isRevendedor"
                  class="hide-tablet"
                  is-wrapper
                  small
                />
              </div>
              <ada-button
                v-if="!readOnly"
                class="gihb-selection"
                color="primary"
                :disabled="!canReservar"
                :loading="grupo.updatingPrice"
                small
                @click.capture.stop="reservar"
              >
                Selecionar
              </ada-button>
            </div>
          </div>
          <div class="gi-actions" :class="{ active: isActive, disabled: !canReservar }">
            <div class="gia-expand">
              <template v-if="canReservar">
                <client-only>
                  <div class="giae-badges">
                    <ada-badge v-if="badges.accessible" small color="blue-light" class="fw-normal">
                      <fa :icon="faWheelchair" class="mr-half" /> Opção acessível
                    </ada-badge>
                    <ada-badge v-if="badges.isTrechoPromo" class="caption fw-normal giaeb-color-pink-gradient">
                      <fa :icon="faStar" class="mr-half" />
                      Oferta
                    </ada-badge>
                    <ada-badge v-if="isBuserPremium" class="caption fw-normal">
                      <fa :icon="faLoveseat" class="mr-half" /> Premium
                    </ada-badge>
                    <badge-grupo v-if="badges.badgeGrupo" :grupo="grupo" :promo="promo" :key-badge="badgeGrupo" />
                    <ada-badge v-if="badges.direto" small color="blue-light" class="fw-normal">
                      <fa :icon="faBoltLightning" class="mr-half" /> Direto
                    </ada-badge>
                    <ada-badge v-if="badges.onibusRosa" color="brand-lightest" class="caption fw-normal">
                      Ônibus rosa
                    </ada-badge>
                    <ada-badge v-if="badges.badgeBlack" color="black" class="giht-bf">
                      <fa :icon="faCircleDollar" class="mr-half" /> Black Friday
                    </ada-badge>
                    <ada-badge v-if="badges.isDesembarqueRapido" color="amber-light" class="caption fw-normal">
                      <fa :icon="faBagsShopping" class="mr-half" /> Apenas bagagem de mão
                    </ada-badge>
                  </div>
                </client-only>

                <div class="giae-detalhes caption color-blue-dark">
                  <fa class="giaed-icon" :icon="faChevronDown" />
                  <span class="fw-500">
                    {{ detalhesText }}
                  </span>
                </div>
              </template>

              <div v-else-if="grupo.updatingPrice" class="pr-2 caption color-grey">
                Carregando...
              </div>

              <div v-else class="pr-2 caption color-grey">
                Vagas esgotadas
              </div>
            </div>
          </div>
        </div>
      </template>

      <template v-if="canReservar">
        <div v-if="accordionContentRendered" class="g-hidden-area" data-testid="grupo-hidden-area">
          <company-logo v-if="isSmallScreen" :grupo="grupo" />

          <div class="gha-content">
            <travel-itinerary
              class="ghac-itinerario"
              show-duration
              :itinerario="trimmedItinerario"
              :datetime-ida="grupo.datetime_ida"
              :chegada-ida="grupo.chegada_ida"
              :loading-itinerario="loadingItinerario"
              :show-itinerario-marketplace="showItinerarioMarketplace"
              :itinerario-marketplace="itinerarioMarketplace"
              @extend-itinerary="extendItinerary"
            />

            <div class="ghac-informacoes">
              <company-logo v-if="!isSmallScreen" :grupo="grupo" />

              <card-buser-premium
                v-if="isBuserPremium"
                class="mb-2"
              />

              <instrucoes-viagem
                class="mb-2"
                :grupo="grupo"
                :is-marketplace="isMarketplace"
                :is-viagem-internacional="isViagemInternacional"
              />

              <ada-alert v-if="isAccessible" class="mb-2" :icon="faWheelchair" type="info">
                <p class="text-sm">
                  Poltrona acessível para cadeira de rodas e pessoas com mobilidade reduzida.
                </p>
              </ada-alert>

              <share-travel :grupo="grupo" class="mb-2" />

              <revendedor-consulta-gratuidade-antt
                v-if="isRevendedor && isMarketplace"
                class="mb-3"
                :trecho-classe-id="grupo.id"
              />
            </div>
          </div>

          <ada-button
            v-if="!readOnly"
            color="primary"
            class="show-desktop"
            outline
            block
            @click="reservar"
          >
            Selecionar essa viagem

            <fa class="ml-1" :icon="faArrowRight" />
          </ada-button>
        </div>
        <div v-else>
          <LazyHydrate ssr-only>
            <trecho-itinerario-hidden
              :duracao-ida="grupo.duracao_ida"
              :itinerario="grupo.itinerario"
              :origem="grupo.origem"
              :destino="grupo.destino"
              :datetime-ida="grupo.datetime_ida"
              :chegada-ida="grupo.chegada_ida"
              :show-origem-description="!isMarketplaceOrHibrido"
              :show-destino-description="!isMarketplaceOrHibrido"
            />
          </LazyHydrate>
        </div>
      </template>
    </ada-accordion-item>
  </ada-accordion>
</template>

<script>
import { faBoltLightning, faArrowRight, faCircleDollar, faWheelchair, faBagsShopping } from '@fortawesome/pro-regular-svg-icons'
import { faChevronDown, faLoveseat, faStar } from '@fortawesome/pro-solid-svg-icons'
import dayjs from 'dayjs'
import { mapState, mapActions } from 'pinia'
import LazyHydrate from 'vue-lazy-hydration'
import { getItinerarioMarketplace } from '~api/search.js'
import { mapLocaisEmbarqueFoto } from '~/assets/js/ponto-de-embarque.ts'
import { isOfertaBf } from '~/helpers/blackFridayhelper.js'
import dateEventInfo from '~/helpers/date-event-info.js'
import EventBus from '~/helpers/eventbus.js'
import { dateFormat, DATE_FORMATS, real } from '~/helpers/formatters.js'
import { getAndTrimItinerario, isMarketplaceOrHibrido, isMarketplace, isViagemInternacional, isViagemDireta, groupsHaveBuserPremium } from '~/helpers/grouphelper.js'
import { FILTRO_KEYS } from '~/stores/filtro.js'
import { useRevendedorStore } from '~/stores/revendedor.js'
import { useSearchStore } from '~/stores/search.js'
import { useSettingsStore } from '~/stores/settings.js'
import avisoVagasRestantes from '~/components/shared/aviso-vagas-restantes/aviso-vagas-restantes.vue'
import shareTravel from '~/components/shared/share-travel/share-travel.vue'
import cardBuserPremium from '~/components/search-result/grupos/grupo/card-buser-premium/card-buser-premium.vue'
import companyLabel from '~/components/search-result/grupos/grupo/company-label.vue'
import companyLogo from '~/components/search-result/grupos/grupo/company-logo.vue'
import instrucoesViagem from '~/components/search-result/grupos/grupo/instrucoes-viagem/instrucoes-viagem.vue'
import itinerarioResumido from '~/components/search-result/grupos/grupo/itinerario-resumido.vue'
import poltrona from '~/components/search-result/grupos/grupo/poltrona.vue'
import preco from '~/components/search-result/grupos/grupo/preco/preco.vue'
import badgeGrupo from '~/components/search-result/shared/badge-grupo/badge-grupo.vue'
import trechoItinerarioHidden from '~/components/search-result/shared/trecho-itinerario-hidden.vue'
import travelItinerary from '~/components/viagens/travel-itinerary/travel-itinerary.vue'
import { Grupo } from './types.ts'
export default {
  name: 'Grupo',
  ssrComputedCache: true,
  components: {
    LazyHydrate,
    travelItinerary,
    companyLabel,
    companyLogo,
    badgeGrupo,
    preco,
    poltrona,
    instrucoesViagem,
    itinerarioResumido,
    trechoItinerarioHidden,
    avisoVagasRestantes,
    shareTravel,
    cardBuserPremium,
    revendedorConsultaGratuidadeAntt: () => import('~/components/checkout/checkout-revendedor-gratuidade/revendedor-consulta-gratuidade-antt.vue')
  },
  props: {
    index: {
      type: Number
    },
    grupo: {
      type: Grupo,
      required: true
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    hidePreco: {
      type: Boolean,
      default: false
    },
    recomendacao: {
      type: Object,
      default: null
    },
    isGrupoDaMadrugada: {
      type: Boolean,
      default: false
    },
    searchrankApiRecommended: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      accordionContentRendered: false,
      loadingItinerario: false,
      itinerarioMarketplace: undefined,
      faChevronDown,
      faArrowRight,
      faBoltLightning,
      faCircleDollar,
      faWheelchair,
      faStar,
      faBagsShopping,
      faLoveseat
    }
  },
  computed: {
    // NOTE: `trechosPromocionaisSet` não deveria estar na store da search. Cards de ofertas de grupos
    // existem também fora do fluxo de home -> search -> ofertas, como o itens semelhantes ou promoções,
    // os quais o usuário pode muito bem encontrar sem passar pela search, não populando consequentemente
    // a `searchStore`.
    ...mapState(useSearchStore, ['activePromo', 'isVolta', 'badgesByDate', 'ordenacao', 'trecho', 'trechosPromocionaisSet']),
    ...mapState(useSettingsStore, ['permiteLayoutBlackfriday']),
    ...mapState(useRevendedorStore, ['hasSelected', 'isRevendedor']),
    isSmallScreen() {
      return this.$andromeda.breakpoint.lgAndDown
    },
    isMobile() {
      return this.$andromeda.breakpoint.smAndDown
    },
    detalhesText() {
      if (!this.isActive)
        return 'Ver detalhes'

      return (this.isMobile) ? 'Esconder' : 'Esconder detalhes'
    },
    precoAntigo() {
      const maxValue = Math.max(this.grupo.max_split_value, this.grupo.ref_split_value)
      if (this.grupo.zera_preco) {
        return maxValue
      }
      if (
        this.promo &&
        this.promo.promotional_value &&
        this.promo.promotional_value !== maxValue
      ) {
        return maxValue
      }

      if (maxValue > this.grupo.max_split_value) return maxValue

      return 0
    },
    precoAtual() {
      if (this.grupo.zera_preco) {
        return 0
      }

      if (this.promo && (this.promo.promotional_value || this.promo.promotional_value === 0)) {
        return this.promo.promotional_value
      }

      return this.grupo.max_split_value
    },
    hasPrecoPromocional() {
      const value = this.grupo.max_split_value
      const refValue = this.grupo.ref_split_value

      return value < refValue
    },
    showParcelamento() {
      if (!this.grupo?.parcelamento || this.grupo.zera_preco) return false
      // Não iremos mostrar valor de parcelamento caso exista algum cupom aplicado
      // pois nestes casos usamos os valores retornados do promogroups
      // TODO: implementar o valor parcelado no promogroups
      if (this.promotionalValueExists) return false
      return this.canReservar
    },
    textoParcelamento() {
      if (!this.showParcelamento) return
      const { valor_por_parcela, quantidade_de_parcelas } = this.grupo.parcelamento
      return `${quantidade_de_parcelas}x ${real(valor_por_parcela)}`
    },
    promo() {
      if (!this.isTrechoPromo && this.activePromo?.code === 'trecho-promo') {
        return null
      }

      const availableGroups = this.activePromo?.availableGroups

      const promoObj = availableGroups?.[this.grupo.id]

      return promoObj && promoObj.promocao
    },
    sortingRank() {
      const ranking = this.grupo?.ranking
      return {
        heuristic_em_alta_v1: ranking?.heuristic_em_alta_v1,
        searchrank_memory_based_v1: ranking?.searchrank_memory_based_v1
      }[this.ordenacao] || this.index + 1
    },
    promotionalValueExists() {
      return typeof this.promo?.promotional_value === 'number'
    },
    canReservar() {
      if (this.readOnly) return true
      // canMakeReservation from searchhelper (kill to helper)
      return (
        !this.grupo.updatingPrice &&
        this.grupo.status in { pending: 1, travel_confirmed: 1 } &&
        !this.grupo.closed &&
        this.grupo.vagas > 0 &&
        this.isBeforeDatetimeIda
      )
    },
    trimmedItinerario() {
      return getAndTrimItinerario(this.grupo, true, true)
    },
    datetimeIdaWeekdayFormatted() {
      return dateFormat(DATE_FORMATS.extendedweekday, this.grupo.datetime_ida)
    },
    isBeforeDatetimeIda() {
      return dayjs().isBefore(this.grupo.datetime_ida)
    },
    isMarketplaceOrHibrido() {
      return isMarketplaceOrHibrido(this.grupo)
    },
    isMarketplace() {
      return isMarketplace(this.grupo)
    },
    showItinerarioMarketplace() {
      return this.isMarketplace && !!this.grupo.extra_mkp_servico
    },
    isBuscaSemdata() {
      return !this.$route.query.ida
    },
    isBuscaComVolta() {
      return !!this.$route.query.volta
    },
    isViagemInternacional() {
      return isViagemInternacional(this.grupo)
    },
    cssVars() {
      if (!this.recomendacao) return {}

      return {
        '--recomendacao-text-color': this.recomendacao.textColor,
        '--recomendacao-border-color': this.recomendacao.borderColor,
        '--recomendacao-background-color': this.recomendacao.backgroundColor
      }
    },
    isViagemDireta() {
      return isViagemDireta(this.grupo)
    },
    badgeGrupo() {
      if (this.showBestPrice) {
        return 'menor-preco'
      }
      if (this.showFaster) {
        return 'mais-rapido'
      }
      if (this.showMostComfortable) {
        return 'mais-confortavel'
      }
      return null
    },
    isAccessible() {
      return this.grupo.tem_acessibilidade
    },
    taxaDeServico() {
      if (!this.grupo.percentual_taxa_servico)
        return
      const value = this.grupo.max_split_value * (this.grupo.percentual_taxa_servico / 100)
      return real(value)
    },
    isTrechoPromo() {
      return this.trechosPromocionaisSet.has(this.grupo.id)
    },
    shouldShowDesembarqueRapido() {
      return this.grupo?.destino?.desembarque_rapido
    },
    badges() {
      let quantidadeDeBadgesMaxima = null
      if ((this.isAccessible || this.shouldShowDesembarqueRapido) && this.isMobile) quantidadeDeBadgesMaxima = 1
      else if (this.isMobile) quantidadeDeBadgesMaxima = 2

      const badgesMap = {
        accessible: this.isAccessible,
        isDesembarqueRapido: this.shouldShowDesembarqueRapido,
        isTrechoPromo: this.isTrechoPromo,
        taxaZero: this.isTaxaZero,
        badgeGrupo: this.badgeGrupo,
        badgeBlack: isOfertaBf(this.grupo.max_split_value, this.grupo.ref_split_value, this.grupo.modelo_venda) && this.permiteLayoutBlackfriday,
        direto: this.isViagemDireta && this.grupo.modelo_venda !== 'marketplace',
        onibusRosa: this.grupo.onibus_plotado
      }

      if (!quantidadeDeBadgesMaxima) return badgesMap

      return Object.keys(badgesMap).reduce((acc, key) => {
        if (badgesMap[key] && quantidadeDeBadgesMaxima > 0) {
          acc[key] = badgesMap[key]
          quantidadeDeBadgesMaxima--
        }
        return acc
      }, {})
    },
    showBestPrice() {
      return this.badgesByDate.best_price_by_date && this.badgesByDate.best_price_by_date[this.grupo.id]
    },
    showMostComfortable() {
      return this.badgesByDate.most_comfortable_by_date && this.badgesByDate.most_comfortable_by_date[this.grupo.id]
    },
    showFaster() {
      return this.badgesByDate.faster_by_date && this.badgesByDate.faster_by_date[this.grupo.id]
    },
    isBuserPremium() {
      return groupsHaveBuserPremium([this.grupo])
    }
  },
  mounted() {
    // Parte do teste AB de fotos no ponto de embarque
    // Essas fotos virão do backend caso o teste ganhe
    let fotoParaOrigem = mapLocaisEmbarqueFoto[this.grupo.origem_slug]
    let fotoParaDestino = mapLocaisEmbarqueFoto[this.grupo.destino_slug]

    if (fotoParaOrigem) {
      fotoParaOrigem = fotoParaOrigem()
      this.$set(this.grupo.origem, 'fotos', fotoParaOrigem)
    }

    if (fotoParaDestino) {
      fotoParaDestino = fotoParaDestino()
      this.$set(this.grupo.destino, 'fotos', fotoParaDestino)
    }
  },
  methods: {
    ...mapActions(useSearchStore, ['next']),
    ...mapActions(useRevendedorStore, ['toggleGrupoSelecionado']),
    async reservar() {
      const amplitudeParams = {
        grupo: this.grupo,
        company_id: this.grupo.company_id,
        modelo_venda: this.grupo.modelo_venda,
        should_update_price_marketplace: this.grupo.shouldUpdatePriceMarketplace,
        difDataDeAgora: dateEventInfo.difDataDeAgora(this.grupo.datetime_ida),
        is_grupo_recomendado: !!this.recomendacao && !this.recomendacao?.googleTransit,
        googleTransit: this.recomendacao?.googleTransit,
        has_parcelamento: this.showParcelamento,
        isGrupoDaMadrugada: this.isGrupoDaMadrugada,
        isPrecoPromocional: this.hasPrecoPromocional,
        dataIda: dateFormat(DATE_FORMATS.dbdatetime, this.grupo.datetime_ida),
        preco: this.grupo.max_split_value,
        searchrankApiRecommended: this.searchrankApiRecommended,
        hasBuserPremium: groupsHaveBuserPremium([this.grupo])
      }
      if (!this.recomendacao && this.$route.query.ida) {
        amplitudeParams.modoOrdenacao = this.ordenacao
        amplitudeParams.rankOrdenacao = this.sortingRank
      }
      EventBus.$emit('selecionou-grupo', amplitudeParams)

      if (this.isBuscaComVolta && !this.isVolta(this.$route)) {
        // Quando o usuário seleciona grupo na ida e vai pra volta, não podemos repassar os filtros pra busca de volta
        const cleanedRouteQuery = {}
        let hasChangedQueryParams = false
        for (const key in this.$route.query) {
          if (!FILTRO_KEYS.includes(key)) {
            cleanedRouteQuery[key] = this.$route.query[key]
          } else {
            hasChangedQueryParams = true
          }
        }
        if (hasChangedQueryParams) {
          this.$router.replace({ query: cleanedRouteQuery, params: { isTravelRemarcadaGratis: this.$route.params.isTravelRemarcadaGratis } })
        }
      }
      await this.next(this.grupo, this.$route)
    },
    handleCardClick(toggle) {
      if (!this.canReservar) return
      this.toggleAccordionContent(toggle)
    },
    async extendItinerary() {
      if (this.grupo.extra_mkp_servico && !this.itinerarioMarketplace && !this.loadingItinerario) {
        try {
          this.loadingItinerario = true
          const resp = await getItinerarioMarketplace(this.grupo)
          this.itinerarioMarketplace = resp.itinerario
          this.loadingItinerario = false
        } catch (error) {
          this.loadingItinerario = false
          this.itinerarioMarketplace = null
        }
      }
    },
    toggleAccordionContent(toggleFunc) {
      if (!this.accordionContentRendered) {
        const { origem, destino } = this.$route.params

        let amplitudeParams = {
          trecho: this.trecho,
          origem,
          destino,
          data: this.$route.query.ida ?? 'próximos-14-dias',
          hash_id: this.grupo.id,
          hasFoto: !!this.grupo.origem?.fotos || !!this.grupo.destino?.fotos,
          origemName: this.grupo.origem?.fotos ? this.grupo.origem_slug : 'not-image',
          destinoName: this.grupo.destino?.fotos ? this.grupo.destino_slug : 'not-image'
        }
        if (!this.recomendacao && this.$route.query.ida) {
          amplitudeParams.modoOrdenacao = this.ordenacao
          amplitudeParams.rankOrdenacao = this.sortingRank
        }
        EventBus.$emit('clicou-ver-detalhes', amplitudeParams)
      }
      this.$nextTick(toggleFunc)
      this.accordionContentRendered = true
    },
    onSelect() {
      this.toggleGrupoSelecionado(this.grupo, this.precoAntigo, this.precoAtual, this.isVolta(this.$route))
    }
  }
}
</script>

<style lang="scss" scoped>
.grupo {
  position: relative;
  overflow: hidden;
  border-radius: $border-radius-md;
  background-color: $color-white;
  border-top: 1px solid $color-grey-light;
  border-bottom: 1px solid $color-grey-light;
  box-sizing: border-box;
  box-shadow: 0 2px 3px rgb(0 0 0 / 8%);

  @media (min-width: $screen-desktop-min) {
    margin-inline: 0;
  }

  &.disabled {
    .g-info {
      cursor: default;
    }
  }

  &.is-recomendacao {
    border: 2px solid var(--recomendacao-border-color);
    border-radius: $border-radius-md;

    @media (max-width: $screen-desktop-max) {
      margin-inline: (-$spacing-1);
      margin-top: $spacing-3 !important;
    }

    .gi-actions {
      border-radius: 0 0 $border-radius-md $border-radius-md;
    }
  }

  .g-titulo-recomendacao {
    display: flex;
    align-items: center;
    padding: $spacing-1 $spacing-2;
    color: var(--recomendacao-text-color);
    font-weight: $font-weight-medium;
    background-color: var(--recomendacao-background-color);
    border-top-right-radius: $border-radius-md;
    border-top-left-radius: $border-radius-md;
    line-height: 1;

    @media (min-width: $screen-desktop-min) {
      padding-block: $spacing-one-half;
    }
  }

  .g-info {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-between;

    .gi-header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: $spacing-2;
      gap: $spacing-2;

      @media (min-width: $screen-desktop-min) {
        align-items: center;
        flex-wrap: nowrap;
        padding: $spacing-2;
        gap: $spacing-4;
      }

      &:hover {
        cursor: pointer;
      }

      .gih-dia {
        // Mostra no grupo recomendado
        width: 100%;
        font-size: $font-size-sm;
        font-weight: $font-weight-bold;
        color: $color-grey-dark;
        margin-top: $spacing-half;
        border-bottom: 1px solid $color-grey-light;
        padding-bottom: $spacing-1;

        &::first-letter {
          text-transform: uppercase;
        }

        @media (min-width: $screen-desktop-min) {
          display: none;
        }
      }

      .gih-top {
        width: 100%;
        display: flex;
        align-self: flex-start;
        justify-content: space-between;
        align-items: center;
        gap: $spacing-half;

        @media (min-width: $screen-desktop-min) {
          width: 140px;
          flex-direction: column-reverse;
          align-items: flex-start;
          gap: $spacing-1;
        }

        .giht-top-checkbox {
          display: flex;
          align-items: flex-start;
        }

        .giht-bf {
          font-weight: $font-weight-regular;
          font-size: $font-size-xs;
          border-radius: $border-radius-pill;
        }
      }

      .gih-itinerario-resumido {
        @media (min-width: $screen-desktop-min) {
          flex: 1;
          margin-left: $spacing-1;
        }
      }

      .gih-body {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        @media (min-width: $screen-desktop-min) {
          flex: 1;
          gap: $spacing-1;
        }

        .gihb-poltrona {
          font-size: $font-size-xs !important;
        }

        .gihb-oferta {
          display: flex;
          flex-direction: column;
          gap: $spacing-half;
          text-align: right;
          flex: 1;
          margin-right: $spacing-1;

          @media (min-width: $screen-tablet-min) {
            flex: 1 0 93px;
          }

          .gihbo-preco {
            align-self: flex-end;
            display: flex;
            flex-direction: column;
            align-items: center;

            @media (min-width: $screen-desktop-min) {
              flex: 0 0 auto;
              align-items: flex-end;
            }

            .gihbo-valor {
              align-items: flex-end;
            }
          }
        }

        .gihb-selection {
          flex: 0;

          @media (min-width: $screen-desktop-min) {
            height: 44px;
            line-height: 44px;
            padding: 0 $spacing-2;
            font-size: $font-size-md;
          }
        }
      }
    }

    .gi-actions {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: $spacing-1;
      background-color: $color-grey-lightest;
      border-top: 1px solid $color-grey-light;
      padding: $spacing-1 $spacing-2;
      line-height: 1;

      &.active {
        .gia-expand {
          .giae-detalhes {
            .giaed-icon {
              transform: rotate(180deg);
            }
          }
        }
      }

      &.disabled {
        .gia-expand {
          justify-content: flex-end;
          cursor: default;
        }
      }

      .gia-expand {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        width: 100%;
        min-height: $spacing-3;

        .giae-badges {
          display: flex;
          gap: $spacing-half;

          .giaeb-color-pink-gradient {
            background: linear-gradient(90deg, #FF23B6 0%, #FF0075 100%) !important;
          }
        }

        .giae-detalhes {
          text-align: end;
          line-height: 1;
          flex-grow: 1;

          @media (min-width: $screen-tablet-min) {
            padding-right: $spacing-1;
          }

          .giaed-icon {
            margin-right: $spacing-half;
            transition: transform 0.2s ease-in-out;
            color: $color-blue-dark;
          }
        }

      }
    }
  }

  .g-hidden-area {
    color: $color-grey-dark;

    .gha-content {
      display: flex;
      align-items: flex-start;
      flex-direction: column;

      @media (min-width: $screen-desktop-min) {
        flex-direction: row;
        gap: $spacing-3;
      }

      .ghac-itinerario {
        width: 100%;
        flex: 1;
        margin-bottom: $spacing-3;

        @media (min-width: $screen-desktop-min) {
          margin-bottom: 0;
        }
      }

      .ghac-informacoes {
        flex: 1;
        flex-direction: column;
        width: 100%;
      }
    }
  }
}
</style>
