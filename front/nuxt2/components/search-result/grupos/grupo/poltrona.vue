<template>
  <div class="poltrona">
    <p v-if="!hideTitle" class="p-label">
      {{ title }}
    </p>
    <div class="p-assento">
      <img v-if="showIcon" :style="iconStyle" :src="poltronaSrc">
      <div class="d-flex fd-column text-sm">
        <p :class="{ 'caption': captionLabel, 'color-brand-medium': usePaintedColors }">
          <template v-if="hasMarcacaoAssento">
            <span v-if="poltrona">
              nº {{ poltrona }} {{ poltronaLabel }}
            </span>
            <span v-else>
              Em Breve
              <fa-sprite
                sprite="search"
                icon="far-fa-circle-info"
                class="p-info-icon"
                alt="Veja os tipos de poltrona"
                @click="openQualMinhaPoltronaPopup"
              />
            </span>
          </template>
          <template v-else>
            {{ poltronaLabel }}
            <fa-sprite
              v-if="showInfo"
              sprite="search"
              icon="far-fa-circle-info"
              class="p-info-icon"
              alt="Veja os tipos de poltrona"
              @click.stop="openGlobalPoltronasModal"
            />
          </template>
        </p>
      </div>
    </div>
    <p v-if="!temBanheiro" class="caption">
      Ônibus sem banheiro
    </p>
    <popup-qual-minha-poltrona
      v-if="hasMarcacaoAssento"
      :visible="showQualMinhaPoltronaPopup"
      @close="showQualMinhaPoltronaPopup = false"
    />
  </div>
</template>
<script>
import { mapWritableState } from 'pinia'
import EventBus from '~/helpers/eventbus.js'
import { paintedIcons, icons, labels } from '~/helpers/tipoAssento.js'
import { useSearchStore } from '~/stores/search.js'

export default {
  name: 'Poltrona',
  components: {
    popupQualMinhaPoltrona: () => import('~/components/promo/popup-qual-e-minha-poltrona.vue')
  },
  props: {
    tipoAssento: String,
    hasMarcacaoAssento: {
      type: Boolean,
      default: false
    },
    poltrona: Number,
    tipoVeiculo: String,
    temBanheiro: {
      type: Boolean,
      default: true
    },
    showInfo: Boolean,
    hideTitle: Boolean,
    hideIcon: {
      type: Boolean,
      default: false
    },
    captionLabel: {
      type: Boolean,
      default: false
    },
    iconStyle: {
      type: Object,
      default: () => ({
        marginRight: '8px',
        width: '20px',
        height: '20px'
      })
    },
    usePaintedColors: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showQualMinhaPoltronaPopup: false
    }
  },
  computed: {
    ...mapWritableState(useSearchStore, ['showPoltronasModal']),
    poltronaLabel() {
      if (!this.tipoAssento) {
        return ''
      }

      if (this.tipoVeiculo === 'van') {
        return 'Van executiva'
      }

      if (this.tipoVeiculo === 'micro-onibus') {
        return `Micro-ônibus: ${labels[this.tipoAssento]}`
      }

      return labels[this.tipoAssento]
    },
    baseIcons() {
      return this.usePaintedColors ? paintedIcons : icons
    },
    poltronaSrc() {
      if (this.tipoAssento) {
        return this.baseIcons[this.tipoAssento]
      }
      return ''
    },
    showIcon() {
      return !this.hideIcon && this.tipoVeiculo !== 'van'
    },
    title() {
      if (this.tipoAssento === 'carro') return 'Veículo:'

      return 'Poltrona:'
    }
  },
  methods: {
    openGlobalPoltronasModal() {
      EventBus.$emit('abriu-poltronas-modal', { pagina: this.$route.name })
      this.showPoltronasModal = true
    },
    openQualMinhaPoltronaPopup() {
      EventBus.$emit('abriu-poltronas-marcadas-modal', { pagina: this.$route.name })
      this.showQualMinhaPoltronaPopup = true
    }
  }
}
</script>

<style lang="scss" scoped>
.poltrona {
  text-align: left;

  .p-label {
    color: $color-grey;
    font-size: $font-size-xs;
    font-weight: $font-weight-bold;
  }

  .p-info-icon {
    color: $color-brand;
    cursor: pointer;
  }

  .p-assento {
    display: flex;
    align-items: center;

  }
}
</style>
