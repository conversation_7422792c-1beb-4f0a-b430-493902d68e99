<template>
  <custom-select
    :id="inputId"
    :value="value"
    :items="optionsFiltradas"
    :search-function="null"
    :loading="loading"
    v-bind="$attrs"
    item-value="id"
    item-text="label"
    class="select-city"
    return-object
    searchable
    v-on="{
      ...$listeners,
      input: selectCity,
      search: searchDebounced
    }"
  >
    <template #start>
      <slot name="start" />
    </template>

    <template #item="{ item }">
      <span
        class="sc-label"
        v-html="item.highlightText || item.label"
      />
    </template>
  </custom-select>
</template>

<script>
import fuzzysort from 'fuzzysort'
import { mapState, mapWritableState, mapActions } from 'pinia'
import { normalize } from '~/helpers/stringshelper.js'
import { useFavoritePlacesStore } from '~/stores/favorite-places.js'
import { useSearchboxStore } from '~/stores/searchbox.js'
import customSelect from '~/components/shared/custom-select/custom-select.vue'

export default {
  components: {
    customSelect
  },
  props: {
    value: Object,
    inputId: String,
    isOrigem: {
      type: Boolean,
      default: true
    },
    trechosVendidosMap: {
      type: Object,
      default: () => ({})
    },
    remote: {
      type: Boolean,
      default: false
    },
    onlyCities: {
      type: Boolean,
      default: false
    },
    listCities: {
      type: Array,
      default: () => []
    }
  },
  emits: ['input', 'clear'],
  inheritAttrs: false,
  data() {
    return {
      loading: false,
      filteredCities: [],
      searchText: null,
      debounceTimeout: null
    }
  },
  computed: {
    ...mapState(useFavoritePlacesStore, {
      cities: 'favoritePlaces'
    }),
    ...mapState(useSearchboxStore, ['origemItems', 'destinoItems']),
    ...mapWritableState(useSearchboxStore, ['fromCity', 'toCity']),
    options() {
      if (!this.remote) {
        return this.filteredCities.length ? this.filteredCities : this.citiesListAggregated
      }

      if (this.isOrigem && this.origemItems.length) {
        return this.origemItems
      }

      if (!this.isOrigem && this.destinoItems.length) {
        return this.destinoItems
      }

      return this.citiesSample
    },
    optionsFiltradas() {
      if (this.onlyCities) {
        return this.options.filter(option => !option.id.includes('-'))
      }
      return this.options
    },
    citiesListAggregated() {
      return this.citiesSample.concat(this.listCities)
    },
    citiesSample() {
      return this.cities ? this.cities.slice(0, 30) : []
    }
  },
  watch: {
    value(cidade) {
      const searchText = cidade ? cidade.label : null

      const isTheSameSearch = searchText === this.fromCity?.label || searchText === this.toCity?.label
      if (isTheSameSearch || searchText === this.searchText) {
        return
      }

      if (this.remote && searchText) {
        this.searchDebounced(searchText, false)
      }
    }
  },
  mounted() {
    if (this.value && this.remote) {
      this.searchDebounced(this.value.label, false, true)
    }
  },
  methods: {
    ...mapActions(useSearchboxStore, ['searchItems']),
    searchDebounced(searchText, cancelPreviousCalls = true, firstLoad = false) {
      if (!firstLoad) {
        // Não remova isso sob pena de paulada
        // isso evita um bug do searchbox que apaga o que o usuário escreveu
        // caso o que ele esteja escrevendo não esteja entre os itens do filteredCities
        if (this.isOrigem) this.fromCity = null
        if (!this.isOrigem) this.toCity = null
      }
      clearTimeout(this.debounceTimeout)
      const searchHandler = this.remote ? this.remoteSearch : this.search
      this.searchText = searchText
      searchText = searchText && searchText.trim()

      if (!searchText) {
        this.$emit('clear')
        this.filteredCities = []
        return
      }

      this.debounceTimeout = setTimeout(() => searchHandler(searchText, cancelPreviousCalls), 300)
    },
    async remoteSearch(searchText, cancelPreviousCalls) {
      this.loading = true

      await this.searchItems(searchText, { cancelPreviousCalls, isOrigem: this.isOrigem })

      this.loading = false
    },
    search(searchText) {
      this.loading = true

      const normalizedTextSearch = normalize(searchText)
      const results = fuzzysort.go(
        normalizedTextSearch,
        this.citiesListAggregated,
        { key: 'normalizedText', limit: 100, threshold: -10000 }
      )
      this.filteredCities = results
        .map(result => ({
          ...result.obj,
          rank: this.makeRank(result, normalizedTextSearch),
          highlightText: fuzzysort.highlight({ ...result, target: result.obj.label }, '<strong>', '</strong>')
        }))
        .sort((a, b) => b.rank - a.rank)
        .slice(0, 30)

      this.loading = false
    },
    makeRank({ obj, indexes }, textSearch) {
      let rank = 0

      const fullWordMatch = obj.normalizedText.split(/\s+/).includes(textSearch)
      if (fullWordMatch) {
        rank += 2
      }

      const sequencialMatch = indexes.every((num, i) => i === indexes.length - 1 || num + 1 === indexes[i + 1])
      if (sequencialMatch) {
        rank += 1
      }

      if (obj.rank) {
        rank += (10 - obj.rank) * 0.1
      }

      if (this.trechosVendidosMap && this.trechosVendidosMap[obj.id]) {
        rank += 0.2
      }

      // cidades devem aparecer antes dos pontos de embarque
      if (obj.id && !obj.id.includes('-')) {
        rank += 0.2
      }

      return rank
    },
    selectCity(cidade) {
      if (!cidade) { // clicar sobre a label usando v-model
        return
      }
      this.searchText = cidade.label
      this.$emit('input', cidade)
    }
  }
}
</script>

<style lang="scss">
.select-city {
  width: 100%;

  .sc-label > em {
    font-weight: $font-weight-semibold;
    font-style: normal;
  }
}
</style>
