<template>
  <div
    v-if="gruposPorDia"
    class="grupos-por-dia"
  >
    <h2
      v-if="(buscandoSemData && temGrupos && !hideTitle) || forceTitle"
      class="title-xs fw-600"
    >
      {{ datetimeIdaFormatted }}
    </h2>

    <div
      v-for="(grupo, idx) in grupos"
      :key="grupo.id"
    >
      <card-whatsapp v-if="!isRevendedorSite && idx === 3 && showWhatsappCard" />
      <grupo-conexao
        v-if="grupo.is_conexao"
        :grupo="grupo"
        :is-grupo-da-madrugada="isGrupoDaMadrugada"
      />

      <template v-else>
        <div :class="{ 'gpd-premium': isBuserBlack(grupo), 'gpd-premium-padding': hasBuserPremium && !isBuserBlack(grupo) }">
          <div v-if="isBuserBlack(grupo)" class="color-white pt-half pl-2">
            <p class="text-sm fw-700">
              Viagem Premium
            </p>
            <p class="caption pb-half">
              Conforto completo do começo ao fim
            </p>
          </div>
          <grupo
            :grupo="grupo"
            :index="idx"
            :is-grupo-da-madrugada="isGrupoDaMadrugada"
          />
        </div>
      </template>
    </div>
    <card-whatsapp v-if="!isRevendedorSite && temGrupos && grupos.length < 4 && showWhatsappCard" />
  </div>
</template>

<script>
import { mapState } from 'pinia'
import { dateFormat, DATE_FORMATS } from '~/helpers/formatters.js'
import { groupsHaveBuserPremium } from '~/helpers/grouphelper.js'
import { useRevendedorStore } from '~/stores/revendedor.js'
import cardWhatsapp from '~/components/search-result/card-whatsapp.vue'
import grupoConexao from '~/components/search-result/grupos/grupo-conexao/grupo-conexao.vue'
import grupo from '~/components/search-result/grupos/grupo.vue'

export default {
  name: 'GruposPorDia',
  components: {
    grupo,
    grupoConexao,
    cardWhatsapp
  },
  props: {
    gruposPorDia: {
      type: Object,
      default: () => ({})
    },
    hideTitle: {
      type: Boolean,
      default: false
    },
    forceTitle: {
      type: Boolean,
      default: false
    },
    isGrupoDaMadrugada: {
      type: Boolean,
      default: false
    },
    showWhatsappCard: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState(useRevendedorStore, ['isRevendedorSite']),
    buscandoSemData() {
      return !this.$route.query.ida
    },
    grupos() {
      return this.gruposPorDia?.grupos || []
    },
    temGrupos() {
      return this.grupos.length > 0
    },
    datetimeIdaFormatted() {
      return dateFormat(DATE_FORMATS.extendedweekday, this.gruposPorDia.datetime_ida)
    },
    hasBuserPremium() {
      return groupsHaveBuserPremium(this.grupos)
    }
  },
  methods: {
    isBuserBlack(grupo) {
      return groupsHaveBuserPremium([grupo])
    }
  }
}
</script>

<style lang="scss" scoped>
.grupos-por-dia {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
  margin-bottom: $spacing-3;

  .gpd-premium-padding {
    padding: 0 $spacing-half;
  }

  .gpd-premium {
    background-color: $color-brand-medium;
    padding: $spacing-half;
    border-radius: $border-radius-md;
  }
}
</style>
