<template>
  <card-travel-info
    :show-content="!initialLoading"
    :show-expand-button="hasSelectedPassengers && shouldLoadStatement"
    :expand-button-state="hasStatementExpanded"
    title="Extrato"
    class="statement"
    @toggle-expand="toggleStatement"
  >
    <template #skeleton>
      <skeleton-statement :has-volta="hasVolta" />
    </template>

    <transition
      name="fade"
      mode="out-in"
    >
      <div
        v-if="isLoadingStatement"
        class="s-loading-status"
        role="status"
        aria-busy="true"
        aria-live="polite"
      >
        <ada-loader class="mb-3" />
        Atualizando o extrato
      </div>
    </transition>

    <ada-accordion
      v-if="!hasStatementError && !initialLoading"
      :value="expandedAccordionItems"
      class="s-accordion"
      :outlined="false"
      multiple
    >
      <ada-accordion-item
        v-if="completeStatement.extrato_cancelamento"
        background-color="white"
        borderless
        paddless
      >
        <template #title>
          <h3 class="mb-one-half text-md">
            {{ completeStatement.extrato_cancelamento.title }}
          </h3>
        </template>

        <div
          v-for="item in completeStatement.extrato_cancelamento.entries"
          :key="item.title"
          class="sa-content"
        >
          <div class="sac-title">
            <p class="text-sm">
              {{ item.title }}
            </p>
            <p
              v-if="item.subtitle"
              class="caption text-grey"
            >
              {{ item.subtitle }}
            </p>
          </div>

          <p class="text-sm">
            {{ formatReal(item.value) }}
          </p>
        </div>

        <ada-divider
          color="grey-light"
          class="mb-2"
        />
      </ada-accordion-item>

      <ada-accordion-item
        v-if="!isItemAdicional"
        background-color="white"
        borderless
        paddless
      >
        <template #title="{ isActive }">
          <div class="d-flex jc-between mb-one-half">
            <div>
              <h3 class="text-md fw-500">
                {{ tituloExtratoPrimeiraViagem }}
              </h3>

              <p
                v-if="idaTemTaxa && !isActive"
                class="caption color-grey"
              >
                Reserva + taxa de revenda
              </p>
            </div>

            <b v-if="valorIda !== null">{{ valorIdaFormatted }}</b>
            <b v-else>-</b>
          </div>
        </template>

        <template v-if="shouldLoadStatement && statementHasEntries">
          <div
            v-for="(item, index) in idaEntries"
            :key="`${index}-${item.title}`"
            class="sa-content"
          >
            <div class="sac-title">
              <p class="text-sm">
                {{ item.title }}

                <tooltip-credito v-if="isCreditoEntry(item.title)" />
                <tooltip-taxa-servico v-else-if="isTaxaRevenda(item.type)" />
                <tooltip-poltronas-antecipadas v-else-if="isPoltronaAntecipada(item.title)" />
              </p>
              <p class="caption text-grey">
                {{ item.subtitle }}
              </p>
            </div>

            <p
              :class="{'color-green': item.value < 0 || isDescontoTaxaZero(item)}"
              class="text-sm"
            >
              {{ formatValor(item) }}
            </p>
          </div>

          <ada-divider
            v-if="hasVolta"
            color="grey-light"
            class="mb-2"
          />
        </template>
      </ada-accordion-item>

      <ada-accordion-item
        v-if="hasVolta"
        background-color="white"
        borderless
        paddless
      >
        <template #title="{ isActive }">
          <div class="d-flex jc-between mb-one-half">
            <div>
              <h3 class="text-md fw-500">
                {{ tituloExtratoSegundaViagem }}
              </h3>

              <p
                v-if="voltaTemTaxa && !isActive"
                class="caption color-grey"
              >
                Reserva + taxa de revenda
              </p>
            </div>

            <b v-if="valorVolta !== null">{{ valorVoltaFormatted }}</b>
            <b v-else>-</b>
          </div>
        </template>

        <template v-if="shouldLoadStatement && statementHasEntries">
          <div
            v-for="(item, index) in voltaEntries"
            :key="`${index}-${item.title}`"
            class="sa-content"
          >
            <div class="sac-title">
              <p class="text-sm">
                {{ item.title }}

                <tooltip-credito v-if="isCreditoEntry(item.title)" />
                <tooltip-taxa-servico v-else-if="isTaxaRevenda(item.type)" />
                <tooltip-poltronas-antecipadas v-else-if="isPoltronaAntecipada(item.title)" />
              </p>
              <p class="caption text-grey">
                {{ item.subtitle }}
              </p>
            </div>

            <p
              :class="{'color-green': item.value < 0 || isDescontoTaxaZero(item)}"
              class="text-sm"
            >
              {{ formatValor(item) }}
            </p>
          </div>
        </template>
      </ada-accordion-item>
      <ada-accordion-item
        v-if="isItemAdicional"
        background-color="white"
        borderless
        paddless
      >
        <template #title>
          <div class="d-flex jc-between mb-one-half">
            <div>
              <h3 class="text-md fw-500">
                {{ tituloItensAdicionais }}
              </h3>
            </div>

            <b v-if="valorItensAdicionais !== null">{{ valorItensAdicionaisFormatted }}</b>
            <b v-else>-</b>
          </div>
        </template>

        <template v-if="isItemAdicional">
          <div
            v-for="(item, index) in itensAdicionaisEntries"
            :key="`${index}-${item.title}`"
            class="sa-content"
          >
            <div class="sac-title">
              <p class="text-sm">
                {{ item.title }}

                <tooltip-credito v-if="isCreditoEntry(item.title)" />
                <tooltip-taxa-servico v-else-if="isTaxaRevenda(item.type)" />
                <tooltip-poltronas-antecipadas v-else-if="isPoltronaAntecipada(item.title)" />
              </p>
              <p class="caption text-grey">
                {{ item.subtitle }}
              </p>
            </div>

            <p
              :class="{'color-green': item.value < 0 || isDescontoTaxaZero(item)}"
              class="text-sm"
            >
              {{ formatValor(item) }}
            </p>
          </div>
        </template>
      </ada-accordion-item>
    </ada-accordion>

    <ada-divider color="grey-light" />

    <section
      v-if="!initialLoading"
      class="s-footer"
      aria-label="Total a pagar"
    >
      <template v-if="parcelaCount > 1">
        <h3 class="text-md">
          Total a pagar (com juros)
        </h3>

        <div class="sf-price">
          <b v-if="valorTotalComJuros !== null">
            {{ valorTotalComJurosFormatted }}
          </b>
          <b v-else>-</b>
        </div>
      </template>

      <template v-else>
        <h3 class="text-md fw-500">
          Total a pagar
        </h3>

        <div class="sf-price">
          <p
            v-if="teveDesconto && userPrecisaPagar"
            class="caption s-p-crossed"
          >
            <s>{{ valorSemDescontoFormatted }}</s>
          </p>
          <p v-if="valorTotal !== null">
            <strong>{{ valorTotalFormatted }}</strong>
          </p>
          <b v-else>-</b>
        </div>
      </template>
    </section>

    <ada-alert
      v-if="!userPrecisaPagar"
      type="success"
      :icon="faFaceTongueMoney"
      class="mt-2"
    >
      <p class="text-sm">
        Sua viagem ficou menos de R$ 2,00! Pode deixar por nossa conta!
      </p>
    </ada-alert>
  </card-travel-info>
</template>

<script>
import { faFaceTongueMoney } from '@fortawesome/pro-regular-svg-icons'
import { mapState, mapWritableState } from 'pinia'
import { real } from '~/helpers/formatters.js'
import { useReservationStore } from '~/stores/reservation.js'
import { useSessionStore } from '~/stores/session.js'
import { useStatementStore } from '~/stores/statement.js'
import cardTravelInfo from '~/components/shared/card-travel-info/card-travel-info.vue'
import { MODELOS_OPERACAO } from '~/constants.ts'
import tooltipCredito from '~/components/checkout/checkout-statement/tooltip-credito.vue'
import tooltipPoltronasAntecipadas from '~/components/checkout/checkout-statement/tooltip-poltronas-antecipadas.vue'
import tooltipTaxaServico from '~/components/checkout/checkout-statement/tooltip-taxa-servico.vue'
import skeletonStatement from './skeleton-statement/skeleton-statement.vue'

export default {
  components: {
    skeletonStatement,
    cardTravelInfo,
    tooltipTaxaServico,
    tooltipCredito,
    tooltipPoltronasAntecipadas
  },
  props: {
    linkPagamento: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      hasStatementExpanded: false,
      faFaceTongueMoney
    }
  },
  mounted() {
    this.parcelaCount = 0
  },
  computed: {
    ...mapState(useSessionStore, ['loggedIn']),
    ...mapWritableState(useStatementStore, ['parcelaCount']),
    ...mapState(useStatementStore, ['completeStatement', 'parcelamentoOptions', 'isLoadingStatement']),
    ...mapState(useReservationStore, ['hasVolta', 'selectedPassengers', 'isUserInfoFilled', 'reservationData', 'gruposIda', 'gruposVolta']),
    initialLoading() {
      if (!this.shouldLoadStatement) return false

      return !this.completeStatement.extrato_reserva
    },
    hasStatementError() {
      return !this.completeStatement || !!this.completeStatement?.error
    },
    statementHasEntries() {
      return this.completeStatement?.extrato_reserva?.every(
        er => er.title?.startsWith('Total') || er.entries?.length >= 0
      )
    },
    hasSelectedPassengers() {
      return this.selectedPassengers.length > 0
    },
    expandedAccordionItems() {
      return this.hasStatementExpanded && this.hasSelectedPassengers && this.shouldLoadStatement ? [0, 1] : []
    },
    valorIda() {
      if (!this.hasSelectedPassengers || !this.completeStatement?.extrato_reserva?.length || !this.shouldLoadStatement) {
        return null
      }

      return this.completeStatement.extrato_reserva.find(
        er => er.title === 'Total ida'
      )?.value
    },
    idaEntries() {
      return this.completeStatement?.extrato_reserva?.filter(er => er.title === 'Extrato ida').reduce((acc, er) => {
        acc.push(...er.entries)
        return acc
      }, [])
    },
    valorIdaFormatted() {
      return real(this.valorIda)
    },
    valorVolta() {
      if (this.hasStatementError || !this.hasSelectedPassengers || !this.hasVolta || !this.completeStatement?.extrato_reserva?.length || !this.shouldLoadStatement) {
        return null
      }

      return this.completeStatement.extrato_reserva.find(
        er => er.title === 'Total volta'
      )?.value
    },
    voltaEntries() {
      return this.completeStatement?.extrato_reserva?.filter(er => er.title === 'Extrato volta').reduce((acc, er) => {
        acc.push(...er.entries)
        return acc
      }, [])
    },
    valorVoltaFormatted() {
      return real(this.valorVolta)
    },
    isItemAdicional() {
      return this.completeStatement?.isItemAdicional
    },
    itensAdicionaisEntries() {
      return this.completeStatement?.extrato_item_adicional?.filter(er => er.title !== 'Total itens adicionais') || []
    },
    valorItensAdicionais() {
      if (this.hasStatementError || !this.hasSelectedPassengers || !this.isItemAdicional || !this.completeStatement?.extrato_item_adicional?.length || !this.shouldLoadStatement) {
        return null
      }

      return this.completeStatement.extrato_item_adicional.find(
        er => er.title === 'Total itens adicionais'
      )?.value
    },
    valorItensAdicionaisFormatted() {
      return real(this.valorItensAdicionais)
    },
    valorTotal() {
      if (this.completeStatement?.pagamento_total || this.isItemAdicional) return this.completeStatement.pagamento_total
      if (this.hasStatementError || !this.hasSelectedPassengers || !this.completeStatement?.extrato_reserva?.length || !this.shouldLoadStatement) {
        return null
      }
      return this.completeStatement.extrato_reserva.find(
        er => er.title === 'Total a pagar'
      )?.value
    },
    valorTotalFormatted() {
      return real(this.valorTotal)
    },
    valorSemDesconto() {
      if (this.hasStatementError || !this.hasSelectedPassengers || !this.completeStatement?.extrato_reserva?.length || !this.shouldLoadStatement) {
        return null
      }
      let valorSemDesconto = 0
      for (const item of this.completeStatement.extrato_reserva[0].entries) {
        if (item.value > 0) {
          valorSemDesconto += item.value
        }
      }
      return valorSemDesconto.toFixed(2)
    },
    valorSemDescontoFormatted() {
      return real(this.valorSemDesconto)
    },
    valorTotalComJuros() {
      if (this.hasStatementError || !this.hasSelectedPassengers || !this.parcelamentoOptionsCartaoSelecionado?.length || !this.shouldLoadStatement) {
        return null
      }
      return this.parcelamentoOptionsCartaoSelecionado[this.parcelaCount - 1]?.total_parcelado
    },
    valorTotalComJurosFormatted() {
      return real(this.valorTotalComJuros)
    },
    teveDesconto() {
      return (this.valorSemDesconto > this.valorTotal || !this.userPrecisaPagar)
    },
    userPrecisaPagar() {
      if (!this.selectedPassengers.length || this.initialLoading) return true
      if (this.valorTotal === null || this.valorTotal === undefined) return true
      if (this.isItemAdicional && !this.completeStatement?.extrato_item_adicional?.length) return true
      return this.valorTotal >= 2
    },
    isUserInfoValid() {
      return this.loggedIn || this.isUserInfoFilled || this.linkPagamento
    },
    shouldLoadStatement() {
      return this.isUserInfoValid && this.selectedPassengers.length > 0
    },
    parcelamentoOptionsCartaoSelecionado() {
      const parcelamento = this.parcelamentoOptions.find(item => item.bin === this.reservationData.binCartaoSelecionado)
      return parcelamento ? parcelamento.options : []
    },
    idaTemTaxa() {
      if (!this.statementHasEntries) return false
      return this.idaEntries.some(item => this.isTaxaRevenda(item.type))
    },
    voltaTemTaxa() {
      if (!this.statementHasEntries) return false
      return this.voltaEntries.some(item => this.isTaxaRevenda(item.type))
    },
    tituloExtratoPrimeiraViagem() {
      return 'Extrato da ida'
    },
    tituloExtratoSegundaViagem() {
      return 'Extrato da volta'
    },
    tituloItensAdicionais() {
      return 'Extrato de itens adicionais'
    }
  },
  methods: {
    toggleStatement() {
      this.hasStatementExpanded = !this.hasStatementExpanded
    },
    isDescontoTaxaZero(item) {
      return item.type === 'taxa_revenda' && item.value === 0
    },
    formatValor(item) {
      if (this.isDescontoTaxaZero(item)) {
        return 'Taxa zero'
      }
      return this.formatReal(item.value)
    },
    formatReal(value) {
      return real(value)
    },
    isTaxaRevenda(type) {
      return type === 'taxa_revenda'
    },
    isCreditoEntry(title) {
      return title === 'Crédito utilizado'
    },
    isPoltronaAntecipada(title) {
      // TODO: usar modelo_precificacao
      if (title === 'Reserva ida') {
        return this.gruposIda?.some(grupo =>
          grupo.modelo_operacao === MODELOS_OPERACAO.POLTRONAS_ANTECIPADAS
        )
      } else if (title === 'Reserva volta') {
        return this.gruposVolta?.some(grupo =>
          grupo.modelo_operacao === MODELOS_OPERACAO.POLTRONAS_ANTECIPADAS
        )
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.statement {
  position: relative;
  overflow: hidden;

  .s-loading-status {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: $z-index-main;
    width: 100%;
    height: 100%;
    font-weight: 500;
    font-size: $font-size-sm;
    color: $color-grey-dark;
    background-color: rgba($color-white, 0.85);
  }

  .s-accordion {
    .sa-content {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-one-half;

      .sac-title {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }
    }
  }

  .s-footer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: $spacing-one-half;

    .sf-price {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }
  }

  .s-p-crossed {
    text-decoration: line-through;
    white-space: nowrap;
    text-align: start;
    line-height: 1.1;

    @media (min-width: $screen-tablet-min) {
      text-align: end;
    }
  }
}
</style>
