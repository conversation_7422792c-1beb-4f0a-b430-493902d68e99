<template>
  <ada-tooltip
    :position="position"
    strategy="fixed"
  >
    <template #activator="{ on }">
      <fa
        class="color-brand"
        :size="size"
        :icon="faCircleInfo"
        v-on="on"
      />
    </template>
    <p class="ta-center">
      Eventuais diferenças entre os valores das passagens e os descritos no bilhete são devidas ao modelo de parceria com a Buser.
    </p>
  </ada-tooltip>
</template>

<script>
import { faCircleInfo } from '@fortawesome/pro-regular-svg-icons'

export default {
  props: {
    position: {
      type: String,
      default: 'top'
    },
    size: {
      type: String,
      default: '1x'
    }
  },
  data() {
    return {
      faCircleInfo
    }
  }
}
</script>
