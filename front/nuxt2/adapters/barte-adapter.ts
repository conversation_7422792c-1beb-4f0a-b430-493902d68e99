import { useNuxtApp } from '#app'
import { v4 as uuidv4 } from 'uuid'
import { createCardToken } from '~api/checkout.js'
import { CreditCardInfo } from '~api/checkout.types.ts'
import sentryhelper from '~sentryhelper'
import { BarteBuyerResponse, BarteCardTokenResponse } from '~/adapters/barte-adapter.types.ts'
import abtesthelper from '~/helpers/abtest/abtest.js'
import AbStorageAdapter from '~/helpers/abtest/adapters/abstorage-adapter.js'
import cookiehelper from '~/helpers/cookiehelper.js'
import { useSessionStore } from '~/stores/session.js'
import { useSettingsStore } from '~/stores/settings.js'
import { PaymentAdapterInterface } from './adapter-interface.ts'

declare global {
  interface Window {
    dftp: any;
  }
}
export class BarteAdapter implements PaymentAdapterInterface {
  config: unknown
  attemptReference: undefined
  buyer: BarteBuyerResponse | undefined

  constructor(config) {
    this.config = config
    this.attemptReference = uuidv4()
  }

  init() {
    const nethoneOptions = {
      attemptReference: this.attemptReference,
      sensitiveFields: ['cardHolderName', 'cardCvv', 'cardExpirationDate', 'cardNumber']
    }

    window.dftp.init(nethoneOptions)
  }

  getClient() {}

  isEnabled() {
    const settingsStore = useSettingsStore()
    return settingsStore.permiteBarte
  }

  getScript() {
    return []
  }

  getRefKey() {
    return this.attemptReference
  }

  async generateCreditCardToken(creditCardInfo: CreditCardInfo, user) {
    try {
      let recaptchaToken = ''
      const cardValidade = creditCardInfo.validade
      const validade = `${cardValidade.substring(0, 2)}/20${cardValidade.substring(2)}`

      if (!user?.id) {
        recaptchaToken = await useNuxtApp().$recaptcha.execute('purchase')
      }

      const holderName = creditCardInfo.cardName.replace(/[^a-zA-Z0-9\sÁÉÍÓÚáéíóúÇçÃãÕõ]/g, '')

      const response: BarteCardTokenResponse = await createCardToken({
        user,
        holderName,
        number: creditCardInfo.card,
        cvv: creditCardInfo.cvv,
        expiration: validade,
        grtoken: recaptchaToken
      })

      return response.cardId
    } catch (error) {
      sentryhelper.captureEvent({ message: '[BarteAdapter] Erro ao tentar gerar card token', extra: error, tags: null })
      throw error
    }
  }

  getDeviceId() {
    return ''
  }

  isReprocessTokenRequired() {
    return false
  }
}

export function isBarteAbEnabled() {
  const settingsStore = useSettingsStore()
  const sessionStore = useSessionStore()
  const currentUser = sessionStore.user

  if (process.server) {
    return false
  }

  if (!settingsStore.permiteBarte) {
    return false
  }

  if (settingsStore.barteUsers.size) {
    return currentUser ? settingsStore.barteUsers.has(currentUser.id) : false
  }

  const { useCookie } = require('#app')

  const result = abtesthelper.setWithRestrictions(useCookie(), {
    label: 'ab_barte',
    options: {
      true: 0.10,
      false: 0.90
    },
    enableSSR: false
  })

  try {
    return JSON.parse(result)
  } catch (error) {}

  return false
}

export function isBarteFallbackEnabled() {
  return useSettingsStore().habilitaFallbackBarte
}

export async function disableBarte() {
  const abStorageAdapter = new AbStorageAdapter(false, document.cookie)
  cookiehelper.setItem('ab_barte', false, 10)
  await abStorageAdapter.set('ab_barte', false, 3600)

  window.location.reload()
}
