import { defineStore } from 'pinia'
import { searchPlaces } from '~api/search.js'
import { search } from '~api/searchbox.js'
import { useRevendedorStore } from '~/stores/revendedor.js'

export const useSearchboxStore = defineStore('searchbox', {
  state: () => ({
    fromCity: null,
    toCity: null,
    departureDate: null,
    returnDate: null,
    origemItems: [],
    destinoItems: []
  }),
  actions: {
    async searchItems(searchText, { cancelPreviousCalls, isOrigem }) {
      try {
        const results = await search(searchText, null, null, cancelPreviousCalls)
        const makeResults = results.map(item => {
          item.label = makeLabel(item)
          item.highlightText = makeHighlightText(item)
          return item
        })

        if (isOrigem) {
          this.origemItems = makeResults
          return
        }

        this.destinoItems = makeResults
        if (useRevendedorStore().isRevendedorSite) {
          useRevendedorStore().resetSelectedGroups()
        }
      } catch (error) { }
    },
    async setFromCityBySlug(slug) {
      const places = await searchPlaces(slug, null)

      this.fromCity = places.origem
    },
    swapOrigemDestino() {
      const oldOrigem = this.fromCity
      const oldDestino = this.toCity
      const oldOrigemItems = this.origemItems
      const oldDestinoItems = this.destinoItems

      this.origemItems = oldDestinoItems
      this.destinoItems = oldOrigemItems
      this.fromCity = oldDestino
      this.toCity = oldOrigem
    }
  }
})

const makeLabel = (item) => {
  if (!item.city) {
    return item.label
  }

  if (item.alias) {
    return `${item.alias}, ${item.city} - ${item.state}`
  }

  const label = `${item.city} - ${item.state}`

  return item.addr ? `${label}, ${item.addr}` : label
}

const makeHighlightText = (item) => {
  const city = item.formatted?.city || item.city
  const addr = item?.formatted.addr || item?.addr
  const state = item.formatted?.state || item.state
  const alias = item.formatted?.alias || item.alias

  if (!city) {
    return item.label
  }
  if (alias) {
    return `${alias}, ${city} - ${state}`
  }

  const label = `${city} - ${state}`

  return addr ? `${label}, ${addr}` : label
}
