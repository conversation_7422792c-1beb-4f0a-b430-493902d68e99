import dayjs from 'dayjs'
import { defineStore } from 'pinia'
import {
  search,
  searchOrigem,
  searchDestino,
  searchPromoGroups,
  searchHit,
  searchConexoesV2,
  trechoPromo,
  searchMarketplace,
  upsertGrupo
} from '~api/search.js'
import { isOfertaBf } from '~/helpers/blackFridayhelper.js'
import { conexoes } from '~/helpers/conexoes.js'
import EventBus from '~/helpers/eventbus.js'
import { isGroupBuserPremium } from '~/helpers/grouphelper.js'
import { compareSearchItems } from '~/helpers/matchmarketplace.js'
import searchhelper from '~/helpers/searchhelper.js'
import { setTrechoSign } from '~/helpers/signedtrecho.js'
import viagensproibidas from '~/helpers/viagensproibidas.js'
import { processBadges } from '~/stores/badges.ts'
import { useConexaoStore } from '~/stores/conexao.js'
import { useFiltroStore } from '~/stores/filtro.js'
import { useSearchboxStore } from '~/stores/searchbox.js'
import { useRevendedorStore } from './revendedor.js'
export const ACTIVE_PROMO_INITIAL_STATE = {
  code: null,
  availableGroups: {},
  origemSlug: null,
  destinoSlug: null,
  departureDate: null
}

export const JK_ID = 181

const WEEK_DAY_MAP = {
  hoje: 'hoje',
  amanha: 'amanhã',
  domingo: 'domingo',
  'segunda-feira': 'segunda-feira',
  'terca-feira': 'terça-feira',
  'quarta-feira': 'quarta-feira',
  'quinta-feira': 'quinta-feira',
  'sexta-feira': 'sexta-feira',
  sabado: 'sábado'
}

export const DEFAULT_ORDERING_OPTIONS = [
  {
    key: 'hsaida-crescente',
    type: 'hsaida',
    order: 'crescente',
    text: 'Mais cedo'
  },
  {
    key: 'hsaida-decrescente',
    type: 'hsaida',
    order: 'decrescente',
    text: 'Mais tarde'
  },
  {
    key: 'preco-crescente',
    type: 'preco',
    order: 'crescente',
    text: 'Menor preço'
  },
  {
    key: 'preco-decrescente',
    type: 'preco',
    order: 'decrescente',
    text: 'Maior preço'
  },
  {
    key: 'duracao-crescente',
    type: 'duracao',
    order: 'crescente',
    text: 'Viagem mais curta'
  },
  {
    key: 'duracao-decrescente',
    type: 'duracao',
    order: 'decrescente',
    text: 'Viagem mais longa'
  }
]
export const useSearchStore = defineStore('search', {
  state: () => ({
    loading: true,
    results: {},
    searchResultCache: {},
    suggestions: {},
    trecho: { origem: null, destino: null, alerta: null },
    trechoCidadeProxima: { origem: null, destino: null },
    trechoAlternativo: { origem: null, destino: null },
    grupoIda: null,
    grupoVolta: null,
    selectedDate: null,
    weekDay: null,
    completed: false,
    gruposByDate: [],
    gruposProximoDia: {},
    gruposProximoDiaFiltrados: {},
    badgesByDate: {},
    grupoBadgeMap: {},
    gruposTrechoPesquisado: [],
    gruposTrechoAlternativo: [],
    usersSearching: null,
    ordenacao: 'hsaida',
    ordem: 'crescente',
    orderingOptions: DEFAULT_ORDERING_OPTIONS,
    cacheSuggestionKeys: {},
    gruposFiltrados: [],
    quantidadeGruposFiltrados: 0,
    showPopupMapa: false,
    pontoMapa: [],
    pontoMapaType: null,
    // Global behaviors
    showPoltronasModal: false,
    activePromo: ACTIVE_PROMO_INITIAL_STATE,
    grupoDestaque: null,
    gruposRecomendadosBased: [],
    idsGruposRecomendados: [],
    trechosPromocionaisSet: new Set([])
  }),
  getters: {
    gruposRecomendados(state) {
      return state.results?.grupos_recomendados
    },
    gruposRecomendadosConexao(state) {
      return state.results?.gruposRecomendadosConexao || {}
    },
    sugestoesCidadeOrigem(state) {
      return state.suggestions?.cidadeOrigem
    },
    sugestoesCidadeDestino(state) {
      return state.suggestions?.cidadeDestino
    },
    isTrechoVendido(state) {
      return state.results.is_trecho_vendido
    },
    hasOfertaBlackFriday(state) {
      if (!state.gruposTrechoPesquisado?.length) return false
      return state.gruposTrechoPesquisado.some(d => d.grupos.some((g) => {
        let valido = isOfertaBf(g.max_split_value, g.ref_split_value, g.modelo_venda)
        if (state.selectedDate) {
          valido = valido && d.datetime_ida === state.selectedDate
        }
        return valido
      }))
    },
    encontrouGrupos() {
      return this.hasGruposTrechoPesquisado || this.hasGruposTrechoAlternativos
    },
    hasVoltaQuery: function() {
      // TODO: Arrumar $route reativo dentro da store pra não precisar passar como parametro para actions e getters
      return ($route) => {
        return !!$route.query.volta
      }
    },
    isVolta: function(state) {
      return ($route) => {
        return state.grupoIda && this.hasVoltaQuery($route)
      }
    },
    hasGruposTrechoPesquisado(state) {
      return state.gruposTrechoPesquisado.some(groupsByDate => groupsByDate.grupos.length)
    },
    hasGruposTrechoAlternativos(state) {
      return state.gruposTrechoAlternativo.some(groupsByDate => groupsByDate.grupos.length)
    },
    quantidadeGruposTrechoAlternativo(state) {
      return state.gruposTrechoAlternativo.reduce((acc, item) => acc + item.grupos.length, 0)
    },
    quantidadeGruposTrechoPesquisado(state) {
      return state.gruposTrechoPesquisado.reduce((acc, item) => acc + item.grupos.length, 0)
    },
    weekDayFormatted() {
      return WEEK_DAY_MAP[this.weekDay]
    },
    hasResultadosSuficientes() {
      const filtroStore = useFiltroStore()
      if (filtroStore.temFiltrosAplicados) return true
      const amountOfResultadosPesquisado = this.quantidadeGruposTrechoPesquisado
      return amountOfResultadosPesquisado > 10
    },
    hasGruposProximoDia(state) {
      return state.gruposProximoDia?.grupos?.length > 0
    },
    canFilterByCompany: function() {
      return ($route) => {
        const { company, ida, volta } = $route.query
        return searchhelper.ALLOWED_COMPANIES_TO_FILTER.includes(company) && ida && !volta
      }
    }
  },
  actions: {
    _emptyResults() {
      const results = {
        is_trecho_vendido: false,
        trecho_alternativo: null,
        groups_by_date: [],
        itinerarios: {},
        locais: {},
        locais_de_embarque: [],
        locais_de_desembarque: [],
        badges_by_date: {
          best_price_by_date: {},
          most_comfortable_by_date: {},
          recommended_by_date: {},
          promotional_price_new_route: {}
        }
      }

      return results
    },
    init(params) {
      this.origemSlug = params.origemSlug
      this.destinoSlug = params.destinoSlug
      this.departureDate = params.departureDate
      this.returnDate = params.returnDate
      this.weekDay = params.weekDay
      this.idGrupoDestaque = params.idGrupoDestaque
      this.grupoIda = null
      this.grupoVolta = null
      this.completed = false
      this.buscaIdaEVolta = false
      this.buscaPromocao = params.buscaPromocao
      this.searchResultCache = {}
    },
    async search({ date, origem, destino, weekDay, company }, $route, headers) {
      // Usa preferencialmente origem/destino passados por paramêtro.
      const origemSlug = origem || $route.params.origem
      const destinoSlug = destino || $route.params.destino
      const weekDaySlug = weekDay || $route.params.weekDay
      const companyId = company && $route.query.company

      if (!origemSlug || !destinoSlug) {
        return
      }

      this.loading = true

      EventBus.$emit('click-search', {
        origem: origemSlug,
        destino: destinoSlug,
        departureDate: $route.query.ida,
        returnDate: $route.query.volta,
        weekDay: weekDaySlug
      })

      const filtroStore = useFiltroStore()
      const conexaoStore = useConexaoStore()

      // Limpo os filtros e as conexões
      filtroStore.cleanFiltros()
      this.gruposFiltrados = []
      this.quantidadeGruposFiltrados = 0
      this.gruposProximoDiaFiltrados = {}
      conexaoStore.$reset()

      this.selectedDate = date
      this.idGrupoDestaque = $route.query.idGrupoDestaque
      const cacheKey = cacheKeyFromState(this, { origemSlug, destinoSlug, companyId })
      const cachedResults = this.searchResultCache[cacheKey]
      if (cachedResults) {
        await this.commitResults(cachedResults, companyId, $route)
        return
      }
      const params = (() => {
        if (this.isVolta($route)) {
          return {
            origemSlug: destinoSlug,
            destinoSlug: origemSlug,
            departureDate: date
          }
        }

        return {
          origemSlug,
          destinoSlug,
          departureDate: date,
          weekDay: weekDaySlug
        }
      })()

      const promises = [search(params, headers), searchConexoesV2(params, headers), searchMarketplace(params, headers)]

      const dataResult = await Promise.allSettled(promises)

      const [results, resultConexoes, resultMarketplace] = dataResult.map(data => {
        if (data.status !== 'fulfilled') {
          console.error(data.reason, data.status)
          return this._emptyResults()
        }
        return data.value
      })

      if (hasResults(results) && hasResults(resultConexoes)) {
        concatConexoesAndGroupsNormalSearch(resultConexoes, results)
      }

      if (hasResults(results) && resultMarketplace?.items?.length > 0) {
        concatMarketplaceAndGroupsNormalSearch(resultMarketplace, results, params.departureDate)
      }

      if (date && hasResults(results)) {
        const { groupsNextDay, groupsByDate } = searchhelper.splitGroupsByNextDay(results.groups_by_date, date)
        results.groups_next_day = groupsNextDay
        results.groups_by_date = groupsByDate
      }

      // Se é viagem proibida a nivel interestadual ou intermunicipal (origem e destino iguais), desconsiderar os resultados
      // TODO Essa lógica deveria ser no back e nem precisar retornar o que é proibido, evitando filtrar aqui na store
      const isOrigemOrDestinoProibidos = viagensproibidas.checkIsViagemWithOrigemOrDestinoProibidos(origemSlug, destinoSlug)
      if (isOrigemOrDestinoProibidos) {
        Object.assign(results, this._emptyResults())
      }

      const hasProibicaoOnModeloVenda = viagensproibidas.checkHasProibicaoOnModeloVenda(origemSlug, destinoSlug)
      // Filtra fora os resultados de grupos com modelo de venda proibido mutando o os grupos do groups_by_date
      if (hasProibicaoOnModeloVenda && hasResults(results)) {
        let idx = results.groups_by_date.length
        while (idx--) {
          const gbd = results.groups_by_date[idx]
          gbd.grupos = gbd.grupos.filter(g => !viagensproibidas.checkIsViagemWithModeloVendaProibido(g))
          if (gbd.grupos.length === 0) {
            results.groups_by_date.splice(idx, 1)
          }
        }
      }

      await this.commitResults(results, companyId, $route)

      const locationKey = {
        origemSlug: results.trecho.origem.slug,
        destinoSlug: results.trecho.destino.slug,
        companyId
      }

      if (!companyId) {
        Object.keys(this.searchResultCache).forEach(cacheKey => {
          if (cacheKey.includes('company')) {
            delete this.searchResultCache[cacheKey]
          }
        })
      }

      this.searchResultCache = {
        ...this.searchResultCache,
        [cacheKeyFromState(this, locationKey)]: results
      }

      EventBus.$emit('view-search-content', results.trecho)
      // O hit inicial é feito no mounted da busca, para nunca rodar no SSR.
      if (process.browser) {
        this.hit($route)
      }
    },
    async fetchSuggestions({ origem, destino, limit, withSEOContent, maxValue, bucketizado, promo }) {
      const searchboxStore = useSearchboxStore()

      // Validacao para ver se os dados buscados anteriormente ainda são validos.
      const cached = this.cacheSuggestionKeys
      let cachedlimitIsBigger = false
      if (limit && cached.limit) {
        const currentLimit = parseInt(limit) || null
        const cachedLimit = parseInt(cached.limit) || null
        cachedlimitIsBigger = !cachedLimit || cachedLimit >= currentLimit
      }
      const isCacheValid = cached.origem === origem && cached.destino === destino && cachedlimitIsBigger && maxValue === cached.maxValue
      if (isCacheValid) {
        return
      }

      const origemSlug = origem
      const destinoSlug = destino

      let cidadeOrigem = null
      let cidadeDestino = null

      if (origemSlug) {
        cidadeOrigem = searchOrigem({ origemSlug, limit, bucketizado, promo })
      }
      if (destinoSlug) {
        cidadeDestino = searchDestino({ destinoSlug, limit, withSEOContent })
      }

      const [origemResult, destinoResult] = await Promise.allSettled([cidadeOrigem, cidadeDestino])
      const errorOrigem = origemResult.status === 'rejected'
      const errorDestino = destinoResult.status === 'rejected'
      let errorMessage = ''

      cidadeOrigem = errorOrigem ? null : origemResult.value
      cidadeDestino = errorDestino ? null : destinoResult.value

      // Precisamos verificar separadamente os erros que vieram das chamadas de origem e destino
      // dessa forma evitamos estourar erros desnecessários pro usuário e renderizamos conteúdo importante para SEO
      if (errorOrigem) {
        const origemErrorEvent = origemResult.reason
        if (origemErrorEvent.response?.status === 400 || origemErrorEvent.response?.status === 404) {
          cidadeOrigem = null
          errorMessage = 'Não encontramos ônibus disponíveis com a origem informada.'
          searchboxStore.fromCity = null
        } else {
          throw origemErrorEvent
        }
      }

      if (errorDestino) {
        const destinoErrorEvent = destinoResult.reason
        if (destinoErrorEvent.response?.status === 400 || destinoErrorEvent.response?.status === 404) {
          cidadeDestino = null
          errorMessage = 'Não encontramos ônibus disponíveis com o destino informado.'
          searchboxStore.toCity = null
        } else {
          throw destinoErrorEvent
        }
      }

      // Filtra somente trechos permitidos
      // TODO Essa lógica deveria ser no back e nem precisar retornar o que é proibido, evitando filtrar aqui na store
      if (cidadeOrigem) cidadeOrigem.saindo_de = cidadeOrigem.saindo_de.filter(destino => !viagensproibidas.checkIsViagemWithOrigemOrDestinoProibidos(origemSlug, destino.slug))
      if (cidadeDestino) cidadeDestino.indo_para = cidadeDestino.indo_para.filter(origem => !viagensproibidas.checkIsViagemWithOrigemOrDestinoProibidos(origem.slug, destinoSlug))

      if (maxValue && cidadeOrigem?.saindo_de?.length) {
        cidadeOrigem.saindo_de = cidadeOrigem.saindo_de.filter(trecho => trecho.best_price <= maxValue)
      }

      // caso a busca tenha sido apenas origem ou destino
      if (cidadeOrigem && !cidadeDestino) {
        searchboxStore.fromCity = cidadeOrigem
      }

      if (cidadeDestino && !cidadeOrigem) {
        searchboxStore.toCity = cidadeDestino
      }

      // Após tudo finalizado salvamos na store os dados
      this.suggestions = { ...this.suggestions, cidadeOrigem, cidadeDestino }
      this.setTrecho({ cidadeOrigem, cidadeDestino })
      this.cacheSuggestionKeys = { origem, destino, limit, maxValue }

      if (errorOrigem && errorDestino) {
        errorMessage = 'Não encontramos ônibus disponíveis com a origem e destino informados.'
      }

      if (errorMessage) {
        throw new Error(errorMessage)
      }
    },
    async commitResults(results, company, $route) {
      await this.setupTrechoPromo(results)
      const searchboxStore = useSearchboxStore()

      const companyId = company && $route.query.company

      if (companyId && this.canFilterByCompany($route)) {
        filterGroupsByCompany(results, companyId)
      }
      // Ajudinha Filtro: Verifique se e necessário colocar a funcao da store aqui
      // Actions dos filtros
      const filtroStore = useFiltroStore()
      filtroStore.filtraLocaisEmbarque(results)
      filtroStore.filtraLocaisDesembarque(results)
      filtroStore.filtraProximoAoMetro(results)
      filtroStore.filtraBlackFriday(results)
      filtroStore.filtraAcessibilidade(results)
      filtroStore.filtraTrechoPromo(results)
      // Separo os grupos do trecho alternativo e trecho pesquisado, a search retorna ambos
      // Atualmente exibimos ou trecho pesquisado, ou trecho alternativo, nunca os dois juntos.
      // Não alterei o back para retornar apenas os pesquisados quando informado um local pois pode
      // ser útil ter todos no futuro, para trazer mais informações na página.
      const gruposTrechoPesquisado = []
      const gruposTrechoAlternativo = []

      for (const groupByDate of results.groups_by_date) {
        const _gruposTrechoPesquisado = []
        const _gruposTrechoAlternativo = []

        for (const grupo of groupByDate.grupos) {
          let target = _gruposTrechoPesquisado
          if (grupo.trecho_alternativo) {
            target = _gruposTrechoAlternativo
          }
          target.push(grupo)
        }

        if (_gruposTrechoPesquisado.length) {
          gruposTrechoPesquisado.push({
            datetime_ida: groupByDate.datetime_ida,
            grupos: _gruposTrechoPesquisado
          })
        }

        if (_gruposTrechoAlternativo.length) {
          gruposTrechoAlternativo.push({
            datetime_ida: groupByDate.datetime_ida,
            grupos: _gruposTrechoAlternativo
          })
        }
      }

      this.gruposTrechoPesquisado = gruposTrechoPesquisado
      this.gruposTrechoAlternativo = gruposTrechoAlternativo

      // Mutations que não dependem de tratamento
      this.gruposByDate = results.groups_by_date
      this.gruposProximoDia = results.groups_next_day
      this.badgesByDate = results.badges_by_date
      this.results = results
      this.setTrecho(results)
      if (results.trecho_alternativo) {
        this.results.trecho_alternativo = results.trecho_alternativo
      }

      // Em alguns casos queremos forçar carregar as conexões
      if (this.hasGruposTrechoPesquisado) {
        const { origem, destino } = $route.params
        const slugConexao = conexoes[`${origem}:${destino}`]
        if (slugConexao) {
          const conexaoStore = useConexaoStore()
          await conexaoStore.load(origem, destino, slugConexao)
        }
      }
      // Aplica Badges Comparativas
      const badgesMap = processBadges(this.results.groups_by_date.flatMap(groupByDate => groupByDate.grupos))
      Object.assign(this.badgesByDate, badgesMap)

      filtroStore.criarCacheFiltros(this.results)

      searchboxStore.$patch({
        fromCity: results.trecho.origem,
        toCity: results.trecho.destino
      })
      this.handleGrupoDestacado()
      this.handleRecommendation()
      this.reordenaGrupos()
      this.loading = false
    },
    cleanSearchStore($route) {
      this.selectedDate = $route.query.ida
      this.idGrupoDestaque = $route.query.idGrupoDestaque
      this.setGrupoIda(null)
    },
    async next(grupo, $route) {
      if (grupo.hot_offer_marketplace) {
        const result = await upsertGrupo(grupo)
        grupo.id = result.id
        if (result?.signed) {
          grupo.signed = result.signed
        }
      }

      this.loading = true
      if (!this.grupoIda) {
        this.setGrupoIda(grupo)
        if (process.client) {
          if (grupo.is_conexao) {
            grupo.conexoes.forEach(conexao => {
              setTrechoSign(conexao.id, conexao.signed, 40 * 60)
            })
          } else {
            setTrechoSign(this.grupoIda.id, this.grupoIda.signed, 40 * 60)
          }
        }
        if (this.isVolta($route)) {
          const dateVolta = $route.query.volta
          this.selectedDate = dateVolta
          this.search({ date: dateVolta }, $route, null, true)
        } else {
          this.completed = true
        }
      } else if (this.grupoIda.id !== grupo.id) {
        this.setGrupoVolta(grupo)
        if (grupo.is_conexao) {
          grupo.conexoes.forEach(conexao => {
            setTrechoSign(conexao.id, conexao.signed, 40 * 60)
          })
        } else {
          setTrechoSign(this.grupoVolta.id, this.grupoVolta.signed, 40 * 60)
        }
      }
      if (this.grupoIda && this.grupoVolta) {
        this.completed = true
      }
    },
    async hit($route) {
      const searchboxStore = useSearchboxStore()
      let origem = searchboxStore.fromCity
      let destino = searchboxStore.toCity

      if (!origem || !destino) {
        return
      }

      if (this.isVolta($route)) {
        [origem, destino] = [destino, origem]
      }

      const params = {
        origem: origem.slug,
        destino: destino.slug
      }
      const data_ida = this.selectedDate
      if (data_ida) {
        params.data_ida = data_ida
      }

      const { users_searching } = await searchHit(params)
      this.usersSearching = users_searching
    },
    async setupActivePromo(code, deviceToken) {
      if (!code) {
        this.activePromo = ACTIVE_PROMO_INITIAL_STATE
        return
      }
      const trecho = this.trecho
      const origemSlug = trecho.origem && trecho.origem.slug
      const destinoSlug = trecho.destino && trecho.destino.slug
      const departureDate = this.selectedDate
      try {
        const data = await searchPromoGroups(
          { code, origemSlug, destinoSlug, departureDate, deviceToken }
        )
        const availableGroups = data.reduce((obj, entry) => {
          obj[entry.id] = entry
          return obj
        }, {})
        this.activePromo = { code, availableGroups, origemSlug, destinoSlug, departureDate }
      } catch (e) {
        this.activePromo = ACTIVE_PROMO_INITIAL_STATE
      }
    },
    async setupTrechoPromo(results) {
      if (this.activePromo?.code || useRevendedorStore().isRevendedorSite) return

      const hasGroups = results.groups_by_date.some(groupsByDate => groupsByDate.grupos.length > 0)
      if (!hasGroups) return

      try {
        const trecho = results.trecho
        const origemSlug = trecho.origem && trecho.origem.slug
        const destinoSlug = trecho.destino && trecho.destino.slug
        const departureDate = this.selectedDate
        const code = 'trecho-promo'

        if (!(origemSlug && destinoSlug)) return

        const { trechos, discount } = await trechoPromo({ origemSlug, destinoSlug })

        if (trechos.length && discount > 0) {
          this.trechosPromocionaisSet = new Set(trechos)

          let availableGroups = {}

          results.groups_by_date.forEach(groupsByDate => {
            groupsByDate.grupos.forEach(grupo => {
              if (this.trechosPromocionaisSet.has(grupo.id)) {
                availableGroups[grupo.id] = {
                  promocao: {
                    promotional_value: grupo.max_split_value - (grupo.max_split_value * discount)
                  }
                }
                grupo.is_promocional = true
              }
            })
          })

          if (this.activePromo?.origemSlug === origemSlug && this.activePromo?.destinoSlug === destinoSlug) {
            availableGroups = { ...this.activePromo.availableGroups, ...availableGroups }
          }

          this.activePromo = { code, availableGroups, origemSlug, destinoSlug, departureDate }
        }
      } catch (error) {
        console.error(error)
      }
    },
    removeActivePromo() {
      this.activePromo = ACTIVE_PROMO_INITIAL_STATE
    },
    reordenaGrupos() {
      this.setNewGroupsOrder(this.gruposTrechoPesquisado)
      this.setNewGroupsOrder(this.gruposTrechoAlternativo)
      this.setNewGroupsOrder(this.gruposFiltrados)
      if (this.gruposProximoDiaFiltrados?.grupos) {
        this.setNewGroupsOrder([this.gruposProximoDiaFiltrados])
      }
    },
    filtraGrupos() {
      const gruposFiltrados = []
      let quantidadeGruposFiltrados = 0
      const filtroStore = useFiltroStore()
      for (const datetimeGrupos of this.gruposTrechoPesquisado) {
        const newDatetimeGrupos = {
          datetime_ida: datetimeGrupos.datetime_ida,
          grupos: []
        }
        for (const grupo of datetimeGrupos.grupos) {
          const visible = filtroStore.isGroupVisible(grupo)
          if (visible) {
            newDatetimeGrupos.grupos.push(grupo)
          }
        }
        if (newDatetimeGrupos.grupos.length) {
          quantidadeGruposFiltrados += newDatetimeGrupos.grupos.length
          gruposFiltrados.push(newDatetimeGrupos)
        }
      }
      this.gruposFiltrados = gruposFiltrados

      // Aplica badges comparativas
      const badgesMap = processBadges(this.gruposFiltrados.flatMap(groupByDate => groupByDate.grupos))
      Object.assign(this.badgesByDate, badgesMap)

      this.quantidadeGruposFiltrados = quantidadeGruposFiltrados
      this.handleGrupoDestacado()
      this.handleRecommendation()
      if (this.gruposProximoDia?.datetime_ida && this.gruposProximoDia?.grupos) {
        const gruposProximoDiaFiltrados = { datetime_ida: this.gruposProximoDia.datetime_ida, grupos: [] }
        for (const grupo of this.gruposProximoDia.grupos) {
          if (filtroStore.isGroupVisible(grupo)) {
            gruposProximoDiaFiltrados.grupos.push(grupo)
          }
        }
        this.gruposProximoDiaFiltrados = gruposProximoDiaFiltrados
        this.quantidadeGruposFiltrados += gruposProximoDiaFiltrados.grupos.length
      }

      this.reordenaGrupos()
    },
    setGrupoIda(grupo) {
      this.grupoIda = grupo

      if (this.grupoIda && this.grupoIda.id) {
        this.grupoIda.badge = this.grupoBadgeMap[this.grupoIda.id]
      }
    },
    setGrupoVolta(grupo) {
      this.grupoVolta = grupo

      if (this.grupoVolta && this.grupoVolta.id) {
        this.grupoVolta.badge = this.grupoBadgeMap[this.grupoVolta.id]
      }
    },
    setNewGroupsOrder(gruposPorDia) {
      const { orderFunction, orderSign } = this.atributosOrdenacao()
      gruposPorDia.forEach(groupItem => {
        groupItem.grupos.sort((grupoA, grupoB) => orderSign * orderFunction(grupoA, grupoB))
      })
    },
    atributosOrdenacao() {
      const orderFunction = ordenacoesMap[this.ordenacao]
      const orderSign = this.ordem === 'crescente' ? 1 : -1
      return { orderFunction, orderSign }
    },
    setTrecho(result) {
      // Hoje realizamos 2 tipos de busca nas telas de search, o /search e o /search/(origem|destino)
      // O primeiro é para trazer os grupos (ou ausencia de), e o segundo traz as sugestões de partidas e chegadas
      // Pode acontecer de buscarmos nos 2 endpoints, ou só em um deles.
      // Esses endpoints trazem os dados da cidade buscada, então decidi optar por centralizar aqui
      // a responsabilidade de informar a cidade de origem e/ou destino buscada, por qualquer um dos endpoints.
      if (result.trecho) {
        const { origem, destino, alerta } = result.trecho
        this.trecho = { origem, destino, alerta }
      }

      if (result.trechoAlternativo) {
        this.trechoAlternativo = result.trechoAlternativo
      }

      if (result.cidadeDestino || result.cidadeOrigem) {
        const origem = result.cidadeOrigem
        const destino = result.cidadeDestino
        const alerta = this.trecho.alerta
        this.trecho = { origem, destino, alerta }
      }
    },
    setGrupoBadge(grupo, badge) {
      this.grupoBadgeMap[grupo.id] = badge.toLowerCase().split(' ').join('-')
    },
    sortTrechosFromCidadeOrigemByDiscount() {
      const saindoDe = this.suggestions?.cidadeOrigem?.saindo_de || []
      return saindoDe.sort((a, b) => (b.max_discount) - (a.max_discount))
    },
    handleGrupoDestacado() {
      // Seta o estado grupoDestaque com o grupo com o mesmo id que foi passado pela url do google transit
      this.grupoDestaque = null
      if (!this.selectedDate) return

      let grupos = this.gruposFiltrados?.find(group => group.datetime_ida === this.selectedDate)?.grupos ?? []

      if (!grupos.length) {
        grupos = this.results?.groups_by_date?.find(({ datetime_ida }) => datetime_ida === this.selectedDate)?.grupos ?? []
      }

      if (!grupos) return

      let gruposFiltrados = [{ datetime_ida: this.selectedDate, grupos: [] }]

      grupos.forEach(group => {
        if (this.idGrupoDestaque === group.id) {
          this.grupoDestaque = group
          return
        }

        gruposFiltrados[0].grupos.push(group)
      })

      if (this.grupoDestaque) {
        // retira os grupos destacados da listagem normal para não ficar duplicado
        this.gruposFiltrados = gruposFiltrados
        const filtroStore = useFiltroStore()
        filtroStore.quantidadeFiltrosAplicados += 1
      }
    },
    handleRecommendation() {
      const departureDate = this.selectedDate
      if (!departureDate) {
        this.gruposRecomendadosBased = []
        return
      }

      let results = this.gruposFiltrados.find(group => group.datetime_ida === departureDate)?.grupos ?? []

      if (!results.length) {
        results = this.results?.groups_by_date?.find(({ datetime_ida }) => datetime_ida === departureDate)?.grupos ?? []
      }

      let gruposRecomendados = results.filter(group => group.ranking?.searchrank_api).sort((a, b) => b.ranking.searchrank_api - a.ranking.searchrank_api)

      if (!gruposRecomendados.length) {
        this.gruposRecomendadosBased = []
        return
      }
      const maxRecomendacoes = 3

      let umTerco = parseInt(results.length / 3) <= 1 ? 1 : parseInt(results.length / 3)
      if (umTerco > gruposRecomendados.length) {
        umTerco = gruposRecomendados.length
      }
      const corte = Math.min(umTerco, maxRecomendacoes)

      const recomendadosAgora = gruposRecomendados.slice(0, corte)
      this.idsGruposRecomendados = this.idsGruposRecomendados.concat(recomendadosAgora.map(grupo => grupo.id))

      this.gruposRecomendadosBased = boostPositionJK(recomendadosAgora)
      this.gruposRecomendadosBased = boostBuserPremium(recomendadosAgora, results)
    }
  }
})

function cacheKeyFromState(state, { origemSlug, destinoSlug, companyId }) {
  const searching = state.grupoIda === null ? 'ida' : 'volta'
  const date = state.selectedDate || state.departureDate
  const company = companyId ? `_company_${companyId}` : ''

  return `${date}_${searching}_${origemSlug}_${destinoSlug}${company}`
}

const ordenacoesMap = {
  preco: (grupoA, grupoB) => grupoA.max_split_value - grupoB.max_split_value,
  duracao: (grupoA, grupoB) => {
    const duracaoA = dayjs(grupoA.chegada_ida).diff(dayjs(grupoA.datetime_ida))
    const duracaoB = dayjs(grupoB.chegada_ida).diff(dayjs(grupoB.datetime_ida))
    return duracaoA - duracaoB
  },
  hsaida: (grupoA, grupoB) => {
    return dayjs(grupoA.datetime_ida).diff(dayjs(grupoB.datetime_ida)) || grupoA.max_split_value - grupoB.max_split_value
  },
  searchrank_api: (grupoA, grupoB) => {
    const rankGrupoA = grupoA.ranking?.searchrank_api || Number.MIN_SAFE_INTEGER
    const rankGrupoB = grupoB.ranking?.searchrank_api || Number.MIN_SAFE_INTEGER
    if (!rankGrupoA) return 1
    if (!rankGrupoB) return -1
    return rankGrupoA - rankGrupoB
  }
}

function filterGroupsByCompany(results, company) {
  const filterGroups = grupo => {
    return String(grupo.company_id) === company
  }

  results.groups_by_date.forEach(groupObj => {
    groupObj.grupos = groupObj.grupos.filter(filterGroups)
  })
  results.groups_next_day.grupos = results.groups_next_day.grupos.filter(filterGroups)
}

function concatConexoesAndGroupsNormalSearch(resultConexoes, results) {
  results.gruposRecomendadosConexao = resultConexoes.grupos_recomendados

  if (!hasResults(resultConexoes)) {
    return
  }

  if (!hasResults(results)) {
    results.groups_by_date = resultConexoes.groups_by_date
    results.locais = resultConexoes.locais
    results.locais_de_embarque = resultConexoes.locais_de_embarque
    results.locais_de_desembarque = resultConexoes.locais_de_desembarque
    return
  }

  const mapConexoes = resultConexoes.groups_by_date.reduce((acc, groupByDate) => {
    const datetimeIda = groupByDate.datetime_ida
    acc[datetimeIda] = groupByDate.grupos
    return acc
  }, {})

  results.groups_by_date.forEach(groupByDate => {
    const datetimeIda = groupByDate.datetime_ida
    const grupos = mapConexoes[datetimeIda]
    if (grupos) {
      groupByDate.grupos = groupByDate.grupos.concat(grupos)
      delete mapConexoes[datetimeIda]
    }
  })

  results.locais = { ...resultConexoes.locais, ...results.locais }
  results.locais_de_embarque = results.locais_de_embarque.concat(resultConexoes.locais_de_embarque.filter(item => !results.locais_de_embarque.includes(item)))
  results.locais_de_desembarque = results.locais_de_desembarque.concat(resultConexoes.locais_de_desembarque.filter(item => !results.locais_de_desembarque.includes(item)))

  if (Object.keys(mapConexoes).length) {
    results.groups_by_date = results.groups_by_date.concat(Object.entries(mapConexoes).map(([datetimeIda, grupos]) => {
      return { datetime_ida: datetimeIda, grupos }
    }))
  }
}

function concatMarketplaceAndGroupsNormalSearch(resultMarketplace, resultBuser, departureDate) {
  let itensMarketplaceBuserdjango = []

  if (resultBuser.groups_by_date?.[0]?.datetime_ida === departureDate) {
    itensMarketplaceBuserdjango = resultBuser.groups_by_date[0].grupos
  }

  const match = compareSearchItems(resultMarketplace.items, itensMarketplaceBuserdjango)

  for (const { buser, marketplace } of match) {
    if (marketplace && !buser) {
      resultBuser.groups_by_date[0].grupos.push({ ...marketplace, hot_offer_marketplace: true })

      const origem = marketplace.itinerario[0].local
      if (!resultBuser.locais?.[origem.id]) {
        resultBuser.locais[origem.id] = origem
        resultBuser.locais_de_embarque.push(origem.id)
      }

      const destino = marketplace.itinerario[1].local
      if (!resultBuser.locais?.[destino.id]) {
        resultBuser.locais[destino.id] = destino
        resultBuser.locais_de_desembarque.push(destino.id)
      }
    }

    if (marketplace && buser) {
      const desatualizado = !marketplace.extra_mkp_last_synced_at || !buser.extra_mkp_last_synced_at || new Date(marketplace.extra_mkp_last_synced_at) > new Date(buser.extra_mkp_last_synced_at)
      const vagas_diferentes = marketplace.vagas !== buser.vagas && buser.vagas < 13 // Existe uma regra que trunca o número de vagas em 13 na serialização do backend
      const diferente = vagas_diferentes || Math.abs(marketplace.max_split_value - buser.max_split_value) > 0.1

      if (!desatualizado || !diferente) continue

      buser.max_split_value = marketplace.max_split_value
      buser.vagas = marketplace.vagas
      buser.extra_mkp_last_synced_at = marketplace.extra_mkp_last_synced_at
      buser.hot_offer_marketplace = true
    }
  }
  resultBuser.groups_by_date[0].grupos = resultBuser.groups_by_date[0].grupos.filter(item => item.vagas > 0)
}

function hasResults(results) {
  return results?.groups_by_date?.length > 0
}

function boostPositionJK(grupos) {
  grupos.sort((a, b) => {
    return a.company_id === JK_ID && b.modelo_venda !== 'buser' ? -1 : 1
  })
  return grupos
}

function boostBuserPremium(grupos, allGrupos) {
  if (grupos?.length < 2) return grupos

  const grupoPremium = allGrupos.find(grupo => isGroupBuserPremium(grupo))

  if (!grupoPremium) return grupos
  grupos[1] = grupoPremium
  return grupos
}
