import { create<PERSON><PERSON>, setActive<PERSON><PERSON> } from 'pinia'
import { useSearchStore } from './search.js'

describe('Store: search', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  describe('actions', () => {
    describe('handleRecommendation', () => {
      it('handleRecommendation should not set gruposRecomendadosBased', () => {
        const searchStore = useSearchStore()
        searchStore.selectedDate = null
        searchStore.handleRecommendation()
        expect(searchStore.gruposRecomendadosBased.length).toBe(0)
      })

      it('handleRecommendation should not set gruposRecomendadosBased without results 10+', () => {
        const searchStore = useSearchStore()
        searchStore.selectedDate = '2024-01-01'
        searchStore.results.groups_by_date = [
          {
            datetime_ida: '2024-01-01',
            grupos: [
              {}, {}]
          },
          {
            datetime_ida: '2024-01-02',
            grupos: []
          }

        ]
        searchStore.handleRecommendation()
        expect(searchStore.gruposRecomendadosBased.length).toBe(0)
      })
      it('handleRecommendation should not set gruposRecomendadosBased without recommendations', () => {
        const searchStore = useSearchStore()
        searchStore.selectedDate = '2024-01-01'
        searchStore.results.groups_by_date = [
          {
            datetime_ida: '2024-01-01',
            grupos: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}] // somente para ter 10+ resultados
          },
          {
            datetime_ida: '2024-01-02',
            grupos: []
          }

        ]
        searchStore.handleRecommendation()
        expect(searchStore.gruposRecomendadosBased.length).toBe(0)
      })

      it('handleRecommendation should set gruposRecomendadosBased', () => {
        const searchStore = useSearchStore()
        searchStore.selectedDate = '2024-01-01'
        searchStore.results.groups_by_date = [
          {
            datetime_ida: '2024-01-01',
            grupos: [
              { ranking: { searchrank_api: 1 } },
              { ranking: { searchrank_api: 2 } },
              { ranking: { searchrank_api: 1 } },
              { ranking: { searchrank_api: 2 } },
              { ranking: { searchrank_api: 1 } },
              { ranking: { searchrank_api: 2 } },
              { ranking: { searchrank_api: 2 } },
              { ranking: { searchrank_api: 1 } }
            ]
          },
          {
            datetime_ida: '2024-01-02',
            grupos: []
          }

        ]
        searchStore.handleRecommendation()
        expect(searchStore.gruposRecomendadosBased.length).toBe(2)
      })
      it('handleRecommendation boosts JK position', () => {
        const searchStore = useSearchStore()
        searchStore.selectedDate = '2024-01-01'
        searchStore.results.groups_by_date = [
          {
            datetime_ida: '2024-01-01',
            grupos: [
              { ranking: { searchrank_api: 1 } },
              { ranking: { searchrank_api: 2 } },
              { ranking: { searchrank_api: 3 } },
              { ranking: { searchrank_api: 4 } },
              { ranking: { searchrank_api: 5 } },
              { ranking: { searchrank_api: 6 } },
              { ranking: { searchrank_api: 6 } },
              { ranking: { searchrank_api: 6 } },
              { ranking: { searchrank_api: 6 } },
              { ranking: { searchrank_api: 7 }, company_id: 181 },
              { ranking: { searchrank_api: 8 } }
            ]
          },
          {
            datetime_ida: '2024-01-02',
            grupos: []
          }

        ]
        searchStore.handleRecommendation()
        expect(searchStore.gruposRecomendadosBased[0].company_id).toEqual(181)
      })
      it('handleRecommendation boosts buser premium position', () => {
        const searchStore = useSearchStore()
        searchStore.selectedDate = '2024-01-01'
        searchStore.results.groups_by_date = [
          {
            datetime_ida: '2024-01-01',
            grupos: [
              { ranking: { searchrank_api: 1 } },
              { ranking: { searchrank_api: 2 } },
              { ranking: { searchrank_api: 3 } },
              { ranking: { searchrank_api: 4 } },
              { ranking: { searchrank_api: 5 } },
              { ranking: { searchrank_api: 6 } },
              { ranking: { searchrank_api: 6 } },
              { ranking: { searchrank_api: 6 } },
              { ranking: { searchrank_api: 6 } },
              { ranking: { searchrank_api: 1 }, company_id: 168, modelo_venda: 'buser', tipo_assento: 'cama' },
              { ranking: { searchrank_api: 8 } }
            ]
          },
          {
            datetime_ida: '2024-01-02',
            grupos: []
          }

        ]
        searchStore.handleRecommendation()
        expect(searchStore.gruposRecomendadosBased[1].company_id).toEqual(168)
      })
    })
  })

  describe('ordenacao', () => {
    it('hsaida must sort first by datetime_ida and then by max_split_value', () => {
      const searchStore = useSearchStore()
      searchStore.ordenacao = 'hsaida'
      searchStore.ordem = 'crescente'

      const gruposPorDia = [{
        datetime_ida: '2024-08-01',
        grupos: [
          { id: '6', datetime_ida: '2024-08-01T03:00:00-03:00', max_split_value: 1 },
          { id: '5', datetime_ida: '2024-08-01T02:00:00-03:00', max_split_value: 10 },
          { id: '2', datetime_ida: '2024-08-01T01:00:00-03:00', max_split_value: 2 },
          { id: '4', datetime_ida: '2024-08-01T02:00:00-03:00', max_split_value: 1 },
          { id: '3', datetime_ida: '2024-08-01T01:00:00-03:00', max_split_value: 10 },
          { id: '1', datetime_ida: '2024-08-01T01:00:00-03:00', max_split_value: 1 }
        ]
      }]

      searchStore.setNewGroupsOrder(gruposPorDia)
      const resultIds = gruposPorDia[0].grupos.map(grupo => grupo.id)

      const expectedIdOrder = ['1', '2', '3', '4', '5', '6']
      expect(resultIds).toStrictEqual(expectedIdOrder)
    })
  })
})
