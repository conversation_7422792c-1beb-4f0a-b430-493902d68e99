import imageBackgroundPopMobile from '~/assets/images/festival/background-pop-festival-mobile.webp'
import imageBackgroundPop from '~/assets/images/festival/background-pop-festival.webp'
import imageBackgroundRock from '~/assets/images/landing-page/pessoas-em-show-croped.webp'
type HeaderTexts = {
  title: string,
  subtitle: string,
  auxTitle: string,
}

export type Festival = {
  imageBackground: string,
  imageBackgroundMobile: string,
  headerTexts: HeaderTexts
}

export const festivais: Record<string, Festival> = {
  pop: {
    imageBackground: imageBackgroundPop,
    imageBackgroundMobile: imageBackgroundPopMobile,
    headerTexts: {
      auxTitle: 'Mother Monster no Rio!',
      title: 'Brasil não fique mais devasted, tá na hora do show!',
      subtitle: 'Reserve sua viagem com segurança, praticidade e bora aproveitar'
    }
  },
  rock: {
    imageBackground: imageBackgroundRock,
    imageBackgroundMobile: imageBackgroundRock,
    headerTexts: {
      auxTitle: 'Vai viajar pro festival?',
      title: 'Esco<PERSON>ha a trilha sonora, o caminho é com a gente.',
      subtitle: 'Com a Buser, o show começa na estrada. Aqui você viaja com mais conforto, preço justo e suporte 24h. Bora embarcar?'
    }
  }
}
