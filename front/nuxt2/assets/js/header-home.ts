// import headerDefaultImg from '~/assets/images/home/<USER>/HEADER_DEFAULT.webp'
// import headerMGImg from '~/assets/images/home/<USER>/HEADER_MG.webp'
// import headerRJImg from '~/assets/images/home/<USER>/HEADER_RJ.webp'
// import headerSPImg from '~/assets/images/home/<USER>/HEADER_SP.webp'
import headerDefaultImg from '~/assets/images/home/<USER>/PRODUTO_HEADER_INVERNO_GENERICO.webp'
import headerMGImg from '~/assets/images/home/<USER>/PRODUTO_HEADER_INVERNO_MG.webp'
import headerRJImg from '~/assets/images/home/<USER>/PRODUTO_HEADER_INVERNO_RJ.webp'
import headerSPImg from '~/assets/images/home/<USER>/PRODUTO_HEADER_INVERNO_SP.webp'
import bannerBF from '~/assets/images/home/<USER>/bfBanner.webp'
import preBlackfriday from '~/assets/images/home/<USER>/pre-blackfriday-header.webp'

export type ObjImage = {
  srcOriginal: string;
  width: string;
}

export const PreBlackfridayBanner: ObjImage = {
  srcOriginal: preBlackfriday,
  width: '2400'
}

export const BlackfridayBanner: ObjImage = {
  srcOriginal: bannerBF,
  width: '2400'
}

export const EmptyMobile: ObjImage = {
  srcOriginal: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=', // 1x1 transparent pixel
  width: '600'
}

export const HeaderDefault: ObjImage = { srcOriginal: headerDefaultImg, width: '2400' }

export const HeaderByRegionMap: Record<string, ObjImage> = {
  MG: { srcOriginal: headerMGImg, width: '2400' },
  RJ: { srcOriginal: headerRJImg, width: '2400' },
  SP: { srcOriginal: headerSPImg, width: '2400' }
}

export const OptionsHeaderByRegion: string[] = Object.keys(HeaderByRegionMap)
