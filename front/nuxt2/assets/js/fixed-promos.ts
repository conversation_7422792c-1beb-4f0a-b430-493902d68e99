// import paginaPromocoesImage from '~/assets/images/home/<USER>/BANNER_PROMOCOES.webp'
// import paginaDescubraImage from '~/assets/images/home/<USER>/PRODUTO_BANNER_DESCUBRA.webp'
// import bannerBuserMais from '~/assets/images/home/<USER>/campanha/BANNER_BUSERMAIS.webp'
// import bannerPrimeira30 from '~/assets/images/home/<USER>/campanha/BANNER_PRIMEIRA_30_2.webp'
import bannerCloseFriends from '~/assets/images/home/<USER>/BANNER_CLOSE_FRIENDS.webp'
import bannerCupom20 from '~/assets/images/home/<USER>/BANNER_CUPOM_20.webp'
import bannerValeBuser from '~/assets/images/home/<USER>/BANNER_VALE_BUSER.webp'

interface Promo {
  code: string // Por padrão o link do cupom será /promo/{code}
  promo_image: string
  description: string
}

interface FixedPromo extends Promo {
  path?: string // O link pode ser customizado aqui
  pathsAB?: string[] // Caso queira fazer um teste AB com links diferentes, usa isso daqui
  target?: string
  options?: {
    loggedIn?: boolean // true = só logado, false = só deslogado, undefined = todos
    showEsquentaBF?: boolean
    showBF?: boolean
    hideApp?: boolean
    targetPage?: string,
  }
}

// const DESCUBRA: FixedPromo = {
//   code: 'DESCUBRA',
//   promo_image: paginaDescubraImage,
//   description: 'Descubra ofertas',
//   path: '/onibus/de/descubra'
// }

// const VALE_BUSER: FixedPromo = {
//   code: 'VALE_BUSER',
//   promo_image: paginaValeBuserImage,
//   description: 'Valer Buser.',
//   path: 'https://vale.buser.com.br',
//   target: '_blank'
// }

// const PROMOCOES: FixedPromo = {
//   code: 'PROMOCOES',
//   promo_image: paginaPromocoesImage,
//   description: 'Viagens em promoção.',
//   path: '/onibus/promo/de'
// }

// const PRIMEIRA_30: FixedPromo = {
//   code: 'NAOVIAJA',
//   promo_image: bannerPrimeira30,
//   description: 'Primeira viagem com 30% de desconto.'
// }

const CUPOM_20: FixedPromo = {
  code: 'CUPOM_20',
  promo_image: bannerCupom20,
  description: 'Use FERIASDEINVERNO e bora se jogar na estrada!',
  path: '/promo/FERIASDEINVERNO'
}

const VALE_BUSER: FixedPromo = {
  code: 'VALE_BUSER',
  promo_image: bannerValeBuser,
  description: 'Presenteie com vale viagem!',
  path: 'https://vale.buser.com.br',
  target: '_blank'
}

const CLOSE_FRIENDS: FixedPromo = {
  code: 'CLOSE_FRIENDS',
  promo_image: bannerCloseFriends,
  description: 'Chega de vou ver e te aviso. Indique a galera e todo mundo ganha dinheiro ou desconto.',
  path: '/perfil/close-friends'
}

const FIXED_PROMOS: FixedPromo[] = [
  CUPOM_20,
  VALE_BUSER,
  CLOSE_FRIENDS
]

export default () => FIXED_PROMOS
