import { isValid as isValidCNPJ } from '@fnando/cnpj'
import { isValid as isValidCPF } from '@fnando/cpf'
import dayjs from 'dayjs'
import {
  VALID_AREA_CODES,
  isValidEmail,
  isValidMobilePhoneBr,
  isInternationalPhone,
  isValidCurrency,
  moedaToFloat,
  isValidPassword
} from '~/helpers/forms.js'

export function required(value) {
  return !!value || 'Campo obrigatório'
}

export function notBlank(value) {
  return (value && !!value.trim()) || 'Digite caracteres válidos'
}

export function minLen(length) {
  return value => {
    if (value === undefined || value === null) {
      return true
    }

    value = value.toString()

    return value.length >= length || `Digite pelo menos ${length} caracteres`
  }
}

export function maxLen(length) {
  return value => {
    if (value === undefined || value === null) {
      return true
    }

    value = value.toString()

    return value.length <= length || `Máximo de ${length} caracteres`
  }
}

export function minBrDate(minDate, msg) {
  return value => {
    if (_ddmmyyyy(value) !== true) {
      return _ddmmyyyy(value)
    }

    const valid = dayjs(value, 'DDMMYYYY') >= minDate

    return valid || msg
  }
}

export function maxBrDate(maxDate, msg = 'Digite uma data válida') {
  return value => {
    if (_ddmmyyyy(value) !== true) {
      return _ddmmyyyy(value)
    }

    const valid = dayjs(value, 'DDMMYYYY') <= maxDate

    return valid || msg
  }
}

export function email(value, notBuser = false, isNewEmail = false) {
  if (value === undefined || value === null || value === '') {
    return true
  }

  const valid = isValidEmail(value.trim(), isNewEmail)

  if (notBuser === true) {
    const domain = value.split('@')[1]

    return (valid && !domain.startsWith('buser.')) || 'Digite um e-mail válido.'
  }

  return valid || 'Digite um e-mail válido.'
}

export function nomeCompleto(nome) {
  return nome?.split(' ')?.length >= 2 || 'Digite o nome Completo'
}

export function ddmmyyyy(value) {
  return _ddmmyyyy(value)
}

export function phone(value) {
  if (value === undefined || value === null) {
    return true
  }

  const valid = value.length >= 10 && isValidMobilePhoneBr(value)

  return valid || 'Digite um telefone válido.'
}

export function phoneMobileBR(phone) {
  if (!phone) return true
  const digits = phone.replace(/[^\d]+/, '') || ''
  if (digits.startsWith('0')) return 'Não coloque zero no início.'
  if (digits.length >= 2 && !VALID_AREA_CODES.includes(digits.substring(0, 2))) return `${digits.substring(0, 2)} não é um código de área válido`
  if (digits.length >= 3 && digits.substring(2, 3) !== '9') return 'Informe um número de celular válido.'

  return isValidMobilePhoneBr(phone) || 'Digite um telefone de celular válido com 11 dígitos.'
}

export function cpfOpcional(value) {
  if (value === '' || value === undefined || value === null) {
    return true
  }
  return isValidCPF(value) || 'CPF inválido'
}

export function cpf(value) {
  if (value === undefined || value === null) {
    return true
  }

  return isValidCPF(value) || 'CPF inválido'
}

export function cnpj(value) {
  if (value === undefined || value === null) {
    return true
  }
  return isValidCNPJ(value) || 'CNPJ inválido'
}

export function emailOrCPFOrBrazilianPhone(value) {
  if (isValidEmail(value)) {
    return true
  }
  if (isValidCPF(value)) {
    return true
  }
  if (isValidMobilePhoneBr(value)) {
    return true
  }
  if (isInternationalPhone(value)) {
    return 'Digite um telefone do Brasil sem o código do país'
  }

  return 'Digite um email, telefone ou CPF válido'
}

export function emailOrBrazilianPhone(value) {
  if (isValidEmail(value)) {
    return true
  }
  if (isValidMobilePhoneBr(value)) {
    return true
  }
  if (isInternationalPhone(value)) {
    return 'Digite um telefone do Brasil sem o código do país'
  }

  return 'Digite um email ou telefone válido'
}

export function password(value) {
  const message = 'A senha deve conter pelo menos 8 caracteres'
  const valid = isValidPassword(value)
  return valid || message
}

export function between(start, end, isMoney) {
  return value => {
    if (value === undefined || value === null) {
      return true
    }

    let startFormatted = start
    let endFormatted = end

    if (isMoney) {
      startFormatted = start.toFixed(2).toString().replace('.', ',')
      endFormatted = end.toFixed(2).toString().replace('.', ',')
    }

    const message = `Valor deve estar entre ${startFormatted} e ${endFormatted}`
    if (isValidCurrency(value)) {
      value = moedaToFloat(value)
    }
    const valid = value >= start && value <= end
    return valid || message
  }
}

export function money(value) {
  const message = 'O valor inserido é inválido. Exemplo de entrada válida: 1.500,90'
  const valid = isValidCurrency(value)
  return valid || message
}

export function greaterThan(vmin) {
  const message = `Valor deve ser maior que ${vmin}`
  return value => {
    if (value === undefined || value === null) {
      return message
    }
    value = value.toString()
    if (isValidCurrency(value)) {
      value = moedaToFloat(value)
    } else {
      value = value.replace(',', '.')
    }
    const valid = value > vmin
    return valid || message
  }
}

export function alphanumeric(value) {
  if (value === undefined || value === null) {
    return true
  }
  return value.match(/^[a-z0-9]+$/i) !== null || 'Apenas letras e números'
}

export function capitalLettersAndNumbers(value) {
  if (value === undefined || value === null) {
    return true
  }
  return value.match(/^[A-Z0-9]+$/) !== null || 'Apenas letras maiúsculas e números'
}

export function number(value) {
  if (value === undefined || value === null || value === '') {
    return true
  }
  return value.match(/^[0-9]+$/i) !== null || 'Apenas números'
}

export function notNumber(value) {
  if (value === undefined || value === null) {
    return true
  }
  return value.match(/^[0-9]+$/i) === null || 'Não pode conter apenas números'
}

export function notNumberAtAll(value) {
  if (value === undefined || value === null) {
    return true
  }
  return /^([^0-9]*)$/.test(value) || 'Não pode conter números'
}

export function exactLength(len) {
  return value => {
    if (value === undefined || value === null) {
      return true
    }
    value = value.toString()
    return value.length === len || `Campo deve ter exatamente ${len} caracteres`
  }
}

export function creditCard(value) {
  const ACCEPTED_CREDIT_CARDS = {
    visa: /^4[0-9]{12}(?:[0-9]{3})?$/,
    master: /^5[1-5][0-9]{14}$|^2(?:2(?:2[1-9]|[3-9][0-9])|[3-6][0-9][0-9]|7(?:[01][0-9]|20))[0-9]{12}$/,
    amex: /^3[47][0-9]{13}$/,
    jcb: /^(?:2131|1800|35[0-9]{3})[0-9]{11}$/,
    hipercard: /^606282|^3841(?:[0|4|6]{1})0/,
    elo: /^4011(78|79)|^43(1274|8935)|^45(1416|7393|763(1|2))|^50(4175|6699|67[0-6][0-9]|677[0-8]|9[0-8][0-9]{2}|99[0-8][0-9]|999[0-9])|^627780|^63(6297|6368|6369)|^65(0(0(3([1-3]|[5-9])|4([0-9])|5[0-1])|4(0[5-9]|[1-3][0-9]|8[5-9]|9[0-9])|5([0-2][0-9]|3[0-8]|4[1-9]|[5-8][0-9]|9[0-8])|7(0[0-9]|1[0-8]|2[0-7])|9(0[1-9]|[1-6][0-9]|7[0-8]))|16(5[2-9]|[6-7][0-9])|50(0[0-9]|1[0-9]|2[1-9]|[3-4][0-9]|5[0-8]))/
  }

  function _validateCreditCard(value) {
    let count = 0
    let shouldDouble = false

    for (let index = value.length - 1; index >= 0; index--) {
      let digit = Number(value[index])

      if (shouldDouble) {
        digit *= 2

        if (digit >= 10) {
          digit -= 9
        }
      }

      count += digit
      shouldDouble = !shouldDouble
    }

    return (count % 10) === 0
  }

  if (value === undefined || value === null) {
    return ''
  }

  const isValidCreditCard = _validateCreditCard(value)

  if (!isValidCreditCard) {
    return false
  }

  return Object.keys(ACCEPTED_CREDIT_CARDS).find(key => ACCEPTED_CREDIT_CARDS[key].test(value))
}

export function cep(value) {
  if (value === undefined || value === null) {
    return true
  }
  return (value.match(/^[0-9]+$/i) !== null && value.length === 8) || 'Digite um CEP válido'
}

export function placa(value) {
  if (value === undefined || value === null) {
    return true
  }
  return (value.match(/^[a-zA-Z]{3}[0-9]{4}$/i) !== null && value.length === 7) || 'Digite uma placa válida'
}

export function mmaa(value) {
  function _mmaanow() {
    const s = new Date().toISOString()
    const sy = s.substring(2, 4)
    const sm = s.substring(5, 7)
    return `${sy}${sm}`
  }

  if (value === undefined || value === null) {
    return true
  }
  const errmsg = 'Digite a validade no formato MM/AA'
  if (value.length !== 4 || value.match(/^[0-9]+$/i) === null) {
    return errmsg
  }
  const sm = value.substring(0, 2)
  const sy = value.substring(2, 4)
  const m = parseInt(sm)
  const y = parseInt(sy)
  if (m <= 0 || m > 12) {
    return 'O mês deve estar entre 01 e 12'
  }
  if (y > 50) {
    return errmsg
  }
  if (`${sy}${sm}` < _mmaanow()) {
    return 'Validade expirada'
  }
  return true
}

export function matchText(pattern, exact = false) {
  return value => {
    if (value === undefined || value === null) {
      return true
    }
    const error = `Digite "${pattern}" para prosseguir`
    return exact
      ? value === pattern || error
      : value.toLowerCase() === pattern || error
  }
}

export function maxSize(sizeInMB) {
  return file => {
    if (!file || (Array.isArray(file) && !file.length)) {
      return true
    }
    return file.size < sizeInMB * 1024 * 1024 || `O tamanho máximo permitido do anexo é de ${sizeInMB}MB.`
  }
}

export function protocoloAtendimentoRevendedor(value) {
  required(value)

  exactLength(11)

  const regex = /^\d{8}-\d{2}$/
  if (!regex.test(value)) {
    return 'Formato inválido. Use o formato: 12345678-90'
  }

  return true
}

function _ddmmyyyy(value) {
  if (value === undefined || value === null) {
    return true
  }

  value = value.toString()

  const message = 'Digite uma data válida'
  const pattern = /^\s*(3[01]|[12][0-9]|0?[1-9])(1[012]|0?[1-9])((?:19|20)\d{2})\s*$/

  if (value.length !== 8 || !pattern.test(value)) {
    return message
  }

  const [d, m, y] = [value.substring(0, 2), value.substring(2, 4), value.substring(4, 8)]

  // Fevereiro só vai até dia 29, coisa
  if (Number(m) === 2 && d >= 30) {
    return message
  }
  if (d > 31 || m > 12 || y > 2100) {
    return message
  }

  return true
}

export function creditCardHolderName(nome) {
  if (nome !== nome.trim()) {
    return 'Não pode conter espaço no inicio ou final.'
  }
  // A regex para validar o nome
  const regexNome = /^[A-Za-zÀ-ÖØ-öø-ÿ]+\.?(?: [A-Za-zÀ-ÖØ-öø-ÿ]+\.?)*$/
  // Retorna true se a string corresponder à regex, e false caso contrário
  return regexNome.test(nome) || 'Nome inválido.'
}
