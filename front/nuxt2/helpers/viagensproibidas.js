/**
 * TODO Passar essas lógicas de proibição pro back, a fim de já devolver pro
 * front o necessário ou nem retornar nada relacionado a locais com proibições
 */

import { removeCidadeFromCidadeSlug, removeUfCidadeSlug } from '~/helpers/stringshelper.js'

export const VIAGENS_PROIBIDAS = {
  df: {
    modelo_venda: { // AC-1199 - https://app.clickup.com/t/30953227/AC-1199 (Junho 2022)
      buser: { qualquer_cidade: true },
      marketplace: null,
      hibrido: null
    }
  },
  sc: { // AC-1411 - https://app.clickup.com/t/30953227/AC-1411 (Setembro 2022)
    modelo_venda: {
      buser: { interestadual: true },
      hibrido: null,
      marketplace: null
    }
  },
  ce: {
    modelo_venda: { // AC-1232 - https://app.clickup.com/t/30953227/AC-1232 (Junho 2022)
      buser: { qualquer_cidade: true },
      hibrido: null,
      marketplace: null
    }
  },
  se: {
    modelo_venda: { // AC-1232 - https://app.clickup.com/t/30953227/AC-1232 (Junho 2022)
      buser: { qualquer_cidade: true },
      hibrido: null,
      marketplace: null
    }
  },
  ba: {
    modelo_venda: { // AC-1332 - https://app.clickup.com/t/30953227/AC-1332 (Agosto 2022)
      buser: {
        salvador: {
          para: {
            'maceio-al': true,
            'joao-pessoa-pb': true
          }
        }
      },
      hibrido: null,
      marketplace: null
    }
  },
  al: {
    modelo_venda: { // AC-1332 - https://app.clickup.com/t/30953227/AC-1332 (Agosto 2022)
      buser: {
        maceio: {
          para: {
            'salvador-ba': true // Volta para Salvador
          }
        }
      },
      hibrido: null,
      marketplace: null
    }
  },
  pb: {
    modelo_venda: { // AC-1332 - https://app.clickup.com/t/30953227/AC-1332 (Agosto 2022)
      buser: {
        'joao-pessoa': {
          para: {
            'salvador-ba': true // Volta para Salvador
          }
        }
      },
      hibrido: null,
      marketplace: null
    }
  }
}

export default {
  checkIsViagemWithOrigemOrDestinoProibidos(origemSlug, destinoSlug, viagensProibidasMap = VIAGENS_PROIBIDAS) {
    const origemUf = removeCidadeFromCidadeSlug(origemSlug)
    const destinoUf = removeCidadeFromCidadeSlug(destinoSlug)

    if (!origemSlug) {
      return !!viagensProibidasMap?.[destinoUf]?.interestadual?.destino
    }

    if (!destinoSlug) {
      return !!viagensProibidasMap?.[origemUf]?.interestadual?.origem
    }

    if (origemUf === destinoUf) {
      return !!viagensProibidasMap?.[origemUf]?.intermunicipal
    }

    return !!(viagensProibidasMap?.[origemUf]?.interestadual?.origem || viagensProibidasMap?.[destinoUf]?.interestadual?.destino)
  },
  checkIsViagemWithModeloVendaProibido(grupo, viagensProibidasMap = VIAGENS_PROIBIDAS) {
    const origemSlug = grupo?.origem?.slug
    const destinoSlug = grupo?.destino?.slug
    const origemUf = removeCidadeFromCidadeSlug(origemSlug)
    const destinoUf = removeCidadeFromCidadeSlug(destinoSlug)
    const origemCidade = removeUfCidadeSlug(origemSlug)

    const isQualquerCidade = !!viagensProibidasMap?.[origemUf]?.modelo_venda?.[grupo?.modelo_venda]?.qualquer_cidade ||
                             !!viagensProibidasMap?.[destinoUf]?.modelo_venda?.[grupo?.modelo_venda]?.qualquer_cidade

    const isInterestadual = (origemUf !== destinoUf) &&
                            (!!viagensProibidasMap?.[origemUf]?.modelo_venda?.[grupo?.modelo_venda]?.interestadual ||
                             !!viagensProibidasMap?.[destinoUf]?.modelo_venda?.[grupo?.modelo_venda]?.interestadual)

    const isTrechoEspecifico = !!viagensProibidasMap?.[origemUf]?.modelo_venda?.[grupo?.modelo_venda]?.[origemCidade]?.para?.[destinoSlug]

    if (isQualquerCidade) return true

    if (isInterestadual) return true

    return isTrechoEspecifico
  },
  checkHasProibicaoOnModeloVenda(origemSlug, destinoSlug, viagensProibidasMap = VIAGENS_PROIBIDAS) {
    const origemUf = removeCidadeFromCidadeSlug(origemSlug)
    const destinoUf = removeCidadeFromCidadeSlug(destinoSlug)

    const hasProibicaoOnModeloVenda = !!viagensProibidasMap?.[origemUf]?.modelo_venda ||
                                      !!viagensProibidasMap?.[destinoUf]?.modelo_venda

    return hasProibicaoOnModeloVenda
  },
  // Assume que se o fretamento é proibido na UF, devemos restringir infos de
  // grupos híbridos para esconder a logo da empresa dona do ônibus, e mostrar
  // no lugar a logo e nome da empresa licenciadora (`parent_company`)
  checkHasInfosHibridoRestringidas(grupo, viagensProibidasMap = VIAGENS_PROIBIDAS) {
    const origemUf = removeCidadeFromCidadeSlug(grupo?.origem?.slug)
    const destinoUf = removeCidadeFromCidadeSlug(grupo?.destino?.slug)

    const hasFretamentoProibido = !!viagensProibidasMap?.[origemUf]?.modelo_venda?.buser ||
                                  !!viagensProibidasMap?.[destinoUf]?.modelo_venda?.buser

    return hasFretamentoProibido
  }
}
