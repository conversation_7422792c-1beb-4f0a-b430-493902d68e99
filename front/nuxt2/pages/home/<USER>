<template>
  <div class="home">
    <taskbar fixed transparent :force-default-logo-desktop="permiteLayoutPreBlackfriday" />

    <home-header
      :header-img="headerImg"
      :pre-black="permiteLayoutPreBlackfriday"
      :is-mobile="isMobile"
      :is-loading="isLoadingVantagens"
      :vantagens="vantagens"
    />

    <personalized-content v-show="!isRevendedor" @personalized-content-loaded="handlePersonalizedContentLoaded" />

    <cupons-carrossel
      v-if="promosNoCarrossel >= 1"
      v-show="!isRevendedor || isRevendedorInterno"
      class="mt-7"
      event-name="home"
    />

    <secao-destinos-home v-show="!isRevendedor || isRevendedorExterno" layout="new" default-tag="destinos no sul" />

    <LazyHydrate v-slot="{ hydrated }" when-visible>
      <valores-buser v-if="hydrated" v-show="!isApp" class="h-valores-buser" />
    </LazyHydrate>

    <div v-show="!isRevendedor">
      <LazyHydrate v-slot="{ hydrated }" when-visible>
        <midia-section v-if="hydrated" v-show="!isApp" class="mt-10" />
      </LazyHydrate>

      <LazyHydrate v-slot="{ hydrated }" when-visible>
        <banners-home v-if="hydrated" v-show="!isApp" class="mt-10" />
      </LazyHydrate>

      <top-destinos class="mt-5 pt-5" />
    </div>

    <div v-show="!isApp && !isRevendedor" class="pt-5 mt-5">
      <LazyHydrate v-slot="{ hydrated }" when-visible>
        <motivos-para-viajar v-if="hydrated" :motivos="motivosParaViajar" />
      </LazyHydrate>

      <LazyHydrate v-slot="{ hydrated }" when-visible>
        <seguranca-section v-if="hydrated" class="mt-6" />
      </LazyHydrate>
    </div>
  </div>
</template>

<script>
import { defineNuxtComponent } from '#app'
import { mapActions, mapState } from 'pinia'
import LazyHydrate from 'vue-lazy-hydration'
import api from '~api'
import { ensureCsrf } from '~api/checkout.js'
import { VANTAGENS, MOTIVOS_PARA_VIAJAR } from '~/assets/js/atributos-dinamicos.ts'
import fixedPromos from '~/assets/js/fixed-promos.ts'
import {
  PreBlackfridayBanner,
  BlackfridayBanner,
  EmptyMobile,
  HeaderByRegionMap,
  HeaderDefault
} from '~/assets/js/header-home.ts'
import cookiehelper from '~/helpers/cookiehelper.js'
import EventBus from '~/helpers/eventbus.js'
import metahelper from '~/helpers/metahelper.js'
import { quantidadePromos } from '~/helpers/promocarrossel/promocarrossel.js'
import cacheControl from '~/middleware/cache-control.js'
import { useAbandonedCartStore } from '~/stores/abandoned-cart.js'
import { useBuscasRecentesStore } from '~/stores/buscas-recentes.js'
import { useFavoritePlacesStore } from '~/stores/favorite-places.js'
import { useLeadPopupStore } from '~/stores/lead-popup.js'
import { useRevendedorStore } from '~/stores/revendedor.js'
import { useSessionStore } from '~/stores/session.js'
import { useSettingsStore } from '~/stores/settings.js'
import { useUserStatusStore } from '~/stores/user-status.js'
import taskbar from '~/components/shared/taskbar/taskbar.vue'
import topDestinos from '~/components/shared/top-destinos.vue'
import homeHeader from '~/components/home/<USER>'
import personalizedContent from '~/components/home/<USER>/personalized-content.vue'
import secaoDestinosHome from '~/components/secao-destinos/secao-destinos-home.vue'
export default defineNuxtComponent({
  middleware: cacheControl('public'),
  components: {
    taskbar,
    LazyHydrate,
    homeHeader,
    topDestinos,
    personalizedContent,
    secaoDestinosHome,
    cuponsCarrossel: () => import('~/components/shared/cupons-carrossel.vue'),
    valoresBuser: () => import('~/components/home/<USER>'),
    midiaSection: () => import('~/components/shared/midia-section.vue'),
    carouselNovosNegocios: () => import('~/components/home/<USER>/carousel-novos-negocios.vue'),
    bannersHome: () => import('~/components/home/<USER>'),
    motivosParaViajar: () => import('~/components/home/<USER>'),
    segurancaSection: () => import('~/components/home/<USER>')
  },
  async asyncData({ $pinia, req }) {
    const promosPromise = quantidadePromos()

    let promosNoCarrossel = 0
    let geolocation = {}
    try {
      const results = await Promise.allSettled([
        promosPromise,
        api.passenger.geoLocation(req?.headers)
      ]);

      ([promosNoCarrossel, geolocation] = results.map(result => result.value))
    } catch (e) {
      console.error(e)
    }

    const headerImg = HeaderByRegionMap[geolocation?.server_geolocation?.uf] || HeaderDefault
    return { promosNoCarrossel, headerImg }
  },
  data() {
    return {
      isLoadingVantagens: true,
      personalizedContent: null
    }
  },
  head() {
    const preloadPromos = fixedPromos().map(p => ({
      as: 'image',
      href: p.promo_image
    }))

    preloadPromos[0].fetchpriority = 'high'

    const preloadHeader = { as: 'image', fetchpriority: 'high' }
    let imagesrcset = `${EmptyMobile.srcOriginal} 1x, `
    if (this.permiteLayoutBlackfriday) {
      imagesrcset += `${BlackfridayBanner.srcOriginal} 2x`
    } else if (this.permiteLayoutPreBlackfriday) {
      imagesrcset += `${PreBlackfridayBanner.srcOriginal} 2x`
    } else {
      imagesrcset += `${this.headerImg.srcOriginal} 2x`
    }
    preloadHeader.imagesrcset = imagesrcset

    let linkPreload = [preloadHeader, ...preloadPromos]

    return {
      ...metahelper.generateMetaTags({
        title: this.permiteLayoutPreBlackfriday || this.permiteLayoutBlackfriday ? 'Black Friday 2024 com viagens de ônibus em oferta' : 'Viagens de ônibus com mais conforto pelo menor preço',
        description:
          this.permiteLayoutPreBlackfriday || this.permiteLayoutBlackfriday
            ? 'Aproveite a Black Friday 2024 da Buser para comprar passagens rodoviárias e viagens de fretamento. Destinos: São Paulo, Belo Horizonte, Rio de Janeiro, Paraguai, Ribeirão Preto.'
            : 'Buser, o app do ônibus. Viaje de ônibus com até 60% de economia. Conforto e segurança por um preço justo. Destinos: Belo Horizonte, São Paulo, Uberlândia, Ipatinga, São José do Rio Preto, Rio de Janeiro, Campinas, São José dos Campos.',
        url: 'https://www.buser.com.br',
        linkPreload
      })
    }
  },
  async mounted() {
    const inicialPromises = [
      this.fetchUserSession
    ]

    try {
      await Promise.allSettled(inicialPromises.map(task => task()))
    } catch (e) {
      console.error(e)
    }

    this.fetchFavoritePlaces()
    const androidPushtoken = cookiehelper.getItem('pushtoken')
    const apnsToken = this.$route.query.apnstoken

    if (this.user) {
      await this.fetchUserStatus()
    }

    if (this.loggedIn) {
      if (androidPushtoken || apnsToken) {
        await ensureCsrf()
      }
      if (androidPushtoken) {
        api.everyone.saveDevice({ token: androidPushtoken, kind: 'fcm' })
      }

      if (apnsToken) {
        this.savecookie('apnstoken', apnsToken)
        api.everyone.saveDevice({ token: apnsToken, kind: 'apns' })
      }

      this.sendLoginEventIfNeeded()
    }

    this.initLeadPopup(this.isMobile)
    this.isLoadingVantagens = false
  },
  methods: {
    ...mapActions(useFavoritePlacesStore, ['fetchFavoritePlaces']),
    ...mapActions(useSessionStore, ['fetchUserSession']),
    ...mapActions(useUserStatusStore, ['fetchUserStatus']),
    ...mapActions(useLeadPopupStore, {
      initLeadPopup: 'init',
      destroyLeadPopup: 'destroy'
    }),
    handlePersonalizedContentLoaded(data) {
      this.personalizedContent = data
    },
    savecookie(name, value) {
      window.document.cookie = `${name}=${value}`
    },
    sendLoginEventIfNeeded() {
      if (document && document.referrer) {
        const loginFromFacebook = document.referrer.includes('facebook')
        const loginFromGoogle = document.referrer.includes('google')

        if (loginFromFacebook || loginFromGoogle) {
          const plataforma = loginFromFacebook ? 'facebook' : 'google'
          EventBus.$emit('login', { plataforma, type: 'social', user: this.user })
        }
      }
    }
  },
  computed: {
    ...mapState(useRevendedorStore, ['isRevendedorInterno', 'isRevendedorExterno', 'isRevendedor']),
    ...mapState(useAbandonedCartStore, ['hasAbandonedCart']),
    ...mapState(useBuscasRecentesStore, ['quantidadeBuscas']),
    ...mapState(useSettingsStore, ['isApp', 'permiteLayoutPreBlackfriday', 'permiteLayoutBlackfriday']),
    ...mapState(useSessionStore, ['loggedIn', 'neverTraveled', 'user']),
    ...mapState(useUserStatusStore, ['hasNextTravel']),
    isMobile() {
      return this.$andromeda.breakpoint.smAndDown
    },
    isTablet() {
      return this.$andromeda.breakpoint.lgAndDown
    },
    vantagens() {
      const tmpVantagens = (this.loggedIn && !this.neverTraveled) ? VANTAGENS.loggedIn : VANTAGENS.loggedOut
      return (this.isTablet) ? tmpVantagens.slice(0, 2) : tmpVantagens
    },
    motivosParaViajar() {
      return (this.loggedIn && !this.neverTraveled) ? MOTIVOS_PARA_VIAJAR.loggedIn : MOTIVOS_PARA_VIAJAR.loggedOut
    }
  },
  destroyed() {
    this.destroyLeadPopup(this.isMobile)
  },
  watch: {
    personalizedContent: {
      handler() {
        EventBus.$emit('acessou-home', {
          hasNextTravel: this.hasNextTravel,
          recentSearchesCount: this.quantidadeBuscas,
          hasAbandonedCart: this.hasAbandonedCart,
          mobile: this.isMobile,
          personalizedContentType: this.personalizedContent?.type || 'sem-conteudo-personalizado',
          hasUpsell: !!this.personalizedContent?.hasUpsell
        })
      },
      immediate: false
    }
  }
})
</script>

<style lang="scss" scoped>
.home {
  .h-valores-buser {
    margin-top: $spacing-5;
    padding-top: $spacing-5;
  }
}
</style>
