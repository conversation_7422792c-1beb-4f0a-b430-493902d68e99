import dayjs from 'dayjs'
import searchhelper from '~/helpers/searchhelper.js'
import { pickFields } from '~/stores/abandoned-cart.js'
import { conexoes, conexoesFactory } from './data/db_conexoes.js'
import { groupsFactory } from './data/db_groups.js'
import { groupsMarketplaceFactory } from './data/db_groups_marketplace.js'
import {
  searchOrigemRotas,
  searchDestinoRotas,
  searchStatistics
} from './data/db_rotas.js'
import { trechos } from './data/db_trechos.js'
import { mockasync } from './utils/index.js'

const groups = groupsFactory()
const groupsMarketplace = groupsMarketplaceFactory()

function _flatten(groups) {
  const classes = []
  groups.forEach((g) => {
    g.classes.forEach((c) => {
      classes.push({ ...g, ...c })
    })
  })
  return classes
}

export function search({ origemSlug, destinoSlug, departureDate, weekDay }) {
  const trecho =
    trechos.find((trecho) => {
      return (
        trecho.origem.slug === origemSlug && trecho.destino.slug === destinoSlug
      )
    }) || trechos[8] // não mockar puxando por default um trecho com origem ou destino proibidos do /helpers/viagensproibidas.js enquanto filtramos isso pelo front na store de search
  const groupsByDate = [
    {
      datetime_ida: dayjs(departureDate).add(-1, 'days').format('YYYY-MM-DD'),
      grupos: trecho && trecho.id === 9 ? [] : _flatten(groups)
    },
    {
      datetime_ida: dayjs(departureDate).format('YYYY-MM-DD'),
      grupos: _flatten(groups)
    },
    {
      datetime_ida: dayjs(departureDate).add(1, 'days').format('YYYY-MM-DD'),
      grupos: trecho && trecho.id === 9 ? [] : _flatten([])
    },
    {
      datetime_ida: dayjs(departureDate).add(2, 'days').format('YYYY-MM-DD'),
      grupos: _flatten(groups)
    },
    {
      datetime_ida: dayjs(departureDate).add(3, 'days').format('YYYY-MM-DD'),
      grupos:
        trecho && trecho.id === 9 ? [] : _flatten([groups[15], groups[16]])
    }
  ]

  const allGroups = [].concat.apply(
    [],
    groupsByDate.map((date) => date.grupos)
  )
  const allLocaisEmbarque = [
    ...new Set([
      ...allGroups.map((g) => g.origem),
      ...allGroups.map((g) => g.destino)
    ])
  ]
  const result = {
    trecho,
    trechoAlternativo: trecho,
    is_trecho_vendido: true,
    users_searching: Math.round(Math.random() * 30),
    groups_by_date: groupsByDate.filter((g) =>
      departureDate ? g.datetime_ida === departureDate : true
    ),
    badges_by_date: {
      best_price_by_date: {},
      most_comfortable_by_date: {},
      recommended_by_date: { 1: true },
      promotional_price_new_route: { 2: true }
    },
    // Retorna lista de locais de embarque da busca igual ad back => { localEmbarqueId: localEmbarque... }
    locais: Object.assign(
      {},
      ...allLocaisEmbarque.map((localEmbarque) => ({
        [localEmbarque.id]: localEmbarque
      }))
    ),

    // IDs de locais de embarque únicos só das origens igual ad back
    locais_de_embarque: [...new Set(allGroups.map((grupo) => grupo.origem.id))],
    locais_de_desembarque: [
      ...new Set(allGroups.map((grupo) => grupo.destino.id))
    ],
    grupos_recomendados: {
      menor_preco: groupsByDate[1].grupos[2].id // Um id de grupo qualquer só pra mockar
    }
  }
  result.counts = groupsByDate.reduce((acc, current) => {
    acc[current.datetime_ida] = current.grupos.length
    return acc
  }, {})
  result.best_price_by_date = {}

  return mockasync(result).then((r) => r.data)
}

export function searchConexoesV2({ origemSlug, destinoSlug, departureDate }) {
  const result = conexoesFactory()

  return mockasync(result).then((response) => {
    return searchhelper.parseSearchConexaoResponse(response.data)
  })
}

export function searchMarketplace({ origemSlug, destinoSlug, departureDate }) {
  return mockasync({ items: groupsMarketplace }).then((r) => r.data)
}

export function upsertGrupo(grupoIda) {
  return mockasync({ id: groups[0].id }).then((r) => r.data)
}

export function searchConexoes(
  origemSlug,
  destinoSlug,
  cidadeIntermediaria = null,
  limit = null
) {
  const conexao = JSON.parse(JSON.stringify(conexoes))
  return mockasync(conexao).then((response) => {
    return response.data
  })
}

export function getGroupsV2(idsIda, idsVolta = null) {
  const gruposIda = groups.filter((grupo) => idsIda.includes(grupo.id))
  const gruposVolta = groups.filter((grupo) => idsVolta.includes(grupo.id))
  return mockasync({
    gruposIda: gruposIda.map((g) => _flatten([g])[0]),
    gruposVolta: gruposVolta.map((g) => _flatten([g])[0])
  }).then((r) => r.data)
}

export function searchSameGroup(grupoId) {
  const groupsByDate = [
    {
      datetime_ida: dayjs(groups[0].datetime_ida).format('YYYY-MM-DD'),
      grupos: _flatten(groups)
    }
  ]
  return mockasync({ groups_by_date: groupsByDate }).then((r) => r.data)
}

export function searchPromoGroups({
  code,
  origemSlug,
  destinoSlug,
  departureDate,
  deviceToken
}) {
  const promo = {
    code: 'CORPUS',
    title: 'Feriadão Corpus Christi',
    regra: 'Desconto de 100% na ida',
    background_image:
      'https://d38o79igrco1pc.cloudfront.net/onibus-buser-estrada.jpg',
    description: 'O DIA É DAS CRIANÇAS E NA BUSER ELAS TÊM SEGURANÇA.',
    start_date: '2020-01-01T17:27:38.565Z',
    due_date: '2021-01-04T17:27:38.565Z',
    datetime_ida_start: '2020-01-04T17:27:38.565Z',
    datetime_ida_end: '2030-01-04T17:27:38.565Z',
    specific_groups: true,
    is_volta: true,
    is_nunca_viajou: false,
    is_apenas_com_convidados: false,
    reservations_per_cpf: 1,
    is_valid: true,
    invalid_msg: 'inválido',
    value: 50,
    qtde_max_uso: 50,
    fixed_value: 50,
    count_usages: 20
  }
  const promoGroups = _flatten(groups)
  promoGroups.forEach((grupo) => {
    const max_split_value = grupo.max_split_value
    const discount = promo.value
    grupo.promocao = {
      promotional_value: Math.max(max_split_value - discount, 0),
      value: discount,
      description: `Desconto cupom ${promoCode}`
    }
  })
  return mockasync(promoGroups).then((r) => r.data)
}

export function searchPlaces(origem, destino) {
  const places = {
    origem: {
      id: 27,
      name: 'São José dos Campos',
      city_name: 'São José dos Campos',
      label: 'São José dos Campos - SP',
      uf: 'SP',
      slug: 'sao-jose-dos-campos-sp',
      sigla: 'SJK',
      timezone: 'America/Sao_Paulo'
    }
  }

  return mockasync(places).then((response) => response.data)
}

export function searchOrigem({ origemSlug, limit }) {
  const rotas = JSON.parse(JSON.stringify(searchOrigemRotas))
  let has_more = false
  if (limit) {
    has_more = limit < rotas.saindo_de.length
    rotas.saindo_de = rotas.saindo_de.slice(0, limit)
  }
  return mockasync(rotas).then((response) => {
    return { has_more, ...response.data }
  })
}

export function searchDestino({ destinoSlug, limit }) {
  const rotas = JSON.parse(JSON.stringify(searchDestinoRotas))
  rotas.id = `${destinoSlug}-${+new Date()}` // Pra nao quebrar v-for :key
  let has_more = false
  if (limit) {
    has_more = limit < rotas.indo_para.length
    rotas.indo_para = rotas.indo_para.splice(0, limit)
  }
  return mockasync(rotas).then((response) => {
    return { has_more, ...response.data }
  })
}

export function getNearbyDepartures({
  origemSlug,
  destinoSlug,
  date,
  incluirTrechoAlternativo
}) {
  const result = {
    dias_com_grupos: [
      dayjs().add(-1, 'days').format('YYYY-MM-DD'),
      dayjs().add(1, 'days').format('YYYY-MM-DD'),
      dayjs().add(2, 'days').format('YYYY-MM-DD')
    ]
  }
  return mockasync(result).then((response) => response.data)
}

export function searchHit() {
  return mockasync({ users_searching: 13 }).then((response) => response.data)
}

export function getSearchStatistics({ origemSlug, destinoSlug }) {
  return mockasync(searchStatistics).then((response) => response.data)
}

export function trechoPromo({ origemSlug, destinoSlug }, headers = null) {
  return mockasync({ trechos: groups.map((g) => g.id), discount: 0.2 }).then(
    (response) => response.data
  )
}

export function searchSimilarGroups({
  idaId,
  voltaId,
  shouldUseSearchRankAPI
}) {
  const flattenedGroups = _flatten(groups).slice(0, 2)
  return mockasync({ ida: flattenedGroups, volta: flattenedGroups }).then(
    (response) => response.data
  )
}

export function searchSimilarConnectionGroups({ idasIds, voltasIds }) {
  const gs = _flatten(groups).slice(0, 2)
  const checkpoint_by_local_id = gs.reduce((acc, g) => {
    return {
      ...acc,
      ...g.itinerario.reduce((iAcc, i) => {
        return {
          ...iAcc,
          [i.local_id]: i
        }
      }, {})
    }
  }, {})
  return mockasync({
    idas: [{
      ...pickFields(gs),
      itinerario: [
        checkpoint_by_local_id[gs[0].origem.id],
        checkpoint_by_local_id[gs[1].origem.id]
      ],
      conexoes: gs
    }]
  }).then((response) => response.data)
}

export function getItinerarioMarketplace({ trecho_mkp_extra }) {
  return mockasync({
    itinerario: [
      {
        name: 'SAO PAULO - SP (ROD. TIETE)',
        departure_at: '2025-06-12T23:58:00+00:00',
        distance_km: 0,
        stopover: false
      },
      {
        name: 'JACAREI - SP (RODOVIARIA)',
        departure_at: '2025-06-13T01:00:00+00:00',
        distance_km: 78.2,
        stopover: true
      },
      {
        name: 'SAO JOSE DOS CAMPOS - SP (RODOVIARIA)',
        departure_at: '2025-06-13T01:30:00+00:00',
        distance_km: 13.7,
        stopover: false
      },
      {
        name: 'TAUBATE - SP (RODOVIÁRIA)',
        departure_at: '2025-06-13T02:10:00+00:00',
        distance_km: 42.2,
        stopover: false
      }
    ]
  }, 2000).then((response) => response.data)
}
