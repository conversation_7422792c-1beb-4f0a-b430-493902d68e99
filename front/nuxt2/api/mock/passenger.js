import dayjs from 'dayjs'
import { groupsFactory } from './data/db_groups.js'
import {
  promosDestaque,
  promoDestaqueSearchResult
} from './data/db_promo_cupom.js'
import {
  topRotas,
  topRotasByOrigem,
  rotasComGrupos,
  topDestinos
} from './data/db_rotas.js'
import { mockasync } from './utils/index.js'

export default {
  getPromosDestaqueHome() {
    return mockasync(promosDestaque).then((r) => r.data)
  },
  getPromoDestaqueSearchResult({ origemSlug, destinoSlug }) {
    const trechos = ['belo-horizonte-mg:sao-paulo-sp']

    let promo = {}

    if (trechos.includes(`${origemSlug}:${destinoSlug}`)) {
      promo = promoDestaqueSearchResult
    }
    return mockasync(promo).then((r) => r.data)
  },
  getNumPromosDestaque() {
    return mockasync(promosDestaque.length).then((r) => r.data)
  },
  getTopRotas(destinoSlug, bairroSlug, nicknameSlug) {
    return mockasync(topRotas).then((r) => r.data)
  },
  getTopRotasByOrigem(slug, limit) {
    return mockasync(topRotasByOrigem).then((r) => {
      r.data.saindo_de = r.data.saindo_de.slice(0, limit)
      return r.data
    })
  },
  getAlternativeDestinations(citySlug) {
    return mockasync(topDestinos).then((r) => r.data)
  },
  getMinMaxPricePerDate({ origemSlug, destinoSlug, dates }) {
    const min_max_price = {}
    for (const date of dates.split(',')) {
      min_max_price[date] = { min_price: 10, max_price: 70.7 }
    }
    min_max_price[dates[3]] = null
    return mockasync(min_max_price).then((r) => r.data)
  },
  geoLocation() {
    const geolocation = {
      city: 'São José dos Campos',
      state: 'Sao Paulo',
      uf: 'SP',
      slug: 'sao-jose-dos-campos-sp',
      latitude: -23.1023,
      longitude: -45.922
    }
    return mockasync(geolocation).then((r) => r.data)
  },
  getRoutesWithGroupsList({ paginator }) {
    const { page, rowsPerPage } = paginator
    const idxStart = rowsPerPage * (page - 1)
    return mockasync({
      items: rotasComGrupos.slice(idxStart, idxStart + rowsPerPage),
      count_items: rotasComGrupos.length,
      count_pages: Math.ceil(rotasComGrupos.length / paginator.rowsPerPage)
    }).then((r) => r.data)
  },
  conheca(code) {
    const page_daily = {
      codigo: 'LP01',
      titulo: 'Landing Page 01',
      texto:
        'Imagina que esse aqui é um copy muito bom, que certamente é capaz de gerar bastante engajamento e conversão. Leu isso aqui = se cadastrou e comprou. Não tem jeito.',
      pre_input:
        'Imagina que aqui eu to dando um argumento final muito sagaz pra você dar seus dados',
      cta: 'Se inscreve pra ficar bonitão',
      ativo: true,
      imagem:
        'https://static.poder360.com.br/2021/03/buser-parceria-empresas-onibus-1080x630.jpg',
      cupom: {
        active: {
          code: 'BUSERTOPSTER',
          date: '2021-07-01',
          regra: 'Desconto de R$ 10.00'
        },
        expired: [
          {
            code: 'EXPIRED3',
            date: '2021-06-30',
            regra: 'Desconto de R$ 20.00'
          },
          {
            code: 'EXPIRED2',
            date: '2021-06-29',
            regra: 'Desconto de R$ 30.00'
          },
          {
            code: 'EXPIRED1',
            date: '2021-06-28',
            regra: 'Desconto de R$ 40.00'
          }
        ]
      },
      expiring_method: 'daily'
    }
    const page_hourly = {
      codigo: 'LP02',
      titulo: 'Landing Page 02',
      texto:
        'Imagina que esse aqui é um copy muito bom, que certamente é capaz de gerar bastante engajamento e conversão. Leu isso aqui = se cadastrou e comprou. Não tem jeito.',
      pre_input:
        'Imagina que aqui eu to dando um argumento final muito sagaz pra você dar seus dados',
      cta: 'Se inscreve pra ficar bonitão',
      ativo: true,
      imagem:
        'https://static.poder360.com.br/2021/03/buser-parceria-empresas-onibus-1080x630.jpg',
      cupom: {
        active: {
          code: 'BUSERTOPSTER',
          time: '00:23:12',
          regra: 'Desconto de R$ 10,00'
        },
        expired: [
          {
            code: 'EXPIRED3',
            time: '00:23:12',
            regra: 'Desconto de R$ 20,00'
          },
          {
            code: 'EXPIRED2',
            time: '01:23:12',
            regra: 'Desconto de R$ 30,00'
          },
          {
            code: 'EXPIRED1',
            time: '02:23:12',
            regra: 'Desconto de R$ 40,00'
          }
        ]
      },
      cupom_start_time: dayjs().format('HH:mm'),
      expiring_method: 'hourly'
    }
    const page_hidden_form = {
      codigo: 'LP03',
      titulo: 'Landing Page 03',
      texto:
        'Imagina que esse aqui é um copy muito bom, que certamente é capaz de gerar bastante engajamento e conversão. Leu isso aqui = se cadastrou e comprou. Não tem jeito.',
      pre_input:
        'Imagina que aqui eu to dando um argumento final muito sagaz pra você dar seus dados',
      cta: 'Se inscreve pra ficar bonitão',
      ativo: true,
      hidden_form: true,
      imagem:
        'https://static.poder360.com.br/2021/03/buser-parceria-empresas-onibus-1080x630.jpg'
    }
    const pages = [page_daily, page_hourly, page_hidden_form]
    return mockasync(pages.find((page) => page.codigo === code)).then(
      (r) => r.data
    )
  },
  getFiltrosDisponiveisDescubra(origemSlug) {
    const filtrosDisponiveis = [
      {
        id: 1,
        ativo: true,
        icone: 'money-bill-wave',
        titulo: 'Praia',
        value: 'praia'
      },
      {
        id: 2,
        ativo: true,
        icone: 'money-bill-wave',
        titulo: 'Férias',
        value: 'ferias'
      },
      {
        id: 3,
        ativo: true,
        icone: 'money-bill-wave',
        titulo: 'Carnaval',
        value: 'carnaval'
      },
      {
        id: 4,
        ativo: true,
        icone: 'money-bill-wave',
        titulo: 'Folia',
        value: 'folia'
      },
      {
        id: 5,
        ativo: true,
        icone: 'money-bill-wave',
        titulo: 'Numsei',
        value: 'numsei'
      }
    ]
    const result = { filtrosDisponiveis }
    return mockasync(result).then((response) => response.data)
  },
  getBestPrices({ origem, destino, limit = 4 }) {
    const origemResponse = {
      id: 1,
      name: 'Maceió',
      slug: 'maceio-al',
      uf: 'AL',
      sigla: 'MCO',
      timezone: 'America/Maceio',
      label: 'Maceió - AL',
      city_name: 'Maceió'
    }

    const destinoResponse = {
      id: 2,
      name: 'Aracaju',
      slug: 'aracaju-se',
      uf: 'SE',
      sigla: 'AJU',
      timezone: 'America/Maceio',
      label: 'Aracaju - SE',
      city_name: 'Aracaju'
    }

    const trechos = []
    for (let i = 0; i < limit; i++) {
      trechos.push(
        {
          id: i,
          price: 20.0,
          old_price: 23.0,
          datetime_ida: '2023-08-11T06:00:00-03:00',
          datetime_chegada: '2023-08-11T07:30:00-03:00',
          tipo_assento: 'executivo',
          modelo_venda: 'buser'
        }
      )
    }
    const result = { origem: origemResponse, destino: destinoResponse, trechos }
    return mockasync(result).then((response) => response.data)
  },
  getMelhoresViagensNoTrecho({ origem, destino, limit = 3 }) {
    const origemResponse = {
      name: 'Maceió',
      slug: 'maceio-al',
      uf: 'AL',
      sigla: 'MCO',
      timezone: 'America/Maceio',
      label: 'Maceió - AL',
      city_name: 'Maceió'
    }

    const destinoResponse = {
      name: 'Aracaju',
      slug: 'aracaju-se',
      uf: 'SE',
      sigla: 'AJU',
      timezone: 'America/Maceio',
      label: 'Aracaju - SE',
      city_name: 'Aracaju'
    }

    const trechos = []
    for (let i = 0; i < limit; i++) {
      trechos.push(
        {
          id: i,
          price: 20.0,
          old_price: 23.0,
          datetime_ida: '2023-08-11T06:00:00-03:00',
          datetime_chegada: '2023-08-11T07:30:00-03:00',
          tipo_assento: 'executivo',
          modelo_venda: 'buser'
        }
      )
    }
    const result = { origem: origemResponse, destino: destinoResponse, trechos }
    return mockasync(result).then((response) => response.data)
  },
  getOfertas({ origem, destino, mes, ano, limit }) {
    const origemResponse = {
      name: 'Maceió',
      slug: 'maceio-al',
      uf: 'AL',
      sigla: 'MCO',
      timezone: 'America/Maceio',
      label: 'Maceió - AL',
      city_name: 'Maceió'
    }

    const destinoTrecho = {
      name: 'Aracaju',
      slug: 'aracaju-se',
      uf: 'SE',
      sigla: 'AJU',
      timezone: 'America/Maceio',
      label: 'Aracaju - SE',
      city_name: 'Aracaju'
    }
    if (!limit && limit !== 0) limit = 12

    const trechos = []
    const datetimeIda = dayjs(`${ano}-${mes}-01`)
    const datetimeVolta = datetimeIda.add(1, 'day')
    for (let i = 0; i < limit; i++) {
      trechos.push(
        {
          id: i,
          price: 20.0,
          old_price: 23.0,
          datetime_ida: datetimeIda.format(),
          datetime_chegada: datetimeVolta.format(),
          tipo_assento: 'executivo',
          modelo_venda: 'buser',
          vagas: i * 2 + 1,
          destino: destinoTrecho
        }
      )
    }

    const result = { origem: origemResponse, destino: destinoTrecho, trechos }
    return mockasync(result).then((response) => response.data)
  },
  getOfertasEsgotadas({ origem, destino, mes, ano, limit }) {
    const destinoTrecho = {
      name: 'Aracaju',
      uf: 'SE'
    }

    const datetimeIda = dayjs(`${ano}-${mes}-01`)
    const datetimeVolta = datetimeIda.add(1, 'day')

    const trechos_esgotatos = []
    for (let i = 0; i < 3; i++) {
      trechos_esgotatos.push(
        {
          id: i,
          price: 20.0,
          old_price: 23.0,
          datetime_ida: datetimeIda.format(),
          datetime_chegada: datetimeVolta.format(),
          tipo_assento: 'executivo',
          modelo_venda: 'buser',
          destino: destinoTrecho
        }
      )
    }
    const result = { trechos: trechos_esgotatos }
    return mockasync(result).then((response) => response.data)
  },
  getVistoRecentementeRecommendations(buscas) {
    let groups = groupsFactory().slice(0, 7)
    // remove grupo repetido
    groups.splice(2, 1)

    let result = groups.map((g) => {
      return { origem: { name: g.origem.name, slug: g.origem.slug }, destino: { name: g.destino.name, slug: g.destino.slug }, img: g.destino.picture_url }
    })
    result[0].caption = 'Visto Recentemente'
    result[1].caption = 'Visto Recentemente'

    result[0].dataIda = dayjs().add(1, 'days').format('YYYY-MM-DD')
    result[0].dataVolta = dayjs().add(5, 'days').format('YYYY-MM-DD')
    result[1].dataIda = dayjs().add(2, 'days').format('YYYY-MM-DD')
    result[5].dataIda = dayjs().add(3, 'days').format('YYYY-MM-DD')
    return mockasync(result).then((response) => response.data)
  }
}
