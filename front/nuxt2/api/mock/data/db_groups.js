import dayjs from 'dayjs'
import { MODELOS_OPERACAO } from '~/constants.ts'
import { busMap } from './db_cars.js'
import getItinerarioFrom from './db_itinerarios.js'
import { locals } from './db_places.js'

export const groupsFactory = () => [
  {
    // [0]
    id: 'casG13',
    ranking: {
      heuristic_em_alta_v1: 18.0,
      searchrank_memory_based_v1: 0.018
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[0].slug}:${locals[3].slug}`,
    vagas: 5,
    // modelo_venda: 'buser',
    modelo_operacao: 'default',
    closed: false,
    trecho_id: 1,
    itinerario: getItinerarioFrom(0, 3, [1, 2, 3, 4, 5]),
    datetime_ida: dayjs().add(21, 'minutes').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(7, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: 21600000,
    status: 'pending',
    origem: locals[0],
    origem_id: locals[0].id,
    destino: locals[3],
    destino_id: locals[3].id,
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: null,
    tipo_assento: 'executivo',
    embarque_status: 'EMBARQUE_INICIADO',
    foto_onibus: 'https://assets.volvo.com/is/image/VolvoInformationTechnologyAB/1860x1050-onibus-rodoviario-imagem-release?wid=720',
    confirming_probability: 'very_low',
    onibus_plotado: true,
    is_blackfriday_bucket: true,
    travel_log: {
      created_at: '2018-08-17T17:59:57.200328+00:00',
      updated_at: '2018-08-17T18:00:11.504512+00:00',
      // logurl: 'https://d1q5r6jf4yxfe1.cloudfront.net/travellogs/2018/08/travellog-20180817175957-3-12981804888-U5EWWT4.json',
      logurl: '/sampletravellog.json',
      id: 3,
      status: 'pending'
    },
    color: '#AD1457',
    preco_base: 200,
    classes: [
      {
        id: 'casG13',
        grupo_id: 1,
        closed: false,
        preco_rodoviaria: 166.18,
        max_split_value: 120,
        ref_split_value: 120,
        // promocao: {
        //   promotional_value: 0,
        //   value: 20,
        //   description: 'Desconto 30% Feriadão'
        // },
        destaque: true,
        vagas: 5,
        layout: busMap[0],
        tipo_assento: 'executivo',
        tipo_veiculo: 'DD'
      }
    ],
    onibus: {
      count_groups: 5,
      classe: 'ricardo / sampaio',
      capacidade: '25 / 25',
      next_group: null,
      id: 2,
      name: 'Sampaio Ricardo',
      tipo: 'DD',
      company: {
        id: 6,
        bank_account: null,
        antt_enabled: false,
        payment_option: '',
        is_enabled: true,
        name: 'Carreta Furacão',
        cnpj: ''
      },
      last_group: {
        vagas: 2,
        checkin_status: 'pending',
        origem: locals[0],
        origem_id: locals[0],
        trecho_id: 25,
        confirming_probability: 'very_low',
        status: 'done',
        destino: locals[1],
        destino_id: locals[1].id,
        id: 376,
        duracao_volta: null,
        chegada_volta: null,
        duracao_ida: ********,
        datetime_ida: dayjs()
          .add(1, 'hours')
          .format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
        chegada_ida: dayjs().add(73, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ')
      },
      placa: 'RIC1234'
    },
    company_id: 42,
    company_rating: 4.2,
    company_name: 'Cia. de Fretamento com nome muito grande',
    company_logo_url:
      'https://i.imgur.com/y9sTq12.jpg',
    rotina_onibus_id: 15,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    onibus_placa: 'OIE1234',
    onibus_features: ['adaptado contra covid'],
    driver_name: 'Embalagem Econômica',
    modelo_venda: 'buser',
    cancellation_deadline_hours: 3,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [1]
    id: 'fjkl12',
    ranking: {
      heuristic_em_alta_v1: 17.0,
      searchrank_memory_based_v1: 0.17
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[1].slug}:${locals[0].slug}`,
    itinerario: getItinerarioFrom(1, 0, [1, 2, 3, 4, 5]),
    trecho_id: 2,
    vagas: 5,
    max_split_value: 110,
    closed: false,
    embarque_status: 'ULTIMA_CHAMADA',
    datetime_ida: dayjs().add(24, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(29, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: 18000000,
    status: 'travel_confirmed',
    foto_onibus: 'https://www.islabus.com.br/imagens-midia/informacoes/thumbs/fretamento-onibus-turismo-sp-01.webp',
    company_pos_bo: 'Buser',
    onibus_pos_bo: 'ABCDE',
    tipo_pos_bo: 'Ônibus leito',
    origem: locals[1],
    origem_id: locals[1].id,
    destino: locals[0],
    destino_id: locals[0].id,
    tipo_assento: 'executivo',
    tipo_veiculo: 'micro-onibus',
    confirming_probability: 'high',
    onibus_plotado: false,
    is_blackfriday_bucket: true,
    travel_log: {
      created_at: '2018-12-17T17:59:57.200328+00:00',
      updated_at: '2018-12-17T18:00:11.504512+00:00',
      // logurl: 'https://d1q5r6jf4yxfe1.cloudfront.net/travellogs/2018/08/travellog-20180817175957-3-12981804888-U5EWWT4.json',
      id: 4,
      status: 'pending'
    },
    problem_description:
      'Furou o pneu, o onibus capotou, ninguém se feriu, mas o ônibus está inutilizável, afinal de contas ele explodiu quando capotou, sad story mas é o que aconteceu, pois é né.',
    color: '#F4511E',
    preco_base: 250,
    classes: [
      {
        id: 'fjkl12',
        grupo_id: 2,
        closed: false,
        preco_rodoviaria: 168,
        ref_split_value: 110,
        // promocao: {
        //   promotional_value: 80,
        //   value: 20,
        //   description: 'Desconto 30% Feriadão'
        // },
        vagas: 30,
        current_split_value: 73.94,
        layout: busMap[0],
        trecho_alternativo: false
      }
    ],
    parcelamento: {
      valor_por_parcela: 36,
      quantidade_de_parcelas: 3
    },
    onibus: {
      cadeirinhas: 4,
      count_groups: 5,
      classe: 'ricardo / sampaio',
      capacidade: '25 / 25',
      next_group: null,
      id: 2,
      name: 'Sampaio Ricardo',
      tipo: 'DD',
      company: {
        id: 6,
        bank_account: null,
        antt_enabled: false,
        payment_option: '',
        is_enabled: true,
        name: 'Carreta Furacão',
        cnpj: ''
      },
      last_group: {
        vagas: 2,
        checkin_status: 'pending',
        origem: locals[3],
        origem_id: locals[3].id,
        destino: locals[2],
        destino_id: locals[2].id,
        trecho_id: 25,
        confirming_probability: 'very_low',
        status: 'travel_confirmed',
        id: 376,
        duracao_volta: null,
        chegada_volta: null,
        duracao_ida: ********,
        datetime_ida: dayjs()
          .add(2, 'hours')
          .format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
        chegada_ida: dayjs().add(13, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ')
      },
      placa: 'RIC1234'
    },
    company_id: 42,
    company_name: 'Raid',
    company_rating: 4.2,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    onibus_placa: 'WTF5678',
    driver_name: 'Agite Antes de Usar',
    modelo_venda: 'buser',
    company_logo_url:
      'https://i.imgur.com/y9sTq12.jpg',
    rotina_onibus_id: 15,
    cancellation_deadline_hours: 1,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [2]
    id: 'FAS1gm',
    ranking: {
      heuristic_em_alta_v1: 16.0,
      searchrank_memory_based_v1: 0.16
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[0].slug}:${locals[3].slug}`,
    local_retirada: {
      tipo: 'outro',
      descricao:
        'no guichê da Expresso, localizada no box 19. PS: A retirada deve ser feita até as 21h, após esse horário o guichê estará fechado'
    },
    signed: 'SIGNMOCK',
    itinerario: getItinerarioFrom(0, 3, [1, 2, 3, 4, 5]),
    trecho_id: 2,
    datetime_ida: dayjs().add(72, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(77, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: 18000000,
    status: 'travel_confirmed',
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: 'hide',
    origem: locals[0],
    origem_id: locals[0].id,
    destino: locals[3],
    destino_id: locals[3].id,
    tipo_assento: 'executivo',
    tipo_veiculo: 'DD',
    confirming_probability: 'medium',
    onibus_plotado: true,
    is_blackfriday_bucket: true,
    travel_log: {
      created_at: '2018-12-17T17:59:57.200328+00:00',
      updated_at: '2018-12-17T18:00:11.504512+00:00',
      // logurl: 'https://d1q5r6jf4yxfe1.cloudfront.net/travellogs/2018/08/travellog-20180817175957-3-12981804888-U5EWWT4.json',
      id: 4,
      status: 'in_route'
    },
    onibus_placa: 'OIE1235',
    problem_description:
      'Furou o pneu, o onibus capotou, ninguém se feriu, mas o ônibus está inutilizável, afinal de contas ele explodiu quando capotou, sad story mas é o que aconteceu, pois é né.',
    color: '#F4511E',
    preco_base: 300,
    classes: [
      {
        id: 'FAS1gm',
        grupo_id: 22,
        closed: false,
        preco_rodoviaria: 168,
        max_split_value: 98,
        ref_split_value: 140,
        // promocao: {
        //   promotional_value: 80,
        //   value: 20,
        //   description: 'Desconto 30% Feriadão'
        // },
        vagas: 28,
        current_split_value: 73.94,
        layout: busMap[0]
      },
      {
        id: '4tgdf',
        grupo_id: 'sfsfcx',
        closed: false,
        preco_rodoviaria: 168,
        max_split_value: 98,
        ref_split_value: 140,
        vagas: 0,
        current_split_value: 73.94,
        layout: busMap[0]
      }
    ],
    parcelamento: {
      valor_por_parcela: 32.60,
      quantidade_de_parcelas: 3
    },
    company_id: 44,
    company_rating: 4.2,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    modelo_venda: 'hibrido',
    parent_company_name: 'Transbrasil',
    parent_company_logo_url: 'https://i.imgur.com/y9sTq12.jpg',
    company_name: 'Cometinha',
    company_logo_url:
      'https://i.imgur.com/y9sTq12.jpg',
    rotina_onibus_id: 15,
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    cancellation_deadline_hours: 3,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [3]
    id: 'fa345r',
    ranking: {
      heuristic_em_alta_v1: 15.0,
      searchrank_memory_based_v1: 0.15
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[6].slug}:${locals[11].slug}`,
    local_retirada: {
      tipo: 'guiche',
      descricao:
        'no guichê da Expresso, localizada no box 19. PS: A retirada deve ser feita até as 21h, após esse horário o guichê estará fechado'
    },
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: 'Taxi/Uber',
    itinerario: getItinerarioFrom(6, 11, [1, 2, 3, 4, 5]),
    trecho_id: 5,
    datetime_ida: dayjs().add(1, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(30, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: *********,
    status: 'pending',
    origem: locals[6],
    origem_id: locals[6].id,
    destino: locals[11],
    destino_id: locals[11].id,
    onibus_placa: 'AEZ5689',
    confirming_probability: 'medium',
    onibus_plotado: false,
    tipo_veiculo: 'van',
    is_blackfriday_bucket: true,
    preco_base: 170,
    classes: [
      {
        id: 'fa345r',
        grupo_id: 6,
        closed: false,
        preco_rodoviaria: 189,
        max_split_value: 102,
        ref_split_value: 110,
        // promocao: {
        //   promotional_value: 80,
        //   value: 20,
        //   description: 'Desconto 30% Feriadão'
        // },
        vagas: 10,
        current_split_value: 126,
        tipo_assento: 'executivo',
        tipo_veiculo: 'van',
        layout: busMap[0]
      }
    ],
    company_id: 44,
    company_name: 'Cometinha',
    company_rating: 4.2,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    modelo_venda: 'marketplace',
    company_logo_url:
      'https://i.imgur.com/y9sTq12.jpg',
    rotina_onibus_id: 15,
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    cancellation_deadline_hours: 1,
    trecho_alternativo: false,
    has_marcacao_assento: true,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [4]
    id: 'df235r',
    ranking: {
      heuristic_em_alta_v1: 14.0,
      searchrank_memory_based_v1: 0.14
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[1].slug}:${locals[3].slug}`,
    local_retirada: {
      tipo: 'guiche',
      descricao:
        'no guichê da Expresso, localizada no box 19. PS: A retirada deve ser feita até as 21h, após esse horário o guichê estará fechado'
    },
    itinerario: getItinerarioFrom(1, 3, [1, 2, 3, 4, 5]),
    trecho_id: 3,
    datetime_ida: dayjs().add(8, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(38, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: *********,
    status: 'pending',
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: null,
    origem: locals[1],
    origem_id: locals[1].id,
    destino: locals[3],
    destino_id: locals[3].id,
    confirming_probability: 'medium',
    onibus_plotado: true,
    tipo_veiculo: 'DD',
    is_blackfriday_bucket: false,
    alerta: {
      title: 'Problema na Bahia',
      description: 'Descrição do problema na Bahia',
      link: 'https://buser.com.br/ba',
      pages: ['reserva-confirmada', 'search', 'travel-details']
    },
    color: '#E4C441',
    preco_base: 500,
    classes: [
      {
        id: 'df235r',
        grupo_id: 3,
        closed: false,
        preco_rodoviaria: 189,
        max_split_value: 103,
        ref_split_value: 120,
        promocao: {
          promotional_value: 80,
          value: 20,
          description: 'Desconto 30% Feriadão'
        },
        pessoas: 12,
        vagas: 9,
        current_split_value: 123,
        tipo_assento: 'leito',
        tipo_veiculo: 'DD',
        layout: busMap[1]
      },
      {
        id: 'a5dfg',
        grupo_id: 5,
        closed: false,
        preco_rodoviaria: 189,
        max_split_value: 103,
        ref_split_value: 120,
        // promocao: {
        //   promotional_value: 80,
        //   value: 20,
        //   description: 'Desconto 30% Feriadão'
        // },
        vagas: 10,
        pessoas: 3,
        current_split_value: 123,
        tipo_assento: 'leito cama',
        tipo_veiculo: 'LD',
        layout: busMap[1]
      }
    ],
    company_id: 44,
    company_name: 'Cometinha',
    company_rating: 4.2,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    modelo_venda: 'marketplace',
    company_logo_url:
      'https://i.imgur.com/y9sTq12.jpg',
    rotina_onibus_id: 15,
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    cancellation_deadline_hours: 3,
    trecho_alternativo: false,
    has_marcacao_assento: true,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [5]
    id: 'u75o90',
    ranking: {
      heuristic_em_alta_v1: 13.0,
      searchrank_memory_based_v1: 0.13
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[1].slug}:${locals[5].slug}`,
    local_retirada: {
      tipo: 'guiche',
      descricao:
        'no guichê da Expresso, localizada no box 19. PS: A retirada deve ser feita até as 21h, após esse horário o guichê estará fechado'
    },
    itinerario: getItinerarioFrom(1, 5, [1, 2, 3, 4, 5]),
    trecho_id: 4,
    datetime_ida: dayjs().add(35, 'minutes').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(72, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: *********,
    status: 'pending',
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: null,
    origem: locals[1],
    origem_id: locals[1].id,
    destino: locals[5],
    destino_id: locals[5].id,
    confirming_probability: 'very_low',
    onibus_plotado: false,
    is_blackfriday_bucket: true,
    color: '#0B8043',
    preco_base: 20,
    classes: [
      {
        id: 'u75o90',
        grupo_id: 4,
        closed: false,
        preco_rodoviaria: 191,
        max_split_value: 20,
        ref_split_value: 20,
        // promocao: {
        //   promotional_value: 80,
        //   value: 20,
        //   description: 'Desconto 30% Feriadão'
        // },
        vagas: 12,
        vagas_bucket: 4,
        current_split_value: 124,
        tipo_assento: 'leito',
        tipo_veiculo: 'leito',
        layout: busMap[2]
      }
    ],
    company_id: 44,
    company_name: 'Viação Piraporense de Transporte e Turismo LTDA',
    company_rating: 4.2,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    modelo_venda: 'marketplace',
    company_logo_url:
      'https://www.cometinha.com.br/wp-content/uploads/2019/07/logo-cometinha.png',
    rotina_onibus_id: 15,
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    cancellation_deadline_hours: 3,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [6]
    id: 'sadkfj',
    ranking: {
      heuristic_em_alta_v1: 12.0,
      searchrank_memory_based_v1: 0.12
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[4].slug}:${locals[2].slug}`,
    local_retirada: {
      tipo: 'guiche',
      descricao:
        'no guichê da Expresso, localizada no box 19. PS: A retirada deve ser feita até as 21h, após esse horário o guichê estará fechado'
    },
    itinerario: getItinerarioFrom(4, 2),
    trecho_id: 5551,
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: null,
    datetime_ida: dayjs().add(2, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(22.5, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: 82800000,
    status: 'pending',
    problem_description:
      'Furou o pneu, o onibus capotou, ninguém se feriu, mas o ônibus está inutilizável, afinal de contas ele explodiu quando capotou, sad story mas é o que aconteceu, pois é né.',
    ressarcido: true,
    ressarcimento_factor: 0.5,
    ressarcimento_reason:
      'Ressarcimos 50% do valor do rateio por causa do atraso de 10 min.',
    origem: locals[4],
    origem_id: locals[4].id,
    destino: locals[2],
    destino_id: locals[2].id,
    color: '#3F51B5',
    nf_url: 'blablabla',
    preco_base: 92,
    classes: [
      {
        id: 'sadkfj',
        grupo_id: 5551,
        closed: false,
        preco_rodoviaria: 202,
        max_split_value: 62,
        ref_split_value: 62,
        vagas: 2,
        current_split_value: 126,
        tipo_assento: 'leito cama',
        tipo_veiculo: 'DD',
        layout: busMap[1]
      }
    ],
    company_id: 44,
    company_name: 'Cometinha',
    company_rating: 4.2,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    modelo_venda: 'marketplace',
    company_logo_url:
      'https://www.cometinha.com.br/wp-content/uploads/2019/07/logo-cometinha.png',
    rotina_onibus_id: 15,
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    cancellation_deadline_hours: 3,
    cancellation_deadline: dayjs()
      .add(4, 'hours')
      .format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [7]
    id: 'GefxB2',
    ranking: {
      heuristic_em_alta_v1: 11.0,
      searchrank_memory_based_v1: 0.11
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[4].slug}:${locals[2].slug}`,
    local_retirada: {
      tipo: 'guiche',
      descricao:
        'no guichê da Expresso, localizada no box 19. PS: A retirada deve ser feita até as 21h, após esse horário o guichê estará fechado'
    },
    itinerario: getItinerarioFrom(4, 2),
    trecho_id: 5552,
    datetime_ida: dayjs().add(11.5, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(33, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: 79200000,
    status: 'pending',
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: null,
    problem_description:
      'Furou o pneu, o onibus capotou, ninguém se feriu, mas o ônibus está inutilizável, afinal de contas ele explodiu quando capotou, sad story mas é o que aconteceu, pois é né.',
    ressarcido: true,
    ressarcimento_factor: 0.5,
    ressarcimento_reason:
      'Ressarcimos 50% do valor do rateio por causa do atraso de 10 min.',
    origem: locals[4],
    origem_id: locals[4].id,
    destino: locals[2],
    destino_id: locals[2].id,
    color: '#3F51B5',
    nf_url: 'blablabla',
    preco_base: 120,
    classes: [
      {
        id: 'GefxB2',
        grupo_id: 5552,
        closed: false,
        preco_rodoviaria: 202,
        max_split_value: 62,
        ref_split_value: 62,
        vagas: 2,
        current_split_value: 126,
        tipo_assento: 'leito cama',
        tipo_veiculo: 'DD',
        layout: busMap[1]
      }
    ],
    parcelamento: {
      valor_por_parcela: 20.30,
      quantidade_de_parcelas: 3
    },
    company_id: 44,
    company_name: 'Cometinha',
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    modelo_venda: 'marketplace',
    company_logo_url:
      'https://www.cometinha.com.br/wp-content/uploads/2019/07/logo-cometinha.png',
    rotina_onibus_id: 15,
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    cancellation_deadline_hours: 3,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [8]
    id: 'dghanb',
    ranking: {
      heuristic_em_alta_v1: 10.0,
      searchrank_memory_based_v1: 0.10
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[4].slug}:${locals[2].slug}`,
    local_retirada: {
      tipo: 'guiche',
      descricao:
        'no guichê da Expresso, localizada no box 19. PS: A retirada deve ser feita até as 21h, após esse horário o guichê estará fechado'
    },
    itinerario: getItinerarioFrom(4, 2),
    trecho_id: 5553,
    datetime_ida: dayjs().add(12.5, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(50, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: *********,
    status: 'pending',
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: null,
    problem_description:
      'Furou o pneu, o onibus capotou, ninguém se feriu, mas o ônibus está inutilizável, afinal de contas ele explodiu quando capotou, sad story mas é o que aconteceu, pois é né.',
    ressarcido: true,
    ressarcimento_factor: 0.5,
    ressarcimento_reason:
      'Ressarcimos 50% do valor do rateio por causa do atraso de 10 min.',
    origem: locals[4],
    origem_id: locals[4].id,
    destino: locals[2],
    destino_id: locals[2].id,
    color: '#3F51B5',
    nf_url: 'blablabla',
    preco_base: 130,
    classes: [
      {
        id: 'dghanb',
        grupo_id: 5553,
        closed: false,
        preco_rodoviaria: 202,
        max_split_value: 62,
        ref_split_value: 62,
        vagas: 2,
        current_split_value: 126,
        tipo_assento: 'leito cama',
        tipo_veiculo: 'DD',
        layout: busMap[1]
      }
    ],
    company_id: 44,
    company_name: 'Cometinha',
    company_rating: 4.2,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    modelo_venda: 'marketplace',
    company_logo_url:
      'https://www.cometinha.com.br/wp-content/uploads/2019/07/logo-cometinha.png',
    rotina_onibus_id: 15,
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    cancellation_deadline_hours: 3,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [9]
    id: 'gvae23',
    ranking: {
      heuristic_em_alta_v1: 9.0,
      searchrank_memory_based_v1: 0.09
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[4].slug}:${locals[2].slug}`,
    local_retirada: {
      tipo: 'outro',
      descricao:
        'no guichê da Expresso, localizada no box 19. PS: A retirada deve ser feita até as 21h, após esse horário o guichê estará fechado'
    },
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: null,
    itinerario: getItinerarioFrom(4, 2),
    trecho_id: 5554,
    datetime_ida: dayjs().add(13.5, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(78, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: *********,
    status: 'pending',
    problem_description:
      'Furou o pneu, o onibus capotou, ninguém se feriu, mas o ônibus está inutilizável, afinal de contas ele explodiu quando capotou, sad story mas é o que aconteceu, pois é né.',
    ressarcido: true,
    ressarcimento_factor: 0.5,
    ressarcimento_reason:
      'Ressarcimos 50% do valor do rateio por causa do atraso de 10 min.',
    origem: locals[4],
    origem_id: locals[4].id,
    destino: locals[2],
    destino_id: locals[2].id,
    color: '#3F51B5',
    nf_url: 'blablabla',
    preco_base: 150,
    classes: [
      {
        id: 'gvae23',
        grupo_id: 5554,
        closed: false,
        preco_rodoviaria: 202,
        max_split_value: 62,
        ref_split_value: 62,
        vagas: 2,
        current_split_value: 126,
        tipo_assento: 'leito cama',
        tipo_veiculo: 'DD',
        layout: busMap[1]
      }
    ],
    company_id: 44,
    company_rating: 4.2,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    modelo_venda: 'hibrido',
    parent_company_name: 'Transbrasil',
    parent_company_logo_url: 'https://i.imgur.com/y9sTq12.jpg',
    company_name: 'Cometinha',
    company_logo_url:
      'https://www.cometinha.com.br/wp-content/uploads/2019/07/logo-cometinha.png',
    rotina_onibus_id: 15,
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    cancellation_deadline_hours: 3,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [10]
    id: '23Gdas',
    ranking: {
      heuristic_em_alta_v1: 8.0,
      searchrank_memory_based_v1: 0.08
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[4].slug}:${locals[2].slug}`,
    local_retirada: {
      tipo: 'guiche',
      descricao:
        'no guichê da Expresso, localizada no box 19. PS: A retirada deve ser feita até as 21h, após esse horário o guichê estará fechado'
    },
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: null,
    itinerario: getItinerarioFrom(4, 2),
    trecho_id: 5555,
    datetime_ida: dayjs().add(14, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(27, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: 46800000,
    status: 'pending',
    problem_description:
      'Furou o pneu, o onibus capotou, ninguém se feriu, mas o ônibus está inutilizável, afinal de contas ele explodiu quando capotou, sad story mas é o que aconteceu, pois é né.',
    ressarcido: true,
    ressarcimento_factor: 0.5,
    ressarcimento_reason:
      'Ressarcimos 50% do valor do rateio por causa do atraso de 10 min.',
    origem: locals[4],
    origem_id: locals[4].id,
    destino: locals[2],
    destino_id: locals[2].id,
    color: '#3F51B5',
    nf_url: 'blablabla',
    preco_base: 88,
    classes: [
      {
        id: '23Gdas',
        grupo_id: 555513,
        closed: false,
        preco_rodoviaria: 202,
        max_split_value: 62,
        ref_split_value: 62,
        vagas: 2,
        current_split_value: 126,
        tipo_assento: 'leito cama',
        tipo_veiculo: 'DD',
        layout: busMap[1]
      }
    ],
    company_id: 44,
    company_name: 'Cometinha',
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    modelo_venda: 'marketplace',
    company_logo_url:
      'https://www.cometinha.com.br/wp-content/uploads/2019/07/logo-cometinha.png',
    rotina_onibus_id: 15,
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    cancellation_deadline_hours: 3,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [11]
    id: 'gf254s',
    ranking: {
      heuristic_em_alta_v1: 7.0,
      searchrank_memory_based_v1: 0.07
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[4].slug}:${locals[2].slug}`,
    local_retirada: {
      tipo: 'guiche',
      descricao:
        'no guichê da Expresso, localizada no box 19. PS: A retirada deve ser feita até as 21h, após esse horário o guichê estará fechado'
    },
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: null,
    itinerario: getItinerarioFrom(4, 2),
    trecho_id: 55510,
    datetime_ida: dayjs().add(23, 'minutes').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(80, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: *********,
    status: 'done',
    problem_description:
      'Furou o pneu, o onibus capotou, ninguém se feriu, mas o ônibus está inutilizável, afinal de contas ele explodiu quando capotou, sad story mas é o que aconteceu, pois é né.',
    ressarcido: true,
    ressarcimento_factor: 0.5,
    ressarcimento_reason:
      'Ressarcimos 50% do valor do rateio por causa do atraso de 10 min.',
    origem: locals[4],
    origem_id: locals[4].id,
    destino: locals[2],
    destino_id: locals[2].id,
    color: '#3F51B5',
    nf_url: 'blablabla',
    preco_base: 100,
    classes: [
      {
        id: 'gf254s',
        grupo_id: 55510,
        closed: true,
        preco_rodoviaria: 202,
        max_split_value: 62,
        ref_split_value: 62,
        vagas: 2,
        current_split_value: 126,
        tipo_assento: 'leito cama',
        tipo_veiculo: 'DD',
        layout: busMap[1]
      }
    ],
    parcelamento: {
      valor_por_parcela: 20.30,
      quantidade_de_parcelas: 3
    },
    company_id: 44,
    company_name: 'Cometinha',
    company_rating: 4.2,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    modelo_venda: 'marketplace',
    company_logo_url:
      'https://www.cometinha.com.br/wp-content/uploads/2019/07/logo-cometinha.png',
    rotina_onibus_id: 15,
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    cancellation_deadline_hours: 3,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [12]
    id: '3rfbh',
    ranking: {
      heuristic_em_alta_v1: 6.0,
      searchrank_memory_based_v1: 0.06
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[13].slug}:${locals[14].slug}`,
    local_retirada: {
      tipo: 'guiche',
      descricao:
        'no guichê da Expresso, localizada no box 19. PS: A retirada deve ser feita até as 21h, após esse horário o guichê estará fechado'
    },
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: null,
    itinerario: getItinerarioFrom(13, 14),
    trecho_id: 55510,
    datetime_ida: dayjs().add(23, 'minutes').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(80, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: *********,
    status: 'going',
    problem_description:
      'Furou o pneu, o onibus capotou, ninguém se feriu, mas o ônibus está inutilizável, afinal de contas ele explodiu quando capotou, sad story mas é o que aconteceu, pois é né.',
    ressarcido: true,
    ressarcimento_factor: 0.5,
    ressarcimento_reason:
      'Ressarcimos 50% do valor do rateio por causa do atraso de 10 min.',
    origem: locals[13],
    origem_id: locals[13].id,
    destino: locals[14],
    destino_id: locals[14].id,
    color: '#3F51B5',
    nf_url: 'blablabla',
    preco_base: 62,
    classes: [
      {
        id: '3rfbh',
        grupo_id: 5511,
        closed: true,
        preco_rodoviaria: 202,
        max_split_value: 62,
        ref_split_value: 62,
        vagas: 2,
        current_split_value: 126,
        tipo_assento: 'leito cama',
        tipo_veiculo: 'DD',
        layout: busMap[1]
      }
    ],
    company_id: 67,
    company_name: 'Jundiá',
    company_rating: 4.2,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    modelo_venda: 'marketplace',
    company_logo_url:
      'https://www.cometinha.com.br/wp-content/uploads/2019/07/logo-cometinha.png',
    rotina_onibus_id: 15,
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    cancellation_deadline_hours: 3,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [13]
    id: 'jghtj6',
    ranking: {
      heuristic_em_alta_v1: 5.0,
      searchrank_memory_based_v1: 0.05
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[1].slug}:${locals[0].slug}`,
    itinerario: getItinerarioFrom(1, 0),
    trecho_id: 33332,
    embarque_status: 'ULTIMA_CHAMADA',
    datetime_ida: dayjs().add(-24, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(-1, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: 82800000,
    status: 'travel_confirmed',
    foto_onibus: 'https://www.islabus.com.br/imagens-midia/informacoes/thumbs/fretamento-onibus-turismo-sp-01.webp',
    company_pos_bo: 'Buser',
    onibus_pos_bo: 'ABCDE',
    tipo_pos_bo: 'Ônibus leito',
    origem: locals[1],
    origem_id: locals[1].id,
    destino: locals[0],
    destino_id: locals[0].id,
    tipo_assento: 'executivo',
    tipo_veiculo: 'micro-onibus',
    confirming_probability: 'high',
    onibus_plotado: false,
    is_blackfriday_bucket: false,
    travel_log: {
      created_at: '2018-12-17T17:59:57.200328+00:00',
      updated_at: '2018-12-17T18:00:11.504512+00:00',
      id: 4,
      status: 'in_route'
    },
    problem_description:
      'Furou o pneu, o onibus capotou, ninguém se feriu, mas o ônibus está inutilizável, afinal de contas ele explodiu quando capotou, sad story mas é o que aconteceu, pois é né.',
    color: '#F4511E',
    preco_base: 110,
    classes: [
      {
        id: 'jghtj6',
        grupo_id: 3333,
        closed: false,
        preco_rodoviaria: 168,
        max_split_value: 110,
        ref_split_value: 110,
        vagas: 30,
        current_split_value: 73.94,
        layout: busMap[0],
        trecho_alternativo: false
      }
    ],
    onibus: {
      cadeirinhas: 4,
      count_groups: 5,
      classe: 'ricardo / sampaio',
      capacidade: '25 / 25',
      next_group: null,
      id: 2,
      name: 'Sampaio Ricardo',
      tipo: 'DD',
      company: {
        id: 6,
        bank_account: null,
        antt_enabled: false,
        payment_option: '',
        is_enabled: true,
        name: 'Carreta Furacão',
        cnpj: ''
      },
      last_group: {
        vagas: 2,
        checkin_status: 'pending',
        origem: locals[3],
        origem_id: locals[3].id,
        destino: locals[2],
        destino_id: locals[2].id,
        trecho_id: 25,
        confirming_probability: 'very_low',
        status: 'travel_confirmed',
        id: 376,
        duracao_volta: null,
        chegada_volta: null,
        duracao_ida: ********,
        datetime_ida: dayjs()
          .add(2, 'hours')
          .format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
        chegada_ida: dayjs().add(13, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ')
      },
      placa: 'RIC1234'
    },
    company_id: 42,
    company_name: 'Raid',
    company_rating: 4.2,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    onibus_placa: 'WTF5678',
    driver_name: 'Agite Antes de Usar',
    modelo_venda: 'buser',
    company_logo_url:
      'https://i.imgur.com/y9sTq12.jpg',
    rotina_onibus_id: 15,
    cancellation_deadline_hours: 1,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [14]
    id: '6uyjdw',
    ranking: {
      heuristic_em_alta_v1: 4.0,
      searchrank_memory_based_v1: 0.04
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[10].slug}:${locals[3].slug}`,
    itinerario: getItinerarioFrom(10, 3),
    trecho_id: 33333,
    embarque_status: 'ULTIMA_CHAMADA',
    datetime_ida: dayjs().add(-24, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(-1, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: 82800000,
    status: 'travel_confirmed',
    foto_onibus: 'https://www.islabus.com.br/imagens-midia/informacoes/thumbs/fretamento-onibus-turismo-sp-01.webp',
    company_pos_bo: 'Buser',
    onibus_pos_bo: 'ABCDE',
    tipo_pos_bo: 'Ônibus leito',
    origem: locals[10],
    origem_id: locals[10].id,
    destino: locals[3],
    destino_id: locals[3].id,
    tipo_assento: 'executivo',
    tipo_veiculo: 'micro-onibus',
    confirming_probability: 'high',
    onibus_plotado: false,
    is_blackfriday_bucket: false,
    travel_log: {
      created_at: '2018-12-17T17:59:57.200328+00:00',
      updated_at: '2018-12-17T18:00:11.504512+00:00',
      id: 4,
      status: 'in_route'
    },
    problem_description:
      'Furou o pneu, o onibus capotou, ninguém se feriu, mas o ônibus está inutilizável, afinal de contas ele explodiu quando capotou, sad story mas é o que aconteceu, pois é né.',
    color: '#F4511E',
    preco_base: 110,
    classes: [
      {
        id: '6uyjdw',
        grupo_id: 3334,
        closed: false,
        preco_rodoviaria: 168,
        max_split_value: 110,
        ref_split_value: 110,
        vagas: 30,
        current_split_value: 73.94,
        layout: busMap[0],
        trecho_alternativo: false
      }
    ],
    parcelamento: {
      valor_por_parcela: 36.50,
      quantidade_de_parcelas: 3
    },
    onibus: {
      cadeirinhas: 4,
      count_groups: 5,
      classe: 'ricardo / sampaio',
      capacidade: '25 / 25',
      next_group: null,
      id: 2,
      name: 'Sampaio Ricardo',
      tipo: 'DD',
      company: {
        id: 6,
        bank_account: null,
        antt_enabled: false,
        payment_option: '',
        is_enabled: true,
        name: 'Carreta Furacão',
        cnpj: ''
      },
      last_group: {
        vagas: 2,
        checkin_status: 'pending',
        origem: locals[10],
        origem_id: locals[10].id,
        destino: locals[3],
        destino_id: locals[3].id,
        trecho_id: 25,
        confirming_probability: 'very_low',
        status: 'travel_confirmed',
        id: 376,
        duracao_volta: null,
        chegada_volta: null,
        duracao_ida: ********,
        datetime_ida: dayjs()
          .add(2, 'hours')
          .format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
        chegada_ida: dayjs().add(13, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ')
      },
      placa: 'RIC1234'
    },
    company_id: 42,
    company_rating: 4.2,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    onibus_placa: 'WTF5678',
    driver_name: 'Agite Antes de Usar',
    modelo_venda: 'hibrido',
    parent_company_name: 'Transbrasil',
    parent_company_logo_url: 'https://i.imgur.com/y9sTq12.jpg',
    company_name: 'Raid',
    company_logo_url:
      'https://i.imgur.com/y9sTq12.jpg',
    rotina_onibus_id: 15,
    cancellation_deadline_hours: 1,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [15]
    id: '356uhj',
    ranking: {
      heuristic_em_alta_v1: 3.0,
      searchrank_memory_based_v1: 0.03
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[17].slug}:${locals[11].slug}`,
    local_retirada: {
      tipo: 'guiche',
      descricao:
        'no guichê da Expresso, localizada no box 19. PS: A retirada deve ser feita até as 21h, após esse horário o guichê estará fechado'
    },
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: null,
    itinerario: getItinerarioFrom(17, 11),
    trecho_id: 7777,
    datetime_ida: dayjs().add(14, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(27, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: 46800000,
    status: 'done',
    problem_description:
      'Furou o pneu, o onibus capotou, ninguém se feriu, mas o ônibus está inutilizável, afinal de contas ele explodiu quando capotou, sad story mas é o que aconteceu, pois é né.',
    ressarcido: true,
    ressarcimento_factor: 0.5,
    ressarcimento_reason:
      'Ressarcimos 50% do valor do rateio por causa do atraso de 10 min.',
    origem: locals[17],
    origem_id: locals[17].id,
    destino: locals[11],
    destino_id: locals[11].id,
    color: '#3F51B5',
    nf_url: 'blablabla',
    preco_base: 62,
    classes: [
      {
        id: '356uhj',
        grupo_id: 777714,
        closed: false,
        preco_rodoviaria: 202,
        max_split_value: 62,
        ref_split_value: 62,
        vagas: 2,
        current_split_value: 126,
        tipo_assento: 'leito cama',
        tipo_veiculo: 'DD',
        layout: busMap[1]
      },
      {
        id: 777720,
        grupo_id: 777720,
        closed: false,
        preco_rodoviaria: 202,
        max_split_value: 62,
        ref_split_value: 62,
        vagas: 2,
        current_split_value: 126,
        tipo_assento: 'leito individual',
        tipo_veiculo: 'DD',
        layout: busMap[1]
      }
    ],
    parcelamento: {
      valor_por_parcela: 21,
      quantidade_de_parcelas: 3
    },
    company_id: 44,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    modelo_venda: 'hibrido',
    parent_company_name: 'Transbrasil',
    parent_company_logo_url: 'https://i.imgur.com/y9sTq12.jpg',
    company_name: 'Cometinha',
    company_logo_url:
      'https://www.cometinha.com.br/wp-content/uploads/2019/07/logo-cometinha.png',
    rotina_onibus_id: 15,
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    cancellation_deadline_hours: 3,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [16]
    id: 'w546u3',
    ranking: {
      heuristic_em_alta_v1: 2.0,
      searchrank_memory_based_v1: 0.02
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[17].slug}:${locals[11].slug}`,
    local_retirada: {
      tipo: 'guiche',
      descricao:
        'no guichê da Expresso, localizada no box 19. PS: A retirada deve ser feita até as 21h, após esse horário o guichê estará fechado'
    },
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: null,
    itinerario: getItinerarioFrom(17, 11),
    trecho_id: 7777,
    datetime_ida: dayjs().add(10, 'minutes').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(2, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: 7200,
    status: 'done',
    problem_description:
      'Furou o pneu, o onibus capotou, ninguém se feriu, mas o ônibus está inutilizável, afinal de contas ele explodiu quando capotou, sad story mas é o que aconteceu, pois é né.',
    ressarcido: true,
    ressarcimento_factor: 0.5,
    ressarcimento_reason:
      'Ressarcimos 50% do valor do rateio por causa do atraso de 10 min.',
    origem: locals[17],
    origem_id: locals[17].id,
    destino: locals[11],
    destino_id: locals[11].id,
    color: '#3F51B5',
    nf_url: 'blablabla',
    preco_base: 62,
    classes: [
      {
        id: 'w546u3',
        grupo_id: 777715,
        closed: false,
        preco_rodoviaria: 202,
        max_split_value: 62,
        ref_split_value: 62,
        vagas: 2,
        current_split_value: 126,
        tipo_assento: 'leito cama',
        tipo_veiculo: 'DD',
        layout: busMap[1]
      }
    ],
    parcelamento: {
      valor_por_parcela: 21,
      quantidade_de_parcelas: 3
    },
    company_id: 44,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    modelo_venda: 'buser',
    parent_company_name: 'Transbrasil',
    parent_company_logo_url: 'https://i.imgur.com/y9sTq12.jpg',
    company_name: 'Cometinha',
    company_logo_url:
      'https://www.cometinha.com.br/wp-content/uploads/2019/07/logo-cometinha.png',
    rotina_onibus_id: 15,
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    cancellation_deadline_hours: 3,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  },
  {
    // [17] - Grupo que vai pra Argentina
    id: '2345kj',
    ranking: {
      heuristic_em_alta_v1: 1.0,
      searchrank_memory_based_v1: 0.01
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[18].slug}:${locals[16].slug}`,
    local_retirada: {
      tipo: 'guiche',
      descricao:
        'no guichê da Expresso, localizada no box 19. PS: A retirada deve ser feita até as 21h, após esse horário o guichê estará fechado'
    },
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: null,
    itinerario: getItinerarioFrom(18, 16),
    trecho_id: 20222,
    datetime_ida: dayjs().add(10, 'minutes').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(2, 'days').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: *********,
    status: 'pending',
    problem_description: null,
    ressarcido: true,
    ressarcimento_factor: 0.5,
    ressarcimento_reason:
      'Ressarcimos 50% do valor do rateio por causa do atraso de 10 min.',
    origem: locals[18],
    origem_id: locals[18].id,
    destino: locals[16],
    destino_id: locals[16].id,
    color: '#3F51B5',
    nf_url: 'blablabla',
    preco_base: 1200,
    classes: [
      {
        id: '2345kj',
        grupo_id: 2022,
        closed: false,
        preco_rodoviaria: 1000,
        max_split_value: 620,
        ref_split_value: 620,
        vagas: 2,
        current_split_value: 700,
        tipo_assento: 'leito cama',
        tipo_veiculo: 'DD',
        layout: busMap[1]
      },
      {
        id: '34gtsdf',
        grupo_id: 2022,
        closed: false,
        preco_rodoviaria: 1000,
        max_split_value: 620,
        ref_split_value: 620,
        vagas: 10,
        current_split_value: 700,
        tipo_assento: 'cama premium individual',
        tipo_veiculo: 'DD',
        layout: busMap[1]
      },
      {
        id: 2053,
        grupo_id: 2022,
        closed: false,
        preco_rodoviaria: 1000,
        max_split_value: 620,
        ref_split_value: 620,
        vagas: 0,
        current_split_value: 700,
        tipo_assento: 'leito cama individual',
        tipo_veiculo: 'DD',
        layout: busMap[1]
      }
    ],
    parcelamento: {
      valor_por_parcela: 216,
      quantidade_de_parcelas: 3
    },
    company_id: 44,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    modelo_venda: 'marketplace',
    company_name: 'Boca Juniors',
    company_logo_url:
      'https://a.espncdn.com/combiner/i?img=/i/teamlogos/soccer/500/5.png',
    rotina_onibus_id: 15,
    onibus_features: [
      'acessibilidade_deficientes',
      'wifi',
      'tomadas_coletivas'
    ],
    cancellation_deadline_hours: 3,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    operacao_configs: {
      modelo_atendimento: 'buser'
    },
    modelo_operacao: MODELOS_OPERACAO.POLTRONAS_ANTECIPADAS,
    extra_mkp_ota_config_id: 1,
    extra_mkp_stopovers: [{ IdViagem: 13829 }, { IdViagem: 13830 }],
    extra_mkp_servico: 'some-unique-code-for-marketplace-travel',
    extra_mkp_extra: { IdViagem: 12345 }
  },
  {
    // [18] grupos pos 00h
    id: '6ikjfg',
    ranking: {
      heuristic_em_alta_v1: 19,
      searchrank_memory_based_v1: 0.19
    },
    grupo_classe_id: '46BBF01',
    trecho: `${locals[0].slug}:${locals[3].slug}`,
    trecho_id: 19,
    itinerario: getItinerarioFrom(0, 3),
    datetime_ida: dayjs(dayjs().format('YYYY-MM-DD')).add(25, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    chegada_ida: dayjs().add(27, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
    duracao_ida: 21600000,
    status: 'pending',
    origem: locals[0],
    origem_id: locals[0].id,
    destino: locals[3],
    destino_id: locals[3].id,
    company_pos_bo: null,
    onibus_pos_bo: null,
    tipo_pos_bo: null,
    embarque_status: 'EMBARQUE_INICIADO',
    foto_onibus: 'https://assets.volvo.com/is/image/VolvoInformationTechnologyAB/1860x1050-onibus-rodoviario-imagem-release?wid=720',
    confirming_probability: 'very_low',
    onibus_plotado: true,
    is_blackfriday_bucket: true,
    travel_log: {
      created_at: '2018-08-17T17:59:57.200328+00:00',
      updated_at: '2018-08-17T18:00:11.504512+00:00',
      // logurl: 'https://d1q5r6jf4yxfe1.cloudfront.net/travellogs/2018/08/travellog-20180817175957-3-12981804888-U5EWWT4.json',
      logurl: '/sampletravellog.json',
      id: 19,
      status: 'pending'
    },
    color: '#AD1457',
    preco_base: 200,
    classes: [
      {
        id: '6ikjfg',
        grupo_id: 19,
        closed: false,
        preco_rodoviaria: 166.18,
        max_split_value: 120,
        ref_split_value: 120,
        // promocao: {
        //   promotional_value: 0,
        //   value: 20,
        //   description: 'Desconto 30% Feriadão'
        // },
        destaque: true,
        vagas: 5,
        layout: busMap[0],
        tipo_assento: 'executivo',
        tipo_veiculo: 'DD'
      }
    ],
    parcelamento: {
      valor_por_parcela: 40,
      quantidade_de_parcelas: 3
    },
    onibus: {
      count_groups: 5,
      classe: 'ricardo / sampaio',
      capacidade: '25 / 25',
      next_group: null,
      id: 2,
      name: 'Sampaio Ricardo',
      tipo: 'DD',
      company: {
        id: 6,
        bank_account: null,
        antt_enabled: false,
        payment_option: '',
        is_enabled: true,
        name: 'Uma bela empresa',
        cnpj: ''
      },
      last_group: {
        vagas: 2,
        checkin_status: 'pending',
        origem: locals[0],
        origem_id: locals[0],
        trecho_id: 25,
        confirming_probability: 'very_low',
        status: 'done',
        destino: locals[1],
        destino_id: locals[1].id,
        id: 376,
        duracao_volta: null,
        chegada_volta: null,
        duracao_ida: ********,
        datetime_ida: dayjs()
          .add(1, 'hours')
          .format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
        chegada_ida: dayjs().add(73, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSSZ')
      },
      placa: 'RIC1234'
    },
    company_id: 42,
    company_rating: 4.2,
    company_name: 'Uma bela empresa',
    company_logo_url:
      'https://i.imgur.com/y9sTq12.jpg',
    rotina_onibus_id: 15,
    company_operational_phone: '**********',
    company_whatsapp_phone: '***********',
    company_email: '<EMAIL>',
    onibus_placa: 'OIE1234',
    onibus_features: ['adaptado contra covid'],
    driver_name: 'Embalagem Econômica',
    modelo_venda: 'buser',
    cancellation_deadline_hours: 3,
    trecho_alternativo: false,
    has_marcacao_assento: false,
    extra_mkp_stopovers: null,
    extra_mkp_servico: null,
    extra_mkp_extra: null
  }
]
