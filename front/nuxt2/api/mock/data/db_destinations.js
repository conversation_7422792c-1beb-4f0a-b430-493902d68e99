import { cities } from './db_places.js'

const hidrateDestination = (city) => {
  return {
    ...city,
    best_price: Math.random() * (200 - 50) + 50, // 50-200
    max_discount: Math.random() * (0.5 - 0) // 0-50%
  }
}

const addRandomTag = (city) => {
  return {
    ...city,
    // esse campo não exite no back, serve só pra filtrar no mock
    _tag: ['destinos no sul', 'praia', 'bate-volta', 'ferias', 'menor-preco'][Math.floor(Math.random() * 5)]
  }
}

const taggedCities = cities.map(addRandomTag)

export const getDestinations = (citySlug, tag, { itemsPerPage, page }) => {
  const originCity = taggedCities.find(c => c.slug === citySlug)
  const pageStart = itemsPerPage * (page - 1)
  const destinationCities = taggedCities.filter(c => c.slug !== originCity && tag ? c._tag === tag : true).map(hidrateDestination)

  return {
    originCity,
    destinationCities: destinationCities.slice(pageStart, pageStart + itemsPerPage),
    hasMoreDestinationCities: destinationCities.length > itemsPerPage,
    totalDestinations: destinationCities?.length
  }
}
