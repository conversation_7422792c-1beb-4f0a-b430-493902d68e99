import type { RedirectOption } from 'vue-router'
// Para definir o squad responsável no Sentry, é preciso adicionar a propriedade squad ao meta.
// Este valor será enviado para o Sentry, em uma tag chamada 'squad',
// Para mapear a tag/squad no sentry, é preciso criar uma regra em Issue Owners,
// Mapeando o valor definido aqui no squad para a tag, e o team responsável.
// https://sentry.io/settings/buser/projects/buser-front/ownership/

type RouteMetaTaskbar = Partial<{
  hide: boolean, // Esconde taskbar e padding superior
  custom: boolean, // Esconde a taskbar mas deixa o padding superior
  title: string,
  transparent: boolean,
  onlyEssentialLinks: boolean,
  onlyCheckoutLinks: boolean,
  hideNotLoggedInMenu: boolean,
}>

type RouteMeta = Partial<{
  squad: string,
  taskbar: RouteMetaTaskbar,
  darkMode: boolean
  hideFooter: boolean,
  hideNavBottom: boolean,
  hideOnScrollDown: boolean,
  prefetch: Array<string | { name: string }>,
  keepTravel: boolean,
  isPromo: boolean,
  showItineraryMap: boolean,
  redirectNuxt3ByMeta: boolean,
  redirectNuxt3ByCookie?: string
}>

type RouteItem = {
  path: string,
  component?: string,
  components?: Object,
  alias?: string,
  name?: string,
  props?: boolean | Object,
  children?: Array<RouteItem>,
  redirect?: RedirectOption,
  meta?: RouteMeta
}

// temporário para rotas do nuxt2 até migração 100% para nuxt3
const nuxt3Migrated = { redirectNuxt3ByMeta: true }

const meta: Record<string, RouteMeta> = {
  // Conversão
  home: { squad: 'conversao', taskbar: { hide: true } },
  login: { taskbar: { hideNotLoggedInMenu: true }, hideFooter: true, hideNavBottom: true, squad: 'conversao' },
  verificarCadastro: { hideFooter: true, squad: 'conversao' },
  checkout: { taskbar: { hide: true, onlyCheckoutLinks: true }, hideFooter: true, hideNavBottom: true, squad: 'conversao' },
  checkoutAddon: { taskbar: { title: 'Finalizar compra', onlyCheckoutLinks: true }, hideFooter: true, hideNavBottom: true, squad: 'conversao' },
  pagamentoDivida: { taskbar: { onlyEssentialLinks: true }, hideFooter: true, hideNavBottom: true, squad: 'conversao' },
  destinoInverno: { squad: 'conversao' },
  urlPromocionalSP: { squad: 'conversao' },
  promo: { squad: 'conversao' },
  rotas: { squad: 'conversao' },
  searchResult: { taskbar: { custom: true }, hideNavBottom: true, squad: 'conversao', showItineraryMap: true },
  searchFestival: { taskbar: { custom: true }, hideNavBottom: true, squad: 'conversao' },
  searchDestination: { taskbar: { custom: true }, squad: 'conversao' },
  searchOrigin: { taskbar: { custom: true }, squad: 'conversao' },
  novaBusca: { taskbar: { title: 'Buscar viagem' }, hideFooter: true, hideNavBottom: true, squad: 'conversao' },
  blackFriday: { squad: 'conversao', taskbar: { custom: true } },
  aniversario: { hideNavBottom: true, squad: 'conversao' },
  feriado: { squad: 'conversao' },
  feriados: { squad: 'conversao' },
  promoDestination: { squad: 'aquisicao', isPromo: true },
  descubra: { taskbar: { title: 'Descubra' }, squad: 'aquisicao' },
  firstLead: { squad: 'conversao', hideFooter: true, hideNavBottom: true },

  // PVV
  profile: { taskbar: { title: 'Perfil' }, hideFooter: true, squad: 'pvv' },
  acompanharViagem: { hideFooter: true, squad: 'pvv' },
  alterarReserva: { hideFooter: true, squad: 'pvv', showItineraryMap: true },
  viagens: { taskbar: { title: 'Viagens' }, hideFooter: true, squad: 'pvv' },
  viagem: { taskbar: { title: 'Detalhes da viagem' }, hideFooter: true, squad: 'pvv' },
  reservaConfirmada: { taskbar: { onlyEssentialLinks: true }, hideFooter: true, hideOnScrollDown: true, squad: 'conversao' },
  reservaConfirmadaDeslogado: { hideFooter: true, hideOnScrollDown: true, squad: 'pvv' },
  retrospectiva: { hideFooter: true, hideOnScrollDown: true, hideNavBottom: true, taskbar: { hide: true } },
  mgmListing: { squad: 'pvv', hideFooter: true, hideNavBottom: true },
  parceria: { squad: 'conversao', showItineraryMap: true },
  // Aquisição
  excluirDados: { hideFooter: true, squad: 'aquisicao' },

  // Marketplace
  bpe: { hideFooter: true, squad: 'marketplace' },

  // Falta definir
  motoristas: { taskbar: { hide: true }, hideNavBottom: true },
  parceiros: { taskbar: { hide: true }, hideNavBottom: true },
  conheca: { taskbar: { hide: true }, hideNavBottom: true },
  error: { taskbar: { hideNotLoggedInMenu: true }, hideFooter: true }
}

const routes: Array<RouteItem> = [
  // Redirecionamentos com prioridade
  { path: '/convite/:invitecode', redirect: '/auth' },
  { path: '/fuiconvidado/:token', redirect: '/auth' },
  { path: '/reserva/viagens/proximas', redirect: { name: 'viagens', query: { status: 'next' } } },
  { path: '/perfil/viagens/finalizadas', redirect: { name: 'viagens', query: { status: 'done' } } },
  { path: '/perfil/viagens/canceladas', redirect: { name: 'viagens', query: { status: 'canceled' } } },

  // Ressarcimento automatico
  { path: '/reembolso', name: 'ressarcimentoAuto', component: 'ressarcimento-automatico' },

  // parcerias
  { path: '/parceria/:nickname/:origem?/:destino?', name: 'parceria', component: 'parceria/parceria', meta: meta.parceria },
  // URLs Promocionais
  { path: '/onibus/sao-paulo-sp/ate-50-reais', name: 'urlPromocionalSP', component: 'url-promocional-sp', meta: meta.urlPromocionalSP },

  // Home
  { path: '/', name: 'home', component: 'home/home', meta: { ...meta.home, prefetch: [{ name: 'searchPageV1' }, { name: 'searchOriginPageV1' }] } },

  // Viagens e Checkout
  { path: '/onibus', name: 'rotas', component: 'rotas', meta: meta.rotas },
  { path: '/onibus/para/:destino', name: 'searchDestinationPageV1', component: 'search/search', meta: meta.searchDestination },
  { path: '/onibus/de/descubra/:cidadeSlug?', name: 'descubra', component: 'descubra/descubra', meta: meta.descubra, props: true },
  {
    path: '/descubra/:cidadeSlug?',
    redirect: (to) => {
      const { params, query } = to
      return { name: 'descubra', params: { cidadeSlug: params.cidadeSlug }, query }
    }
  },
  { path: '/onibus/festival/:slugFestival/:origem?/:destino?', name: 'searchPageFestival', component: 'search-result-festival', meta: { ...meta.searchFestival, prefetch: [{ name: 'checkout' }] } },
  { path: '/onibus/promo/de/:origem?', name: 'promoOrigin', component: 'noop', meta: nuxt3Migrated },
  { path: '/onibus/promo/para/:destino', name: 'promoDestination', component: 'promo-destination/promo-destination', meta: meta.promoDestination, props: true },
  { path: '/onibus/:origem/:destino', name: 'searchPageV1', component: 'search/search', meta: { ...meta.searchResult, prefetch: [{ name: 'checkout' }] } },
  { path: '/onibus/:origem', name: 'searchOriginPageV1', component: 'search/search', meta: { ...meta.searchOrigin, prefetch: [{ name: 'checkout' }] } },
  { path: '/onibus/:weekDay/:origem/:destino', name: 'searchPageWeekDay', component: 'search/search', meta: meta.searchResult },
  { path: '/reserva/grupos/:idIda/:idVolta?', name: 'checkout', component: 'checkout/checkout-page', meta: meta.checkout },
  { path: '/reserva/bpe/:reservationCode', name: 'bpe', component: 'bpe/bpe', meta: meta.bpe, props: true },
  { path: '/reserva/bpe-status/:passagemRodoviariaId', name: 'bpeEmContingencia', component: 'bpe-em-contingencia/bpe-em-contingencia', meta: meta.bpe, props: true },
  { path: '/reserva/viagens/:id/alterar/:step?', name: 'alterarReserva', component: 'alterar-reserva/alterar-reserva', meta: meta.alterarReserva, props: true },
  { path: '/reserva/:status/:travelIdaId/:travelVoltaId?', name: 'reserva', component: 'reserva/reserva', meta: meta.reservaConfirmada },
  { path: '/link-pagamento/:code', name: 'linkPagamento', component: 'link-pagamento/link-pagamento' },
  { path: '/pagamento-pendente', name: 'pagamentoDivida', component: 'pagamento-divida/pagamento-divida', meta: meta.pagamentoDivida },
  { path: '/nova-busca/:origem?/:destino?', name: 'novaBusca', component: 'nova-busca/nova-busca', meta: meta.novaBusca },
  { path: '/checkout/addon/:travel', name: 'checkoutAddon', component: 'checkout-addon/checkout-addon-page', meta: meta.checkoutAddon },
  // Promoções & Aquisições
  { path: '/festival/:slugFestival?', name: 'festival', component: 'noop', meta: nuxt3Migrated },
  { path: '/promo', name: 'promo-novo', component: 'promo', meta: meta.promo },
  { path: '/black-friday/:origem?/:destino?', name: 'blackFriday', component: 'black-friday/black-friday', meta: meta.blackFriday, props: true },
  { path: '/aniversario/:origem?/:destino?', name: 'aniversarioBuser', component: 'aniversario/aniversario', meta: meta.aniversario, props: true },
  { path: '/feriado/:origem?', name: 'feriado', component: 'feriado/feriado', meta: meta.feriado, props: true },
  { path: '/feriados/:code', name: 'feriados', component: 'noop', meta: nuxt3Migrated },
  { path: '/promo/:code', name: 'promocaoCode-novo', component: 'promo', meta: meta.promo },
  { path: '/first-lead-page', name: 'firstLeadPage', component: 'first-lead-page/first-lead-page', meta: meta.firstLead }, // TODO: Definir meta de squad
  { path: '/convite', name: 'mgmLeadPage', component: 'noop', meta: nuxt3Migrated },
  { path: '/conheca/:code?', name: 'conheca', component: 'conheca', meta: meta.conheca },
  { path: '/destinos-de-inverno', name: 'destinosInverno', component: 'destinos-inverno', meta: meta.destinoInverno },
  {
    path: '/fidelidade',
    component: 'noop',
    meta: nuxt3Migrated,
    children: [
      { path: '', name: 'fidelidade', component: 'noop' },
      { path: ':id', name: 'detalhesFidelidade', component: 'noop' }
    ]
  },

  // Suporte
  { path: '/chat', name: 'chat', component: 'noop', meta: nuxt3Migrated }, // TODO: Definir meta de squad
  { path: '/chat-renderer-mobile-version', name: 'chat-renderer', component: 'noop', meta: nuxt3Migrated }, // TODO: Definir meta de squad
  {
    path: '/ajuda',
    component: 'noop',
    meta: nuxt3Migrated,
    children: [
      {
        name: 'ajuda',
        path: '',
        component: 'noop'
      },
      {
        name: 'ajudaTopic',
        path: ':topicSlug',
        component: 'noop'
      },
      {
        name: 'ajudaQuestion',
        path: ':topicSlug/:questionSlug',
        component: 'noop'
      }
    ]
  },
  { path: '/fale-conosco', name: 'faleConosco', component: 'noop', meta: nuxt3Migrated },

  // Autenticação
  { path: '/auth/confirmar-telefone/:origin/:token', name: 'confirmarTelefoneViaLink', component: 'noop', meta: nuxt3Migrated }, // TODO: Definir meta de squad
  { path: '/auth/redefinir-senha', name: 'redefinirSenha', component: 'noop', meta: nuxt3Migrated }, // TODO: Definir meta de squad
  { path: '/auth/:step?/:origin?', name: 'login', component: 'auth/login/login', props: true, meta: meta.login },

  // Institucional
  { path: '/sul', name: 'sobreSul', component: 'noop', meta: nuxt3Migrated }, // TODO: Definir meta de squad
  { path: '/sobre', name: 'sobre', component: 'noop', meta: nuxt3Migrated }, // TODO: Definir meta de squad
  { path: '/pix', name: 'pix', component: 'noop', meta: nuxt3Migrated },
  { path: '/imprensa', name: 'imprensa', component: 'noop', meta: nuxt3Migrated }, // TODO: Definir meta de squad
  { path: '/empresas', name: 'empresas', component: 'noop', meta: nuxt3Migrated }, // TODO: Definir meta de squad
  { path: '/empresas/:empresaSlug', name: 'empresa', component: 'noop', meta: nuxt3Migrated }, // TODO: Definir meta de squad
  { path: '/whatsapp', name: 'whatsapp', component: 'noop', meta: nuxt3Migrated }, // TODO: Definir meta de squad
  { path: '/noticias', name: 'noticias', component: 'noop', meta: nuxt3Migrated }, // TODO: Definir meta de squad
  { path: '/carreiras', name: 'carreiras', component: 'noop', meta: nuxt3Migrated },
  { path: '/motoristas', name: 'motoristas', component: 'noop', meta: nuxt3Migrated },
  { path: '/parceiros', name: 'parceiros', component: 'noop', meta: nuxt3Migrated },
  { path: '/sobre/sustentabilidade', name: 'sustentabilidade', component: 'noop', meta: nuxt3Migrated }, // TODO: Definir meta de squad
  { path: '/sobre/:textSlug', name: 'termosEPoliticas', component: 'noop', meta: nuxt3Migrated }, // TODO: Definir meta de squad

  // Conteúdo & SEO
  // TODO: Definir meta de squad de todos abaixo
  { path: '/destinos', name: 'destinos', component: 'noop', meta: nuxt3Migrated },
  { path: '/destinos/pontos-turisticos', name: 'pontosTuristicos', component: 'pontos-turisticos' },
  { path: '/destinos/pontos-turisticos/:uf([a-z][a-z])', name: 'pontosTuristicosEstado', component: 'pontos-turisticos-uf' },
  { path: '/destinos/pontos-turisticos/:uf([a-z][a-z])/:cidadeSlug', name: 'pontosTuristicosCidade', component: 'pontos-turisticos-cidade/pontos-turisticos-cidade' },
  { path: '/destinos/pontos-turisticos/:uf([a-z][a-z])/:cidadeSlug/todos', name: 'pontosTuristicosCidadeTodos', component: 'pontos-turisticos-cidade-todos/pontos-turisticos-cidade-todos' },
  { path: '/destinos/pontos-turisticos/:uf([a-z][a-z])/:cidadeSlug/:pontoSlug', name: 'pontoTuristico', component: 'ponto-turistico' },
  { path: '/destinos/pontos-turisticos/:categorySlug', name: 'pontosTuristicosCategoriaNacional', component: 'pontos-turisticos-categoria-nacional/pontos-turisticos-categoria-nacional' },
  { path: '/destinos/pontos-turisticos/:categorySlug/:uf([a-z][a-z])', name: 'pontosTuristicosCategoriaEstado', component: 'pontos-turisticos-categoria-estado/pontos-turisticos-categoria-estado' },
  { path: '/destinos/pontos-turisticos/:categorySlug/:uf([a-z][a-z])/:cidadeSlug', name: 'pontosTuristicosCategoriaCidade', component: 'pontos-turisticos-categoria-cidade/pontos-turisticos-categoria-cidade' },
  { path: '/destinos/pontos-turisticos/:categorySlug/:subcategorySlug', name: 'pontosTuristicosSubcategoriaNacional', component: 'pontos-turisticos-categoria-nacional/pontos-turisticos-categoria-nacional' },
  { path: '/destinos/pontos-turisticos/:categorySlug/:subcategorySlug/:uf([a-z][a-z])', name: 'pontosTuristicosSubcategoriaEstado', component: 'pontos-turisticos-subcategoria-estado/pontos-turisticos-subcategoria-estado' },
  { path: '/destinos/pontos-turisticos/:categorySlug/:subcategorySlug/:uf([a-z][a-z])/:cidadeSlug', name: 'pontosTuristicosSubcategoriaCidade', component: 'pontos-turisticos-subcategoria-cidade/pontos-turisticos-subcategoria-cidade' },
  { path: '/destinos/:cidadeSlug', name: 'destino', component: 'destino' },
  { path: '/pontos', name: 'pontosDeEmbarque', component: 'noop', meta: nuxt3Migrated },
  { path: '/agencias-autorizadas', name: 'agenciasAutorizadas', component: 'noop', meta: nuxt3Migrated },
  { path: '/pontos/:uf/:cidadeSlug', name: 'pontosDeEmbarqueCidade', component: 'pontos-de-embarque-cidade' },
  { path: '/pontos/:uf/:cidadeSlug/:bairroSlug/:nicknameSlug', name: 'pontoDeEmbarque', component: 'ponto-de-embarque' },
  { path: '/glossario', name: 'glossario', component: 'noop', meta: nuxt3Migrated },
  { path: '/glossario/(.*)', name: 'glossarioItem', component: 'glossario/glossario-item' },

  // Área do usuário
  { path: '/perfil', name: 'profile', component: 'noop', meta: nuxt3Migrated },
  { path: '/perfil/opcoes', name: 'profileOptions', component: 'noop', meta: nuxt3Migrated },
  {
    path: '/perfil/editar',
    component: 'user/profile/edit-profile',
    children: [
      { path: '', name: 'editProfileDados', component: 'noop', meta: nuxt3Migrated },
      { path: 'marketing', name: 'editProfileMarketing', component: 'noop', meta: nuxt3Migrated }
    ]
  },
  { path: '/perfil/carteira', name: 'carteira', component: 'noop', meta: nuxt3Migrated },
  { path: '/perfil/formas-pagamento', name: 'formasPagamento', component: 'noop', meta: nuxt3Migrated },
  { path: '/perfil/notificacoes', name: 'notificacoes', component: 'noop', meta: nuxt3Migrated },
  { path: '/perfil/viajantes', name: 'viajantes', component: 'noop', meta: nuxt3Migrated },
  { path: '/perfil/compartilhamento-de-dados', name: 'manageLinkedUsers', component: 'noop', meta: nuxt3Migrated },
  { path: '/perfil/promocoes', name: 'promocoes', component: 'noop', meta: nuxt3Migrated },
  { path: '/perfil/sugerir-viagem', name: 'sugerirViagem', component: 'noop', meta: nuxt3Migrated },
  { path: '/perfil/viagens', name: 'viagens', component: 'user/viagens/viagens', meta: meta.viagens },
  { path: '/perfil/linked_user/:token', name: 'confirmLinkedUser', component: 'noop', meta: nuxt3Migrated },
  {
    path: '/perfil/viagens/:id',
    component: 'user/viagem/viagem',
    props: true,
    meta: { ...meta.viagem, keepTravel: true },
    children: [
      { path: '', name: 'viagem', components: { default: 'user/viagem/detalhes-viagem/detalhes-viagem', skeleton: 'user/viagem/detalhes-viagem/detalhes-viagem-skeleton' } },
      { path: 'nao-reconheco-essa-viagem', name: 'optOutTravel', component: 'noop', meta: nuxt3Migrated },
      { path: 'desvincular-travel', name: 'noop', meta: nuxt3Migrated },
      { path: 'alteracao-na-reserva', name: 'infoAlteracoes', component: 'noop', meta: nuxt3Migrated },
      { path: 'ajuda/perdi-um-item', name: 'perdiUmItem', component: 'noop', meta: nuxt3Migrated },
      { path: 'ajuda/problemas-com-a-bagagem', name: 'problemasComABagagem', component: 'noop', meta: nuxt3Migrated },
      { path: 'ajuda/nao-consegui-viajar', name: 'viagemNoShow', component: 'noop', meta: nuxt3Migrated },
      { path: 'ajuda/rodoviaria', name: 'ajudaMarketplace', component: 'noop', meta: nuxt3Migrated },
      { path: 'ajuda/rodoviaria/atraso-onibus', name: 'atrasoOnibusMarketplace', component: 'noop', meta: nuxt3Migrated },
      { path: 'ajuda/rodoviaria/cancelar-viagem', name: 'cancelarViagemMarketplace', component: 'noop', meta: nuxt3Migrated },
      { path: 'ajuda/rodoviaria/nao-consegui-viajar', name: 'naoConseguiViajarMarketplace', component: 'noop', meta: nuxt3Migrated },
      { path: 'ajuda/rodoviaria/acidente', name: 'marketplaceAcidente', component: 'noop', meta: nuxt3Migrated },
      { path: 'ajuda/rodoviaria/reembolso', name: 'ajudaReembolsoMarketplace', component: 'noop', meta: nuxt3Migrated },
      { path: 'ajuda/rodoviaria/falha-mecanica', name: 'falhaMecanicaMarketplace', component: 'noop', meta: nuxt3Migrated },
      { path: 'ajuda/rodoviaria/empresa-rodoviaria', name: 'empresaRodoviariaMarketplace', component: 'user/viagem/ajuda/marketplace/empresa-rodoviaria/empresa-rodoviaria' },
      { path: 'ajuda/rodoviaria/documentos-embarque', name: 'documentosEmbarqueMarketplace', component: 'user/viagem/ajuda/marketplace/documentos-embarque/documentos-embarque' },
      { path: 'cancelar/', name: 'cancelarReserva', component: 'cancelar-reserva/cancelar-reserva', meta: { hideNavBottom: true, taskbar: { onlyEssentialLinks: true } } },
      { path: 'cancelada', name: 'reservaCancelada', component: 'cancelar-reserva/reserva-cancelada' }
    ]
  },
  { path: '/perfil/verificar-cadastro', name: 'verificarCadastro', component: 'user/verificar-cadastro/verificar-cadastro', meta: meta.verificarCadastro },
  { path: '/perfil/completar-cadastro', name: 'completarCadastro', component: 'noop', meta: nuxt3Migrated },
  { path: '/perfil/close-friends', name: 'MGMListing', component: 'noop', meta: nuxt3Migrated },
  { path: '/perfil/pesquisa-relacional', name: 'pesquisaRelacional', component: 'noop', meta: nuxt3Migrated },
  { path: '/perfil/lead-opt-out', name: 'leadOptOut', component: 'noop', meta: nuxt3Migrated },
  { path: '/perfil/confirm-lead-opt-out/:token', name: 'confirmLeadOptOut', component: 'noop', meta: nuxt3Migrated },
  { path: '/perfil/retrospectiva', name: 'retrospectiva', component: 'user/retrospectiva/retrospectiva', meta: meta.retrospectiva },
  { path: '/seguro-adicional/:reserva', name: 'seguroAdicional', component: 'noop', meta: nuxt3Migrated },

  // Páginas relacionadas a viagem do pax, mas que não precisam de autenticação
  { path: '/viagem/acompanhar/:reservationCode/:departureDate', name: 'acompanharViagem', component: 'acompanhar-viagem/acompanhar-viagem', meta: meta.acompanharViagem },
  { path: '/viagem/:travel_id/pesquisa-pos-embarque/:lead_id', name: 'pesquisaPosEmbarque', component: 'noop', meta: nuxt3Migrated },
  { path: '/viagem/:travel_id/pesquisa-pos-embarque/:lead_id/:copywriting_id', name: 'pesquisaPosEmbarqueCopywriting', component: 'noop', meta: nuxt3Migrated },
  { path: '/viagem/:travel_id/pesquisa-viagem', name: 'pesquisaViagem', component: 'noop', meta: nuxt3Migrated },
  { path: '/viagem/:travelId/pesquisa-nps', name: 'pesquisaNps', component: 'noop', meta: nuxt3Migrated },

  // Erro global
  { path: '/erro', name: 'error', component: 'noop', meta: nuxt3Migrated },

  // Revendedor
  { path: '/revendedor/dashboard/:kind', name: 'dashboardPerformance', component: 'noop', meta: nuxt3Migrated },
  { path: '/reserva/vendas', name: 'vendasRevendedor', component: 'noop', meta: nuxt3Migrated },
  { path: '/consultar-pax', name: 'consultarPax', component: 'noop', meta: nuxt3Migrated },
  { path: '/revendedor/cupom', name: 'RevendedorCupons', component: 'noop', meta: nuxt3Migrated },
  { path: '/revendedor/carteira', name: 'RevendedorCarteira', component: 'noop', meta: nuxt3Migrated },

  // Redirecionamentos
  { path: '/reserva/viagens/:id', redirect: { name: 'viagem' } },
  { path: '/perfil/redefinir_senha', redirect: { name: 'redefinirSenha' } },
  { path: '/reserva/viagens/:id/cancelar/:step?', redirect: { name: 'cancelarReserva' } },
  { path: '/(black|blackfriday)', redirect: { name: 'blackFriday' } },
  { path: '/perfil/entrar/:step?/:origin?', redirect: '/auth/:step?/:origin?' },
  { path: '/upgrade-poltrona/:travel_id', name: 'upgradePoltronaRedirect', component: 'upgrade-poltrona-redirect' }
]

export default routes
